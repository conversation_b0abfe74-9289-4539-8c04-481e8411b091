
// File generated by amnesia version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:29 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\amnesia.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Dindirect_bitf +Orls -NRwL -NRbL -NRbH +Orrmw +Osps SysInit-db3ca8 mrk3

toolrelease _19R3;
//Children of func_bndl

rd_res_reg_R7 : rd_res_reg, func_bndl {
}
rd_res_reg_R7_B1 : rd_res_reg_R7 {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 810;
    isg : t;
    inp : ( R7 );
    out : ( SSP_r );
    rsc : (1) SSP_r ;
    opn : ( SSP_r_rd_R7_E1 );
    ins : 297;
}

wr_res_reg_R7 : wr_res_reg, func_bndl {
}
wr_res_reg_R7_B1 : wr_res_reg_R7 {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 811;
    isg : t;
    inp : ( SSP_w );
    out : ( R7 );
    rsc : (1) __rsrc_R7_wr_SSP_w_E1 ;
    opn : ( R7_wr_SSP_w_E1 );
    ins : 298;
}
wr_res_reg_R7_B2 : wr_res_reg_R7 {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 812;
    isg : t;
    inp : ( SSP_w );
    out : ( R7 );
    rsc : (1) __rsrc_R7_wr_SSP_w_s1_E1 ;
    opn : ( R7_wr_SSP_w_s1_E1 );
    ins : 42;
}

stack_load_bndl_1 : func_bndl, stack_load {
    arg : ( int8_:o int8_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_load_bndl_B1 : stack_load_bndl_1 {
    rid : 813;
    isg : t;
    inp : ( __spill_DM R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( DM_r );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_r DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 299;
}
stack_load_bndl_B2 : stack_load_bndl_1 {
    rid : 814;
    isg : t;
    inp : ( __spill_DM R7 __CTagu_1_pint3__cstP13_2_0_E1 );
    out : ( DM_r );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_r DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 300;
}

stack_load_bndl_2 : func_bndl, stack_load {
    arg : ( int16_:o int16_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_load_bndl_B3 : stack_load_bndl_2 {
    rid : 815;
    isg : t;
    inp : ( __spill_DMw R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( DMw_r );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_r DM_r1 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 301;
}
stack_load_bndl_B4 : stack_load_bndl_2 {
    rid : 816;
    isg : t;
    inp : ( __spill_DMw R7 __CTagu_1_pint4_step2__cstP13_3_1_E1 );
    out : ( DMw_r );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_r DM_r1 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 302;
}

stack_load_indirect_bndl_1 : func_bndl, stack_load_indirect {
    arg : ( int8_:o int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_load_indirect_bndl_B1 : stack_load_indirect_bndl_1 {
    rid : 817;
    isg : t;
    inp : ( __spill_DM R7 );
    out : ( DM_r );
    rsc : (1) DM_r SSP_r DM_rmw_addr ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 303;
}

stack_load_indirect_bndl_2 : func_bndl, stack_load_indirect {
    arg : ( int16_:o int16_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_load_indirect_bndl_B2 : stack_load_indirect_bndl_2 {
    rid : 818;
    isg : t;
    inp : ( __spill_DMw R7 );
    out : ( DMw_r );
    rsc : (1) DM_r DM_r1 SSP_r DM_rmw_addr ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 304;
}

stack_store_bndl_1 : func_bndl, stack_store {
    arg : ( int8_:o int8_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_store_bndl_B1 : stack_store_bndl_1 {
    rid : 819;
    isg : t;
    inp : ( DM_w R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( __spill_DM );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 305;
}
stack_store_bndl_B2 : stack_store_bndl_1 {
    rid : 820;
    isg : t;
    inp : ( DM_w R7 __CTagu_1_pint3__cstP13_2_0_E1 );
    out : ( __spill_DM );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 306;
}

stack_store_bndl_2 : func_bndl, stack_store {
    arg : ( int16_:o int16_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_store_bndl_B3 : stack_store_bndl_2 {
    rid : 821;
    isg : t;
    inp : ( DMw_w R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( __spill_DMw );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 307;
}
stack_store_bndl_B4 : stack_store_bndl_2 {
    rid : 822;
    isg : t;
    inp : ( DMw_w R7 __CTagu_1_pint4_step2__cstP13_3_1_E1 );
    out : ( __spill_DMw );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 308;
}

stack_store_indirect_bndl_1 : func_bndl, stack_store_indirect {
    arg : ( int8_:o int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_store_indirect_bndl_B1 : stack_store_indirect_bndl_1 {
    rid : 823;
    isg : t;
    inp : ( DM_w R7 );
    out : ( __spill_DM );
    rsc : (1) SSP_r DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 309;
}

stack_store_indirect_bndl_2 : func_bndl, stack_store_indirect {
    arg : ( int16_:o int16_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_store_indirect_bndl_B2 : stack_store_indirect_bndl_2 {
    rid : 824;
    isg : t;
    inp : ( DMw_w R7 );
    out : ( __spill_DMw );
    rsc : (1) SSP_r DM_rmw_addr ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 310;
}

_pl_rd_res_reg_const_wr_res_reg_1 : func_bndl {
    arg : ( int16_:o int16_:i int16_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 1 );
}
_pl_rd_res_reg_const_wr_res_reg_1_B1 : _pl_rd_res_reg_const_wr_res_reg_1 {
    rid : 825;
    isg : t;
    inp : ( __CTa_w1_uint16__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16__cstP16_E1
            a_w1_conv0___CTa_w1_uint16__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 311;
}
_pl_rd_res_reg_const_wr_res_reg_1_B2 : _pl_rd_res_reg_const_wr_res_reg_1 {
    rid : 826;
    isg : t;
    inp : ( __CTa_w1_uint16_0_32767__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) mylea a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_lea_as_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_mylea_1_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16_0_32767__cstP16_E1
            a_w1_conv0___CTa_w1_uint16_0_32767__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_dummy_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 312;
}

ret_1 : func_bndl, ret {
    fst : 1;
}
ret_1_B1 : ret_1 {
    rid : 827;
    isg : t;
    opn : ( vd_ret_E1 );
    ins : 189;
}

load_const__or_const_store_1 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int8_:i uint16_:i any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 );
}
load_const__or_const_store_1_B1 : load_const__or_const_store_1 {
    rid : 828;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 PM );
    out : ( DM9 PM nz_flag_w );
    rsc : (1) DM_r DM_rmw_addr a_b2 a_b0 a_b1 DM_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            a_b2_bwor_a_b0_a_b1_alu_E1
            a_b0_copy0_DM_r_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_w_copy0_a_b2_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 313;
}

jump_const_1 : func_bndl, jump, relative {
    arg : ( rel8_:i );
    fst : 1;
    ist : ( 1 );
}
jump_const_1_B1 : jump_const_1 {
    rid : 829;
    isg : t;
    inp : ( __CToffs_rel8__cstP8_E1 );
    rsc : (1) offs ;
    opn : ( vd_jump_offs_E1
            offs_rel8__cstP8_E1
            offs_copy0___CToffs_rel8__cstP8_E1 );
    ins : 176;
}

store_const_const_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
store_const_const_1_B1 : store_const_const_1 {
    rid : 830;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 );
    out : ( DM9 );
    rsc : (1) a_b0 a_b2 DM_w DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 );
    ins : 314;
}

load_const_cmp_const_cc_eq__jump_const_1 : func_bndl, jump, relative {
    arg : ( int16_:i rel8_:i int8_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 2 1 );
    ost : ( 1 1 1 );
}
load_const_cmp_const_cc_eq__jump_const_1_B1 : load_const_cmp_const_cc_eq__jump_const_1 {
    rid : 831;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint9__cstP7_8P16_7_0_E1 __CToffs_rel8__cstP8_E2 DM9 );
    out : ( c_flag_w nz_flag_w o_flag_w );
    rsc : (1) DM_r DM_rmw_addr c_flag_w o_flag_w nz_flag_w a_b0 a_b1 __nz_flag_pipe_w ,
          (2) tcc __nz_flag_pipe_r offs ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP7_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP7_8P16_7_0_E1
            vd_cmp_a_b0_a_b1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_b0_copy0_DM_r_E1
            a_b1_uint8__cstP24_E1
            a_b1_conv0___CTa_b1_uint8__cstP24_E1
            tcc_cc_eq_nz_flag_pipe_ccu_E2
            nz_flag_pipe_copy0_nz_flag_w_E1
            _pipe_nz_flag_pipe_E1
            vd_jump_tcc_offs_E2
            offs_rel8__cstP8_E2
            offs_copy0___CToffs_rel8__cstP8_E2 );
    ins : 315;
}

const_1 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_1_B1 : const_1 {
    rid : 833;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 316;
}
const_1_O1 : const_1 {
}
const_1_B2 : const_1_O1 {
    rid : 834;
    isg : t;
    out : ( a_w0 );
    rsc : (1) a_w0 ;
    opn : ( a_w0_uint4_0_10__cstP9_E1
            a_w0_conv0___CTa_w0_uint4_0_10__cstP9_E1 );
    ins : 317;
}
const_1_B3 : const_1_O1 {
    rid : 835;
    isg : t;
    out : ( a_w0 );
    rsc : (1) a_w0 ;
    opn : ( a_w0_uint3__cstP10_E1
            a_w0_conv0___CTa_w0_uint3__cstP10_E1 );
    ins : 318;
}

call_const_1 : func_bndl, call, absolute {
    arg : ( int16_:i );
    fst : 1;
    ist : ( 1 );
}
call_const_1_B1 : call_const_1 {
    rid : 836;
    isg : t;
    inp : ( __CTtrgt_uint16__cstP16_E1 );
    rsc : (1) trgt ;
    opn : ( vd_call_trgt_E1
            trgt_uint16__cstP16_E1
            trgt_conv0___CTtrgt_uint16__cstP16_E1 );
    ins : 319;
}

const_2 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_2_B1 : const_2 {
    rid : 837;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 320;
}
const_2_O1 : const_2 {
}
const_2_B2 : const_2_O1 {
    rid : 838;
    isg : t;
    out : ( a_w0 );
    rsc : (1) a_w0 ;
    opn : ( a_w0_uint4_0_10__cstP9_E1
            a_w0_conv0___CTa_w0_uint4_0_10__cstP9_E1 );
    ins : 321;
}
const_2_B3 : const_2_O1 {
    rid : 839;
    isg : t;
    out : ( a_w0 );
    rsc : (1) a_w0 ;
    opn : ( a_w0_uint3__cstP10_E1
            a_w0_conv0___CTa_w0_uint3__cstP10_E1 );
    ins : 322;
}

load__pl_rd_res_reg_const__ad_const_store_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int8_:i int16_:i any:o );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 );
}
load__pl_rd_res_reg_const__ad_const_store_1_B1 : load__pl_rd_res_reg_const__ad_const_store_1 {
    rid : 841;
    isg : t;
    inp : ( __CTagu_1_uint9__cstP13_8P16_7_0_E1 DM DM R7 );
    out : ( DM nz_flag_w );
    rsc : (1) DM_r agu_2 DM_rmw_addr SSP_r agu_0 agu_1 a_b2 a_b0 a_b1 DM_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint9__cstP13_8P16_7_0_E1
            agu_1_conv0___CTagu_1_uint9__cstP13_8P16_7_0_E1
            a_b2_bwand_a_b0_a_b1_alu_E1
            a_b0_copy0_DM_r_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_w_copy0_a_b2_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 323;
}

_ad_const_cmp_const_1 : func_bndl {
    arg : ( uint2_:o int16_:i );
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
_ad_const_cmp_const_1_B1 : _ad_const_cmp_const_1 {
    rid : 850;
    isg : t;
    inp : ( a_w0 );
    out : ( nz_flag_w );
    rsc : (1) a_w2 a_w1 dummy_c_w dummy_o_w nz_flag_w a_w3 ;
    opn : ( a_w2_bwand_a_w0_a_w1_alu_E1
            a_w1_uint16__cstP16_E1
            a_w1_conv0___CTa_w1_uint16__cstP16_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1
            a_w3_int16__cstV0_E1
            a_w3_copy0___CTa_w3_int16__cstV0_E1 );
    ins : 324;
}

cc_eq__jump_const_1 : func_bndl, jump, relative {
    arg : ( uint2_:i rel8_:i );
    fst : 1;
    ist : ( 1 1 );
}
cc_eq__jump_const_1_B1 : cc_eq__jump_const_1 {
    rid : 851;
    isg : t;
    inp : ( nz_flag_r __CToffs_rel8__cstP8_E1 );
    rsc : (1) tcc offs ;
    opn : ( tcc_cc_eq_nz_flag_r_ccu_E1
            vd_jump_tcc_offs_E1
            offs_rel8__cstP8_E1
            offs_copy0___CToffs_rel8__cstP8_E1 );
    ins : 325;
}

const_3 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_3_B1 : const_3 {
    rid : 852;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 326;
}

const_4 : func_bndl {
    arg : ( uint8_:o );
    fst : 1;
    ost : ( 1 );
}
const_4_B1 : const_4 {
    rid : 854;
    isg : t;
    out : ( __CTa_b0_int8__cstP24_E1 );
    opn : ( a_b0_int8__cstP24_E1 );
    ins : 327;
}
const_4_O1 : const_4 {
}
const_4_B2 : const_4_O1 {
    rid : 855;
    isg : t;
    out : ( a_b0 );
    rsc : (1) a_b0 ;
    opn : ( a_b0_uint4_0_10__cstP9_E1
            a_b0_conv0___CTa_b0_uint4_0_10__cstP9_E1 );
    ins : 328;
}
const_4_B3 : const_4_O1 {
    rid : 856;
    isg : t;
    out : ( a_b0 );
    rsc : (1) a_b0 ;
    opn : ( a_b0_uint3__cstP10_E1
            a_b0_conv0___CTa_b0_uint3__cstP10_E1 );
    ins : 329;
}

load_const_mov_bf_const_const_1 : func_bndl {
    arg : ( int8_:o int8_:o uint16_:o int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 );
}
load_const_mov_bf_const_const_1_B1 : load_const_mov_bf_const_const_1 {
    rid : 859;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint9__cstP12_8P24_7_0_E1 DM9 PM );
    out : ( a_b2 DM9 PM );
    rsc : (1) DM_r DM_rmw_addr a_b2 a_b1 t_bw t_bp ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP12_8P24_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP12_8P24_7_0_E1
            a_b2_mov_bf_a_b1_t_bw_t_bp_alu_E1
            a_b1_copy0_DM_r_E1
            t_bw_uint4__cstP16_E1
            t_bw_copy0___CTt_bw_uint4__cstP16_E1
            t_bp_uint4__cstP20_E1
            t_bp_copy0___CTt_bp_uint4__cstP20_E1 );
    ins : 330;
}

cmp_const_1 : func_bndl {
    arg : ( uint2_:o int8_:i any:o any:o );
    fst : 1;
    ist : ( 1 );
    ost : ( 1 1 1 );
}
cmp_const_1_B1 : cmp_const_1 {
    rid : 860;
    isg : t;
    inp : ( a_b0 );
    out : ( nz_flag_w c_flag_w o_flag_w );
    rsc : (1) c_flag_w o_flag_w nz_flag_w a_b1 ;
    opn : ( vd_cmp_a_b0_a_b1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1 );
    ins : 331;
}
cmp_const_1_B2 : cmp_const_1 {
    rid : 861;
    isg : t;
    inp : ( a_b0 );
    out : ( nz_flag_w c_flag_w o_flag_w );
    rsc : (1) c_flag_w o_flag_w nz_flag_w a_b1 ;
    opn : ( vd_cmp_a_b0_a_b1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_b1_uint4_0_10__cstP9_E1
            a_b1_conv0___CTa_b1_uint4_0_10__cstP9_E1 );
    ins : 332;
}
cmp_const_1_B3 : cmp_const_1 {
    rid : 862;
    isg : t;
    inp : ( a_b0 );
    out : ( nz_flag_w c_flag_w o_flag_w );
    rsc : (1) c_flag_w o_flag_w nz_flag_w a_b1 ;
    opn : ( vd_cmp_a_b0_a_b1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_b1_uint3__cstP10_E1
            a_b1_conv0___CTa_b1_uint3__cstP10_E1 );
    ins : 333;
}

cc_ne__jump_const_1 : func_bndl, jump, relative {
    arg : ( uint2_:i rel8_:i );
    fst : 1;
    ist : ( 1 1 );
}
cc_ne__jump_const_1_B1 : cc_ne__jump_const_1 {
    rid : 863;
    isg : t;
    inp : ( nz_flag_r __CToffs_rel8__cstP8_E1 );
    rsc : (1) tcc offs ;
    opn : ( tcc_cc_ne_nz_flag_r_ccu_E1
            vd_jump_tcc_offs_E1
            offs_rel8__cstP8_E1
            offs_copy0___CToffs_rel8__cstP8_E1 );
    ins : 334;
}

load_const__ad_const_cmp_const_cc_eq__jump_const_1 : func_bndl, jump, relative {
    arg : ( int8_:o uint16_:o int16_:i rel8_:i int8_:i uint16_:i any:o );
    fst : 1;
    ist : ( 1 2 1 1 );
    ost : ( 1 1 1 );
}
load_const__ad_const_cmp_const_cc_eq__jump_const_1_B1 : load_const__ad_const_cmp_const_cc_eq__jump_const_1 {
    rid : 864;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint9__cstP7_8P16_7_0_E1 __CToffs_rel8__cstP8_E2 DM9 PM );
    out : ( DM9 PM nz_flag_w );
    rsc : (1) DM_r DM_rmw_addr a_b2 a_b0 a_b1 dummy_c_w dummy_o_w nz_flag_w a_b3 __nz_flag_pipe_w ,
          (2) tcc __nz_flag_pipe_r offs ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP7_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP7_8P16_7_0_E1
            a_b2_bwand_a_b0_a_b1_alu_E1
            a_b0_copy0_DM_r_E1
            a_b1_uint8__cstP24_E1
            a_b1_conv0___CTa_b1_uint8__cstP24_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1
            a_b3_int8__cstV0_E1
            a_b3_copy0___CTa_b3_int8__cstV0_E1
            tcc_cc_eq_nz_flag_pipe_ccu_E2
            nz_flag_pipe_copy0_nz_flag_w_E1
            _pipe_nz_flag_pipe_E1
            vd_jump_tcc_offs_E2
            offs_rel8__cstP8_E2
            offs_copy0___CToffs_rel8__cstP8_E2 );
    ins : 335;
}

store_const_const_2 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 );
}
store_const_const_2_B1 : store_const_const_2 {
    rid : 865;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) a_w0 a_w2 DM_w DM_w1 DM_rmw_addr ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            a_w0_uint8__cstP24_E1
            a_w0_conv0___CTa_w0_uint8__cstP24_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 );
    ins : 336;
}

load_const__ad_const_store_1 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int8_:i uint16_:i any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 );
}
load_const__ad_const_store_1_B1 : load_const__ad_const_store_1 {
    rid : 866;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 PM );
    out : ( DM9 PM nz_flag_w );
    rsc : (1) DM_r DM_rmw_addr a_b2 a_b0 a_b1 DM_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            a_b2_bwand_a_b0_a_b1_alu_E1
            a_b0_copy0_DM_r_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_w_copy0_a_b2_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 337;
}

load_const__ad_const_store_2 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int8_:i uint16_:i any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 );
}
load_const__ad_const_store_2_B1 : load_const__ad_const_store_2 {
    rid : 867;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 PM );
    out : ( DM9 PM nz_flag_w );
    rsc : (1) DM_r DM_rmw_addr a_b2 a_b0 a_b1 DM_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            a_b2_bwand_a_b0_a_b1_alu_E1
            a_b0_copy0_DM_r_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_w_copy0_a_b2_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 338;
}

load__pl_rd_res_reg_const_1 : func_bndl {
    arg : ( int16_:o int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
load__pl_rd_res_reg_const_1_B1 : load__pl_rd_res_reg_const_1 {
    rid : 868;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DMw_r );
    rsc : (1) DM_r DM_r1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1 );
    ins : 339;
}
load__pl_rd_res_reg_const_1_B2 : load__pl_rd_res_reg_const_1 {
    rid : 869;
    isg : t;
    inp : ( __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( DMw_r );
    rsc : (1) DM_r DM_r1 agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1 );
    ins : 340;
}

store_extract_hi_const_1 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 );
}
store_extract_hi_const_1_B1 : store_extract_hi_const_1 {
    rid : 870;
    isg : t;
    inp : ( __cvT4 __CTDM_rmw_addr_uint16__cstP16_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) a_b0 __RbH_a_b0_rad a_b2 DM_w DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b2_extract_hi___cvT4cvB1
            DM_w_copy0_a_b2_E1
            DM_rmw_addr_uint16__cstP16_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint16__cstP16_E1 );
    ins : 341;
}
store_extract_hi_const_1_B2 : store_extract_hi_const_1 {
    rid : 871;
    isg : t;
    inp : ( __cvT4 __CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) a_b0 __RbH_a_b0_rad a_b2 DM_w DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b2_extract_hi___cvT4cvB1
            DM_w_copy0_a_b2_E1
            DM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 );
    ins : 342;
}

load__pl_rd_res_reg_const___uchar_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
load__pl_rd_res_reg_const___uchar_1_B1 : load__pl_rd_res_reg_const___uchar_1 {
    rid : 872;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( a_b0 );
    rsc : (1) __cvT8 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 DM_r a_b0 ;
    opn : ( __cvT8_ld_DMw_DM_rmw_addrcvB1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            a_b0_extract_lo___cvT8cvB1 );
    ins : 343;
}
load__pl_rd_res_reg_const___uchar_1_B2 : load__pl_rd_res_reg_const___uchar_1 {
    rid : 873;
    isg : t;
    inp : ( __CTagu_1_pint3__cstP13_2_0_E1 DM R7 );
    out : ( a_b0 );
    rsc : (1) __cvT8 agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 DM_r a_b0 ;
    opn : ( __cvT8_ld_DMw_DM_rmw_addrcvB1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1
            a_b0_extract_lo___cvT8cvB1 );
    ins : 344;
}

_ad_const_1 : func_bndl {
    arg : ( int8_:o int8_:i any:o );
    fst : 1;
    ist : ( 1 );
    ost : ( 1 1 );
}
_ad_const_1_B1 : _ad_const_1 {
    rid : 874;
    isg : t;
    inp : ( a_b0 );
    out : ( a_b2 nz_flag_w );
    rsc : (1) a_b2 a_b1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_b2_bwand_a_b0_a_b1_alu_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 345;
}

shl_const_1 : func_bndl {
    arg : ( int8_:o int8_:i any:o any:o );
    fst : 1;
    ist : ( 1 );
    ost : ( 1 1 1 );
}
shl_const_1_B1 : shl_const_1 {
    rid : 875;
    isg : t;
    inp : ( a_b0 );
    out : ( a_b2 c_flag_w nz_flag_w );
    rsc : (1) a_b2 c_flag_w a_b1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_b2_shl_a_b0_a_b1_c_flag_w_shft_E1
            a_b1_uint4__cstP9_E1
            a_b1_conv0___CTa_b1_uint4__cstP9_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 346;
}

_or_const_1 : func_bndl {
    arg : ( int8_:o int8_:i any:o );
    fst : 1;
    ist : ( 1 );
    ost : ( 1 1 );
}
_or_const_1_B1 : _or_const_1 {
    rid : 876;
    isg : t;
    inp : ( a_b0 );
    out : ( a_b2 nz_flag_w );
    rsc : (1) a_b2 a_b1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_b2_bwor_a_b0_a_b1_alu_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 347;
}

store_const_1 : func_bndl {
    arg : ( int8_:o uint16_:o int8_:i int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 );
}
store_const_1_B1 : store_const_1 {
    rid : 877;
    isg : t;
    inp : ( DM_w __CTDM_rmw_addr_uint16__cstP16_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_uint16__cstP16_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint16__cstP16_E1 );
    ins : 348;
}
store_const_1_B2 : store_const_1 {
    rid : 878;
    isg : t;
    inp : ( DM_w __CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 );
    ins : 349;
}

store__pl_rd_res_reg_const_1 : func_bndl {
    arg : ( int8_:o int16_:i int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 );
}
store__pl_rd_res_reg_const_1_B1 : store__pl_rd_res_reg_const_1 {
    rid : 879;
    isg : t;
    inp : ( DMw_w __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 DM_rmw_addr SSP_r agu_0 agu_1 ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1 );
    ins : 350;
}
store__pl_rd_res_reg_const_1_B2 : store__pl_rd_res_reg_const_1 {
    rid : 880;
    isg : t;
    inp : ( DMw_w __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1 );
    ins : 351;
}

const_5 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_5_B1 : const_5 {
    rid : 881;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 352;
}

load_const_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
load_const_1_B1 : load_const_1 {
    rid : 883;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint16__cstP16_E1 DM );
    out : ( DM_r );
    rsc : (1) DM_r DM_rmw_addr ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint16__cstP16_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint16__cstP16_E1 );
    ins : 353;
}

store_const_2 : func_bndl {
    arg : ( int8_:o uint16_:o uint16_1_32768_:i int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 );
}
store_const_2_B1 : store_const_2 {
    rid : 913;
    isg : t;
    inp : ( DMw_w __CTDM_rmw_addr_uint16__cstP16_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_rmw_addr ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_uint16__cstP16_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint16__cstP16_E1 );
    ins : 354;
}
store_const_2_B2 : store_const_2 {
    rid : 914;
    isg : t;
    inp : ( DMw_w __CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_rmw_addr ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 );
    ins : 355;
}

const_6 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_6_B1 : const_6 {
    rid : 915;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 356;
}

const_7 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_7_B1 : const_7 {
    rid : 918;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 357;
}

const_8 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_8_B1 : const_8 {
    rid : 921;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 358;
}

const_9 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_9_B1 : const_9 {
    rid : 924;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 359;
}

store_const_const_3 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 );
}
store_const_const_3_B1 : store_const_const_3 {
    rid : 925;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) a_b0 a_b2 DM_w DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 );
    ins : 314;
}

store_const_const_4 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 );
}
store_const_const_4_B1 : store_const_const_4 {
    rid : 926;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) a_b0 a_b2 DM_w DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 );
    ins : 360;
}

store_const_const_5 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 );
}
store_const_const_5_B1 : store_const_const_5 {
    rid : 928;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) a_b0 a_b2 DM_w DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 );
    ins : 361;
}

load__pl_rd_res_reg_const_cmp_const_1 : func_bndl {
    arg : ( uint2_:o int16_:i int8_:i int16_:i any:o any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 );
}
load__pl_rd_res_reg_const_cmp_const_1_B1 : load__pl_rd_res_reg_const_cmp_const_1 {
    rid : 929;
    isg : t;
    inp : ( __CTagu_1_uint9__cstP13_8P16_7_0_E1 DM R7 );
    out : ( nz_flag_w c_flag_w o_flag_w );
    rsc : (1) DM_r agu_2 DM_rmw_addr SSP_r agu_0 agu_1 c_flag_w o_flag_w nz_flag_w a_b0 a_b1 ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint9__cstP13_8P16_7_0_E1
            agu_1_conv0___CTagu_1_uint9__cstP13_8P16_7_0_E1
            vd_cmp_a_b0_a_b1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_b0_copy0_DM_r_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1 );
    ins : 362;
}

store_1 : func_bndl {
    arg : ( int8_:o int8_:i int16_:i int8_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
store_1_B1 : store_1 {
    rid : 931;
    isg : t;
    inp : ( DM_w DM_rmw_addr DM );
    out : ( DM );
    opn : ( DM_st_DM_w_DM_rmw_addr_E1 );
    ins : 363;
}

const_10 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_10_B1 : const_10 {
    rid : 932;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 364;
}

store_const_const_6 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 );
}
store_const_const_6_B1 : store_const_const_6 {
    rid : 935;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) a_b0 a_b2 DM_w DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 );
    ins : 365;
}

store_const_const_7 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 );
}
store_const_const_7_B1 : store_const_const_7 {
    rid : 937;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) a_b0 a_b2 DM_w DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 );
    ins : 366;
}

load__pl_rd_res_reg_const_2 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
load__pl_rd_res_reg_const_2_B1 : load__pl_rd_res_reg_const_2 {
    rid : 938;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DM_r );
    rsc : (1) DM_r agu_2 DM_rmw_addr SSP_r agu_0 agu_1 ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1 );
    ins : 343;
}
load__pl_rd_res_reg_const_2_B2 : load__pl_rd_res_reg_const_2 {
    rid : 939;
    isg : t;
    inp : ( __CTagu_1_pint3__cstP13_2_0_E1 DM R7 );
    out : ( DM_r );
    rsc : (1) DM_r agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1 );
    ins : 344;
}

load_const_2 : func_bndl {
    arg : ( int8_:o int8_:o uint16_:o int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 );
}
load_const_2_B1 : load_const_2 {
    rid : 942;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint16__cstP16_E1 DM9 PM );
    out : ( DM_r DM9 PM );
    rsc : (1) DM_r DM_rmw_addr ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint16__cstP16_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint16__cstP16_E1 );
    ins : 353;
}
load_const_2_B2 : load_const_2 {
    rid : 943;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 DM9 PM );
    out : ( DM_r DM9 PM );
    rsc : (1) DM_r DM_rmw_addr ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 );
    ins : 367;
}

_ad_const_2 : func_bndl {
    arg : ( int8_:o int8_:i any:o );
    fst : 1;
    ist : ( 1 );
    ost : ( 1 1 );
}
_ad_const_2_B1 : _ad_const_2 {
    rid : 944;
    isg : t;
    inp : ( a_b0 );
    out : ( a_b2 nz_flag_w );
    rsc : (1) a_b2 a_b1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_b2_bwand_a_b0_a_b1_alu_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 368;
}

store__pl_rd_res_reg_const_2 : func_bndl {
    arg : ( int8_:o int8_:i int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 );
}
store__pl_rd_res_reg_const_2_B1 : store__pl_rd_res_reg_const_2 {
    rid : 945;
    isg : t;
    inp : ( DM_w __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 DM_rmw_addr SSP_r agu_0 agu_1 ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1 );
    ins : 369;
}
store__pl_rd_res_reg_const_2_B2 : store__pl_rd_res_reg_const_2 {
    rid : 946;
    isg : t;
    inp : ( DM_w __CTagu_1_pint3__cstP13_2_0_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1 );
    ins : 370;
}

_or_const_2 : func_bndl {
    arg : ( int8_:o int8_:i any:o );
    fst : 1;
    ist : ( 1 );
    ost : ( 1 1 );
}
_or_const_2_B1 : _or_const_2 {
    rid : 952;
    isg : t;
    inp : ( a_b0 );
    out : ( a_b2 nz_flag_w );
    rsc : (1) a_b2 a_b1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_b2_bwor_a_b0_a_b1_alu_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 371;
}

_ad_const_3 : func_bndl {
    arg : ( int8_:o int8_:i any:o );
    fst : 1;
    ist : ( 1 );
    ost : ( 1 1 );
}
_ad_const_3_B1 : _ad_const_3 {
    rid : 957;
    isg : t;
    inp : ( a_b0 );
    out : ( a_b2 nz_flag_w );
    rsc : (1) a_b2 a_b1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_b2_bwand_a_b0_a_b1_alu_E1
            a_b1_int8__cstP24_E1
            a_b1_copy0___CTa_b1_int8__cstP24_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 372;
}
_ad_const_3_B2 : _ad_const_3 {
    rid : 958;
    isg : t;
    inp : ( a_b0 );
    out : ( a_b2 nz_flag_w );
    rsc : (1) a_b2 a_b1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_b2_bwand_a_b0_a_b1_alu_E1
            a_b1_uint4_0_10__cstP9_E1
            a_b1_conv0___CTa_b1_uint4_0_10__cstP9_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 373;
}

const_11 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_11_B1 : const_11 {
    rid : 963;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 374;
}

const_12 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_12_B1 : const_12 {
    rid : 966;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 375;
}

store_const_3 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 );
}
store_const_3_B1 : store_const_3 {
    rid : 967;
    isg : t;
    inp : ( DMw_w __CTDM_rmw_addr_uint16__cstP16_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_rmw_addr ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_uint16__cstP16_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint16__cstP16_E1 );
    ins : 354;
}
store_const_3_B2 : store_const_3 {
    rid : 968;
    isg : t;
    inp : ( DMw_w __CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_rmw_addr ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 );
    ins : 355;
}

const_13 : func_bndl {
    arg : ( int16_:o );
    fst : 1;
    ost : ( 1 );
}
const_13_B1 : const_13 {
    rid : 969;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 376;
}

const_14 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_14_B1 : const_14 {
    rid : 972;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 377;
}

const_15 : func_bndl {
    arg : ( uint8_:o );
    fst : 1;
    ost : ( 1 );
}
const_15_B1 : const_15 {
    rid : 973;
    isg : t;
    out : ( __CTa_b0_int8__cstP24_E1 );
    opn : ( a_b0_int8__cstP24_E1 );
    ins : 378;
}
const_15_O1 : const_15 {
}
const_15_B2 : const_15_O1 {
    rid : 974;
    isg : t;
    out : ( a_b0 );
    rsc : (1) a_b0 ;
    opn : ( a_b0_uint4_0_10__cstP9_E1
            a_b0_conv0___CTa_b0_uint4_0_10__cstP9_E1 );
    ins : 379;
}
const_15_B3 : const_15_O1 {
    rid : 975;
    isg : t;
    out : ( a_b0 );
    rsc : (1) a_b0 ;
    opn : ( a_b0_uint3__cstP10_E1
            a_b0_conv0___CTa_b0_uint3__cstP10_E1 );
    ins : 380;
}

store_const_const_8 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
store_const_const_8_B1 : store_const_const_8 {
    rid : 977;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 DM9 );
    out : ( DM9 );
    rsc : (1) a_b0 a_b2 DM_w DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            DM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint12__cstP12_11_8P16_7_0_E1 );
    ins : 360;
}

load_const__ad_const_cmp_const_cc_eq__jump_const_2 : func_bndl, jump, relative {
    arg : ( int8_:o uint16_:o int16_:i rel8_:i int8_:i uint16_:i any:o );
    fst : 1;
    ist : ( 1 2 1 1 );
    ost : ( 1 1 1 );
}
load_const__ad_const_cmp_const_cc_eq__jump_const_2_B1 : load_const__ad_const_cmp_const_cc_eq__jump_const_2 {
    rid : 979;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint9__cstP7_8P16_7_0_E1 __CToffs_rel8__cstP8_E2 DM9 PM );
    out : ( DM9 PM nz_flag_w );
    rsc : (1) DM_r DM_rmw_addr a_b2 a_b0 a_b1 dummy_c_w dummy_o_w nz_flag_w a_b3 __nz_flag_pipe_w ,
          (2) tcc __nz_flag_pipe_r offs ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP7_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP7_8P16_7_0_E1
            a_b2_bwand_a_b0_a_b1_alu_E1
            a_b0_copy0_DM_r_E1
            a_b1_uint8__cstP24_E1
            a_b1_conv0___CTa_b1_uint8__cstP24_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1
            a_b3_int8__cstV0_E1
            a_b3_copy0___CTa_b3_int8__cstV0_E1
            tcc_cc_eq_nz_flag_pipe_ccu_E2
            nz_flag_pipe_copy0_nz_flag_w_E1
            _pipe_nz_flag_pipe_E1
            vd_jump_tcc_offs_E2
            offs_rel8__cstP8_E2
            offs_copy0___CToffs_rel8__cstP8_E2 );
    ins : 381;
}

_pl_rd_res_reg_const_store_const_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
_pl_rd_res_reg_const_store_const_1_B1 : _pl_rd_res_reg_const_store_const_1 {
    rid : 980;
    isg : t;
    inp : ( __CTagu_1_uint9__cstP13_8P16_7_0_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r agu_0 agu_1 DM_rmw_addr a_w0 a_w2 DM_w DM_w1 ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint9__cstP13_8P16_7_0_E1
            agu_1_conv0___CTagu_1_uint9__cstP13_8P16_7_0_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1
            a_w0_uint8__cstP24_E1
            a_w0_conv0___CTa_w0_uint8__cstP24_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 382;
}
_pl_rd_res_reg_const_store_const_1_B2 : _pl_rd_res_reg_const_store_const_1 {
    rid : 981;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r agu_0 agu_1 DM_rmw_addr a_w0 a_w2 DM_w DM_w1 ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1
            a_w0_uint4_0_10__cstP9_E1
            a_w0_conv0___CTa_w0_uint4_0_10__cstP9_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 383;
}
_pl_rd_res_reg_const_store_const_1_B3 : _pl_rd_res_reg_const_store_const_1 {
    rid : 982;
    isg : t;
    inp : ( __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r SP_r agu_0 agu_1 DM_rmw_addr a_w0 a_w2 DM_w DM_w1 ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1
            a_w0_uint3__cstP10_E1
            a_w0_conv0___CTa_w0_uint3__cstP10_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 384;
}

_pl_rd_res_reg_const_1 : func_bndl {
    arg : ( int16_:o int16_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 1 1 1 );
}
_pl_rd_res_reg_const_1_B1 : _pl_rd_res_reg_const_1 {
    rid : 983;
    isg : t;
    inp : ( __CTa_w1_uint16_0_32767__cstP16_E1 R7 );
    out : ( a_w2 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) mylea a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_lea_as_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_mylea_1_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16_0_32767__cstP16_E1
            a_w1_conv0___CTa_w1_uint16_0_32767__cstP16_E1
            vd_cmp_dummy_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 385;
}

store_const_4 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
store_const_4_B1 : store_const_4 {
    rid : 984;
    isg : t;
    inp : ( DM_rmw_addr DM );
    out : ( DM );
    rsc : (1) a_b0 a_b2 DM_w ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1 );
    ins : 386;
}
store_const_4_B2 : store_const_4 {
    rid : 985;
    isg : t;
    inp : ( DM_rmw_addr DM );
    out : ( DM );
    rsc : (1) a_b0 a_b2 DM_w ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_uint4_0_10__cstP9_E1
            a_b0_conv0___CTa_b0_uint4_0_10__cstP9_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1 );
    ins : 387;
}

store_const__pl_rd_res_reg_const_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
store_const__pl_rd_res_reg_const_1_B1 : store_const__pl_rd_res_reg_const_1 {
    rid : 989;
    isg : t;
    inp : ( __CTagu_1_uint9__cstP13_8P16_7_0_E1 DM R7 );
    out : ( DM );
    rsc : (1) a_b0 a_b2 DM_w agu_2 DM_rmw_addr SSP_r agu_0 agu_1 ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint9__cstP13_8P16_7_0_E1
            agu_1_conv0___CTagu_1_uint9__cstP13_8P16_7_0_E1 );
    ins : 388;
}
store_const__pl_rd_res_reg_const_1_B2 : store_const__pl_rd_res_reg_const_1 {
    rid : 990;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DM );
    rsc : (1) a_b0 a_b2 DM_w agu_2 DM_rmw_addr SSP_r agu_0 agu_1 ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_uint4_0_10__cstP9_E1
            a_b0_conv0___CTa_b0_uint4_0_10__cstP9_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1 );
    ins : 389;
}
store_const__pl_rd_res_reg_const_1_B3 : store_const__pl_rd_res_reg_const_1 {
    rid : 991;
    isg : t;
    inp : ( __CTagu_1_pint3__cstP13_2_0_E1 DM R7 );
    out : ( DM );
    rsc : (1) a_b0 a_b2 DM_w agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            a_b0_uint3__cstP10_E1
            a_b0_conv0___CTa_b0_uint3__cstP10_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1 );
    ins : 329;
}

_mi_rd_res_reg_const_wr_res_reg_1 : func_bndl {
    arg : ( int16_:o int16_:i int16_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 1 );
}
_mi_rd_res_reg_const_wr_res_reg_1_B1 : _mi_rd_res_reg_const_wr_res_reg_1 {
    rid : 1002;
    isg : t;
    inp : ( __CTa_w1_uint16__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16__cstP16_E1
            a_w1_conv0___CTa_w1_uint16__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 390;
}
_mi_rd_res_reg_const_wr_res_reg_1_B2 : _mi_rd_res_reg_const_wr_res_reg_1 {
    rid : 1003;
    isg : t;
    inp : ( __CTa_w1_uint16_1_32768__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) mylea a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_lea_as_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_mylea_1_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16_1_32768__cstP16_E1
            a_w1_conv0___CTa_w1_uint16_1_32768__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_dummy_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 391;
}

//Children of mv_bndl

nz_flag_1_dr_move_nz_flag_w_1_any : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 1004;
    isg : t;
    inp : ( nz_flag_w );
    out : ( nz_flag );
    rsc : (1) __rsrc_nz_flag_wr_nz_flag_w_E1 ;
    opn : ( nz_flag_wr_nz_flag_w_E1 );
    ins : 392;
}

nz_flag_r_1_dr_move_nz_flag_1_uint2_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 1005;
    isg : t;
    inp : ( nz_flag );
    out : ( nz_flag_r );
    rsc : (1) nz_flag_r ;
    opn : ( nz_flag_r_rd_nz_flag_E1 );
    ins : 93;
}

nz_flag_1_dr_move_nz_flag_w_1_uint2_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 1006;
    isg : t;
    inp : ( nz_flag_w );
    out : ( nz_flag );
    rsc : (1) __rsrc_nz_flag_wr_nz_flag_w_E1 ;
    opn : ( nz_flag_wr_nz_flag_w_E1 );
    ins : 392;
}

c_flag_1_dr_move_c_flag_w_1_any : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 1007;
    isg : t;
    inp : ( c_flag_w );
    out : ( c_flag );
    rsc : (1) __rsrc_c_flag_wr_c_flag_w_E1 ;
    opn : ( c_flag_wr_c_flag_w_E1 );
    ins : 9;
}

o_flag_1_dr_move_o_flag_w_1_any : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 1008;
    isg : t;
    inp : ( o_flag_w );
    out : ( o_flag );
    rsc : (1) __rsrc_o_flag_wr_o_flag_w_E1 ;
    opn : ( o_flag_wr_o_flag_w_E1 );
    ins : 10;
}

a_b0_1_dr_move_Rb_1_int8_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
a_b0_1_dr_move_Rb_1_int8__B0 : a_b0_1_dr_move_Rb_1_int8_ {
    rid : 1009;
    isg : t;
    inp : ( RbL );
    out : ( a_b0 );
    rsc : (1) a_b0 ;
    opn : ( a_b0_rd_RbL_class_Rb___RbL_a_b0_rad_E1 );
    ins : 393;
}
a_b0_1_dr_move_Rb_1_int8__B1 : a_b0_1_dr_move_Rb_1_int8_ {
    rid : 1010;
    isg : t;
    inp : ( RbH );
    out : ( a_b0 );
    rsc : (1) a_b0 ;
    opn : ( a_b0_rd_RbH_class_Rb___RbH_a_b0_rad_E1 );
    ins : 394;
}

Rb_1_dr_move_DM_r_1_int8_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rb_1_dr_move_DM_r_1_int8__B0 : Rb_1_dr_move_DM_r_1_int8_ {
    rid : 1011;
    isg : t;
    inp : ( DM_r );
    out : ( RbL );
    rsc : (1) __RbL_a_b2_wad a_b2 a_b0 __rsrc_RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 ;
    opn : ( a_b0_copy0_DM_r_E1
            a_b2_copy0_a_b0_E1
            RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 );
    ins : 395;
}
Rb_1_dr_move_DM_r_1_int8__B1 : Rb_1_dr_move_DM_r_1_int8_ {
    rid : 1012;
    isg : t;
    inp : ( DM_r );
    out : ( RbH );
    rsc : (1) a_b2 __RbH_a_b2_wad a_b0 __rsrc_RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 ;
    opn : ( a_b0_copy0_DM_r_E1
            a_b2_copy0_a_b0_E1
            RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 );
    ins : 396;
}

DM_w_1_dr_move_Rb_1_int8_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
DM_w_1_dr_move_Rb_1_int8__B0 : DM_w_1_dr_move_Rb_1_int8_ {
    rid : 1013;
    isg : t;
    inp : ( RbL );
    out : ( DM_w );
    rsc : (1) a_b2 a_b0 DM_w ;
    opn : ( a_b0_rd_RbL_class_Rb___RbL_a_b0_rad_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1 );
    ins : 397;
}
DM_w_1_dr_move_Rb_1_int8__B1 : DM_w_1_dr_move_Rb_1_int8_ {
    rid : 1014;
    isg : t;
    inp : ( RbH );
    out : ( DM_w );
    rsc : (1) a_b2 a_b0 DM_w ;
    opn : ( a_b0_rd_RbH_class_Rb___RbH_a_b0_rad_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1 );
    ins : 398;
}

Rb_1_dr_move_a_b2_1_int8_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rb_1_dr_move_a_b2_1_int8__B0 : Rb_1_dr_move_a_b2_1_int8_ {
    rid : 1015;
    isg : t;
    inp : ( a_b2 );
    out : ( RbL );
    rsc : (1) __RbL_a_b2_wad __rsrc_RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 ;
    opn : ( RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 );
    ins : 399;
}
Rb_1_dr_move_a_b2_1_int8__B1 : Rb_1_dr_move_a_b2_1_int8_ {
    rid : 1016;
    isg : t;
    inp : ( a_b2 );
    out : ( RbH );
    rsc : (1) __RbH_a_b2_wad __rsrc_RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 ;
    opn : ( RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 );
    ins : 400;
}

DMw_w_1_dr_move_Rw_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
DMw_w_1_dr_move_Rw_1_int16__B0 : DMw_w_1_dr_move_Rw_1_int16_ {
    rid : 1017;
    isg : t;
    inp : ( RwL );
    out : ( DMw_w );
    rsc : (1) a_w0 DM_w DM_w1 a_w2 ;
    opn : ( a_w0_rd_RwL_class_Rw___RwL_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 401;
}
DMw_w_1_dr_move_Rw_1_int16__B1 : DMw_w_1_dr_move_Rw_1_int16_ {
    rid : 1018;
    isg : t;
    inp : ( R46 );
    out : ( DMw_w );
    rsc : (1) a_w0 DM_w DM_w1 a_w2 ;
    opn : ( a_w0_rd_R46_class_Rw___R46_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 402;
}

Rb_1_dr_move_a_b0_1_int8_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rb_1_dr_move_a_b0_1_int8__B0 : Rb_1_dr_move_a_b0_1_int8_ {
    rid : 1019;
    isg : t;
    inp : ( a_b0 );
    out : ( RbL );
    rsc : (1) __RbL_a_b2_wad a_b2 __rsrc_RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 ;
    opn : ( a_b2_copy0_a_b0_E1
            RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 );
    ins : 282;
}
Rb_1_dr_move_a_b0_1_int8__B1 : Rb_1_dr_move_a_b0_1_int8_ {
    rid : 1020;
    isg : t;
    inp : ( a_b0 );
    out : ( RbH );
    rsc : (1) a_b2 __RbH_a_b2_wad __rsrc_RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 ;
    opn : ( a_b2_copy0_a_b0_E1
            RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 );
    ins : 286;
}

__cvT4_1_dr_move_RwL_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 1021;
    isg : t;
    inp : ( RwL );
    out : ( __cvT4 );
    rsc : (1) __cvT4 ;
    opn : ( __cvT4_rd_RwL___RwL___cvT4_radcvB1 );
    ins : 113;
}

Rw_1_dr_move_DMw_r_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move_DMw_r_1_int16__B0 : Rw_1_dr_move_DMw_r_1_int16_ {
    rid : 1022;
    isg : t;
    inp : ( DMw_r );
    out : ( RwL );
    rsc : (1) a_w0 a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w0_copy0_DMw_r_E1
            a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 403;
}
Rw_1_dr_move_DMw_r_1_int16__B1 : Rw_1_dr_move_DMw_r_1_int16_ {
    rid : 1023;
    isg : t;
    inp : ( DMw_r );
    out : ( R46 );
    rsc : (1) a_w0 a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w0_copy0_DMw_r_E1
            a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 404;
}

a_w0_1_dr_move_Rw_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
a_w0_1_dr_move_Rw_1_int16__B0 : a_w0_1_dr_move_Rw_1_int16_ {
    rid : 1024;
    isg : t;
    inp : ( RwL );
    out : ( a_w0 );
    rsc : (1) a_w0 ;
    opn : ( a_w0_rd_RwL_class_Rw___RwL_a_w0_rad_E1 );
    ins : 405;
}
a_w0_1_dr_move_Rw_1_int16__B1 : a_w0_1_dr_move_Rw_1_int16_ {
    rid : 1025;
    isg : t;
    inp : ( R46 );
    out : ( a_w0 );
    rsc : (1) a_w0 ;
    opn : ( a_w0_rd_R46_class_Rw___R46_a_w0_rad_E1 );
    ins : 406;
}

Rw_1_dr_move_Rw_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move_Rw_1_int16__B0 : Rw_1_dr_move_Rw_1_int16_ {
    rid : 1026;
    isg : t;
    inp : ( RwL );
    out : ( R46 );
    rsc : (1) a_w0 a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w0_rd_RwL_class_Rw___RwL_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 407;
}
Rw_1_dr_move_Rw_1_int16__B1 : Rw_1_dr_move_Rw_1_int16_ {
    rid : 1027;
    isg : t;
    inp : ( R46 );
    out : ( R46 );
    rsc : (1) a_w0 a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w0_rd_R46_class_Rw___R46_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 408;
}
Rw_1_dr_move_Rw_1_int16__B2 : Rw_1_dr_move_Rw_1_int16_ {
    rid : 1028;
    isg : t;
    inp : ( RwL );
    out : ( RwL );
    rsc : (1) a_w0 a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w0_rd_RwL_class_Rw___RwL_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 409;
}
Rw_1_dr_move_Rw_1_int16__B3 : Rw_1_dr_move_Rw_1_int16_ {
    rid : 1029;
    isg : t;
    inp : ( R46 );
    out : ( RwL );
    rsc : (1) a_w0 a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w0_rd_R46_class_Rw___R46_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 410;
}

DM_rmw_addr_1_dr_move_R46_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 1030;
    isg : t;
    inp : ( R46 );
    out : ( DM_rmw_addr );
    rsc : (1) DM_rmw_addr __DM_rmw_addr_r_int16_ ;
    opn : ( __DM_rmw_addr_r_int16__rd_R46___R46___DM_rmw_addr_r_int16__rad_E1
            DM_rmw_addr_conv0___DM_rmw_addr_r_int16__E1 );
    ins : 411;
}

Rw_1_dr_move_a_w2_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move_a_w2_1_int16__B0 : Rw_1_dr_move_a_w2_1_int16_ {
    rid : 1031;
    isg : t;
    inp : ( a_w2 );
    out : ( RwL );
    rsc : (1) __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 412;
}
Rw_1_dr_move_a_w2_1_int16__B1 : Rw_1_dr_move_a_w2_1_int16_ {
    rid : 1032;
    isg : t;
    inp : ( a_w2 );
    out : ( R46 );
    rsc : (1) __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 413;
}

Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B0 : Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8_ {
    rid : 1033;
    isg : t;
    inp : ( __CTa_b0_int8__cstP24_E1 );
    out : ( RbL );
    rsc : (1) __RbL_a_b2_wad a_b2 a_b0 __rsrc_RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 ;
    opn : ( a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 );
    ins : 414;
}
Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B1 : Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8_ {
    rid : 1034;
    isg : t;
    inp : ( __CTa_b0_int8__cstP24_E1 );
    out : ( RbH );
    rsc : (1) a_b2 __RbH_a_b2_wad a_b0 __rsrc_RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 ;
    opn : ( a_b0_copy0___CTa_b0_int8__cstP24_E1
            a_b2_copy0_a_b0_E1
            RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 );
    ins : 415;
}
Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 : Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8_ {
    rid : 1035;
    isg : t;
    inp : ( a_b0 );
    out : ( RbL );
    rsc : (1) __RbL_a_b2_wad a_b2 __rsrc_RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 ;
    opn : ( a_b2_copy0_a_b0_E1
            RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 );
    ins : 282;
}
Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B3 : Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8_ {
    rid : 1036;
    isg : t;
    inp : ( a_b0 );
    out : ( RbH );
    rsc : (1) a_b2 __RbH_a_b2_wad __rsrc_RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 ;
    opn : ( a_b2_copy0_a_b0_E1
            RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 );
    ins : 286;
}

DMw_w_1_dr_move_Rw_1_uint16_1_32768_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
DMw_w_1_dr_move_Rw_1_uint16_1_32768__B0 : DMw_w_1_dr_move_Rw_1_uint16_1_32768_ {
    rid : 1037;
    isg : t;
    inp : ( RwL );
    out : ( DMw_w );
    rsc : (1) a_w0 DM_w DM_w1 a_w2 ;
    opn : ( a_w0_rd_RwL_class_Rw___RwL_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 401;
}
DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 : DMw_w_1_dr_move_Rw_1_uint16_1_32768_ {
    rid : 1038;
    isg : t;
    inp : ( R46 );
    out : ( DMw_w );
    rsc : (1) a_w0 DM_w DM_w1 a_w2 ;
    opn : ( a_w0_rd_R46_class_Rw___R46_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 402;
}

Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B0 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768_ {
    rid : 1039;
    isg : t;
    inp : ( __CTa_w0_uint16__cstP16_E1 );
    out : ( RwL );
    rsc : (1) a_w0 a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w0_conv0___CTa_w0_uint16__cstP16_E1
            a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 416;
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768_ {
    rid : 1040;
    isg : t;
    inp : ( __CTa_w0_uint16__cstP16_E1 );
    out : ( R46 );
    rsc : (1) a_w0 a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w0_conv0___CTa_w0_uint16__cstP16_E1
            a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 417;
}

Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B0 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16_ {
    rid : 1041;
    isg : t;
    inp : ( __CTa_w0_uint16__cstP16_E1 );
    out : ( RwL );
    rsc : (1) a_w0 a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w0_conv0___CTa_w0_uint16__cstP16_E1
            a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 416;
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16_ {
    rid : 1042;
    isg : t;
    inp : ( __CTa_w0_uint16__cstP16_E1 );
    out : ( R46 );
    rsc : (1) a_w0 a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w0_conv0___CTa_w0_uint16__cstP16_E1
            a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 417;
}

Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B0 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ {
    rid : 1043;
    isg : t;
    inp : ( __CTa_w0_uint16__cstP16_E1 );
    out : ( RwL );
    rsc : (1) a_w0 a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w0_conv0___CTa_w0_uint16__cstP16_E1
            a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 416;
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B1 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ {
    rid : 1044;
    isg : t;
    inp : ( __CTa_w0_uint16__cstP16_E1 );
    out : ( R46 );
    rsc : (1) a_w0 a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w0_conv0___CTa_w0_uint16__cstP16_E1
            a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 417;
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ {
    rid : 1045;
    isg : t;
    inp : ( a_w0 );
    out : ( RwL );
    rsc : (1) a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 418;
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B3 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ {
    rid : 1046;
    isg : t;
    inp : ( a_w0 );
    out : ( R46 );
    rsc : (1) a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 419;
}

