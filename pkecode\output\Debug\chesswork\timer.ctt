
// File generated by noodle version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:23 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/timer.c mrk3

#const float_tininess_after_rounding	enum __anonymous0__timer_ 0 (0x0)
#const float_tininess_before_rounding	enum __anonymous0__timer_ 1 (0x1)
#const float_round_nearest_even	enum __anonymous1__timer_ 0 (0x0)
#const float_round_to_zero	enum __anonymous1__timer_ 1 (0x1)
#const float_round_up	enum __anonymous1__timer_ 2 (0x2)
#const float_round_down	enum __anonymous1__timer_ 3 (0x3)
#const SUCCESS	enum __anonymous2__timer_ 0 (0x0)
#const ERROR	enum __anonymous2__timer_ 1 (0x1)
#const FALSE	enum __anonymous3__timer_ 0 (0x0)
#const TRUE	enum __anonymous3__timer_ 1 (0x1)
#const ADC_INPUTSEL_VBAT	enum __anonymous226__timer_ 0 (0x0)
#const ADC_INPUTSEL_EXTERNAL	enum __anonymous226__timer_ 1 (0x1)
#const ADC_INPUTSEL_RFU2	enum __anonymous226__timer_ 2 (0x2)
#const ADC_INPUTSEL_TEMPSENS	enum __anonymous226__timer_ 3 (0x3)
#const ADC_INPUTSEL_RSSI	enum __anonymous226__timer_ 4 (0x4)
#const ADC_INPUTSEL_RFU5	enum __anonymous226__timer_ 5 (0x5)
#const ADC_INPUTSEL_RFU6	enum __anonymous226__timer_ 6 (0x6)
#const ADC_INPUTSEL_RFU7	enum __anonymous226__timer_ 7 (0x7)
#const ADC_REFSEL_BANDGAP	enum __anonymous227__timer_ 0 (0x0)
#const ADC_REFSEL_VBAT_VSS	enum __anonymous227__timer_ 1 (0x1)
#const ADC_REFSEL_EXTERNAL	enum __anonymous227__timer_ 2 (0x2)
#const ADC_REFSEL_TEMPSENS	enum __anonymous227__timer_ 3 (0x3)
#const ADC_REFSEL_BANDGAPRSSI	enum __anonymous227__timer_ 4 (0x4)
#const ADC_REFSEL_RFU5	enum __anonymous227__timer_ 5 (0x5)
#const ADC_REFSEL_RFU6	enum __anonymous227__timer_ 6 (0x6)
#const ADC_REFSEL_RFU7	enum __anonymous227__timer_ 7 (0x7)
#const ADC_SAMPTIME_01US	enum __anonymous228__timer_ 0 (0x0)
#const ADC_SAMPTIME_08US	enum __anonymous228__timer_ 1 (0x1)
#const ADC_SAMPTIME_16US	enum __anonymous228__timer_ 2 (0x2)
#const ADC_SAMPTIME_64US	enum __anonymous228__timer_ 3 (0x3)
#const AUTOZERO	enum __anonymous229__timer_ 0 (0x0)
#const CH1	enum __anonymous229__timer_ 1 (0x1)
#const CH2	enum __anonymous229__timer_ 2 (0x2)
#const CH3	enum __anonymous229__timer_ 3 (0x3)
#const CH_DEFAULT	enum __anonymous229__timer_ 4 (0x4)
#const CHSUM12	enum __anonymous229__timer_ 8 (0x8)
#const CHSUM13	enum __anonymous229__timer_ 9 (0x9)
#const CHSUM23	enum __anonymous229__timer_ 10 (0xa)
#const CHSUM123	enum __anonymous229__timer_ 11 (0xb)
#const CH_INVALID	enum __anonymous229__timer_ 255 (0xff)
#const RSSI_RANGE_54dB	enum __anonymous230__timer_ 5 (0x5)
#const RSSI_RANGE_36dB	enum __anonymous230__timer_ 4 (0x4)
#const RSSI_RANGE_18dB	enum __anonymous230__timer_ 3 (0x3)
#const RSSI_RANGE_0dB	enum __anonymous230__timer_ 2 (0x2)
#const RSSI_RANGE_m18dB	enum __anonymous230__timer_ 1 (0x1)
#const RSSI_RANGE_LIM	enum __anonymous230__timer_ 0 (0x0)
#const RSSI_RANGEEXT_DIS	enum __anonymous231__timer_ 1 (0x1)
#const RSSI_RANGEEXT_EN	enum __anonymous231__timer_ 0 (0x0)
#const NBRSSI_CH_AUTO	enum __anonymous232__timer_ 0 (0x0)
#const NBRSSI_CH1	enum __anonymous232__timer_ 1 (0x1)
#const NBRSSI_CH2	enum __anonymous232__timer_ 2 (0x2)
#const NBRSSI_CH3	enum __anonymous232__timer_ 4 (0x4)
#const NBRSSI_CH_INVALID	enum __anonymous232__timer_ 8 (0x8)
#const NBRSSI_AVG	enum __anonymous233__timer_ 0 (0x0)
#const NBRSSI_QMEAN	enum __anonymous233__timer_ 1 (0x1)
#const NBRSSI_MIN	enum __anonymous233__timer_ 2 (0x2)
#const NBRSSI_MAX	enum __anonymous233__timer_ 3 (0x3)
#const NBRSSI_RAW	enum __anonymous233__timer_ 4 (0x4)
#const NBRSSI_FREQ_ERR	enum __anonymous233__timer_ 5 (0x5)
#const NBRSSI_FILT_CFG_A	enum __anonymous234__timer_ 0 (0x0)
#const NBRSSI_FILT_CFG_B	enum __anonymous234__timer_ 4 (0x4)
#const NBRSSI_FILT_CFG_C	enum __anonymous234__timer_ 3 (0x3)
#const NBRSSI_FILT_CFG_D	enum __anonymous234__timer_ 7 (0x7)
#const NBRSSI_NRE_CFG_NONE	enum __anonymous235__timer_ 0 (0x0)
#const NBRSSI_NRE_CFG_m18dB	enum __anonymous235__timer_ 1 (0x1)
#const NBRSSI_NRE_CFG_m18dB_0dB	enum __anonymous235__timer_ 2 (0x2)
#const NBRSSI_OK	enum __anonymous236__timer_ 0 (0x0)
#const NBRSSI_ERROR_SDADC_DIG_OVERLOAD	enum __anonymous236__timer_ 1 (0x1)
#const NBRSSI_ERROR_SDADC_ANA_OVERLOAD	enum __anonymous236__timer_ 2 (0x2)
#const NBRSSI_ERROR_INBAND_DISTURBER	enum __anonymous236__timer_ 4 (0x4)
#const NBRSSI_ERROR_PHASE_SPEED_HIGH	enum __anonymous236__timer_ 8 (0x8)
#const NBRSSI_ERROR_STRONG_DISTURBER	enum __anonymous236__timer_ 16 (0x10)
#const RNG_CRC16	enum __anonymous238__timer_ 0 (0x0)
#const RNG_LFSR	enum __anonymous238__timer_ 1 (0x1)
#const RNG_TRUE	enum __anonymous238__timer_ 2 (0x2)
#const CPUMCC_SYSTEMCLOCK	enum __anonymous239__timer_ 0 (0x0)
#const CPUMCC_MACHINECLOCK	enum __anonymous239__timer_ 1 (0x1)
#const LF_OK	enum __anonymous242__timer_ 0 (0x0)
#const LF_BITFAIL	enum __anonymous242__timer_ 1 (0x1)
#const LF_INVMODE	enum __anonymous242__timer_ 2 (0x2)
#const LF_NBOVF	enum __anonymous242__timer_ 3 (0x3)
#const LF_WUPXM	enum __anonymous242__timer_ 4 (0x4)
#const LF_INVCRC	enum __anonymous242__timer_ 5 (0x5)
#const LF_INVDATA	enum __anonymous242__timer_ 6 (0x6)
#const LF_TIMEOUT	enum __anonymous242__timer_ 7 (0x7)
#const PPMODE_NOCOMM	enum __anonymous243__timer_ 0 (0x0)
#const PPMODE_CODEVIOL	enum __anonymous243__timer_ 1 (0x1)
#const PPMODE_WUPMATCH	enum __anonymous243__timer_ 2 (0x2)
#const PPMODE_DATAREC	enum __anonymous243__timer_ 3 (0x3)
#const LF_WUP_NONE	enum __anonymous244__timer_ 0 (0x0)
#const LF_WUPA	enum __anonymous244__timer_ 1 (0x1)
#const LF_WUPB	enum __anonymous244__timer_ 2 (0x2)
#const LF_WUPC	enum __anonymous244__timer_ 3 (0x3)
#const LFPWT_NONE	enum __anonymous245__timer_ 0 (0x0)
#const LFPWT_FROMWUP	enum __anonymous245__timer_ 1 (0x1)
#const LFPWT_FROMNEWBYTE	enum __anonymous245__timer_ 2 (0x2)
#const CRC	enum __anonymous246__timer_ 0 (0x0)
#const NO_CRC	enum __anonymous246__timer_ 1 (0x1)
#const STANDARD_EQ	enum __anonymous247__timer_ 248 (0xf8)
#const MODIFIED_EQ	enum __anonymous247__timer_ 252 (0xfc)
#const IIU_ENC_OFF	enum __anonymous248__timer_ 0 (0x0)
#const IIU_ENC_PLAIN	enum __anonymous248__timer_ 1 (0x1)
#const IIU_ENC_MANCHESTER	enum __anonymous248__timer_ 2 (0x2)
#const IIU_ENC_CDP	enum __anonymous248__timer_ 3 (0x3)
#const STRONG_MOD	enum __anonymous249__timer_ 32 (0x20)
#const STANDARD_MOD	enum __anonymous249__timer_ 0 (0x0)
#const IIUSTATE_IDLE	enum __anonymous250__timer_ 0 (0x0)
#const IIUSTATE_RXWAIT	enum __anonymous250__timer_ 1 (0x1)
#const IIUSTATE_RXDATA	enum __anonymous250__timer_ 2 (0x2)
#const IIUSTATE_TXWAIT	enum __anonymous250__timer_ 3 (0x3)
#const IIUSTATE_TXDATA	enum __anonymous250__timer_ 4 (0x4)
#const IIUSTATE_SHIFT	enum __anonymous250__timer_ 5 (0x5)
#const IIUSTATE_SHIFTCONT	enum __anonymous250__timer_ 6 (0x6)
#const IIUSTATE_RFU7	enum __anonymous250__timer_ 7 (0x7)
#const DISTWR_OK	enum __anonymous251__timer_ 0 (0x0)
#const DISTWR_REJECTED	enum __anonymous251__timer_ 51 (0x33)
#const DISTWR_REJECTED_MIN	enum __anonymous251__timer_ 238 (0xee)
#const DISTWR_DISABLED	enum __anonymous251__timer_ 204 (0xcc)
#const DISTWR_WRITE_FAIL_REJ	enum __anonymous251__timer_ 254 (0xfe)
#const DISTWR_WRITE_FAIL	enum __anonymous251__timer_ 255 (0xff)
