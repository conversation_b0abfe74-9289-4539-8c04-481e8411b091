
#include "phcaiKEyLLGenFunc_CS.h"
#include "phcaiKEyLLGenFunc.h"
#include "defs.h"
#include "globvar.h"
#include "timer.h"
#include "SysInit.h"


//-------------------------------------------------------------------------------------------------


/**
  4-byte storage area for IDE in battery-powered registers.
  See ReadIDEFromEEPROM().
 */
#define IDE0_STORAGE_VBATREG     USRBAT4.val
#define IDE1_STORAGE_VBATREG     USRBAT5.val
#define IDE2_STORAGE_VBATREG     USRBAT6.val
#define IDE3_STORAGE_VBATREG     USRBAT7.val

#define PIDE0_STORAGE_VBATREG     USRBAT0.val
#define PIDE1_STORAGE_VBATREG     USRBAT1.val
#define PIDE2_STORAGE_VBATREG     USRBAT2.val
#define PIDE3_STORAGE_VBATREG     USRBAT3.val

uint16_t	g_Vbat_Value;
/*-----------------------------------------------------------------------------
;| Name:
;|   CPUClock_Init
;| 
;| Description:
;|   Clenct CPU clock source and rate:main RC oscillator ,2MHz CPU clock
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none
;| 
-----------------------------------------------------------------------------*/
void CPUClock_Init( void )
{
  // Configure and reset watchdog timer
  WDCON.val = D_WDCON;
  WDRESET;
  // Set CPU clock
  phcaiKEyLLGenFunc_CS_SetClkCon( D_CLKCON0 );
}
/*-----------------------------------------------------------------------------
;| Name:
;|   GPIO_Init
;| 
;| Description:
;|    Set port configuration
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none
;| 
-----------------------------------------------------------------------------*/
void GPIO_Init( void )
{
  P1OUT.val  |= D_P1OUT;
  P1DIR.val  |= D_P1DIR;
#ifdef debug
P1DIR.val  |= 0x01;
#endif
  P2OUT.val  |= D_P2OUT;
  P2DIR.val  |= D_P2DIR;
  P1ALTF.val = ALTERNATIVE_MASK;        // no altern. functions at the moment, except LED driver
  P2ALTF.val = ALTERNATIVE_MASK;
}
/*-----------------------------------------------------------------------------
;| Name:
;|   Vbat_Check
;| 
;| Description:
;|    Vbat threshold chcek
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   CheckRlt : Vbat chcek result 
;| 
-----------------------------------------------------------------------------*/
int8_t Vbat_Check( uint16_t bat_threshold )
{
   int8_t CheckRlt;
   CheckRlt = phcaiKEyLLGenFunc_ADC_check_VbatMin( bat_threshold, &g_Vbat_Value); 
   return CheckRlt;
}
/*-----------------------------------------------------------------------------
;| Name:
;|   WUP_Init
;| 
;| Description:
;|     Initalize LF wake up config
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
void WUP_Init( void )
{
  uint8_t Temp_Buf[4] = {0};
  uint16_t pollingid = 0; 
  uint16_t swversion = 0; 
  BEGIN_CRITICAL_SECTION();                   // Delay LF reset

  // Set port resistor and wake-up configuration registers
  PRESWUP0.val = D_PRESWUP0;                              
  PRESWUP1.val = D_PRESWUP1; 
  PRESWUP2.val = D_PRESWUP2;
  P1WRES.val   = D_P1WRES;
  P2WRES.val   = D_P2WRES;

  // Set interval timer / real-time clock control register default
  RTCCON.val   = D_RTCCON;
  
  // set LF tuning caps default. 
  // TBD: how to handle LFTUNEVDD which is already loaded by boot code?
  LFTUNEVBAT.val = D_LFTUNEVBAT;

  // It is recommended to set the reset bits (DIGFILRST=1, PRERST=1) when  
  // changing the pre-processor registers.
  PRECON2.val    = D_PRECON2 | 0xC0U;
  PRECON3.val    = D_PRECON3;
  PRECON4.val    = D_PRECON4;
  PRECON5.val    = D_PRECON5;
  PRECON6.val    = D_PRECON6;
  PRECON7.val    = D_PRECON7;         
  PRECON8.val    = D_PRECON8;         
  PRECON9.val    = D_PRECON9;
  PRECON10.val   = D_PRECON10;
  PRECON11.val   = D_PRECON11;                 
  PREPD.val      = D_PREPD;

#ifdef MOTION_SENSOR_USED
  MSICON0.val    = D_MSICON0;
  MSICON1.val    = D_MSICON1;
#endif
	  
  PRET.val       = D_PRET;
  PRE3T.val      = D_PRE3T;
 
  WUP1W0.val     = D_WUP1W0;
  WUP1W1.val     = D_WUP1W1;

  // Notes: 
  // IDE is copied from ULPEE to RAM on port or pre-processor wake-up only.
  // To be compatible to the TED-Kit2 GUI for PCF7952/'53, we have to use a modified 
  // LF wake-up pattern, as it is done in the demo application for those types.
  
  WUP2W0.byte.lo = (KEY_PID[3]&0x0F)<<4 | 0x0F;
  WUP2W0.byte.hi = KEY_PID[2];
  WUP2W1.byte.lo = KEY_PID[1];
  WUP2W1.byte.hi = KEY_PID[0];
  
  WUP1W0.byte.lo = (KEY_ID[3]&0x0F)<<4 | 0x0F;
  WUP1W0.byte.hi = KEY_ID[2];
  WUP1W1.byte.lo = KEY_ID[1];
  WUP1W1.byte.hi = KEY_ID[0];
 
  pollingid = phcaiKEyLLGenFunc_ULPEE_ReadOneWord(PID_Adr);
  WUP3W0.byte.lo = (pollingid&0x0F)<<4 | 0x0F;
  WUP3W0.byte.hi = (pollingid>>8) ;
  
  PRECON2.bits.PRERST    = 0U;                // Release pre-processor reset
  PRECON2.bits.DIGFILRST = 0U;                // Release digital filter reset    

  // Clear any all preprocessor wake up sources 
  // that might have been set until this point.
  PRESTAT.val = 0x007FU;
 /*******check tansponder configuration****************************************/ 
  if( ((phcaiKEyLLGenFunc_ULPEE_ReadOneWord(TMCF_Adr))&TMCF_InitVlu)!=0 )
  {
    phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( Temp_Buf,TMCF_PW );
    timer_delay_ms( GAPDLY );
    Temp_Buf[0] = Temp_Buf[0]&0xFC;
    phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait(Temp_Buf,TMCF_PW);
    timer_delay_ms( GAPDLY );
  }
 /*******check version*********************************************************/  
  #if 0
  swversion = phcaiKEyLLGenFunc_ULPEE_ReadOneWord(SWVERSION_Adr);
    if( swversion != (phcaiKEyLLGenFunc_ULPEE_ReadOneWord(SWVERSION_STOR_Adr)) )
  {
    phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( Temp_Buf,POLLINGID_PG );
    timer_delay_ms( GAPDLY );
    swversion = phcaiKEyLLGenFunc_ULPEE_ReadOneWord(SWVERSION_STOR_Adr);
    Temp_Buf[0] = (uint8_t)(swversion>>8);
    Temp_Buf[1] = (uint8_t)swversion;
    phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait(Temp_Buf,POLLINGID_PG);
    timer_delay_ms( GAPDLY );
  }
  #endif
  
  END_CRITICAL_SECTION();
}
/*-----------------------------------------------------------------------------
;| Name:
;|   WUP_Init
;| 
;| Description:
;|     Initalize LF wake up config
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
void hw_refresh_VBAT_VBATREG_registers()
{
  uint8_t u8_save_bits = 0;
  uint8_t Temp_Buf[4] = {0};
  uint16_t pollingid = 0; 

  uint8_t gaincfg = 0; 
  uint16_t swversion = 0; 

  // If previously a battery power-on reset did occur, POWER OFF state will  
  // be entered here. Since BATPORFLAG == 1, the device will restart, going
  // through the battery power-on sequence
  if ( BATSYS0.bits.BATPORFLAG == 1U )
  {
    Power_Off();
  }  
  
  // The application needs to ensure that refreshing the battery powered 
  // registers is not interrupted after this point (LF reset, power down, etc...),
  // otherwise the 3D LF active front end is kept in reset condition   
  BEGIN_CRITICAL_SECTION();                   // Delay LF reset

  phcaiKEyLLGenFunc_CS_SetBatPORFlag( TRUE );

  // Set port resistor and wake-up configuration registers
  PRESWUP0.val   = D_PRESWUP0;                              
  PRESWUP1.val   = D_PRESWUP1; 
  PRESWUP2.val   = D_PRESWUP2;

  // set LF tuning caps default. 
  // TBD: how to handle LFTUNEVDD which is already loaded by boot code?
  LFTUNEVBAT.val = D_LFTUNEVBAT;

  // PRECON2
  // -------
  // 7         | 6      | 5       | 4   | [3:2]       | 1      | 0       
  // DIGFILRST | PRERST | PDLFACT | RFU | BDRATE[3:2] | RTC_EN | LPRC_EN 
  //
  // It is recommended to set the reset bits (DIGFILRST=1, PRERST=1) when  
  // changing the pre-processor registers.
  // RTC_EN bit should not be changed here.
  u8_save_bits   = PRECON2.val & 0x02U;
  PRECON2.val    = (uint8_t)(D_PRECON2 & 0xFDU) | 0xC0U | u8_save_bits;

  PRECON3.val    = D_PRECON3;
  PRECON4.val    = D_PRECON4;
  PRECON5.val    = D_PRECON5;
  
  // PRECON6
  // -------
  // ITCLKSEL and LPRC_CAL bits should not be changed.
  // 7   | [6:5]  | 4        | [3:0]         
  // RFU | CVTYPE | ITCLKSEL | LPRC_CAL[3:0]    
  //
  // ITCLKSEL and LPRC_CAL[3:0] should not be changed here.
  u8_save_bits   = PRECON6.val & 0x1FU;
  PRECON6.val    = (D_PRECON6 & 0xE0U) | u8_save_bits;

  PRECON7.val    = D_PRECON7;
  PRECON8.val    = D_PRECON8;
  PRECON9.val    = D_PRECON9;
  PRECON10.val   = D_PRECON10;
  
  gaincfg = phcaiKEyLLGenFunc_ULPEE_ReadOneByte(GAIN_Adr);
  if(gaincfg == 0x55)
  {
    PRECON11.val   = GAIN6DB;
  }
  else
  {
    PRECON11.val   = GAIN0DB;
  }
  
  PREPD.val      = D_PREPD;
  
#ifdef MOTION_SENSOR_USED
  MSICON0.val    = D_MSICON0;
  MSICON1.val    = D_MSICON1;
#endif
	  
  PRET.val       = D_PRET;
  PRE3T.val      = D_PRE3T;

   
  WUP1W0.val     = D_WUP1W0;
  WUP1W1.val     = D_WUP1W1;

  // Notes: 
  // IDE is copied from ULPEE to RAM on port or pre-processor wake-up only.
  // To be compatible to the TED-Kit2 GUI for PCF7952/'53, we have to use a modified 
  // LF wake-up pattern, as it is done in the demo application for those types.
  
  WUP2W0.byte.lo = (KEY_PID[3]&0x0F)<<4 | 0x0F;
  WUP2W0.byte.hi = KEY_PID[2];
  WUP2W1.byte.lo = KEY_PID[1];
  WUP2W1.byte.hi = KEY_PID[0];
  
  WUP1W0.byte.lo = (KEY_ID[3]&0x0F)<<4 | 0x0F;
  WUP1W0.byte.hi = KEY_ID[2];
  WUP1W1.byte.lo = KEY_ID[1];
  WUP1W1.byte.hi = KEY_ID[0];
        
  pollingid = phcaiKEyLLGenFunc_ULPEE_ReadOneWord(PID_Adr);
  WUP3W0.byte.lo = (pollingid&0x0F)<<4 | 0x0F;
  WUP3W0.byte.hi = (pollingid>>8) ;
               
  PRECON2.bits.PRERST    = 0U;                // Release pre-processor reset
  PRECON2.bits.DIGFILRST = 0U;                // Release digital filter reset    

  // Clear any all preprocessor wake up sources 
  // that might have been set until this point.
  PRESTAT.val    = 0x007FU;
  
  // If the battery regulator is not enabled anymore, a battery power-on reset did occur.
  // It is up to the application of how to deal with this situation
  // Here the decision has been made to enter POWER OFF state. Since BATPORFLAG == 1,
  // the device will restart going through the battery power-on sequence.
  if ( (BATSYS0.bits.BATRGLEN == 0U) || (BATSYS0.bits.BATRGLRST == 1U) )
  {
    Power_Off();
  }

  phcaiKEyLLGenFunc_CS_SetBatPORFlag( FALSE );
  
  if( ((phcaiKEyLLGenFunc_ULPEE_ReadOneWord(TMCF_Adr))&TMCF_InitVlu)!=0 )
  {
    phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( Temp_Buf,TMCF_PW );
    timer_delay_ms( GAPDLY );
    Temp_Buf[0] = Temp_Buf[0]&0xFC;
    phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait(Temp_Buf,TMCF_PW);
    timer_delay_ms( GAPDLY );
  }
   /*******check version*********************************************************/  
  #if 0
  swversion = phcaiKEyLLGenFunc_ULPEE_ReadOneWord(SWVERSION_Adr);
    if( swversion != (phcaiKEyLLGenFunc_ULPEE_ReadOneWord(SWVERSION_STOR_Adr)) )
  {
    phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( Temp_Buf,POLLINGID_PG );
    timer_delay_ms( GAPDLY );
    swversion = phcaiKEyLLGenFunc_ULPEE_ReadOneWord(SWVERSION_STOR_Adr);
    Temp_Buf[0] = (uint8_t)(swversion>>8);
    Temp_Buf[1] = (uint8_t)swversion;
    phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait(Temp_Buf,POLLINGID_PG);
    timer_delay_ms( GAPDLY );
  }
#endif
  END_CRITICAL_SECTION();
}
/*-----------------------------------------------------------------------------
;| Name:
;|   BatReset
;| 
;| Description:
;|   
;|   reset of the VBAT domain
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
void BatReset(void)
{
    phcaiKEyLLGenFunc_CS_ResetVBat();
}
/*-----------------------------------------------------------------------------
;| Name:
;|   hw_power_off
;| 
;| Description:
;|   
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
void Power_Off( void ) property( never_returns )
{
  phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules();

  // switch all ports to input mode to avoid unwanted port wake-up
  P1DIR.val  = 0x00U;
  P2DIR.val  = 0x00U;

  P1ALTF.val = 0x00U;
  P2ALTF.val = 0x00U;

  phcaiKEyLLGenFunc_CS_SetClkCon( D_CLKCON0_END );    // reduce CPU clock

  // Delay to avoid unwanted port wake-up
  phcaiKEyLLGenFunc_timer0_delay_us( t_PSMF_us );

  // Force VDD reset
  PCON0.bits.VDDRST = 1U;

  // Indicate that execution does not continue
  for (;;) {}
}
/*-----------------------------------------------------------------------------
;| Name:
;|   WUP_Init
;| 
;| Description:
;|     Initalize LF wake up config
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
void read_IDE_from_ULPEE( void )
{
  uint8_t u8arr_tmp[4];
  uint8_t u8arrl_tmp[4];
  
  phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait( IDE_PAGE * 4U );
  phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait( EEPAGE_POLLING * 4U );
  // Not possible use SFRs as destination address in system calls!    
  // Therefore copy from temporary buffer.
  phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( u8arr_tmp, IDE_PAGE );     // (Big endian) LSB in index 3
  
  phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( u8arrl_tmp, EEPAGE_POLLING );     // (Big endian) LSB in index 3
  
  IDE0_STORAGE_VBATREG = u8arr_tmp[0];
  IDE1_STORAGE_VBATREG = u8arr_tmp[1];
  IDE2_STORAGE_VBATREG = u8arr_tmp[2];
  IDE3_STORAGE_VBATREG = u8arr_tmp[3];
  
  PIDE0_STORAGE_VBATREG = u8arrl_tmp[0];
  PIDE1_STORAGE_VBATREG = u8arrl_tmp[1];
  PIDE2_STORAGE_VBATREG = u8arrl_tmp[2];
  PIDE3_STORAGE_VBATREG = u8arrl_tmp[3];
  
  restore_IDE_in_RAM();
}

/*-----------------------------------------------------------------------------
;| Name:
;|   WUP_Init
;| 
;| Description:
;|     Initalize LF wake up config
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
void restore_IDE_in_RAM( void )
{
  KEY_ID[0] = IDE0_STORAGE_VBATREG;
  KEY_ID[1] = IDE1_STORAGE_VBATREG;
  KEY_ID[2] = IDE2_STORAGE_VBATREG;
  KEY_ID[3] = IDE3_STORAGE_VBATREG;
  
  KEY_PID[0] = PIDE0_STORAGE_VBATREG;
  KEY_PID[1] = PIDE1_STORAGE_VBATREG;
  KEY_PID[2] = PIDE2_STORAGE_VBATREG;
  KEY_PID[3] = PIDE3_STORAGE_VBATREG;
}

//-------------------------------------------------------------------------------------------------
uint8_t hw_get_button_code( void )
{
  uint8_t u8_ButtonCode;
  uint8_t u8arr_buttoncodes[2]; 

  phcaiKEyLLGenFunc_Util_Debounce( 
    u8arr_buttoncodes,              // one result bit per port
    1000,                           // Delay between samplings in microseconds
    10,                             // Number of samples to take
    3                               // threshold, Minimum number of '1' samples required to give a result of '1'
  );    
  
  u8_ButtonCode =( u8arr_buttoncodes[ BUTTON_REG_INDEX ] & BUTTON_MASK ) ^ BUTTON_MASK;

  return u8_ButtonCode;
}

/* eof */
