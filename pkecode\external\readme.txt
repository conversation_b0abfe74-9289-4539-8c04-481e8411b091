Libraries in this folder have been compiled with the MRK-IIIe IP Programmer release as stated in
the release notes.
If you are using a different release, linking errors might occur. In this case, the libraries
must be re-built using the supplied project (.prx) files.

The device libraries libncf29xy.a contains object files of:
- the hardware register (SFR) allocations (file ncf29xy.c);
- the default vector table (file ncf29xy_vector_table_default.s).
The library file can be compiled using the provided project file
(libncf29xy.prx), which also copies the resulting .a file to the onic/lib/ncf29xy folder.

Note: It is recommended to compile in Debug configuration, to make the register names
and bit fields visible in the ChessDE debugger (variable info).
