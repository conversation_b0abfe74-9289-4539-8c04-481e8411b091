
#ifndef _GLOBVAR_H_
#define _GLOBVAR_H_

#include "types.h"

/**
 * Global Variable Declarations
 * (Definitions in main.c)
 */

/** The 32-bit device IDE from ULPEE page 0x3FF. */
extern uint8_t  KEY_ID[];
extern uint8_t  KEY_PID[];
/** General purpose buffer, used by RKE and PKE modules. */
extern uint8_t  g_u8arr_Buffer[];

/** Flag to indicate that a critical section is being executed. */
extern bool_t   g_b_InCriticalSection;

/** Flag to indicate that a NMI has occurred. */
extern bool_t   g_b_NMI_occurred;

extern uint8_t ISKFream[6];

extern void  process_isr( void ) property(isr);
#endif