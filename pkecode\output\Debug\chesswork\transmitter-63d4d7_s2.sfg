
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-63d4d7 mrk3

[
  -51 : __adr_u16_numbits typ=int16_ bnd=m adro=21
    0 : void_tx_transmit_encoded_bits_DataEnc_t___ushort___ushort typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : u16_data typ=int8_ val=0t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   21 : u16_numbits typ=int8_ val=2t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   22 : e_encoding typ=int8_ val=4t0 bnd=a sz=1 algn=1 stl=DM tref=DataEnc_t_DM
   23 : ENCCON0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ENCCON0_t_DM9
   27 : ENCCON1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ENCCON1_t_DM9
   29 : TXDAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_TXDAT_t_DM9
   33 : __ptr_ENCCON0 typ=int16_ val=0a bnd=m adro=23
   35 : __ptr_ENCCON1 typ=int16_ val=0a bnd=m adro=27
   37 : __ptr_TXDAT typ=int16_ val=0a bnd=m adro=29
   38 : __arg_e_encoding typ=int8_ bnd=p tref=DataEnc_t__
   39 : __arg_u16_numbits typ=int16_ bnd=p tref=uint16_t__
   40 : __arg_u16_data typ=int16_ bnd=p tref=uint16_t__
   45 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   49 : __ct_2t0 typ=int16_ val=2t0 bnd=m
   51 : __adr_u16_numbits typ=int16_ bnd=m adro=21
   53 : __ct_4t0 typ=int16_ val=4t0 bnd=m
   70 : __tmp typ=int16_ bnd=m
   88 : __fch_u16_numbits typ=int16_ bnd=m
  115 : __ct_6s0 typ=int16_ val=6s0 bnd=m
  118 : __fch_u16_data typ=int16_ bnd=m
  122 : __ct_6s0 typ=int16_ val=6s0 bnd=m
  162 : __ct_6s0 typ=int16_ val=6s0 bnd=m
  244 : __apl_c typ=uint1_ bnd=m tref=uint1___
  246 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  249 : __apl_r typ=int16_ bnd=m tref=__uint__
  254 : __apl_c typ=uint1_ bnd=m tref=uint1___
  256 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  262 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  313 : __either typ=bool bnd=m
  314 : __trgt typ=rel8_ val=3j bnd=m
  315 : __trgt typ=rel8_ val=10j bnd=m
  316 : __trgt typ=rel8_ val=4j bnd=m
  317 : __trgt typ=rel8_ val=24j bnd=m
  319 : __seff typ=any bnd=m
  320 : __seff typ=any bnd=m
  321 : __seff typ=any bnd=m
  322 : __seff typ=any bnd=m
  323 : __seff typ=any bnd=m
  324 : __seff typ=any bnd=m
  325 : __seff typ=any bnd=m
  326 : __seff typ=any bnd=m
  327 : __seff typ=any bnd=m
  328 : __seff typ=any bnd=m
  329 : __seff typ=any bnd=m
  333 : __seff typ=any bnd=m
  334 : __seff typ=any bnd=m
  335 : __seff typ=any bnd=m
  336 : __ptr_ENCCON1 typ=int16_ val=0a bnd=m adro=27
  338 : __side_effect typ=any bnd=m
]
Fvoid_tx_transmit_encoded_bits_DataEnc_t___ushort___ushort {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__sp.18 var=19) source ()  <29>;
    (u16_data.19 var=20) source ()  <30>;
    (u16_numbits.20 var=21) source ()  <31>;
    (e_encoding.21 var=22) source ()  <32>;
    (ENCCON0.22 var=23) source ()  <33>;
    (ENCCON1.26 var=27) source ()  <37>;
    (TXDAT.28 var=29) source ()  <39>;
    (__arg_e_encoding.37 var=38 stl=RbL off=0) inp ()  <48>;
    (__arg_u16_numbits.40 var=39 stl=RwL off=1) inp ()  <51>;
    (__arg_u16_data.43 var=40 stl=RwL off=2) inp ()  <54>;
    (__ptr_ENCCON1.808 var=336) const_inp ()  <1041>;
    (__ct_0t0.810 var=45) const_inp ()  <1043>;
    (__ct_2t0.811 var=49) const_inp ()  <1044>;
    (__ct_4t0.812 var=53) const_inp ()  <1045>;
    (__ct_6s0.815 var=162) const_inp ()  <1048>;
    <83> {
      (__sp.51 var=19 __seff.868 var=333 stl=c_flag_w __seff.869 var=334 stl=nz_flag_w __seff.870 var=335 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_6s0.815 __sp.18 __sp.18)  <1096>;
      (__seff.883 var=334 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.869)  <1175>;
      (__seff.884 var=333 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.868)  <1176>;
      (__seff.904 var=335 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.870)  <1196>;
    } stp=0;
    <84> {
      (u16_data.71 var=20) _pl_rd_res_reg_const_store_2_B1 (__arg_u16_data.901 __ct_0t0.810 u16_data.19 __sp.51)  <1097>;
      (__arg_u16_data.901 var=40 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__arg_u16_data.43)  <1193>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <82>;
    } #4 off=3 nxt=5
    #5 off=3 nxt=6
    <81> {
      (u16_numbits.73 var=21) _pl_rd_res_reg_const_store_2_B2 (__arg_u16_numbits.895 __ct_2t0.811 u16_numbits.20 __sp.51)  <1094>;
      (__arg_u16_numbits.895 var=39 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__arg_u16_numbits.40)  <1187>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <84>;
    } #6 off=4 nxt=7
    #7 off=4 nxt=8
    <80> {
      (e_encoding.75 var=22) _pl_rd_res_reg_const_store_1_B2 (__arg_e_encoding.896 __ct_4t0.812 e_encoding.21 __sp.51)  <1093>;
      (__arg_e_encoding.896 var=38 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__arg_e_encoding.37)  <1188>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <86>;
    } #8 off=5 nxt=162
    #162 off=5 nxt=235 tgt=41
    (__trgt.819 var=317) const_inp ()  <1052>;
    <78> {
      (__apl_c.639 var=244 stl=c_flag_w __apl_nz.641 var=246 stl=nz_flag_w __seff.860 var=329 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B3 (__ct_2t0.811 u16_numbits.73 __sp.51)  <1091>;
      (__apl_nz.880 var=246 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.641)  <1172>;
      (__apl_c.882 var=244 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.639)  <1174>;
      (__seff.905 var=329 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.860)  <1197>;
    } stp=0;
    <79> {
      () cc_be__jump_const_1_B1 (__apl_c.881 __apl_nz.879 __trgt.819)  <1092>;
      (__apl_nz.879 var=246 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.880)  <1171>;
      (__apl_c.881 var=244 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.882)  <1173>;
    } stp=1;
    if {
        {
            () if_expr (__either.805)  <121>;
            (__either.805 var=313) undefined ()  <1037>;
        } #11
        {
        } #41 off=30 nxt=44
        {
            #235 off=7 nxt=16
            (__ptr_ENCCON0.807 var=33) const_inp ()  <1040>;
            <75> {
              (__apl_r.645 var=249 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B2 (__ct_4t0.812 e_encoding.75 __sp.51)  <1088>;
              (__apl_r.898 var=249 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.645)  <1190>;
            } stp=0;
            <76> {
              (__tmp.647 var=70 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.897)  <1089>;
              (__apl_r.897 var=249 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.898)  <1189>;
              (__tmp.900 var=70 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.647)  <1192>;
            } stp=1;
            <77> {
              (ENCCON0.125 var=23 __vola.126 var=13) load_const_bf_mov_const_const_store_2_B1 (__tmp.899 __ptr_ENCCON0.807 ENCCON0.22 __vola.12)  <1090>;
              (__tmp.899 var=70 stl=a_w1) a_w1_1_dr_move_Rw_1_int16__B0 (__tmp.900)  <1191>;
            } stp=2;
            call {
                () chess_separator_scheduler ()  <139>;
            } #16 off=11 nxt=176
            #176 off=11 nxt=182 tgt=244
            (__trgt.817 var=315) const_inp ()  <1050>;
            <72> {
              (__apl_c.650 var=254 stl=c_flag_w __apl_nz.652 var=256 stl=nz_flag_w __seff.852 var=328 stl=o_flag_w) load_cmp_const_2_B2 (__adr_u16_numbits.909 u16_numbits.73)  <1085>;
              (__apl_nz.886 var=256 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.652)  <1178>;
              (__apl_c.888 var=254 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.650)  <1180>;
              (__seff.906 var=328 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.852)  <1198>;
              (__adr_u16_numbits.909 var=-51 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_u16_numbits.910)  <1201>;
            } stp=2;
            <74> {
              () cc_b__jump_const_1_B1 (__apl_c.887 __apl_nz.885 __trgt.817)  <1087>;
              (__apl_nz.885 var=256 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.886)  <1177>;
              (__apl_c.887 var=254 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.888)  <1179>;
            } stp=3;
            <100> {
              (__adr_u16_numbits.911 var=-51 stl=a_w2 __side_effect.912 var=338 stl=c_flag_w __side_effect.914 var=338 stl=nz_flag_w __side_effect.916 var=338 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_2t0.811 __sp.51)  <1148>;
              (__adr_u16_numbits.910 var=-51 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_u16_numbits.911)  <1202>;
              (__side_effect.913 var=338 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.912)  <1203>;
              (__side_effect.915 var=338 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.914)  <1204>;
              (__side_effect.917 var=338 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.916)  <1205>;
            } stp=0;
            if {
                {
                    () if_expr (__either.802)  <174>;
                    (__either.802 var=313) undefined ()  <1032>;
                } #19
                {
                    <70> {
                      (__fch_u16_numbits.170 var=88 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_2t0.811 u16_numbits.73 __sp.51)  <1083>;
                      (__fch_u16_numbits.928 var=88 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_u16_numbits.170)  <1213>;
                    } stp=0;
                    <71> {
                      (ENCCON1.178 var=27 __vola.179 var=13) load_const_bf_mov_const_const_store_1_B1 (__fch_u16_numbits.927 __ptr_ENCCON1.808 ENCCON1.26 __vola.126)  <1084>;
                      (__fch_u16_numbits.927 var=88 stl=a_w1) a_w1_1_dr_move_Rw_1_int16__B1 (__fch_u16_numbits.928)  <1212>;
                    } stp=1;
                } #244 off=24 nxt=39
                {
                    #182 off=15 nxt=36 tgt=116
                    (__trgt.816 var=314) const_inp ()  <1049>;
                    <68> {
                      (__apl_nz.658 var=262 stl=nz_flag_w __seff.845 var=326 stl=c_flag_w __seff.846 var=327 stl=o_flag_w) load_cmp_const_1_B2 (__adr_u16_numbits.918 u16_numbits.73)  <1081>;
                      (__seff.889 var=326 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.845)  <1181>;
                      (__apl_nz.891 var=262 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.658)  <1183>;
                      (__seff.907 var=327 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.846)  <1199>;
                      (__adr_u16_numbits.918 var=51 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_u16_numbits.910)  <1206>;
                    } stp=0;
                    <69> {
                      () cc_eq__jump_const_1_B1 (__apl_nz.890 __trgt.816)  <1082>;
                      (__apl_nz.890 var=262 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.891)  <1182>;
                    } stp=1;
                    if {
                        {
                            () if_expr (__either.800)  <226>;
                            (__either.800 var=313) undefined ()  <1029>;
                        } #28
                        {
                            (__trgt.818 var=316) const_inp ()  <1051>;
                            <64> {
                              () jump_const_1_B1 (__trgt.818)  <1077>;
                            } stp=4;
                            <67> {
                              (ENCCON1.232 var=27 __vola.233 var=13 __seff.843 var=325 stl=nz_flag_w) load__ad_const_store_1_B1 (__ptr_ENCCON1.919 ENCCON1.26 __vola.126)  <1080>;
                              (__seff.892 var=325 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.843)  <1184>;
                              (__ptr_ENCCON1.919 var=35 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__ptr_ENCCON1.920)  <1207>;
                            } stp=2;
                            <104> {
                              (__ptr_ENCCON1.921 var=35 stl=__CTa_w0_uint16__cstP16_E1) const_1_B1 (__ptr_ENCCON1.808)  <1158>;
                              (__ptr_ENCCON1.920 var=35 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ptr_ENCCON1.921)  <1208>;
                            } stp=0;
                        } #116 off=19 tgt=39
                        {
                            () sink (__vola.126)  <251>;
                            () sink (__sp.242)  <257>;
                            () sink (u16_data.71)  <258>;
                            () sink (u16_numbits.73)  <259>;
                            () sink (e_encoding.75)  <260>;
                            () sink (ENCCON0.125)  <261>;
                            (__vola.243 var=13) never ()  <269>;
                            (ENCCON1.257 var=27) never ()  <283>;
                            (__ct_6s0.813 var=115) const_inp ()  <1046>;
                            <65> {
                              (__sp.242 var=19 __seff.838 var=322 stl=c_flag_w __seff.839 var=323 stl=nz_flag_w __seff.840 var=324 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_6s0.813 __sp.51 __sp.51)  <1078>;
                              (__seff.924 var=323 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.839)  <1209>;
                              (__seff.925 var=322 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.838)  <1210>;
                              (__seff.926 var=324 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.840)  <1211>;
                            } stp=0;
                            <66> {
                              () ret_1_B1 ()  <1079>;
                            } stp=1;
                        } #36 off=17 nxt=-2
                        {
                            (__vola.273 var=13) merge (__vola.233 __vola.243)  <299>;
                            (ENCCON1.287 var=27) merge (ENCCON1.232 ENCCON1.257)  <313>;
                        } #37
                    } #27
                    #252 tgt=39
                } #25
                {
                    (__vola.303 var=13) merge (__vola.179 __vola.273)  <329>;
                    (ENCCON1.317 var=27) merge (ENCCON1.178 ENCCON1.287)  <343>;
                } #38
            } #18
            #39 off=27 nxt=44
            (__ptr_TXDAT.809 var=37) const_inp ()  <1042>;
            <61> {
              (__fch_u16_data.333 var=118 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.810 u16_data.71 __sp.51)  <1074>;
              (__fch_u16_data.903 var=118 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_u16_data.333)  <1195>;
            } stp=0;
            <62> {
              (TXDAT.338 var=29 __vola.339 var=13) store_const_1_B2 (__fch_u16_data.902 __ptr_TXDAT.809 TXDAT.28 __vola.303)  <1075>;
              (__fch_u16_data.902 var=118 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__fch_u16_data.903)  <1194>;
            } stp=2;
        } #12
        {
            (__vola.340 var=13) merge (__vola.12 __vola.339)  <365>;
            (ENCCON0.350 var=23) merge (ENCCON0.22 ENCCON0.125)  <375>;
            (ENCCON1.354 var=27) merge (ENCCON1.26 ENCCON1.317)  <379>;
            (TXDAT.356 var=29) merge (TXDAT.28 TXDAT.338)  <381>;
        } #42
    } #10
    #44 off=30 nxt=-2
    () sink (__vola.340)  <401>;
    () sink (__sp.375)  <407>;
    () sink (u16_data.71)  <408>;
    () sink (u16_numbits.73)  <409>;
    () sink (e_encoding.75)  <410>;
    () sink (ENCCON0.350)  <411>;
    () sink (ENCCON1.354)  <415>;
    () sink (TXDAT.356)  <417>;
    (__ct_6s0.814 var=122) const_inp ()  <1047>;
    <59> {
      (__sp.375 var=19 __seff.831 var=319 stl=c_flag_w __seff.832 var=320 stl=nz_flag_w __seff.833 var=321 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_6s0.814 __sp.51 __sp.51)  <1072>;
      (__seff.893 var=320 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.832)  <1185>;
      (__seff.894 var=319 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.831)  <1186>;
      (__seff.908 var=321 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.833)  <1200>;
    } stp=0;
    <60> {
      () ret_1_B1 ()  <1073>;
    } stp=1;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,233:0,0);
3 : (0,233:75,0);
4 : (0,233:75,0);
5 : (0,233:53,0);
6 : (0,233:53,0);
7 : (0,233:31,0);
8 : (0,233:31,0);
10 : (0,235:2,4);
12 : (0,236:2,5);
16 : (0,237:24,7);
18 : (0,238:4,7);
25 : (0,242:9,12);
27 : (0,242:9,12);
36 : (0,248:6,17);
39 : (0,250:14,25);
41 : (0,235:2,27);
44 : (0,235:2,30);
116 : (0,244:18,14);
162 : (0,235:19,4);
176 : (0,238:21,7);
182 : (0,242:26,12);
235 : (0,237:16,6);
244 : (0,240:18,9);
----------
82 : (0,233:75,0);
84 : (0,233:53,0);
86 : (0,233:31,0);
121 : (0,235:2,4);
139 : (0,237:24,7);
174 : (0,238:4,7);
226 : (0,242:9,12);
299 : (0,242:9,21);
313 : (0,242:9,21);
329 : (0,238:4,23);
343 : (0,238:4,23);
365 : (0,235:2,29);
375 : (0,235:2,29);
379 : (0,235:2,29);
381 : (0,235:2,29);
1072 : (0,235:2,0) (0,235:2,30);
1073 : (0,235:2,30);
1074 : (0,250:16,24) (0,233:84,0);
1075 : (0,250:9,24);
1078 : (0,248:6,0) (0,248:6,17);
1079 : (0,248:6,17);
1080 : (0,244:18,14);
1081 : (0,242:14,12) (0,242:26,12);
1082 : (0,242:26,12) (0,242:9,12);
1083 : (0,240:41,9) (0,233:62,0);
1084 : (0,240:18,9);
1085 : (0,238:9,7) (0,238:21,7);
1087 : (0,238:21,7) (0,238:4,7);
1088 : (0,237:39,6) (0,233:41,0) (0,237:26,6);
1089 : (0,237:26,6);
1090 : (0,237:16,6);
1091 : (0,235:7,4) (0,233:62,0) (0,235:19,4);
1092 : (0,235:19,4) (0,235:2,4);
1093 : (0,233:41,0) (0,233:31,0);
1094 : (0,233:62,0) (0,233:53,0);
1096 : (0,233:5,0);
1097 : (0,233:84,0) (0,233:75,0);
1148 : (0,233:62,0);
1158 : (0,244:18,0);

