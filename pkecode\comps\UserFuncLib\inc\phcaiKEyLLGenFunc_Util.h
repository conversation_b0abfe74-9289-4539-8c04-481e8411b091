/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_Util.h 28748 2020-09-21 16:18:07Z dep10330 $
  $Revision: 28748 $
*/

/**
 * @file
 * Declarations of various utility functions.
 */

#ifndef PHCAIKEYLLGENFUNC_UTIL_H
#define PHCAIKEYLLGENFUNC_UTIL_H

#include "types.h"
#include "phcaiKEyLLGenFunc_Platform.h"


/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup Util Utility Functions
 * Various utility functions: button de-bouncing, CRC calculations, Random Number Generator (RNG),
 * EROM signature, divide operations,
 * initialization of global and static variables, stack checking.
 * @{
 */


/**
 Reset the RNG
 RNGRUN | BITSHIFT[1:0] |  CONFIG[1:0] | TRIMOSC | RNGEN | RNGRST
  0       xx                xx              x         x      1      = 01h
*/
#define RNGCON_RESET 0x01U

/**
 Initialization mode with oscillators enabled, 17 bit shifts.
 RNGRUN | BITSHIFT[1:0] |  CONFIG[1:0] | TRIMOSC | RNGEN | RNGRST
  0       01                00              1         1      0      = 26h
*/
#define RNGCON_TRUE_INITMODE 0x26U

/**
 Hybrid mode with oscillators enabled, 17 bit shifts.
 RNGRUN | BITSHIFT[1:0] |  CONFIG[1:0] | TRIMOSC | RNGEN | RNGRST
  0       01                11              1         1      0      = 3Eh
*/
#define RNGCON_TRUE_HYBRIDMODE 0x3EU

/**
 CRC16 mode with 16 bit shifts.
 RNGRUN | BITSHIFT[1:0] |  CONFIG[1:0] | TRIMOSC | RNGEN | RNGRST
  0       00                 01             0         0      0      = 08h
*/
#define RNGCON_CRC16_MODE     0x08U

/**
 LFSR mode with 16 bit shifts.
 RNGRUN | BITSHIFT[1:0] |  CONFIG[1:0] | TRIMOSC | RNGEN | RNGRST
  0       00                 10             0         0      0      = 10h
*/
#define RNGCON_LFSR_MODE     0x10U


/**
 Lowest RAM address in Data Memory (DM).
 Applies to all supported products (TOKEN family and SMART2A).
 */
#define RAM_START 0x0180u

/**
 Highest RAM address in Data Memory (DM).
 Applies to all supported products (TOKEN family and SMART2A).
 */
#define RAM_END   (PLATFORM_INITIAL_STACKPTR -1u)

/*-----------------------------------------------------------------------------------------------*/

// Common macros

#define MAX( a, b) ( (a >= b ) ? a : b )
#define MIN( a, b) ( (a <= b ) ? a : b )

/*-----------------------------------------------------------------------------------------------*/

/**
 * Operating mode for RNG used by phcaiKEyLLGenFunc_Util_RNG_Run.
 * @see phcaiKEyLLGenFunc_Util_RNG_Run
 */
typedef enum
{
  /** Pseudo RNG as CRC16 (RNGCONFIG=01) */
  RNG_CRC16,
  /** Pseudo RNG as LFSR  (RNGCONFIG=10) */
  RNG_LFSR,
  /** True RNG, Hybrid mode (RNGCONFIG=11) */
  RNG_TRUE
}
RNG_Mode_t;

/*-----------------------------------------------------------------------------------------------*/

/**
 * Operating mode of CPU cycle counter.
 * @see phcaiKEyLLGenFunc_Util_CpuCycleCounter_start
 */
typedef enum
{
  /** Count system clock cycles (CPUCLK) */
  CPUMCC_SYSTEMCLOCK   = 0u,
  /** Count instruction or machine cycles */
  CPUMCC_MACHINECLOCK  = 1u
}
CpuMCnt_Mode_t;


/*-----------------------------------------------------------------------------------------------*/

#if ( (__tct_release__ * 100 + __tct_patch__) < 120110 )

// warning Tool suite version before 12R1.10 is used, extended divide functions not available.

#else


/**
 * Structure type for extended unsigned divide function.
 * @note Only available for Chess version 12R1.10 or later.
 * @see phcaiKEyLLGenFunc_Util_DivideU32byU16_ext
 */
typedef struct property(keep_in_registers)
{
  /** the quotient  */
  uint16_t quot;
  /** the remainder */
  uint16_t rem;
} phcaiGFL_ldivu_t ;

/**
 * Structure type for extended signed divide function.
 * @note Only available for Chess version 12R1.10 or later.
 * @see phcaiKEyLLGenFunc_Util_DivideI32byI16_ext
 */
typedef struct property(keep_in_registers)
{
  /** the quotient  */
  int16_t quot;
  /** the remainder */
  int16_t rem;
} phcaiGFL_ldivs_t ;

#endif

/*-----------------------------------------------------------------------------------------------*/

/**
 * Symbol used to locate the start address of the _rwinit_data segment in EROM.
 * This external symbol is only generated by the linker if option +i is given.
 * Referenced by function phcaiKEyLLGenFunc_LoadRWInitData().
 * @see phcaiKEyLLGenFunc_Util_LoadRWInitData
 */
extern uint8_t _rwinit_data[];

/*-----------------------------------------------------------------------------------------------*/

/*
 * Debug data and control register (not documented in DS/UM).
 */
extern volatile SFR_word chess_storage(DM9)  DBGDAT;
extern volatile SFR_word chess_storage(DM9)  DBGCON;

/*-----------------------------------------------------------------------------------------------*/

/**
 * Repeatedly reads the state of all input ports P1x, P2x, up to P3x (as far as available in
 * selected platform) and filters the results over a certain period of time.
 * The algorithm works by scanning u8_num_samples readings per port. The function returns
 * a logic '1' for a port that was sensed high ('1') in at least u8_threshold readings.
 * Between each ports sampling, an adjustable delay of approx. u16_delay_us microseconds
 * is inserted.
 * Ports P2x are ignored (not processed) if PLATFORM_HAS_PORT2x is not defined.
 * Ports P3x are ignored (not processed) if PLATFORM_HAS_PORT3x is not defined.
 *
 * @param[out] u8arr_button_results  One result bit per port, index 0 corresponds to P1INS,
 *                                   index 1 to P2INS, index 2 to P3INS.
 * @param[in]  u16_delay_us          Delay between samplings in microseconds.
 * @param[in]  u8_num_samples        Number of samples to take.
 * @param[in]  u8_threshold          Minimum number of '1' samples required to
 *                                   give a result of '1'.
 */
void phcaiKEyLLGenFunc_Util_Debounce( uint8_t  u8arr_button_results[3],
                                      uint16_t u16_delay_us,
                                      uint8_t  u8_num_samples,
                                      uint8_t  u8_threshold );

/*-----------------------------------------------------------------------------------------------*/


/**
 * Convert a (16 bit) number to a five-byte null terminated hex string.
 * @param[out] u8arr_Res Resulting string.
 * @param[in]  u16_Num   The number to convert.
 */
void phcaiKEyLLGenFunc_Util_ToHexStr( uint8_t u8arr_Res[5], const uint16_t u16_Num );


/*-----------------------------------------------------------------------------------------------*/


/**
 * Calculate the CRC-8 for an array of bytes.
 * Uses the CRC hardware unit (registers CRC8DIN and CRCDAT, see data sheet for details).
 * Number of bytes may be 0, in this case the unchanged CRC is returned.
 * Generator polynomial: x^8+x^2+x+1 (known as 'ITU-T' or 'CCITT')
 *
 * @param[in] pu8_data    Pointer to the data array.
 * @param[in] u16_count   Number of bytes (>=0).
 * @param[in] u8_lastCrc  Last computed or initial CRC-8 value.
 * @return    The new CRC-8 value.
 * @see phcaiKEyLLGenFunc_Util_CRC8_update
 */
uint8_t phcaiKEyLLGenFunc_Util_CRC8_bytes( const uint8_t * pu8_data,
                                           uint16_t        u16_count,
                                           uint8_t         u8_lastCrc );

/**
 * Calculate next CRC-8 value for a single byte.
 * Uses the CRC hardware unit (registers CRC8DIN and CRCDAT, see data sheet for details).
 * Must not be used while the CIU (in NCF297x) is receiving or transmitting data via the
 * ISO14443 interface.
 *
 * @param[in] u8_data     The data byte.
 * @param[in] u8_lastCrc  Last CRC-8 value.
 * @return    The new CRC-8 value.
 * @see phcaiKEyLLGenFunc_Util_CRC16_update
 */
uint8_t phcaiKEyLLGenFunc_Util_CRC8_update( uint8_t u8_data, uint8_t u8_lastCrc );


/**
 * Calculate next CRC-16 value for a single byte.
 * (CRC-16 as used in ISO14443.)
 * Software implementation, no look-up table required.
 *
 * @param[in] u8_data      The data byte.
 * @param[in] u16_lastCrc  Last CRC-16 value.
 * @return    The new CRC-16 value.
 * @see phcaiKEyLLGenFunc_Util_CRC16_update
 */
uint16_t phcaiKEyLLGenFunc_Util_CRC16_update_sw( uint8_t u8_data, uint16_t u16_lastCrc );


/**
 * Calculate next CRC-24 value for a single word.
 * Algorithm as used in ROM code for signature calculations (MDI command cCalcSign).
 * Software implementation, no look-up table required.
 *
 * @param[in] u16_input_data The data word.
 * @param[in] u8arr_lastCRC  Last CRC-24 value input and output (3 byte array)
 * @see phcaiKEyLLGenFunc_Util_CRC16_update
 */
void phcaiKEyLLGenFunc_Util_CRC24_update( uint16_t u16_input_data, uint8_t u8arr_lastCRC[3] );

/*-----------------------------------------------------------------------------------------------*/

#ifdef PLATFORM_HAS_GENERICCRC
/**
 * Calculate the Generic CRC for an array of bytes.
 * Uses the Generic CRC hardware unit (registers GCRCCON0, GCRCPOLY, GCRCDIN and CRCDAT,
 * see data sheet for details).
 * Number of bytes may be 0, in this case the unchanged CRC is returned.
 *
 * @param[in] pu8_data       Pointer to the data array.
 * @param[in] u16_count      Number of bytes (>=0).
 * @param[in] u16_initCRC    Last computed or initial CRC-8 value.
 * @param[in] u16_polynomial The 16-bit polynomial, written to register GCRCPOLY.
 * @param[in] u8_CtrlReg     Settings, written to register GCRCCON0.
 * @return    The new CRC value (16 bit).
 * @see phcaiKEyLLGenFunc_Util_GenericCRC_update
 */
uint16_t phcaiKEyLLGenFunc_Util_GenericCRC_bytes( const uint8_t * pu8_data,
                                                  uint16_t        u16_count,
                                                  uint16_t        u16_initCRC,
                                                  uint16_t        u16_polynomial,
                                                  uint8_t         u8_CtrlReg );
#endif

/*-----------------------------------------------------------------------------------------------*/

#ifdef PLATFORM_HAS_GENERICCRC
/**
 * Calculate next Generic CRC value for a single byte.
 * Uses the Generic CRC hardware unit (registers GCRCDIN and CRCDAT, see data sheet for details).
 * Polynomial (GCRCPOLY register) and settings (GCRCCON0) are not changed.
 *
 * @param[in] u8_data     The data byte.
 * @param[in] u8_lastCrc  Last CRC value.
 * @return    The new CRC value.
 * @see phcaiKEyLLGenFunc_Util_GenericCRC_bytes
 */
uint16_t phcaiKEyLLGenFunc_Util_GenericCRC_update( uint8_t u8_data, uint16_t u16_lastCrc );

#endif

/*-----------------------------------------------------------------------------------------------*/

/**
 * Calculate the signature (CRC-24) of the complete EROM, which is the same as returned by
 * MDI cCalcSign command for instance code 2.
 * Uses syscall EROM_read syscall to read the lower EROM addresses.
 * Considers EROM size configuration as set in MEMCFG SFR.
 * Supports all TOKEN family platforms and SMART2A.
 *
 * @param[out] u8arr_signature  The resulting signature (3 byte array)
 * @return SUCCESS with no failures, ERROR in case of error code from the syscall.
 */
error_t phcaiKEyLLGenFunc_Util_CalcEromSignature( uint8_t u8arr_signature[3] );

/*-----------------------------------------------------------------------------------------------*/

/**
 * Return the current value (address in RAM) of the stack pointer (R7).
 * Inline assembly function expanding to one instruction.
 *
 * @return The stack pointer value.
 */
inline assembly uint16_t phcaiKEyLLGenFunc_Util_read_SP( void )
{
  asm_begin
  /* move stack pointer register R7 to R0 and return it to caller */
  MOV R0, R7
  asm_end
}

/**
 * Return the end value (lowest address in RAM) of the stack range
 * as specified in the linker configuration file.
 * Inline assembly function expanding to one instruction.
 *
 * @return The lowest address of the stack range.
 */
inline assembly uint16_t phcaiKEyLLGenFunc_Util_BottomOfStack( void )
{
  asm_begin
  /* move stack pointer end (=bottom) value to R0 and return it to caller */
  MOV R0, #(_sp_end_value_DM -1)
  asm_end
}

/**
 * Check for stack overflow.
 * If the stack pointer value is less than the value specified in the linker configuration file,
 * perform a VDD reset.
 *
 * @note It is recommended to perform this test at the beginning of any recursive
 * function or nested interrupt handler, whenever the stack usage cannot be predicted with
 * 100% confidence, to avoid that a stack overflow causes an undefined state
 * of the controller.
 *
 */
inline void phcaiKEyLLGenFunc_Util_StackWatchdog( void );

/*-----------------------------------------------------------------------------------------------*/

/**
 * Word by word unsigned multiplication (16*16 to 32 bit) by means of
 * MRK-III MULU instruction.
 * @param[in] u16_factor1  The first factor.
 * @param[in] u16_factor2  The second factor.
 * @return  The unsigned 32 bit product ( u16_factor1 * u16_factor2 )
 * @todo find out if/how this could be inlined.
 */
uint32_t chess_storage(RwL)
phcaiKEyLLGenFunc_Util_MulU16byU16( uint16_t chess_storage(R0) u16_factor1,
                                    uint16_t chess_storage(R2) u16_factor2  );

/**
 * Word by word signed multiplication (16*16 to 32 bit) by means of
 * MRK-III MULS instruction.
 * @param[in] i16_factor1  The first factor.
 * @param[in] i16_factor2  The second factor.
 * @return  The signed 32 bit product ( i16_factor1 * i16_factor2 )
 * @todo find out if/how this could be inlined.
 */
int32_t chess_storage(RwL)
phcaiKEyLLGenFunc_Util_MulI16byI16( int16_t chess_storage(R0) i16_factor1,
                                    int16_t chess_storage(R2) i16_factor2  );


/**
 * Long word by word unsigned division by means of MRK-III DIVU instruction.
 * Does not check overflow or division by zero.
 * @param[in] u32_dividend  The dividend.
 * @param[in] u16_divisor   The divisor.
 * @return  The unsigned 16 bit quotient ( u32_dividend / u16_divisor )
 */
uint16_t chess_storage(R0)
phcaiKEyLLGenFunc_Util_DivideU32byU16( uint32_t chess_storage(RwL) u32_dividend,
                                       uint16_t chess_storage(R2)  u16_divisor  );


/**
 * Long word by word signed division by means of MRK-III DIVS instruction.
 * Does not check overflow or division by zero.
 * @param[in] i32_dividend  The dividend.
 * @param[in] i16_divisor   The divisor.
 * @return  The signed 16 bit quotient ( i32_dividend / i16_divisor )
 */
int16_t chess_storage(R0)
phcaiKEyLLGenFunc_Util_DivideI32byI16( int32_t chess_storage(RwL) i32_dividend,
                                       int16_t chess_storage(R2)  i16_divisor  );


#if ( (__tct_release__ * 100 + __tct_patch__) >= 120110 )

/**
 * Long word by word unsigned division by means of MRK-III DIVU instruction, extended format
 * including returning of remainder value and overflow flag.
 * The overflow flag is set also in case of division by zero.
 * @note Only available for Chess version 12R1.10 or later.
 * @param[in]  u32_dividend  The dividend.
 * @param[in]  u16_divisor   The divisor.
 * @param[out] ru8_ovflag    The O(verflow) flag after division (1: overflow, 0: no overflow).
 * @return  A structure type, containing the unsigned 16 bit quotient ( u32_dividend / u16_divisor )
 *          and the remainder value.
 * @see phcaiGFL_ldivu_t
 */
phcaiGFL_ldivu_t chess_storage(RwL)
phcaiKEyLLGenFunc_Util_DivideU32byU16_ext( uint32_t chess_storage(RwL) u32_dividend,
                                           uint16_t chess_storage(R2)  u16_divisor,
                                           chess_output  uint8_t&  chess_storage(R3L) ru8_ovflag   );


/**
 * Long word by word signed division by means of MRK-III DIVS instruction, extended format
 * including returning of remainder value and overflow flag.
 * The overflow flag is set also in case of division by zero.
 * @note Only available for Chess version 12R1.10 or later.
 * @param[in]  i32_dividend  The dividend.
 * @param[in]  i16_divisor   The divisor.
 * @param[out] ru8_ovflag    The O(verflow) flag after division (1: overflow, 0: no overflow).
 * @return  A structure type, containing the unsigned 16 bit quotient ( i32_dividend / i16_divisor )
 *          and the remainder value.
 * @see phcaiGFL_ldivs_t
 */
phcaiGFL_ldivs_t chess_storage(RwL)
phcaiKEyLLGenFunc_Util_DivideI32byI16_ext( int32_t chess_storage(RwL) i32_dividend,
                                           int16_t chess_storage(R2)  i16_divisor,
                                           chess_output  uint8_t&  chess_storage(R3L) ru8_ovflag   );

#endif

/**
 * Return the CPU's overflow flag (located in the PSW, Program Status Word).
 * Experimental, use with care!
 * If available, use the intrinsic function bool cc_o (uint1_ o)
 * @return 1 if the O flag is set, 0 if not.
 */
uint8_t chess_storage(R0L) phcaiKEyLLGenFunc_Util_isOverflow( void );


/**
 * Initialize the RNG unit for non-deterministic mode (TRNG, True Random Number Generator).
 * Applies the Initialization mode for RNGCON.RNGCONFIG, which starts the RNG for nine times
 * using the recommended settings, to collect sufficient entropy.
 * Duration is approx. 1.4 ms.
 *
 * @see phcaiKEyLLGenFunc_Util_RNG_run
 */
void phcaiKEyLLGenFunc_Util_TrueRNG_init( void );


/**
 * Run the RNG to generate one true or pseudo random number.
 * Duration (2 MHz machine clock): PRNG mode: approx. 14 us;
 * TRNG mode, oscillators enabled beforehand: approx. 180 us.
 *
 * @param[in] e_rngmode    Operating mode setting (one of RNG_CRC16, RNG_LFSR, RNG_TRUE).
 * @param[in] b_switchoff  If TRUE, the oscillators are switched off after completion.
 *                         (Only meaningful for TRNG mode.)
 * @return    The resulting 16 bit random number.
 * @see RNG_Mode_t
 * @see phcaiKEyLLGenFunc_Util_TrueRNG_init
 */
uint16_t phcaiKEyLLGenFunc_Util_RNG_run( RNG_Mode_t e_rngmode, bool_t b_switchoff );


/**
 * Initialize global and static variables from initialization segment (.rwinit segment).
 * Using this function requires two additional measures:
 * <UL>
 * <li> Add the option '+i' to the linker options. </li>
 * <li> Add following statements to the linker configuration file: <BR>
 * <CODE>
 * _rwinit_mem DM  <BR>
 * _no_init_range DM  0x0000..0x017F <BR>
 * </CODE>
 * Note: adding the statement _no_init_range ULP 0x0000..0x03FF prevents loading the ULP EEPROM
 * in the debugger and the unwanted placement of ULP initial data in rwinit segment.
 * (This is a bug in 2015.06 release, and can be avoided by the linker instruction
 * _rwinit_exclude ULP which is introduced with the 2016.03 release.)
 * </li>
 * </UL>
 * This function should be called directly after program entry before access to
 * any affected variable. This applies to each entry point or vector, in case the code
 * expects the initialized data.
 *
 * For detailed explanation please refer to the MRK-III tool suite documentation,
 * User Guide IP Programmer for MRK-III(e), section "How to make ROM images".
 *
 * @param[in] b_skip_BSS  If TRUE, skips setting of BSS symbols to zeros, in favor of
 *                        faster execution.
 *
 * @see _rwinit_data
 */
void phcaiKEyLLGenFunc_Util_LoadRWInitData( bool_t b_skip_BSS );


/**
 * Verify correct package compatibility setting in case of TOKEN-PLUS.
 * This function checks if bit TOKEN32_COMPAT in register BATSYS0
 * has the correct setting and returns SUCCESS in this case.
 * See app note AN-SCA1609 for details.
 *
 * @return SUCCESS if setting is OK, ERROR otherwise.
 */
error_t phcaiKEyLLGenFunc_Util_VerifyCompat32( void );



#ifdef PLATFORM_HAS_CPUMCCNT

/**
 * Start the CPU cycle counter.
 * Only available on PLUS and SRX platforms.
 * This counter can either count CPU clock (CPUCLK) cycles, including times
 * with MRK3 in IDLE state, when parameter is set to CPUMCC_SYSTEMCLOCK (CLKSELCNT = 0);
 * If parameter equals CPUMCC_MACHINECLOCK (CLKSELCNT = 1), counts the number of CPU
 * instruction cycles (aka machine cycles), which are zero while MRK3 is in IDLE state.
 * The cycle counter can be used to measure the execution times of functions, system calls,
 * or other code sections.
 *
 * @param[in]   en_mode If CPUMCC_SYSTEMCLOCK, sets CLKSELCNT = 0 such that counter runs with CPUCLK,
 *                      regardless of MRK3 in IDLE or not.
 *                      If CPUMCC_MACHINECLOCK, counts MRK3 machine cycles.
 * @see CpuMCnt_Mode_t
 * @see phcaiKEyLLGenFunc_Util_CpuCycleCounter_stop
 */
void phcaiKEyLLGenFunc_Util_CpuCycleCounter_start( CpuMCnt_Mode_t en_mode );

/**
 * Stop the CPU cycle counter and return the result as 32-bit unsigned integer.
 * Only available on PLUS and SRX platforms.
 *
 * @return The number of CPUCLK or machine cycles since counter started.
 * @see phcaiKEyLLGenFunc_Util_CpuCycleCounter_start
 *
 */
uint32_t phcaiKEyLLGenFunc_Util_CpuCycleCounter_stop( void );

/**
 * Calibrate the CPU cycle counter and return the result as 32-bit unsigned integer.
 * Only available on PLUS and SRX platforms.
 * When calling the _start and _stop functions for the cycle counter, there are always
 * additional cycles due to the instructions for calling a function, possible stack frame
 * setup, and returning to caller, the amount of which depends on the compiler configuration.
 * This function helps to elimitate this offset. Keep the returned value in a u32_t variable
 * and subtract it from the counter value returned by the _stop function, to perform
 * the compensation.
 *
 * @param[in]   en_mode Mode passed to phcaiKEyLLGenFunc_Util_CpuCycleCounter_start.
 * @return The number of CPUCLK or machine cycles to be used for offset compensation.
 * @see phcaiKEyLLGenFunc_Util_CpuCycleCounter_start
 * @see phcaiKEyLLGenFunc_Util_CpuCycleCounter_stop
 */
uint32_t phcaiKEyLLGenFunc_Util_CpuCycleCounter_calibrate( CpuMCnt_Mode_t en_mode );

#endif


/**
 * Sends a text string via the MDI interface to an attached debugger
 * (e.g. MRK-III 2-link).
 * Equivalent to system call phcaiKEyLLGenFunc_CS_MDI_Print but works in user mode
 * such that the MODE bit of the transmitted MDI frames is 0.
 *
 * @param[in]   ps_MdiString   Zero-terminated text string. May be stored in user RAM
 *                             or also in EROM (_rodata).
 */
void phcaiKEyLLGenFunc_Util_MDI_Print( const string_t * const ps_MdiString );

/**
 * Clear CPU data bus after read access to a VBATREG register on TOKEN platform.
 * This function should always be called after reading VBATREG SFR, as a work-around
 * against the anomaly described in data sheets and app note. Adhering to these
 * recommendations prevents possibly corrupted data when reading from a VDD SFR.
 * See app note AN-SCA1609 for details.
 * Inline function, expanding inline to a single MKR3 instruction on TOKEN,
 * empty on other platforms.
 */
inline void phcaiKEyLLGenFunc_Util_ClearVbatRegRead( void );


/**
 * Verify state of the VBAT regulator.
 * Returns SUCCESS if the VBAT regulator is enabled and the VBATREG supplied hardware
 * is not held in reset.
 *
 * @return SUCCESS if setting is OK, ERROR otherwise.
 */
inline error_t phcaiKEyLLGenFunc_Util_VerifyVBatRegulator( void );

/*-----------------------------------------------------------------------------------------------*/

/*
 Definition of C inline functions.
 Note: assembly inline functions must be defined immediately, a separate declaration
 seems not possible.
 */

inline void phcaiKEyLLGenFunc_Util_StackWatchdog( void )
{
  if ( phcaiKEyLLGenFunc_Util_read_SP() < phcaiKEyLLGenFunc_Util_BottomOfStack() )
  {
    /* force VDD reset */
    PCON0.bits.VDDRST = 1;
  }
}


inline void phcaiKEyLLGenFunc_Util_ClearVbatRegRead( void )
{
  #ifdef PLATFORM_TOKEN
  /* work-around for SFR data corruption ("dummy read" from a VDD SFR register). */
  uint8_t u8_dummy_read_vddsfr = P1INS.val;
  #endif
}

#ifdef PLATFORM_HAS_VBATREG

inline error_t phcaiKEyLLGenFunc_Util_VerifyVBatRegulator( void )
{
  if ( ( BATSYS0.bits.BATRGLEN == 1u ) && ( BATSYS0.bits.BATRGLRST == 0u ) ) {
    return SUCCESS;
  }
  else {
    return ERROR;
  }
}

#endif

/*-----------------------------------------------------------------------------------------------*/

/*@}*/
/*@}*/

#endif
/* eof */
