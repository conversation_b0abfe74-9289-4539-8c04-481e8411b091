
#include "types.h"
#include "defs.h"

#if   defined(HT2E_INIT) 
#elif defined(HT3_INIT) 
#elif defined(HTPRO_INIT) 
#elif defined(HTAES_INIT) 
#elif defined(GENERIC_TRANSPONDER) 
#elif defined(ULPEE_DELIVERY_DEFAULTS) 
#else
#warning "No initialization of transponder related ULPEE memory selected."
#endif



// --- Configuration ULPEE pages --------------------------------------------------------------------------------------

/*-----------------------------------------------------------------------------------------------*/
#ifdef ULPEE_DELIVERY_DEFAULTS
// Default values as listed in datasheet
ulp32_t chess_storage(ULP:0x0001) EE_ISK0    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0002) EE_ISK1    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0003) EE_ISK2    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0004) EE_ISK3    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0005) EE_PAGE005 = 0x00000000U;
ulp32_t chess_storage(ULP:0x0006) EE_PAGE006 = 0x00000000U;
ulp32_t chess_storage(ULP:0x0007) EE_PAGE007 = 0x00000000U;
ulp32_t chess_storage(ULP:0x0008) EE_TSI0    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0009) EE_TSI1    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000A) EE_TSI2    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000B) EE_TSI3    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000C) EE_TSI4    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000D) EE_TSI5    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000E) EE_TSI6    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000F) EE_TSI7    = 0x00000000U;

ulp32_t chess_storage(ULP:0x01F6) EE_PAGE1F6 = 0xFFFFF500U;  // page 502
ulp32_t chess_storage(ULP:0x01F8) EE_PAGE1F8 = 0x00000000U;
ulp32_t chess_storage(ULP:0x01F9) EE_PAGE1F9 = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FA) EE_PAGE1FA = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FB) EE_PAGE1FB = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FC) EE_PAGE1FC = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FD) EE_PAGE1FD = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FE) EE_PAGE1FE = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FF) EE_PAGE1FF = 0x00000000U;

#endif

/*-----------------------------------------------------------------------------------------------*/
#ifdef GENERIC_TRANSPONDER
// Try to put settings that work reasonably for any transponder type:
// ISK,config,HT-PRO TSI : all zero
ulp32_t chess_storage(ULP:0x0001) EE_ISK0    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0002) EE_ISK1    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0003) EE_ISK2    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0004) EE_ISK3    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0005) EE_PAGE005 = 0x00000000U;
ulp32_t chess_storage(ULP:0x0006) EE_PAGE006 = 0x00000000U;
ulp32_t chess_storage(ULP:0x0007) EE_PAGE007 = 0x00000000U;
ulp32_t chess_storage(ULP:0x0008) EE_TSI0    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0009) EE_TSI1    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000A) EE_TSI2    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000B) EE_TSI3    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000C) EE_TSI4    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000D) EE_TSI5    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000E) EE_TSI6    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000F) EE_TSI7    = 0x00000000U;

// user memory configuration (HT-2E/-3/-AES): segment 1-4 = 8 blocks plain/plain
ulp32_t chess_storage(ULP:0x0078) EE_MEMCFG0 = 0x03080308U;
ulp32_t chess_storage(ULP:0x0079) EE_MEMCFG1 = 0x03080308U;
ulp32_t chess_storage(ULP:0x007A) EE_MEMCFG2 = 0x00000000U;
ulp32_t chess_storage(ULP:0x007B) EE_MEMCFG3 = 0x00000000U;
ulp32_t chess_storage(ULP:0x007F) EE_MEMIDS  = 0x00000000U;  // set IDS=00

ulp32_t chess_storage(ULP:0x01F6) EE_PAGE1F6 = 0xFFFFF500U;  // page 502
ulp32_t chess_storage(ULP:0x01F7) EE_PAGE1F7 = 0x00000000U;
ulp32_t chess_storage(ULP:0x01F8) EE_PAGE1F8 = 0x1F000000U;  // page 504: HT-PRO UMACFG base, SEG0_S=31 blocks=248 pages
ulp32_t chess_storage(ULP:0x01F9) EE_PAGE1F9 = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FA) EE_PAGE1FA = 0x05000000U;  // SEG0_M: plain/plain
ulp32_t chess_storage(ULP:0x01FB) EE_PAGE1FB = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FC) EE_PAGE1FC = 0x00000007U;  // HT-PRO IDS points to page 7, PIDS=0
ulp32_t chess_storage(ULP:0x01FD) EE_PAGE1FD = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FE) EE_PAGE1FE = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FF) EE_PAGE1FF = 0x00000000U;

#endif

/*-----------------------------------------------------------------------------------------------*/
#ifdef HTPRO_INIT
// initialize ULPEE for HITAG-PRO2
// ISK pages: default = all zero
ulp32_t chess_storage(ULP:0x0001) EE_ISK0    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0002) EE_ISK1    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0003) EE_ISK2    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0004) EE_ISK3    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0005) EE_PAGE005 = 0x00000000U;
ulp32_t chess_storage(ULP:0x0006) EE_PAGE006 = 0x00000000U;
// IDS page                                 
ulp32_t chess_storage(ULP:0x0007) EE_IDS     = 0x00000000U;
// TSI pages                                
ulp32_t chess_storage(ULP:0x0008) EE_TSI0    = 0x00000000U;
ulp32_t chess_storage(ULP:0x0009) EE_TSI1    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000A) EE_TSI2    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000B) EE_TSI3    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000C) EE_TSI4    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000D) EE_TSI5    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000E) EE_TSI6    = 0x00000000U;
ulp32_t chess_storage(ULP:0x000F) EE_TSI7    = 0x00000000U;

// user memory configuration: 1. segment = 1 block plain/plain
ulp32_t chess_storage(ULP:0x01F8) EE_MEMCFG0 = 0x01000000U;
ulp32_t chess_storage(ULP:0x01F9) EE_MEMCFG1 = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FA) EE_MEMCFG2 = 0x05050000U;
ulp32_t chess_storage(ULP:0x01FB) EE_MEMCFG3 = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FC) EE_MEMCFG4 = 0x00000007U;  // IDS points to page 7, PIDS=0
ulp32_t chess_storage(ULP:0x01FD) EE_MEMCFG5 = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FE) EE_MEMCFG6 = 0x00000000U;
ulp32_t chess_storage(ULP:0x01FF) EE_MEMCFG7 = 0x00000000U;
#endif

/*-----------------------------------------------------------------------------------------------*/
#ifdef HT2E_INIT
// initialize ULPEE for HITAG-2(E)
#define LFTEN 0x00000001

// HITAG-2 transponder default secret key
ulp32_t chess_storage(ULP:0x0001) EE_PSK0    = 0x4D494B52U; // ISK lower 32 bits 
ulp32_t chess_storage(ULP:0x0002) EE_PSK1    = 0x00004F4EU; // ISK upper 16 bits
ulp32_t chess_storage(ULP:0x0003) EE_TMCF_PW = 0x55555555U;//0x00AA4854U; // TMCF: ENC=1, others=0
ulp32_t chess_storage(ULP:0x0004) EE_RSKL    = 0x4D494B52U;
ulp32_t chess_storage(ULP:0x0005) EE_RSKH    = 0x00004F4EU;
ulp32_t chess_storage(ULP:0x0006) EE_USER2   = 0x4BF50000U; //For Polling WUP random num
ulp32_t chess_storage(ULP:0x0007) EE_USER3   = 0x00000000U; //SI counter SYNC

ulp32_t chess_storage(ULP:0x0018) EE_SICMP   = 0x00000000U; //SI counter SYNC

// user memory configuration: 1. segment = 1 block plain/plain
ulp32_t chess_storage(ULP:0x0078) EE_MEMCFG0 = 0x03010000U;
ulp32_t chess_storage(ULP:0x0079) EE_MEMCFG1 = 0x00000000U;
ulp32_t chess_storage(ULP:0x007A) EE_MEMCFG2 = 0x00000000U;
ulp32_t chess_storage(ULP:0x007B) EE_MEMCFG3 = 0x00000000U;
ulp32_t chess_storage(ULP:0x007F) EE_MEMIDS  = 0x00000000U;  // set IDS=00

#endif

/*-----------------------------------------------------------------------------------------------*/
/*#ifdef HT3_INIT
// initialize ULPEE for HITAG-3
#define LFTEN 0x00000001

// ISK0..2 pages: default = 111122223333444455556666
ulp32_t chess_storage(ULP:0x0001) EE_ISK0    = 0x11112222;
ulp32_t chess_storage(ULP:0x0002) EE_ISK1    = 0x33334444;
ulp32_t chess_storage(ULP:0x0003) EE_ISK2    = 0x55556666;
//ulp32_t chess_storage(ULP:0x0004) EE_TMCF    = 0x00000000;
// RKE block address: page 4 (bit 3-0) (byte 3 == TMCF)
//ulp32_t chess_storage(ULP:EE_RKE_CFG_ADDR_PAGE ) EE_RKE_CFG_ADDR = 0x00000003; 
// user memory configuration: 1. segment = 1 block plain/plain
ulp32_t chess_storage(ULP:0x0078) EE_MEMCFG0 = 0x03010000;
ulp32_t chess_storage(ULP:0x0079) EE_MEMCFG1 = 0x00000000;
ulp32_t chess_storage(ULP:0x007A) EE_MEMCFG2 = 0x00000000;
ulp32_t chess_storage(ULP:0x007B) EE_MEMCFG3 = 0x00000000;
ulp32_t chess_storage(ULP:0x007F) EE_MEMIDS  = 0x00000000;  // set IDS=00
  
#endif

/*-----------------------------------------------------------------------------------------------*/
#ifdef HTAES_INIT
// initialize ULPEE for HITAG-AES
#define LFTEN 0x00000001
// ISK0..3 pages: default = zero
ulp32_t chess_storage(ULP:0x0001) EE_ISK0     = 0x00000000;
ulp32_t chess_storage(ULP:0x0002) EE_ISK1     = 0x00000000;
ulp32_t chess_storage(ULP:0x0003) EE_ISK2     = 0x00000000;
ulp32_t chess_storage(ULP:0x0004) EE_ISK3     = 0x00000000;
ulp32_t chess_storage(ULP:0x0005) EE_TMCF     = 0x00000000;

// user memory configuration: 1. segment = 1 block plain/plain
ulp32_t chess_storage(ULP:0x0078) EE_MEMCFG0 = 0x03010000U;
ulp32_t chess_storage(ULP:0x0079) EE_MEMCFG1 = 0x00000000U;
ulp32_t chess_storage(ULP:0x007A) EE_MEMCFG2 = 0x00000000U;
ulp32_t chess_storage(ULP:0x007B) EE_MEMCFG3 = 0x00000000U;
ulp32_t chess_storage(ULP:0x007F) EE_MEMIDS  = 0x00000000U;  // set IDS=00

#endif

//*******************************************************************

// initialize Sequence Increment counter with all zeroes
ulp32_t chess_storage(ULP:EE_SI_DEFAULT_PAGE + 0) EE_SI_PAGE0 = 0x00000000;
ulp32_t chess_storage(ULP:EE_SI_DEFAULT_PAGE + 1) EE_SI_PAGE1 = 0x00000000;
ulp32_t chess_storage(ULP:EE_SI_DEFAULT_PAGE + 2) EE_SI_PAGE2 = 0x00000000;
ulp32_t chess_storage(ULP:EE_SI_DEFAULT_PAGE + 3) EE_SI_PAGE3 = 0x00000000;
ulp32_t chess_storage(ULP:EE_SI_DEFAULT_PAGE + 4) EE_SI_PAGE4 = 0x00000000;
ulp32_t chess_storage(ULP:EE_SI_DEFAULT_PAGE + 5) EE_SI_PAGE5 = 0x00000000;
ulp32_t chess_storage(ULP:EE_SI_DEFAULT_PAGE + 6) EE_SI_PAGE6 = 0x00000000;
ulp32_t chess_storage(ULP:EE_SI_DEFAULT_PAGE + 7) EE_SI_PAGE7 = 0x00000000;


//***** User Defeine**************************************************************
// Config page for LF tune at immo boot (value written to LFTUNEVDD register)
ulp32_t chess_storage(ULP:EEPAGE_LFTUNE_IMMO)           EE_LFTUNEVDD        = 0x00000000U; // 0x00007123U; 
//TOMD CONFIG
ulp32_t chess_storage(ULP:EEPAGE_TRANSPONDER_CONFIG)    EE_DCFG             = LFTEN; 
//SET DCFG A, page 3D0h 
ulp32_t chess_storage(ULP:EEPAGE_FLD_CONFIG)            LF_HF_FLD_CONFIG    = 0x00000000U;
//enable the EROM write syscall, page 3D3h
ulp32_t chess_storage(ULP:EEPAGE_ENABLE_WRITE_EROM)     EE_CFG_ENERWR       = 0x00000000U; // 0x00000080;
//Initial Inner ant number 
ulp32_t chess_storage(ULP:EEPAGE_VINH_CONFIG)           VINH_CONFIG         = 0x00000000U;
ulp32_t chess_storage(ULP:EEPAGE_VINL_CONFIG)           VINL_CONFIG         = 0x00000000U;
ulp32_t chess_storage(ULP:EEPAGE_INNERANT_CONFIG)       ANT_CONFIG          = 0x0003FF00U;
ulp32_t chess_storage(ULP:EESYNC_PG)                    SYNC_CONFIG         = 0x12340000U; 
ulp32_t chess_storage(ULP:EEPAGE_SWVERSION)             SWVERSION_CONFIG    = 0x21000000U; 
ulp32_t chess_storage(ULP:EEPAGE_POLLING)               POLLING_CONFIG      = 0x56555655U; 
//Initial SK&CRC backup 





/*-----------------------------------------------------------------------------------------------*/

/* eof */