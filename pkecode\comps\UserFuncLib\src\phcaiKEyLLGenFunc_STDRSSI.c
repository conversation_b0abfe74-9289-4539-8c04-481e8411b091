/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_STDRSSI.c 28748 2020-09-21 16:18:07Z dep10330 $
  $Revision: 28748 $
*/

/*+----------------------------------------------------------------------------- */
/*| NOTE: The code provided herein is still under development and hence          */
/*|       subject to change.                                                     */
/*+----------------------------------------------------------------------------- */

/**
 * @file
 * Implementation of User Library Functions related to standard (wideband) RSSI measurements.
 */

/*
  Change Log:
  -----------
  2014-01-30 (MMr):
  - adapted, first functional draft
  2014-09-12 (MMr):
  - changed handling of ADC parameters: moved number of samples and spacing from
    _RSSI_ADCInit to each measurement function.
  2015-06-19 (MMr):
  - Changes in phcaiKEyLLGenFunc_RSSI_ADCInit:
    moved in code for RSSI start-up from application, added result code,
    moved constant D_RSSICON_STUP to header file.
  - Removed RSSI_HOLD = 0 in phcaiKEyLLGenFunc_RSSI_SelectRange.
  2015-12-08 (NBn):
  - Modified default ADCCON setting for RSSI (D_ADCCON_RSSI) such that
    REFSEL = 100bin as recommended in data sheet.
  2016-04-15 (MMr):
  - removed parameter u16_ADC_samplinginterval_us in functions
    phcaiKEyLLGenFunc_RSSI_MeasOffset and phcaiKEyLLGenFunc_RSSI_MeasChannel.
  2017-01-26 (MMr):
  - started port to TOKEN-PLUS/ACTIC5G-PLUS, only switches adapted. Register handling to be reviewed.
  - added parameter e_samplingtime in phcaiKEyLLGenFunc_RSSI_ADCInit
  - removed compile switches for syscalls not present in obsolete ROM codes (TOKEN before RC004).
  2017-03-02 (MMr):
  - use syscall phcaiKEyLLGenFunc_CS_ADC_poweron only on TOKEN platform
  - do not set ADCCON.POWERON to 1 for PLUS/SRX(v0) since hardware summation does not require it.
  2017-03-23 (NBn):
  - Changes in phcaiKEyLLGenFunc_RSSI_Convert:
    Range information masked with 0x07 to handle added information (bit 7) indication use of LFSHCON
  2017-11-24 (MMr):
  - Added compile switches for supporting Narrowband RSSI (PLATFORM_HAS_NBRSSI)
  2017-12-14 (MMr):
  - phcaiKEyLLGenFunc_RSSI_Convert: fixed conversion of 16-bit unsigned ADC values from NBRSSI
  2018-01-09 (NBn):
  - Module "phcaiKEyLLGenFunc_RSSI.c" split in standard RSSI (..._STDRSSI) and narrow band RSSI (..._NBRSSI) modules.
    Module "phcaiKEyLLGenFunc_RSSI.c" will be include wrapper functions choosing the required RSSI function (standard or narrow band).
 */

#include "phcaiKEyLLGenFunc.h"
#include "phcaiKEyLLGenFunc_CS.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @addtogroup STDRSSI
 * @{
 */

/** Error code from last RSSI operation. */
//phcaiKEyLLGenFunc_RssiError_t e_rssiError;

/** Range/gain selection last used. */
//phcaiKEyLLGenFunc_RssiRange_t e_rangeSelection;


/*-----------------------------------------------------------------------------------------------

 Registers
 ---------

 RSSI control register RSSICON (16 bit)
 |15|14|13|12|  11         | 10           9            8                | 7        | 6              5             |     4     | 3             2         1     | 0
 | 0| 0| 0| 0| RSSI_RANGE2 | RSSI_RANGE1  RSSI_RANGE0  RSSI_RANGEEXTDIS | RSSI_PON | RSSI_CHANSEL1  RSSI_CHANSEL0 | RSSI_HOLD | RSSI_OVF2 RSSI_OVF1 RSSI_OVF0 | RSSI_RST

 ADC control register ADCCON (16 bit)
 |  15  14  13  |  12  11  10 |   9  8  |   7     |   6       |     5      |    4     | 3  2  |    1      |     0     |
 |    INSEL     |    REFSEL   |  SAMTIM | TSENSEN | BATMEASEN | BG1V2BUFEN | BG1V2EN  |  RFU  | CONVRESET | CONVSTART |


  -----------------------------------------------------------------------------------------------*/

/*-----------------------------------------------------------------------------------------------*/
error_t phcaiKEyLLGenFunc_STDRSSI_ADCInit( bool_t b_EnableRangeExt, ADC_SAMTIM_t e_samplingtime )
/*-----------------------------------------------------------------------------------------------*/
{
  error_t res = ERROR;

  /* Dynamic reset of the 2 ms detection */
  PCON2.bits.R2MSDET = 1U;

  /*
    The measurement sequence starts with powering on the RSSI block.
    It is recommended to power on the RSSI block in the "AUTOZERO" configuration
    (RSSI_CHANSEL[1:0] = 00). If the RSSI block was already powered on in the "AUTOZERO" mode
    for the power-on time tRSSI,PON; the channel selection time tCHANSEL can be omitted
    for the following offset measurement.
    Hence for the offset measurement the required settings of RSSI_RANGE[2:0] and
    RSSI_CHANSEL[1:0] are done here AFTER power-on of the RSSI and ADC blocks.
    It is recommended to decide for one setting of RSSI_RANGEEXTDIS
    throughout the application (either ON or OFF).
  */

  /* Avoid VDDA brownout during power-on sequence of RSSI. Req. on TOKEN, ROMCODE_VERSION >= 2 */
  phcaiKEyLLGenFunc_CS_clear_VDDABRNOUT();

  /* 1. power on RSSI with RSSI_RANGEEXTDIS=1 and RSSI_PON=1. */
  /* This sets RSSI_RANGE and RSSI_CHANSEL as needed for the offset measurement. */
  RSSICON.val = D_RSSICON_STUP;
  /* Wait t_RSSI_PON_us */
  phcaiKEyLLGenFunc_timer0_delay_us( t_RSSI_PON_us );

  /* 2. set ADC source selections for RSSI and power on band gap & BG buffer */
  ADCCON.val = D_ADCCON_RSSI;
  /* set the sampling time as desired by application. */
  phcaiKEyLLGenFunc_ADC_SetSamplingTime( e_samplingtime );

  timer0_delay_us( t_BG_PON_us );        /* Wait tBG,PON  */

  #ifdef PLATFORM_TOKEN
  /*
    Switch on the ADC power continuously until sequence is completed,
    to reduce ADC operation time by 8 ADC clock cycles per conversion.
  */
  phcaiKEyLLGenFunc_CS_ADC_poweron();
  #endif

  if ( b_EnableRangeExt != FALSE )
  {
    /* 3. power on RSSI extended range. */
    /* Note: RSSICON[15:12] (W0) are read-write, bitfield write maintains default value '0'. */
    RSSICON.bits.RSSI_RANGEEXTDIS = 0U;
  }
  /* Delay after possible change of RSSI_RANGEEXTDIS. */
  phcaiKEyLLGenFunc_timer0_delay_us( 2U * t_RANGESEL_us );

  /* Restore VDDA brown-out trimming. Required on TOKEN, ROMCODE_VERSION >= 2 */
  phcaiKEyLLGenFunc_CS_restore_VDDABRNOUT();

  if ( PCON1.bits.VDDABRNFLAG == 0U )
  {
    /* Clear VDDABRNREG. */
    PCON2.bits.VDDABRNREG = 0U;
    res = SUCCESS;
  }

  /* Dynamic reset of the 2 ms detection */
  PCON2.bits.R2MSDET = 1U;

  return res;
}

/*-----------------------------------------------------------------------------------------------*/
void phcaiKEyLLGenFunc_STDRSSI_ADCStop( void )
/*-----------------------------------------------------------------------------------------------*/
{
  #ifdef PLATFORM_TOKEN
  /* Switch off the continuous ADC power. */
  phcaiKEyLLGenFunc_CS_ADC_poweroff();
  #endif

  /* Power-off RSSI. */
  RSSICON.val = 0x00u;
  /* Power-off ADC bandgap and buffer. */
  ADCCON.val  = 0x00u;
}

/*-----------------------------------------------------------------------------------------------*/
uint16_t phcaiKEyLLGenFunc_STDRSSI_MeasOffset( uint16_t u16_num_ADC_samples, uint16_t u16_NumFractionalBits  )
/*-----------------------------------------------------------------------------------------------*/
{
  uint16_t u16_adcres;

  /*
   It is recommended to power on the RSSI-block in the "AUTOZERO" configuration
   (RSSI_CHANSEL[1:0] = 00). If the RSSI block was already powered on in the "AUTOZERO" mode
   for the power-on time tRSSI,PON; the channel selection time tCHANSEL can be omitted
   for the first measurement (in general the offset measurement).
   Hence the required RSSI_CHANSEL[1:0] and RSSI_RANGE[2:0] settings are done during
   initialisation in "phcaiKEyLLGenFunc_RSSI_ADCInit(...)" before power-on of the RSSI and ADC blocks.

   The range overflow detection procedure is not required.

   RSSI_RANGE[2:0] had already been set to 0dB during initialisation in
   "phcaiKEyLLGenFunc_RSSI_ADCInit()" before power-on of the RSSI and ADC blocks.
   Nevertheless the peak-detector reset is still required.
  */
  /* Dynamic reset of the 2 ms detection */
  PCON2.bits.R2MSDET = 1U;
  /* Both the amplifier gain and the attenuator have to be set to 0 dB. */
  RSSICON.bits.RSSI_RANGE = (uint8_t)RSSI_RANGE_0dB;
  /* "AUTOZERO" configuration; i.e. channel selection multiplexer input set to GND. */
  RSSICON.bits.RSSI_CHANSEL = (bitfield_t) AUTOZERO;
  RSSICON.bits.RSSI_RST     = (bitfield_t) 1U;
  /* reset time tRESPR min. 2us */
  phcaiKEyLLGenFunc_timer0_delay_us( t_RESPR_us );
  RSSICON.bits.RSSI_RST     = (bitfield_t) 0U;

  /* After releasing the reset again (RSSI_RST = 0) and after considering
     the peak-detector settling time tPEAKDSET the AD conversion can be started. */
  phcaiKEyLLGenFunc_timer0_delay_us( t_PEAKDSET_us );

  u16_adcres = phcaiKEyLLGenFunc_ADC_run_averaged_fractBits( u16_num_ADC_samples, u16_NumFractionalBits );

  /* Dynamic reset of the 2 ms detection */
  PCON2.bits.R2MSDET = 1U;

  return u16_adcres;
}

/*-----------------------------------------------------------------------------------------------*/
uint16_t phcaiKEyLLGenFunc_STDRSSI_MeasChannel( uint16_t u16_num_ADC_samples, uint16_t u16_NumFractionalBits  )
/*-----------------------------------------------------------------------------------------------*/
{
  uint16_t u16_adcres;
  //e_rssiError = RSSI_OK;

  // Code to measure call duration on scope
  //P1DIR.bits.P16D = 1; // output
  //P1OUT.bits.P16O = 1;

  u16_adcres = phcaiKEyLLGenFunc_ADC_run_averaged_fractBits( u16_num_ADC_samples, u16_NumFractionalBits );

  //P1OUT.bits.P16O = 0;

  /* Dynamic reset of the 2 ms detection */
  PCON2.bits.R2MSDET = 1U;

  return u16_adcres;
}


/*-----------------------------------------------------------------------------------------------*/
phcaiKEyLLGenFunc_RssiRange_t
phcaiKEyLLGenFunc_STDRSSI_SelectRange( phcaiKEyLLGenFunc_RssiChannel_t e_channel,
                                       phcaiKEyLLGenFunc_RssiRange_t   e_range,
                                       bool_t                          b_autoRangeInd )
/*-----------------------------------------------------------------------------------------------*/
{
  phcaiKEyLLGenFunc_RssiRange_t   e_range_setting;

  /* Dynamic reset of the 2 ms detection */
  PCON2.bits.R2MSDET = 1U;

  /*
   A single channel RSSI measurement starts with setting the gain to
   54 dB (RSSI_RANGE[2:0] = 101) when RSSI_RANGEEXTDIS == 0 or
   36 dB (RSSI_RANGE[2:0] = 100) when RSSI_RANGEEXTDIS == 1 and
   with selecting the channel to be measured (RSSI_CHANSEL[1:0]).
   Together with the channel selection the peak-detectors have to be reset (RSSI_RST = 1).
   The reset-state has to be kept for the minimum channel selection time (tCHANSEL).
   It is recommended to additionally set RSSI_RST when changing RSSI_RANGE[2:0].
  */
  RSSICON.bits.RSSI_RST = 1U;
  if ( (uint8_t) RSSICON.bits.RSSI_RANGEEXTDIS == (uint8_t) RSSI_RANGEEXT_EN )
  {
    RSSICON.bits.RSSI_RANGE = (uint8_t) RSSI_RANGE_54dB;
  }
  else
  {
    RSSICON.bits.RSSI_RANGE = (uint8_t) RSSI_RANGE_36dB;
  }
  RSSICON.bits.RSSI_CHANSEL = (uint8_t) (0x03U & (uint8_t)e_channel);
  phcaiKEyLLGenFunc_timer0_delay_us( t_CHANSEL_us );
  RSSICON.bits.RSSI_RST = 0U;

  if ( b_autoRangeInd == TRUE )
  {
    /* After releasing the peak-detector reset (RSSI_RST = 0) and  */
    /* waiting for the settling time tIND,  */
    /* the range overflow bits (RSSI_OVF[2:0]) can be evaluated.  */
    phcaiKEyLLGenFunc_timer0_delay_us( t_IND_us );
    /* Note: in TOKEN, read access to these bits is synchronized. */
    e_range = (phcaiKEyLLGenFunc_RssiRange_t) RSSICON.bits.RSSI_OVF;
    if ( e_range == RSSI_RANGE_LIM )
    {
      /* Note: RSSI_RANGE=0 also selects the -18 dB range (applies to all TOKEN family platforms),
      but we keep this for compatibility. */
      e_range = RSSI_RANGE_m18dB;
    }
  }

  /* The gain-chain has to be set accordingly (RSSI_RANGE[2:0]),
    RSSI_RANGEEXTDIS needs to maintain its value.
    Due to the range change, the range selection settling time tRANGESEL has to be considered;
    the peak-detector has to be reset (RSSI_RST = 1) and
    RSSI_HOLD has to be set to 0 for the minimum reset time tRESPR. */
  if ( (e_range == RSSI_RANGE_54dB) && ((uint8_t)RSSICON.bits.RSSI_RANGEEXTDIS == (uint8_t)RSSI_RANGEEXT_DIS) )
  {
    // +54 dB gain is not possible if RSSI_RANGEEXTDIS == RSSI_RANGEEXT_DIS.
    e_range = RSSI_RANGE_36dB;
  }
  // Use the detected as gain setting RSSI_RANGE.
  RSSICON.bits.RSSI_RANGE = (uint8_t) (0x07U & (uint8_t)e_range);

  phcaiKEyLLGenFunc_timer0_delay_us( t_RANGESEL_us );

  RSSICON.bits.RSSI_RST = 1U;
  /*Reset time tRESPR max. 2us */
  phcaiKEyLLGenFunc_timer0_delay_us( t_RESPR_us );
  RSSICON.bits.RSSI_RST = 0U;

  /* After releasing the reset again (RSSI_RST = 0) and after considering
     the peak-detector settling time tPEAKDSET the AD conversion can be started. */
  phcaiKEyLLGenFunc_timer0_delay_us( t_PEAKDSET_us );

  /* Dynamic reset of the 2 ms detection */
  PCON2.bits.R2MSDET = 1U;

  return e_range;
}


/* @} */
/* @} */
