
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:19 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-70e876 mrk3

[
    0 : error_t_tx_enable_PLL_bool_t typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : b_OnOff typ=int8_ val=0t0 bnd=a sz=1 algn=1 stl=DM tref=bool_t_DM
   21 : TXPCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_TXPCON_t_DM9
   22 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   23 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   24 : __extDM_SFR_TXPCON_t typ=int8_ bnd=b stl=DM
   25 : __extPM_void typ=uint16_ bnd=b stl=PM
   26 : __extDM_void typ=int8_ bnd=b stl=DM
   27 : __extULP_void typ=uint32_ bnd=b stl=ULP
   30 : __ptr_TXPCON typ=int16_ val=0a bnd=m adro=21
   31 : __rt typ=int8_ bnd=p tref=error_t__
   32 : __arg_b_OnOff typ=int8_ bnd=p tref=bool_t__
   37 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   46 : __ct_1 typ=uint8_ val=1f bnd=m
   48 : __ct_0 typ=uint8_ val=0f bnd=m
   50 : __ct_238 typ=int8_ val=-18f bnd=m
   52 : error_t_phcaiKEyLLGenFunc_CS_UHF_PllStartUp_bool_t_bool_t___uchar typ=int16_ val=0r bnd=m
   54 : __tmp typ=int8_ bnd=m
   71 : __ct_0 typ=uint8_ val=0f bnd=m
   73 : __ct_2s0 typ=int16_ val=2s0 bnd=m
   91 : __ct_2s0 typ=int16_ val=2s0 bnd=m
  122 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  131 : __either typ=bool bnd=m
  132 : __trgt typ=rel8_ val=5j bnd=m
  133 : __trgt typ=rel8_ val=7j bnd=m
  134 : __seff typ=any bnd=m
  135 : __seff typ=any bnd=m
  136 : __seff typ=any bnd=m
  137 : __seff typ=any bnd=m
  138 : __seff typ=any bnd=m
  139 : __seff typ=any bnd=m
  140 : __seff typ=any bnd=m
  141 : __seff typ=any bnd=m
  142 : __seff typ=any bnd=m
]
Ferror_t_tx_enable_PLL_bool_t {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (b_OnOff.19 var=20) source ()  <30>;
    (TXPCON.20 var=21) source ()  <31>;
    (__extDM_int8_.21 var=22) source ()  <32>;
    (__extDM_int16_.22 var=23) source ()  <33>;
    (__extDM_SFR_TXPCON_t.23 var=24) source ()  <34>;
    (__extPM_void.24 var=25) source ()  <35>;
    (__extDM_void.25 var=26) source ()  <36>;
    (__extULP_void.26 var=27) source ()  <37>;
    (__arg_b_OnOff.31 var=32 stl=RbL off=0) inp ()  <42>;
    (__ct_0t0.219 var=37) const_inp ()  <272>;
    (__ct_2s0.222 var=91) const_inp ()  <275>;
    <66> {
      (__sp.39 var=19 __seff.242 var=140 stl=c_flag_w __seff.243 var=141 stl=nz_flag_w __seff.244 var=142 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.222 __sp.18 __sp.18)  <297>;
      (__seff.253 var=141 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.243)  <347>;
      (__seff.260 var=140 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.242)  <350>;
      (__seff.261 var=142 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.244)  <351>;
    } stp=0;
    <67> {
      (b_OnOff.47 var=20) _pl_rd_res_reg_const_store_1_B1 (__arg_b_OnOff.256 __ct_0t0.219 b_OnOff.19 __sp.39)  <298>;
      (__arg_b_OnOff.256 var=32 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__arg_b_OnOff.31)  <349>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <58>;
    } #4 off=3 nxt=42
    #42 off=3 nxt=44 tgt=9
    (__trgt.223 var=132) const_inp ()  <276>;
    <64> {
      (__apl_nz.207 var=122 stl=nz_flag_w __seff.239 var=138 stl=c_flag_w __seff.240 var=139 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B2 (__ct_0t0.219 b_OnOff.47 __sp.39)  <295>;
      (__apl_nz.252 var=122 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.207)  <346>;
      (__seff.262 var=138 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.239)  <352>;
      (__seff.263 var=139 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.240)  <353>;
    } stp=0;
    <65> {
      () cc_eq__jump_const_1_B1 (__apl_nz.251 __trgt.223)  <296>;
      (__apl_nz.251 var=122 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.252)  <345>;
    } stp=2;
    if {
        {
            () if_expr (__either.216)  <84>;
            (__either.216 var=131) undefined ()  <268>;
        } #7
        {
            #9 off=10 nxt=10
            (error_t_phcaiKEyLLGenFunc_CS_UHF_PllStartUp_bool_t_bool_t___uchar.220 var=52) const_inp ()  <273>;
            <63> {
              () call_const_1_B1 (error_t_phcaiKEyLLGenFunc_CS_UHF_PllStartUp_bool_t_bool_t___uchar.220)  <294>;
            } stp=4;
            <80> {
              (__ct_1.267 var=46 stl=a_b0) const_2_B2 ()  <329>;
              (__ct_1.266 var=46 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_1.267)  <356>;
            } stp=0;
            <81> {
              (__ct_0.269 var=48 stl=a_b0) const_1_B2 ()  <331>;
              (__ct_0.268 var=48 stl=RbH off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B3 (__ct_0.269)  <357>;
            } stp=1;
            <82> {
              (__ct_238.271 var=50 stl=__CTa_b0_int8__cstP24_E1) const_3_B1 ()  <333>;
              (__ct_238.270 var=50 stl=RbL off=1) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_1_int8__B0 (__ct_238.271)  <358>;
            } stp=2;
            call {
                (__tmp.84 var=54 stl=RbL off=0 TXPCON.87 var=21 __extDM.88 var=17 __extDM_SFR_TXPCON_t.89 var=24 __extDM_int16_.90 var=23 __extDM_int8_.91 var=22 __extDM_void.92 var=26 __extPM.93 var=16 __extPM_void.94 var=25 __extULP.95 var=18 __extULP_void.96 var=27 __vola.97 var=13) Ferror_t_phcaiKEyLLGenFunc_CS_UHF_PllStartUp_bool_t_bool_t___uchar (__ct_1.266 __ct_0.268 __ct_238.270 TXPCON.20 __extDM.16 __extDM_SFR_TXPCON_t.23 __extDM_int16_.22 __extDM_int8_.21 __extDM_void.25 __extPM.15 __extPM_void.24 __extULP.17 __extULP_void.26 __vola.12)  <97>;
            } #10 off=16 nxt=45
            #45 off=16 nxt=21
        } #8
        {
            (__ptr_TXPCON.218 var=30) const_inp ()  <271>;
            (__trgt.224 var=133) const_inp ()  <277>;
            <58> {
              (TXPCON.114 var=21 __vola.115 var=13 __seff.234 var=137 stl=nz_flag_w) load_const__ad_const_store_1_B1 (__ptr_TXPCON.218 TXPCON.20 __vola.12)  <289>;
              (__seff.273 var=137 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.234)  <359>;
            } stp=0;
            <59> {
              () jump_const_1_B1 (__trgt.224)  <290>;
            } stp=3;
            <85> {
              (__ct_0.275 var=71 stl=a_b0) const_1_B2 ()  <338>;
              (__ct_0.274 var=71 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_0.275)  <360>;
            } stp=2;
        } #44 off=6 tgt=21
        {
            (__vola.121 var=13) merge (__vola.97 __vola.115)  <123>;
            (__extPM.122 var=16) merge (__extPM.93 __extPM.15)  <124>;
            (__extDM.123 var=17) merge (__extDM.88 __extDM.16)  <125>;
            (__extULP.124 var=18) merge (__extULP.95 __extULP.17)  <126>;
            (TXPCON.125 var=21) merge (TXPCON.87 TXPCON.114)  <127>;
            (__extDM_int8_.126 var=22) merge (__extDM_int8_.91 __extDM_int8_.21)  <128>;
            (__extDM_int16_.127 var=23) merge (__extDM_int16_.90 __extDM_int16_.22)  <129>;
            (__extDM_SFR_TXPCON_t.128 var=24) merge (__extDM_SFR_TXPCON_t.89 __extDM_SFR_TXPCON_t.23)  <130>;
            (__extPM_void.129 var=25) merge (__extPM_void.94 __extPM_void.24)  <131>;
            (__extDM_void.130 var=26) merge (__extDM_void.92 __extDM_void.25)  <132>;
            (__extULP_void.131 var=27) merge (__extULP_void.96 __extULP_void.26)  <133>;
            (__rt.272 var=31 stl=RbL off=0) merge (__tmp.84 __ct_0.274)  <334>;
        } #19
    } #6
    #21 off=16 nxt=-2
    () out (__rt.272)  <143>;
    () sink (__vola.121)  <144>;
    () sink (__extPM.122)  <147>;
    () sink (__extDM.123)  <148>;
    () sink (__extULP.124)  <149>;
    () sink (__sp.139)  <150>;
    () sink (b_OnOff.47)  <151>;
    () sink (TXPCON.125)  <152>;
    () sink (__extDM_int8_.126)  <153>;
    () sink (__extDM_int16_.127)  <154>;
    () sink (__extDM_SFR_TXPCON_t.128)  <155>;
    () sink (__extPM_void.129)  <156>;
    () sink (__extDM_void.130)  <157>;
    () sink (__extULP_void.131)  <158>;
    (__ct_2s0.221 var=73) const_inp ()  <274>;
    <55> {
      (__sp.139 var=19 __seff.228 var=134 stl=c_flag_w __seff.229 var=135 stl=nz_flag_w __seff.230 var=136 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.221 __sp.39 __sp.39)  <286>;
      (__seff.254 var=135 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.229)  <348>;
      (__seff.264 var=134 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.228)  <354>;
      (__seff.265 var=136 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.230)  <355>;
    } stp=0;
    <56> {
      () ret_1_B1 ()  <287>;
    } stp=1;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,117:0,0);
3 : (0,117:23,0);
4 : (0,117:23,0);
6 : (0,119:2,2);
8 : (0,132:4,3);
9 : (0,127:52,3);
10 : (0,127:11,3);
21 : (0,119:2,14);
42 : (0,119:12,2);
----------
58 : (0,117:23,0);
84 : (0,119:2,2);
97 : (0,127:11,3);
123 : (0,119:2,13);
124 : (0,119:2,13);
125 : (0,119:2,13);
126 : (0,119:2,13);
127 : (0,119:2,13);
128 : (0,119:2,13);
129 : (0,119:2,13);
130 : (0,119:2,13);
131 : (0,119:2,13);
132 : (0,119:2,13);
133 : (0,119:2,13);
286 : (0,119:2,0) (0,119:2,14);
287 : (0,119:2,14);
289 : (0,131:15,8);
294 : (0,127:11,3);
295 : (0,119:15,2) (0,117:30,0) (0,119:12,2);
296 : (0,119:12,2) (0,119:2,2);
297 : (0,117:8,0);
298 : (0,117:30,0) (0,117:23,0);
329 : (0,127:48,0);
331 : (0,127:54,0);
333 : (0,127:52,0);
338 : (0,132:11,0);

