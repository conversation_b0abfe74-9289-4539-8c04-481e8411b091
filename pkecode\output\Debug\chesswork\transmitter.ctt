
// File generated by noodle version P-2019.09#78e58cd307#210222, Sat Mar  2 20:51:11 2024
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/transmitter.c mrk3

#const float_tininess_after_rounding	enum __anonymous0__transmitter_ 0 (0x0)
#const float_tininess_before_rounding	enum __anonymous0__transmitter_ 1 (0x1)
#const float_round_nearest_even	enum __anonymous1__transmitter_ 0 (0x0)
#const float_round_to_zero	enum __anonymous1__transmitter_ 1 (0x1)
#const float_round_up	enum __anonymous1__transmitter_ 2 (0x2)
#const float_round_down	enum __anonymous1__transmitter_ 3 (0x3)
#const SUCCESS	enum __anonymous2__transmitter_ 0 (0x0)
#const ERROR	enum __anonymous2__transmitter_ 1 (0x1)
#const FALSE	enum __anonymous3__transmitter_ 0 (0x0)
#const TRUE	enum __anonymous3__transmitter_ 1 (0x1)
#const hardware_version	enum __anonymous4__transmitter_ 0 (0x0)
#const product_version	enum __anonymous4__transmitter_ 1 (0x1)
#const mrk3_cpu_id	enum __anonymous4__transmitter_ 2 (0x2)
#const system_rom_version	enum __anonymous4__transmitter_ 4096 (0x1000)
#const boot_loader_version	enum __anonymous4__transmitter_ 4097 (0x1001)
#const mdi_version	enum __anonymous4__transmitter_ 4098 (0x1002)
#const hal_version	enum __anonymous4__transmitter_ 4099 (0x1003)
#const gen_func_lib_version	enum __anonymous4__transmitter_ 4100 (0x1004)
#const version_version	enum __anonymous4__transmitter_ 4101 (0x1005)
#const hitag_cmn_version	enum __anonymous4__transmitter_ 4102 (0x1006)
#const hitag2_version	enum __anonymous4__transmitter_ 4103 (0x1007)
#const hitag2_ext_version	enum __anonymous4__transmitter_ 4104 (0x1008)
#const hitag_pro_version	enum __anonymous4__transmitter_ 4105 (0x1009)
#const hitag3_version	enum __anonymous4__transmitter_ 4106 (0x100a)
#const iso14443_3_version	enum __anonymous4__transmitter_ 4107 (0x100b)
#const hitag_aes_version	enum __anonymous4__transmitter_ 4108 (0x100c)
#const custom_mod_version	enum __anonymous4__transmitter_ 4112 (0x1010)
#const custom_immo_version	enum __anonymous4__transmitter_ 4115 (0x1013)
#const ide_0	enum __anonymous4__transmitter_ 8192 (0x2000)
#const ide_1	enum __anonymous4__transmitter_ 8193 (0x2001)
#const ide_2	enum __anonymous4__transmitter_ 8194 (0x2002)
#const ide_3	enum __anonymous4__transmitter_ 8195 (0x2003)
#const unused_version_id	enum __anonymous4__transmitter_ 61455 (0xf00f)
#const EE_RAM_AUT	enum phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode 0 (0x0)
#const EE_RAM_STR	enum phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode 16 (0x10)
#const IMMO_HT2	enum phcaiKEyLLGenFunc_SCI_Immo_Type 0 (0x0)
#const IMMO_HT2E	enum phcaiKEyLLGenFunc_SCI_Immo_Type 1 (0x1)
#const IMMO_HT3	enum phcaiKEyLLGenFunc_SCI_Immo_Type 2 (0x2)
#const IMMO_HTAES	enum phcaiKEyLLGenFunc_SCI_Immo_Type 3 (0x3)
#const IMMO_HTPRO	enum phcaiKEyLLGenFunc_SCI_Immo_Type 4 (0x4)
#const IMMO_UNDEF	enum phcaiKEyLLGenFunc_SCI_Immo_Type 5 (0x5)
#const EE_WR_OK	enum eeprom_write_error 0 (0x0)
#const EE_WR_BRN_ERR	enum eeprom_write_error 1 (0x1)
#const EE_WR_HV_ERR	enum eeprom_write_error 2 (0x2)
#const EE_WR_ADDR_ERR	enum eeprom_write_error 4 (0x4)
#const EE_WR_LEN_ZERO	enum eeprom_write_error 8 (0x8)
#const EE_WR_INVALID_START_ADDR	enum eeprom_write_error 24 (0x18)
#const EE_WR_INVALID_END_ADDR	enum eeprom_write_error 40 (0x28)
#const EE_WR_UNALIGNED_ERR	enum eeprom_write_error 56 (0x38)
#const EE_WR_FORBIDDEN_ERR	enum eeprom_write_error 64 (0x40)
#const EE_WR_INVALID_LEN_ERR	enum eeprom_write_error 72 (0x48)
#const SI_SUCCESS	enum SI_RESULT 0 (0x0)
#const SI_NOREPAIR	enum SI_RESULT 1 (0x1)
#const SI_PRGFAIL	enum SI_RESULT 2 (0x2)
#const SI_OVERFLOW	enum SI_RESULT 3 (0x3)
#const SI_INVPARAM	enum SI_RESULT 4 (0x4)
#const KEYLL_FUNC_CODE_AES_LOAD_KEY	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 0 (0x0)
#const KEYLL_FUNC_CODE_AES_LOAD_DATA	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 1 (0x1)
#const KEYLL_FUNC_CODE_AES_INIT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 2 (0x2)
#const KEYLL_FUNC_CODE_AES_ENCRYPT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 3 (0x3)
#const KEYLL_FUNC_CODE_SI_INIT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 4 (0x4)
#const KEYLL_FUNC_CODE_SI_GET	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 5 (0x5)
#const KEYLL_FUNC_CODE_SI_INC	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 6 (0x6)
#const KEYLL_FUNC_CODE_MDI_PRINT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 7 (0x7)
#const KEYLL_FUNC_CODE_MDI_PRINTLN	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 8 (0x8)
#const KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 9 (0x9)
#const KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE_LE	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 10 (0xa)
#const KEYLL_FUNC_CODE_ULPEE_READ_PAGE	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 11 (0xb)
#const KEYLL_FUNC_CODE_ULPEE_READ_PAGE_LE	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 12 (0xc)
#const KEYLL_FUNC_CODE_ULPEE_READ_BYTES	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 13 (0xd)
#const KEYLL_FUNC_CODE_SET_CLKCON	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 14 (0xe)
#const KEYLL_FUNC_CODE_SET_VBATRGL	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 15 (0xf)
#const KEYLL_FUNC_CODE_RESET_VBAT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 16 (0x10)
#const KEYLL_FUNC_CODE_SET_BATPOR_FLAG	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 17 (0x11)
#const KEYLL_FUNC_CODE_IMMO_INIT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 18 (0x12)
#const KEYLL_FUNC_CODE_IMMO_RESET	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 19 (0x13)
#const KEYLL_FUNC_CODE_IMMO_RECEIVE_CMD	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 20 (0x14)
#const KEYLL_FUNC_CODE_IMMO_EXECUTE_CMD	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 21 (0x15)
#const KEYLL_FUNC_CODE_EEPROM_EN	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 22 (0x16)
#const KEYLL_FUNC_CODE_EEPROM_DIS	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 23 (0x17)
#const KEYLL_FUNC_CODE_EEPROM_PROG	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 24 (0x18)
#const KEYLL_FUNC_CODE_GET_VERSION	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 25 (0x19)
#const SMART2_FUNC_CODE_XO_START_UP	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 26 (0x1a)
#const SMART2_FUNC_CODE_PLL_START_UP	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 27 (0x1b)
#const KEYLL_FUNC_CODE_ULPEE_SET_PROGEN	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 28 (0x1c)
#const KEYLL_FUNC_CODE_SET_LFCLKX2DIS	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 29 (0x1d)
#const KEYLL_FUNC_CODE_SET_MDICLKSEL	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 30 (0x1e)
#const SMART2_FUNC_CODE_SET_RFGATE	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 31 (0x1f)
#const SMART2_FUNC_CODE_SET_PAINGATE	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 32 (0x20)
#const SMART2_FUNC_CODE_SET_CP_ICP	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 33 (0x21)
#const SMART2_FUNC_CODE_SET_PTRIM_XO	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 34 (0x22)
#const SMART2_FUNC_CODE_SET_PTRIM_PLL	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 35 (0x23)
#const SMART2_FUNC_CODE_SET_PTRIM_HS	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 36 (0x24)
#const KEYLL_FUNC_CODE_SI_INIT_EXT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 37 (0x25)
#const KEYLL_FUNC_CODE_SI_INC_EXT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 38 (0x26)
#const KEYLL_FUNC_CODE_EROM_READ	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 39 (0x27)
#const KEYLL_FUNC_CODE_EROM_WRITE	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 40 (0x28)
#const SMART2_FUNC_CODE_SET_PA_BITFIELD	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 41 (0x29)
#const SMART2_FUNC_CODE_GET_PA_BITFIELD	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 42 (0x2a)
#const UHF_FUNC_CODE_LOAD_CP_ICP_TRIM	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 43 (0x2b)
#const KEYLL_FUNC_CODE_AES_DISABLE	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 44 (0x2c)
#const ADC_ENABLE_EXT_INPUT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 45 (0x2d)
#const ADC_ALLOW_POWERONOFF	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 46 (0x2e)
#const KEYLL_FUNC_CODE_WRITE_ULPEE_3D4	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 47 (0x2f)
#const KEYLL_FUNC_CODE_CLEAR_VDDABRNTRIM	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 48 (0x30)
#const KEYLL_FUNC_CODE_IIU_RECEIVE	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 49 (0x31)
#const KEYLL_FUNC_CODE_IIU_SEND	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 50 (0x32)
#const KEYLL_FUNC_CODE_IIU_TIMX_SFR_SET	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 51 (0x33)
#const KEYLL_FUNC_CODE_SI_INIT_EXT_NOWAIT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 52 (0x34)
#const KEYLL_FUNC_CODE_SI_INC_EXT_NOWAIT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 53 (0x35)
#const KEYLL_FUNC_CODE_UHF_FORCE_XO_READY	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 54 (0x36)
#const KEYLL_FUNC_CODE_UHF_FORCE_PLL_LOCK_DETECTED	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 55 (0x37)
#const KEYLL_FUNC_CODE_IIU_CAT_INIT	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 56 (0x38)
#const KEYLL_FUNC_CODE_IIU_CAT_SEND	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 57 (0x39)
#const KEYLL_FUNC_CODE_IIU_CAT_SEND_MUTED	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 58 (0x3a)
#const KEYLL_FUNC_CODE_EROM_ENABLE_PF2	enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code 59 (0x3b)
#const ADC_INPUTSEL_VBAT	enum __anonymous229__transmitter_ 0 (0x0)
#const ADC_INPUTSEL_EXTERNAL	enum __anonymous229__transmitter_ 1 (0x1)
#const ADC_INPUTSEL_RFU2	enum __anonymous229__transmitter_ 2 (0x2)
#const ADC_INPUTSEL_TEMPSENS	enum __anonymous229__transmitter_ 3 (0x3)
#const ADC_INPUTSEL_RSSI	enum __anonymous229__transmitter_ 4 (0x4)
#const ADC_INPUTSEL_RFU5	enum __anonymous229__transmitter_ 5 (0x5)
#const ADC_INPUTSEL_RFU6	enum __anonymous229__transmitter_ 6 (0x6)
#const ADC_INPUTSEL_RFU7	enum __anonymous229__transmitter_ 7 (0x7)
#const ADC_REFSEL_BANDGAP	enum __anonymous230__transmitter_ 0 (0x0)
#const ADC_REFSEL_VBAT_VSS	enum __anonymous230__transmitter_ 1 (0x1)
#const ADC_REFSEL_EXTERNAL	enum __anonymous230__transmitter_ 2 (0x2)
#const ADC_REFSEL_TEMPSENS	enum __anonymous230__transmitter_ 3 (0x3)
#const ADC_REFSEL_BANDGAPRSSI	enum __anonymous230__transmitter_ 4 (0x4)
#const ADC_REFSEL_RFU5	enum __anonymous230__transmitter_ 5 (0x5)
#const ADC_REFSEL_RFU6	enum __anonymous230__transmitter_ 6 (0x6)
#const ADC_REFSEL_RFU7	enum __anonymous230__transmitter_ 7 (0x7)
#const ADC_SAMPTIME_01US	enum __anonymous231__transmitter_ 0 (0x0)
#const ADC_SAMPTIME_08US	enum __anonymous231__transmitter_ 1 (0x1)
#const ADC_SAMPTIME_16US	enum __anonymous231__transmitter_ 2 (0x2)
#const ADC_SAMPTIME_64US	enum __anonymous231__transmitter_ 3 (0x3)
#const AUTOZERO	enum __anonymous232__transmitter_ 0 (0x0)
#const CH1	enum __anonymous232__transmitter_ 1 (0x1)
#const CH2	enum __anonymous232__transmitter_ 2 (0x2)
#const CH3	enum __anonymous232__transmitter_ 3 (0x3)
#const CH_DEFAULT	enum __anonymous232__transmitter_ 4 (0x4)
#const CHSUM12	enum __anonymous232__transmitter_ 8 (0x8)
#const CHSUM13	enum __anonymous232__transmitter_ 9 (0x9)
#const CHSUM23	enum __anonymous232__transmitter_ 10 (0xa)
#const CHSUM123	enum __anonymous232__transmitter_ 11 (0xb)
#const CH_INVALID	enum __anonymous232__transmitter_ 255 (0xff)
#const RSSI_RANGE_54dB	enum __anonymous233__transmitter_ 5 (0x5)
#const RSSI_RANGE_36dB	enum __anonymous233__transmitter_ 4 (0x4)
#const RSSI_RANGE_18dB	enum __anonymous233__transmitter_ 3 (0x3)
#const RSSI_RANGE_0dB	enum __anonymous233__transmitter_ 2 (0x2)
#const RSSI_RANGE_m18dB	enum __anonymous233__transmitter_ 1 (0x1)
#const RSSI_RANGE_LIM	enum __anonymous233__transmitter_ 0 (0x0)
#const RSSI_RANGEEXT_DIS	enum __anonymous234__transmitter_ 1 (0x1)
#const RSSI_RANGEEXT_EN	enum __anonymous234__transmitter_ 0 (0x0)
#const NBRSSI_CH_AUTO	enum __anonymous235__transmitter_ 0 (0x0)
#const NBRSSI_CH1	enum __anonymous235__transmitter_ 1 (0x1)
#const NBRSSI_CH2	enum __anonymous235__transmitter_ 2 (0x2)
#const NBRSSI_CH3	enum __anonymous235__transmitter_ 4 (0x4)
#const NBRSSI_CH_INVALID	enum __anonymous235__transmitter_ 8 (0x8)
#const NBRSSI_AVG	enum __anonymous236__transmitter_ 0 (0x0)
#const NBRSSI_QMEAN	enum __anonymous236__transmitter_ 1 (0x1)
#const NBRSSI_MIN	enum __anonymous236__transmitter_ 2 (0x2)
#const NBRSSI_MAX	enum __anonymous236__transmitter_ 3 (0x3)
#const NBRSSI_RAW	enum __anonymous236__transmitter_ 4 (0x4)
#const NBRSSI_FREQ_ERR	enum __anonymous236__transmitter_ 5 (0x5)
#const NBRSSI_FILT_CFG_A	enum __anonymous237__transmitter_ 0 (0x0)
#const NBRSSI_FILT_CFG_B	enum __anonymous237__transmitter_ 4 (0x4)
#const NBRSSI_FILT_CFG_C	enum __anonymous237__transmitter_ 3 (0x3)
#const NBRSSI_FILT_CFG_D	enum __anonymous237__transmitter_ 7 (0x7)
#const NBRSSI_NRE_CFG_NONE	enum __anonymous238__transmitter_ 0 (0x0)
#const NBRSSI_NRE_CFG_m18dB	enum __anonymous238__transmitter_ 1 (0x1)
#const NBRSSI_NRE_CFG_m18dB_0dB	enum __anonymous238__transmitter_ 2 (0x2)
#const NBRSSI_OK	enum __anonymous239__transmitter_ 0 (0x0)
#const NBRSSI_ERROR_SDADC_DIG_OVERLOAD	enum __anonymous239__transmitter_ 1 (0x1)
#const NBRSSI_ERROR_SDADC_ANA_OVERLOAD	enum __anonymous239__transmitter_ 2 (0x2)
#const NBRSSI_ERROR_INBAND_DISTURBER	enum __anonymous239__transmitter_ 4 (0x4)
#const NBRSSI_ERROR_PHASE_SPEED_HIGH	enum __anonymous239__transmitter_ 8 (0x8)
#const NBRSSI_ERROR_STRONG_DISTURBER	enum __anonymous239__transmitter_ 16 (0x10)
#const RNG_CRC16	enum __anonymous241__transmitter_ 0 (0x0)
#const RNG_LFSR	enum __anonymous241__transmitter_ 1 (0x1)
#const RNG_TRUE	enum __anonymous241__transmitter_ 2 (0x2)
#const CPUMCC_SYSTEMCLOCK	enum __anonymous242__transmitter_ 0 (0x0)
#const CPUMCC_MACHINECLOCK	enum __anonymous242__transmitter_ 1 (0x1)
#const LF_OK	enum __anonymous245__transmitter_ 0 (0x0)
#const LF_BITFAIL	enum __anonymous245__transmitter_ 1 (0x1)
#const LF_INVMODE	enum __anonymous245__transmitter_ 2 (0x2)
#const LF_NBOVF	enum __anonymous245__transmitter_ 3 (0x3)
#const LF_WUPXM	enum __anonymous245__transmitter_ 4 (0x4)
#const LF_INVCRC	enum __anonymous245__transmitter_ 5 (0x5)
#const LF_INVDATA	enum __anonymous245__transmitter_ 6 (0x6)
#const LF_TIMEOUT	enum __anonymous245__transmitter_ 7 (0x7)
#const PPMODE_NOCOMM	enum __anonymous246__transmitter_ 0 (0x0)
#const PPMODE_CODEVIOL	enum __anonymous246__transmitter_ 1 (0x1)
#const PPMODE_WUPMATCH	enum __anonymous246__transmitter_ 2 (0x2)
#const PPMODE_DATAREC	enum __anonymous246__transmitter_ 3 (0x3)
#const LF_WUP_NONE	enum __anonymous247__transmitter_ 0 (0x0)
#const LF_WUPA	enum __anonymous247__transmitter_ 1 (0x1)
#const LF_WUPB	enum __anonymous247__transmitter_ 2 (0x2)
#const LF_WUPC	enum __anonymous247__transmitter_ 3 (0x3)
#const LFPWT_NONE	enum __anonymous248__transmitter_ 0 (0x0)
#const LFPWT_FROMWUP	enum __anonymous248__transmitter_ 1 (0x1)
#const LFPWT_FROMNEWBYTE	enum __anonymous248__transmitter_ 2 (0x2)
#const CRC	enum __anonymous249__transmitter_ 0 (0x0)
#const NO_CRC	enum __anonymous249__transmitter_ 1 (0x1)
#const STANDARD_EQ	enum __anonymous250__transmitter_ 248 (0xf8)
#const MODIFIED_EQ	enum __anonymous250__transmitter_ 252 (0xfc)
#const IIU_ENC_OFF	enum __anonymous251__transmitter_ 0 (0x0)
#const IIU_ENC_PLAIN	enum __anonymous251__transmitter_ 1 (0x1)
#const IIU_ENC_MANCHESTER	enum __anonymous251__transmitter_ 2 (0x2)
#const IIU_ENC_CDP	enum __anonymous251__transmitter_ 3 (0x3)
#const STRONG_MOD	enum __anonymous252__transmitter_ 32 (0x20)
#const STANDARD_MOD	enum __anonymous252__transmitter_ 0 (0x0)
#const IIUSTATE_IDLE	enum __anonymous253__transmitter_ 0 (0x0)
#const IIUSTATE_RXWAIT	enum __anonymous253__transmitter_ 1 (0x1)
#const IIUSTATE_RXDATA	enum __anonymous253__transmitter_ 2 (0x2)
#const IIUSTATE_TXWAIT	enum __anonymous253__transmitter_ 3 (0x3)
#const IIUSTATE_TXDATA	enum __anonymous253__transmitter_ 4 (0x4)
#const IIUSTATE_SHIFT	enum __anonymous253__transmitter_ 5 (0x5)
#const IIUSTATE_SHIFTCONT	enum __anonymous253__transmitter_ 6 (0x6)
#const IIUSTATE_RFU7	enum __anonymous253__transmitter_ 7 (0x7)
#const DISTWR_OK	enum __anonymous254__transmitter_ 0 (0x0)
#const DISTWR_REJECTED	enum __anonymous254__transmitter_ 51 (0x33)
#const DISTWR_REJECTED_MIN	enum __anonymous254__transmitter_ 238 (0xee)
#const DISTWR_DISABLED	enum __anonymous254__transmitter_ 204 (0xcc)
#const DISTWR_WRITE_FAIL_REJ	enum __anonymous254__transmitter_ 254 (0xfe)
#const DISTWR_WRITE_FAIL	enum __anonymous254__transmitter_ 255 (0xff)
#const DataEnc_NRZ_single	enum __anonymous255__transmitter_ 0 (0x0)
#const DataEnc_Man_single	enum __anonymous255__transmitter_ 1 (0x1)
#const DataEnc_NRZ_double	enum __anonymous255__transmitter_ 2 (0x2)
#const DataEnc_Man_double	enum __anonymous255__transmitter_ 3 (0x3)
