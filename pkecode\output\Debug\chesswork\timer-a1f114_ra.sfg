
// File generated by showcolor version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:24 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\showcolor.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Dindirect_bitf -t2 -yRw timer-a1f114 mrk3

[
    0 : void_timer_DelayTBit typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : i typ=int8_ val=0t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   26 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   44 : __ct_2s0 typ=int16_ val=2s0 bnd=m
   52 : __ct_2s0 typ=int16_ val=2s0 bnd=m
   60 : __apl_c typ=uint1_ bnd=m tref=uint1___
   62 : __apl_nz typ=uint2_ bnd=m tref=uint2___
   71 : __either typ=bool bnd=m
   72 : __trgt typ=rel8_ val=0j bnd=m
   73 : __seff typ=any bnd=m
   74 : __seff typ=any bnd=m
   75 : __seff typ=any bnd=m
   76 : __seff typ=any bnd=m
   77 : __seff typ=any bnd=m
   78 : __seff typ=any bnd=m
   79 : __seff typ=any bnd=m
   80 : __seff typ=any bnd=m
   81 : __seff typ=any bnd=m
   82 : __seff typ=any bnd=m
]
Fvoid_timer_DelayTBit {
    #6 off=0
    (__vola.12 var=13) source ()  <23>;
    (__sp.18 var=19) source ()  <29>;
    (i.19 var=20) source ()  <30>;
    (__ct_0t0.163 var=26) const_inp ()  <201>;
    (__ct_2s0.165 var=52) const_inp ()  <203>;
    (__trgt.166 var=72) const_inp ()  <204>;
    <23> {
      (__sp.26 var=19 __seff.182 var=80 stl=c_flag_w __seff.183 var=81 stl=nz_flag_w __seff.184 var=82 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1 (__ct_2s0.165 __sp.18 __sp.18)  <216>;
      (__seff.193 var=81 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.183)  <250>;
      (__seff.194 var=80 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.182)  <251>;
      (__seff.197 var=82 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.184)  <254>;
    } stp=3;
    <24> {
      (i.36 var=20) _pl_rd_res_reg_const_store_const_1 (__ct_0t0.163 i.19 __sp.26)  <217>;
    } stp=6;
    do {
        {
            (__vola.52 var=13) entry (__vola.83 __vola.12)  <64>;
            (i.59 var=20) entry (i.97 i.36)  <71>;
        } #11
        {
            call {
                (__vola.62 var=13) Fvoid_nop (__vola.52)  <74>;
            } #13 off=2
            #17 off=3
            <22> {
              (i.68 var=20 __seff.178 var=77 stl=c_flag_w __seff.179 var=78 stl=nz_flag_w __seff.180 var=79 stl=o_flag_w) _mi_load_const__pl_rd_res_reg_const_store_1 (__ct_0t0.163 i.59 i.59 __sp.26)  <215>;
              (__seff.189 var=78 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.179)  <246>;
              (__seff.190 var=77 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.178)  <247>;
              (__seff.191 var=79 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.180)  <248>;
            } stp=3;
            call {
                () chess_separator_scheduler ()  <83>;
            } #19 off=4
            #41 off=5
            <20> {
              (__apl_c.151 var=60 stl=c_flag_w __apl_nz.153 var=62 stl=nz_flag_w __seff.176 var=76 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1 (__ct_0t0.163 i.68 __sp.26)  <213>;
              (__apl_nz.186 var=62 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.153)  <243>;
              (__apl_c.188 var=60 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.151)  <245>;
              (__seff.192 var=76 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.176)  <249>;
            } stp=3;
            <21> {
              () cc_a__jump_const_1 (__apl_c.187 __apl_nz.185 __trgt.166)  <214>;
              (__apl_nz.185 var=62 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.186)  <242>;
              (__apl_c.187 var=60 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.188)  <244>;
            } stp=8;
        } #12
        {
            () while_expr (__either.162)  <98>;
            (__vola.83 var=13 __vola.84 var=13) exit (__vola.62)  <99>;
            (i.97 var=20 i.98 var=20) exit (i.68)  <106>;
            (__either.162 var=71) undefined ()  <200>;
        } #21
    } #10 rng=[1,2147483647]
    #26 off=6 nxt=-2
    () sink (__vola.84)  <153>;
    () sink (__sp.128)  <159>;
    () sink (i.98)  <160>;
    (__ct_2s0.164 var=44) const_inp ()  <202>;
    <18> {
      (__sp.128 var=19 __seff.171 var=73 stl=c_flag_w __seff.172 var=74 stl=nz_flag_w __seff.173 var=75 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1 (__ct_2s0.164 __sp.26 __sp.26)  <211>;
      (__seff.195 var=74 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.172)  <252>;
      (__seff.196 var=73 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.171)  <253>;
      (__seff.198 var=75 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.173)  <255>;
    } stp=3;
    <19> {
      () ret_1 ()  <212>;
    } stp=10;
} #0
0 : 'apps/src/timer.c';
----------
0 : (0,66:0,0);
6 : (0,71:2,2);
10 : (0,71:2,3);
12 : (0,71:2,3);
13 : (0,73:4,3);
17 : (0,74:5,6);
19 : (0,74:5,7);
26 : (0,76:0,15);
41 : (0,71:10,10);
----------
64 : (0,71:2,3);
71 : (0,71:2,3);
74 : (0,73:4,3);
83 : (0,74:5,7);
98 : (0,71:2,10);
99 : (0,71:2,10);
106 : (0,71:2,10);
211 : (0,76:0,0) (0,76:0,15);
212 : (0,76:0,15);
213 : (0,71:9,10) (0,69:11,0) (0,71:10,10);
214 : (0,71:10,10) (0,71:2,10);
215 : (0,74:5,5) (0,74:4,0) (0,69:11,0) (0,74:4,5);
216 : (0,66:5,0);
217 : (0,69:11,0) (0,69:17,1);
