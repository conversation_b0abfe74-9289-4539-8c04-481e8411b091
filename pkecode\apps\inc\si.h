#ifndef _SI_H
#define _SI_H

#include "defs.h"


#define     SI_PAGE0        0x0010u
#define     SI_PAGE1        0x0011u
#define     SI_PAGE2        0x0012u
#define     SI_PAGE3        0x0013u
#define     SI_PAGE4        0x0014u
#define     SI_PAGE5        0x0015u
#define     SI_PAGE6        0X0016u
#define     SI_PAGE7        0X0017u

#define     SI_PagSyncNum   0x03u
#define     SI_PgnNum       0x08u

extern   uint32_t SI_Counts[8] ;
//extern   uint32_t SITemp[1] ;
extern   uint8_t  SI_CntTx[4] ;

extern  uint8_t SI_Init(void );
extern  uint8_t SI_Inc( void );
extern  void SI_Get( uint8_t *si_value );
extern  void SI_ReSync( void );
extern  void SIError_ReSync( void );
extern  void SI_GlobalVariableInit(void);

















#endif