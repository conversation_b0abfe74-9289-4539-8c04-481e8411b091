<project name="Project" processor="mrk3" lib="..">
    <file type="c" name="ncf295A.c" path=""/>
    <file type="a" name="ncf295A_vector_table_default.s" path=""/>
    <option id="cpp.include" value="../ ../../types/" inherit="1"/>
    <option id="ear.mur" value="on"/>
    <option id="project.dir" value=""/>
    <option id="project.name" value="libncf295A.a"/>
    <option id="project.postbuild" value="../copy_archive.bat ncf295A libncf295A.a " inherit="1"/>
    <option id="project.type" value="arch"/>
</project>
