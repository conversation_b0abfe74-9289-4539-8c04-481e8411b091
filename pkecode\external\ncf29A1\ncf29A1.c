/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: ncf29A1.c 22437 2019-10-02 15:23:00Z dep10330 $
  $Revision: 22437 $
*/

#include "shared_sfrdef/platform_token.h"

/**
 * @file
 * Definition of the special function registers (SFR) of the TOKEN
 * (NCF29A1,NCF29A2) platform.
 */

/**
 * @addtogroup tokenhw
 * @{
 */

/*
 * CHANGE LOG
 * 2014-07-16: removed CLK* bits in ENCCON1 and PORCMPOUT in PCON2
 *             (acc. to DS these bits are RFU).
 * 2014-08-28: removed PRECVL register (since not in DS and not relevant to application)
 * 2014-10-16: added RF_MUTE_EN in ASKCON
 *             removed EOC in ADCDAT, changed CONVOV to CONV_OV in ADCDAT
 *             NEXTFIFODATAOK renamed to NEXTFIFODATOK acc. to DS
 *             removed LFSIGPOL in PRECON9
 *             added NEWBYTEOVFHOLD in PRECON8
 *             removed MRCOSC_OUTDIS in CLKCON2
 *             renamed bits in TXPCON acc. to DS
 *             removed PLLFBCLKDIS, VCOCALCLKDIS in CLKRSTCON
 * 2014-10-28: renamed SP1STAT to SPI1STAT
 * 2014-11-19: added bit fields for VCOCALCON
 * 2015-02-03: changed DM to DM9 for consistency with header file
 *             (the SFRs should be listed under DM9 in the map file, otherwise no effect)
 * 2016-02-02: removed any bit fields relating to (not specified) P22, P23, P24
 * 2017-01-11: moved change log items from .c to .h file
 *             removed BISTCON register (as RC001 no longer supported)
 * 2017-09-13: changed to typedefs for each register for consistency of library libncf29A1.a
 *             with header file ncf29A1.h.
 *             removed undocumented DBGDAT, DBGCON.
 * 2019-03-18: added bitfields for ULPCON0 and ULPCON1.
 * 2019-04-09: fixed incorrect bitfields in INTSET3 and ASKRMP.
 *
 */

volatile SFR_CXPC_t       chess_storage(DM9:0x014)  CXPC;
volatile SFR_CXSW_t       chess_storage(DM9:0x016)  CXSW;
volatile SFR_P1INS_t      chess_storage(DM9:0x019)  P1INS;
volatile SFR_P1OUT_t      chess_storage(DM9:0x01A)  P1OUT;
volatile SFR_P1DIR_t      chess_storage(DM9:0x01B)  P1DIR;
volatile SFR_P2INS_t      chess_storage(DM9:0x01C)  P2INS;
volatile SFR_P2OUT_t      chess_storage(DM9:0x01D)  P2OUT;
volatile SFR_P2DIR_t      chess_storage(DM9:0x01E)  P2DIR;
volatile SFR_P1INTDIS_t   chess_storage(DM9:0x01F)  P1INTDIS;
volatile SFR_P2INTDIS_t   chess_storage(DM9:0x020)  P2INTDIS;
volatile SFR_IIUCON2_t    chess_storage(DM9:0x021)  IIUCON2;
volatile SFR_IIUCON0_t    chess_storage(DM9:0x022)  IIUCON0;
volatile SFR_IIUSTAT_t    chess_storage(DM9:0x023)  IIUSTAT;
volatile SFR_IIUCON1_t    chess_storage(DM9:0x024)  IIUCON1;
volatile SFR_IIUDAT_t     chess_storage(DM9:0x025)  IIUDAT;
volatile SFR_IIUSTATE_t   chess_storage(DM9:0x026)  IIUSTATE;
volatile SFR_HTCON_t      chess_storage(DM9:0x027)  HTCON;
volatile SFR_AESDAT_t     chess_storage(DM9:0x028)  AESDAT;
volatile SFR_AESCON_t     chess_storage(DM9:0x02A)  AESCON;
volatile SFR_CRCDAT_t     chess_storage(DM9:0x02C)  CRCDAT;
volatile SFR_CRC8DIN_t    chess_storage(DM9:0x02E)  CRC8DIN;
volatile SFR_WDCON_t      chess_storage(DM9:0x037)  WDCON;
volatile SFR_CLKCON0_t    chess_storage(DM9:0x038)  CLKCON0;
volatile SFR_CLKCON1_t    chess_storage(DM9:0x039)  CLKCON1;
volatile SFR_CLKCON2_t    chess_storage(DM9:0x03A)  CLKCON2;
volatile SFR_CLKCON3_t    chess_storage(DM9:0x03D)  CLKCON3;
volatile SFR_CLKCON4_t    chess_storage(DM9:0x03F)  CLKCON4;
volatile SFR_PREDAT_t     chess_storage(DM9:0x040)  PREDAT;
volatile SFR_RTCCON_t     chess_storage(DM9:0x041)  RTCCON;
volatile SFR_PRECON2_t    chess_storage(DM9:0x042)  PRECON2;
volatile SFR_PRECON3_t    chess_storage(DM9:0x043)  PRECON3;
volatile SFR_PRECON4_t    chess_storage(DM9:0x044)  PRECON4;
volatile SFR_PRECON5_t    chess_storage(DM9:0x045)  PRECON5;
volatile SFR_PRECON6_t    chess_storage(DM9:0x046)  PRECON6;
volatile SFR_PRECON7_t    chess_storage(DM9:0x047)  PRECON7;
volatile SFR_PRECON8_t    chess_storage(DM9:0x048)  PRECON8;
volatile SFR_PRECON9_t    chess_storage(DM9:0x049)  PRECON9;
volatile SFR_PREPD_t      chess_storage(DM9:0x04A)  PREPD;
volatile SFR_USRBATRGLx_t chess_storage(DM9:0x04C)  USRBATRGL0;
volatile SFR_USRBATRGLx_t chess_storage(DM9:0x04D)  USRBATRGL1;
volatile SFR_USRBATRGLx_t chess_storage(DM9:0x04E)  USRBATRGL2;
volatile SFR_USRBATRGLx_t chess_storage(DM9:0x04F)  USRBATRGL3;
volatile SFR_USRBATRGLx_t chess_storage(DM9:0x050)  USRBATRGL4;
volatile SFR_USRBATRGLx_t chess_storage(DM9:0x051)  USRBATRGL5;
volatile SFR_USRBATRGLx_t chess_storage(DM9:0x052)  USRBATRGL6;
volatile SFR_USRBATRGLx_t chess_storage(DM9:0x053)  USRBATRGL7;
volatile SFR_PRESTAT_t    chess_storage(DM9:0x05C)  PRESTAT;
volatile SFR_WUP1W0_t     chess_storage(DM9:0x05E)  WUP1W0;
volatile SFR_WUP1W1_t     chess_storage(DM9:0x060)  WUP1W1;
volatile SFR_WUP2W0_t     chess_storage(DM9:0x062)  WUP2W0;
volatile SFR_WUP2W1_t     chess_storage(DM9:0x064)  WUP2W1;
volatile SFR_WUP3W0_t     chess_storage(DM9:0x066)  WUP3W0;
volatile SFR_PRET_t       chess_storage(DM9:0x068)  PRET;
volatile SFR_PRE3T_t      chess_storage(DM9:0x06A)  PRE3T;
volatile SFR_RTCDAT_t     chess_storage(DM9:0x06C)  RTCDAT;
volatile SFR_PRECON10_t   chess_storage(DM9:0x06E)  PRECON10;
volatile SFR_PRECON11_t   chess_storage(DM9:0x06F)  PRECON11;
volatile SFR_ADCDAT_t     chess_storage(DM9:0x072)  ADCDAT;
volatile SFR_ADCCON_t     chess_storage(DM9:0x074)  ADCCON;
volatile SFR_RSSICON_t    chess_storage(DM9:0x07C)  RSSICON;
volatile SFR_ULPADDR_t    chess_storage(DM9:0x080)  ULPADDR;
volatile SFR_ULPSEL_t     chess_storage(DM9:0x082)  ULPSEL;
volatile SFR_ULPCON0_t    chess_storage(DM9:0x084)  ULPCON0;
volatile SFR_ULPDAT_t     chess_storage(DM9:0x085)  ULPDAT;
volatile SFR_ULPCON1_t    chess_storage(DM9:0x08A)  ULPCON1;
volatile SFR_T0CON0_t     chess_storage(DM9:0x094)  T0CON0;
volatile SFR_T0CON1_t     chess_storage(DM9:0x095)  T0CON1;
volatile SFR_T0REG_t      chess_storage(DM9:0x096)  T0REG;
volatile SFR_T0RLD_t      chess_storage(DM9:0x098)  T0RLD;
volatile SFR_T1CON0_t     chess_storage(DM9:0x09A)  T1CON0;
volatile SFR_T1CON1_t     chess_storage(DM9:0x09B)  T1CON1;
volatile SFR_T1CON2_t     chess_storage(DM9:0x09C)  T1CON2;
volatile SFR_T1REG_t      chess_storage(DM9:0x09E)  T1REG;
volatile SFR_T1CAP_t      chess_storage(DM9:0x0A0)  T1CAP;
volatile SFR_T1CMP_t      chess_storage(DM9:0x0A2)  T1CMP;
volatile SFR_T2CON0_t     chess_storage(DM9:0x0A4)  T2CON0;
volatile SFR_T2CON1_t     chess_storage(DM9:0x0A5)  T2CON1;
volatile SFR_T2REG_t      chess_storage(DM9:0x0A6)  T2REG;
volatile SFR_T2RLD_t      chess_storage(DM9:0x0A8)  T2RLD;
volatile SFR_RNGDAT_t     chess_storage(DM9:0x0AA)  RNGDAT;
volatile SFR_RNGCON_t     chess_storage(DM9:0x0AC)  RNGCON;
volatile SFR_INTCON_t     chess_storage(DM9:0x0AF)  INTCON;
volatile SFR_INTFLAG0_t   chess_storage(DM9:0x0B0)  INTFLAG0;
volatile SFR_INTFLAG1_t   chess_storage(DM9:0x0B1)  INTFLAG1;
volatile SFR_INTFLAG2_t   chess_storage(DM9:0x0B2)  INTFLAG2;
volatile SFR_INTEN0_t     chess_storage(DM9:0x0B3)  INTEN0;
volatile SFR_INTEN1_t     chess_storage(DM9:0x0B4)  INTEN1;
volatile SFR_INTEN2_t     chess_storage(DM9:0x0B5)  INTEN2;
volatile SFR_SYSINTEN0_t  chess_storage(DM9:0x0B6)  SYSINTEN0;
volatile SFR_SYSINTEN1_t  chess_storage(DM9:0x0B7)  SYSINTEN1;
volatile SFR_INTSET0_t    chess_storage(DM9:0x0B8)  INTSET0;
volatile SFR_INTSET1_t    chess_storage(DM9:0x0B9)  INTSET1;
volatile SFR_INTSET2_t    chess_storage(DM9:0x0BA)  INTSET2;
volatile SFR_INTCLR0_t    chess_storage(DM9:0x0BB)  INTCLR0;
volatile SFR_INTCLR1_t    chess_storage(DM9:0x0BC)  INTCLR1;
volatile SFR_INTCLR2_t    chess_storage(DM9:0x0BD)  INTCLR2;
volatile SFR_INTVEC_t     chess_storage(DM9:0x0BE)  INTVEC;
volatile SFR_BATSYS0_t    chess_storage(DM9:0x0C0)  BATSYS0;
volatile SFR_BATSYS1_t    chess_storage(DM9:0x0C1)  BATSYS1;
volatile SFR_PRESWUP0_t   chess_storage(DM9:0x0C2)  PRESWUP0;
volatile SFR_PRESWUP1_t   chess_storage(DM9:0x0C4)  PRESWUP1;
volatile SFR_PRESWUP2_t   chess_storage(DM9:0x0C6)  PRESWUP2;
volatile SFR_P1WRES_t     chess_storage(DM9:0x0C8)  P1WRES;
volatile SFR_P2WRES_t     chess_storage(DM9:0x0C9)  P2WRES;
volatile SFR_USRBATx_t    chess_storage(DM9:0x0CA)  USRBAT0;
volatile SFR_USRBATx_t    chess_storage(DM9:0x0CB)  USRBAT1;
volatile SFR_USRBATx_t    chess_storage(DM9:0x0CC)  USRBAT2;
volatile SFR_USRBATx_t    chess_storage(DM9:0x0CD)  USRBAT3;
volatile SFR_USRBATx_t    chess_storage(DM9:0x0CE)  USRBAT4;
volatile SFR_USRBATx_t    chess_storage(DM9:0x0CF)  USRBAT5;
volatile SFR_USRBATx_t    chess_storage(DM9:0x0D0)  USRBAT6;
volatile SFR_USRBATx_t    chess_storage(DM9:0x0D1)  USRBAT7;
volatile SFR_LFTUNEVBAT_t chess_storage(DM9:0x0D2)  LFTUNEVBAT;
volatile SFR_LFTUNEVDD_t  chess_storage(DM9:0x0D4)  LFTUNEVDD;
volatile SFR_LFSHCON_t    chess_storage(DM9:0x0D6)  LFSHCON;
volatile SFR_PCON0_t      chess_storage(DM9:0x0D8)  PCON0;
volatile SFR_PCON1_t      chess_storage(DM9:0x0D9)  PCON1;
volatile SFR_PCON2_t      chess_storage(DM9:0x0DA)  PCON2;
volatile SFR_SPI0CON0_t   chess_storage(DM9:0x0E0)  SPI0CON0;
volatile SFR_SPI0CON1_t   chess_storage(DM9:0x0E1)  SPI0CON1;
volatile SFR_SPI0DAT_t    chess_storage(DM9:0x0E2)  SPI0DAT;
volatile SFR_SPI0STAT_t   chess_storage(DM9:0x0E3)  SPI0STAT;
volatile SFR_SPI1CON0_t   chess_storage(DM9:0x0E4)  SPI1CON0;
volatile SFR_SPI1CON1_t   chess_storage(DM9:0x0E5)  SPI1CON1;
volatile SFR_SPI1DAT_t    chess_storage(DM9:0x0E6)  SPI1DAT;
volatile SFR_SPI1STAT_t   chess_storage(DM9:0x0E7)  SPI1STAT;
volatile SFR_P1ALTF_t     chess_storage(DM9:0x0EC)  P1ALTF;
volatile SFR_P2ALTF_t     chess_storage(DM9:0x0EE)  P2ALTF;
volatile SFR_BITCNT_t     chess_storage(DM9:0x0F0)  BITCNT;
volatile SFR_BITSWAP_t    chess_storage(DM9:0x0F2)  BITSWAP;
volatile SFR_PREPOLL0_t   chess_storage(DM9:0x0F7)  PREPOLL0;
volatile SFR_PREPOLL1_t   chess_storage(DM9:0x0F8)  PREPOLL1;
volatile SFR_PRECON12_t   chess_storage(DM9:0x0F9)  PRECON12;
volatile SFR_MSICON0_t    chess_storage(DM9:0x0FA)  MSICON0;
volatile SFR_MSICON1_t    chess_storage(DM9:0x0FB)  MSICON1;
volatile SFR_MSISTAT0_t   chess_storage(DM9:0x0FC)  MSISTAT0;
volatile SFR_MSISTAT1_t   chess_storage(DM9:0x0FD)  MSISTAT1;
volatile SFR_INTFLAG3_t   chess_storage(DM9:0x100)  INTFLAG3;
volatile SFR_INTEN3_t     chess_storage(DM9:0x101)  INTEN3;
volatile SFR_INTSET3_t    chess_storage(DM9:0x102)  INTSET3;
volatile SFR_INTCLR3_t    chess_storage(DM9:0x103)  INTCLR3;
volatile SFR_TXPCON_t     chess_storage(DM9:0x104)  TXPCON;
volatile SFR_CLKRSTCON_t  chess_storage(DM9:0x105)  CLKRSTCON;
volatile SFR_VCOCALCON_t  chess_storage(DM9:0x106)  VCOCALCON;
volatile SFR_PLLCON_t     chess_storage(DM9:0x107)  PLLCON;
volatile SFR_TXDAT_t      chess_storage(DM9:0x108)  TXDAT;
volatile SFR_TXSPC_t      chess_storage(DM9:0x10A)  TXSPC;
volatile SFR_ENCCON0_t    chess_storage(DM9:0x10C)  ENCCON0;
volatile SFR_ENCCON1_t    chess_storage(DM9:0x10E)  ENCCON1;
volatile SFR_FREQCON0_t   chess_storage(DM9:0x110)  FREQCON0;
volatile SFR_FREQCON1_t   chess_storage(DM9:0x112)  FREQCON1;
volatile SFR_BRGCON_t     chess_storage(DM9:0x114)  BRGCON;
volatile SFR_FSKCON_t     chess_storage(DM9:0x116)  FSKCON;
volatile SFR_FSKRMP_t     chess_storage(DM9:0x117)  FSKRMP;
volatile SFR_ASKCON_t     chess_storage(DM9:0x118)  ASKCON;
volatile SFR_ASKRMP_t     chess_storage(DM9:0x11A)  ASKRMP;
volatile SFR_PACON_t      chess_storage(DM9:0x11B)  PACON;
volatile SFR_PAPWR_t      chess_storage(DM9:0x11C)  PAPWR;
volatile SFR_PATRIM_t     chess_storage(DM9:0x11D)  PATRIM;
volatile SFR_PALIMIT_t    chess_storage(DM9:0x11E)  PALIMIT;


/*@}*/

/* eof */


