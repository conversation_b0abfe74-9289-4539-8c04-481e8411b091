
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:20 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-c81fac mrk3

[
    0 : void_tx_shutdown typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : TXPCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_TXPCON_t_DM9
   21 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   22 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   23 : __extDM_SFR_TXPCON_t typ=int8_ bnd=b stl=DM
   24 : __extPM_void typ=uint16_ bnd=b stl=PM
   25 : __extDM_void typ=int8_ bnd=b stl=DM
   26 : __extULP_void typ=uint32_ bnd=b stl=ULP
   29 : __ptr_TXPCON typ=int16_ val=0a bnd=m adro=20
   33 : __ct_38 typ=uint8_ val=38f bnd=m
   35 : void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar typ=int16_ val=0r bnd=m
]
Fvoid_tx_shutdown {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (TXPCON.19 var=20) source ()  <30>;
    (__extDM_int8_.20 var=21) source ()  <31>;
    (__extDM_int16_.21 var=22) source ()  <32>;
    (__extDM_SFR_TXPCON_t.22 var=23) source ()  <33>;
    (__extPM_void.23 var=24) source ()  <34>;
    (__extDM_void.24 var=25) source ()  <35>;
    (__extULP_void.25 var=26) source ()  <36>;
    (void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar.86 var=35) const_inp ()  <106>;
    <36> {
      () call_const_1_B1 (void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar.86)  <117>;
    } stp=2;
    <44> {
      (__ct_38.109 var=33 stl=__CTa_b0_int8__cstP24_E1) const_1_B1 ()  <133>;
      (__ct_38.108 var=33 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_1_uint8__B0 (__ct_38.109)  <141>;
    } stp=0;
    call {
        (TXPCON.40 var=20 __extDM.41 var=17 __extDM_SFR_TXPCON_t.42 var=23 __extDM_int16_.43 var=22 __extDM_int8_.44 var=21 __extDM_void.45 var=25 __extPM.46 var=16 __extPM_void.47 var=24 __extULP.48 var=18 __extULP_void.49 var=26 __vola.50 var=13) Fvoid_phcaiKEyLLGenFunc_CS_SetClkCon___uchar (__ct_38.108 TXPCON.19 __extDM.16 __extDM_SFR_TXPCON_t.22 __extDM_int16_.21 __extDM_int8_.20 __extDM_void.24 __extPM.15 __extPM_void.23 __extULP.17 __extULP_void.25 __vola.12)  <52>;
    } #4 off=4 nxt=6
    #6 off=4 nxt=7
    (__ptr_TXPCON.85 var=29) const_inp ()  <105>;
    <33> {
      (TXPCON.57 var=20 __vola.58 var=13) store_const_const_1_B1 (__ptr_TXPCON.85 TXPCON.40 __vola.50)  <114>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <60>;
    } #7 off=6 nxt=9
    #9 off=6 nxt=-2
    () sink (__vola.58)  <67>;
    () sink (__extPM.46)  <70>;
    () sink (__extDM.41)  <71>;
    () sink (__extULP.48)  <72>;
    () sink (__sp.18)  <73>;
    () sink (TXPCON.57)  <74>;
    () sink (__extDM_int8_.44)  <75>;
    () sink (__extDM_int16_.43)  <76>;
    () sink (__extDM_SFR_TXPCON_t.42)  <77>;
    () sink (__extPM_void.47)  <78>;
    () sink (__extDM_void.45)  <79>;
    () sink (__extULP_void.49)  <80>;
    <32> {
      () ret_1_B1 ()  <113>;
    } stp=0;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,166:0,0);
3 : (0,170:39,1);
4 : (0,170:2,1);
6 : (0,172:13,3);
7 : (0,172:13,3);
9 : (0,173:0,3);
----------
52 : (0,170:2,1);
60 : (0,172:13,3);
113 : (0,173:0,3);
114 : (0,172:8,2);
117 : (0,170:2,1);
133 : (0,170:39,0);

