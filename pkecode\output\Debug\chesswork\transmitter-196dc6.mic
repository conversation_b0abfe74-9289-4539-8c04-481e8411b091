
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-196dc6 mrk3


// m5;   next: m6 (next offset: 3)
000000  1 0  "0000010011000111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (8,R7,R7); 
000001  2 0  "0110101001100011"   // (DM[0]) = _pl_rd_res_reg_const_store_1_B1 (RbH[0],0,DM[0],R7); 
000002  0 0  "0000000000000000"   // /

// m6 chess_separator_scheduler;   next: m7 (next offset: 3)

// m7;   next: m8 (next offset: 4)
000003  1 0  "0110101101000000"   // (DM[1]) = _pl_rd_res_reg_const_store_1_B2 (RbL[0],1,DM[1],R7); 

// m8 chess_separator_scheduler;   next: m9 (next offset: 4)

// m9;   next: m10 (next offset: 5)
000004  1 0  "0110101110000001"   // (DM[2]) = store_const__pl_rd_res_reg_const_1_B3 (2,DM[2],R7); 

// m10 chess_separator_scheduler;   next: m11 (next offset: 5)

// m11;   next: m12 (next offset: 6)
000005  1 0  "0110101110000010"   // (DM[3]) = store_const__pl_rd_res_reg_const_1_B3 (3,DM[3],R7); 

// m12 chess_separator_scheduler;   next: m13 (next offset: 6)

// m13;   next: m14 (next offset: 7)
000006  1 0  "0110101110000011"   // (DM[3]) = store_const__pl_rd_res_reg_const_1_B3 (4,DM[3],R7); 

// m14 chess_separator_scheduler;   next: m15 (next offset: 7)

// m15;   next: m16 (next offset: 8)
000007  1 0  "0110101110000100"   // (DM[3]) = store_const__pl_rd_res_reg_const_1_B3 (5,DM[3],R7); 

// m16 chess_separator_scheduler;   next: m17 (next offset: 8)

// m17;   next: m19 (next offset: 9)
000008  1 0  "0110101110000101"   // (DM[3]) = store_const__pl_rd_res_reg_const_1_B3 (6,DM[3],R7); 

// m19 chess_separator_scheduler;   next: m192 (next offset: 9)

// m192;   next: m216, jump target: m24 (next offset: 11)
000009  1 0  "0100001110001000"   // (nz_flag,c_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_3_B3 (1,DM[1],R7); 
000010  1 0  "0101000000010100"   // () = cc_eq__jump_const_1_B1 (nz_flag,20); 

// m216;   next: m50, jump target: m240 (next offset: 13)
000011  1 0  "0100001110010000"   // (nz_flag,c_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_2_B3 (1,DM[1],R7); 
000012  1 0  "0101000100100010"   // () = cc_ne__jump_const_1_B1 (nz_flag,34); 

// m50;   next: m51 (next offset: 14)
000013  1 0  "0110101110000001"   // (DM[2]) = store_const__pl_rd_res_reg_const_1_B3 (2,DM[2],R7); 

// m51 chess_separator_scheduler;   next: m249 (next offset: 14)

// m249;   next: m235 (next offset: 16)
000014  2 0  "0001011111111101"   // (R46[1],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (3,R7); 
000015  0 0  "0000000000000011"   // /

// m235;   next: m252, jump target: m235 (next offset: 29)
000016  1 0  "0110101100000001" .loop_nesting 1    // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B2 (2,DM[2],R7); 
000017  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000018  1 0  "0110110001000100"   // R46[0] = RwL[0]; 
000019  1 0  "0110101100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B2 (2,DM[2],R7); 
000020  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000021  2 0  "0110101000001000"   // (RbL[1]) = _pl_const_load_1_B1 (R46[0],0,DM); 
000022  0 0  "0000000000000000"   // /
000023  1 0  "0001010001101000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_1_B2 (R46[1],RwL[0]); 
000024  1 0  "0110110001000100"   // R46[0] = RwL[0]; 
000025  1 0  "0110100101001000"   // (DM[3]) = store_1_B1 (RbL[1],R46[0],DM[3]); 
000026  1 0  "0001001110001001"   // (DM[2],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B3 (2,DM[2],DM[2],R7); 
000027  1 0  "0100001110100001"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B3 (2,DM[2],R7); 
000028  1 0  "0101100011110100"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-12); 

// m252;   next: m253 (next offset: 29)

// m253, jump target: m240 (next offset: 30)
000029  1 0  "0101101000010001" .loop_nesting 0    // () = jump_const_1_B1 (17); 

// m24;   next: m25 (next offset: 31)
000030  1 0  "0110101110000001"   // (DM[2]) = store_const__pl_rd_res_reg_const_1_B3 (2,DM[2],R7); 

// m25 chess_separator_scheduler;   next: m248 (next offset: 31)

// m248;   next: m211 (next offset: 33)
000031  2 0  "0001011111111101"   // (R46[1],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (3,R7); 
000032  0 0  "0000000000000011"   // /

// m211;   next: m256, jump target: m211 (next offset: 46)
000033  1 0  "0110101100000001" .loop_nesting 1    // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B2 (2,DM[2],R7); 
000034  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000035  1 0  "0110110001000100"   // R46[0] = RwL[0]; 
000036  1 0  "0110101100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B2 (2,DM[2],R7); 
000037  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000038  2 0  "0110101000001000"   // (RbL[1]) = _pl_const_load_1_B1 (R46[0],0,DM); 
000039  0 0  "0000000000000000"   // /
000040  1 0  "0001010001101000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_1_B2 (R46[1],RwL[0]); 
000041  1 0  "0110110001000100"   // R46[0] = RwL[0]; 
000042  1 0  "0110100101001000"   // (DM[3]) = store_1_B1 (RbL[1],R46[0],DM[3]); 
000043  1 0  "0001001110001001"   // (DM[2],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B3 (2,DM[2],DM[2],R7); 
000044  1 0  "0100001110100001"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B3 (2,DM[2],R7); 
000045  1 0  "0101100011110100"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-12); 

// m256;   next: m240 (next offset: 46)

// m240;   next: m75 (next offset: 53)
000046  2 0  "0110101000000011" .loop_nesting 0    // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B1 (0,DM[0],R7); 
000047  0 0  "0000000000000000"   // /
000048  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000049  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (3,R7); 
000050  0 0  "0000000000000011"   // /
000051  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000052  0 0  "0000000000000000"   // /

// m75 subroutine call;   next: m78 (next offset: 53)

// m78 (next offset: /)
000053  1 0  "0001010011000111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (8,R7,R7); 
000054  1 0  "0001101111000100"   // () = ret_1_B1 (); 

