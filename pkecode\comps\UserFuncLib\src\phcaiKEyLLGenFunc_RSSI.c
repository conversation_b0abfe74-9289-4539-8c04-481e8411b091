/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_RSSI.c 28748 2020-09-21 16:18:07Z dep10330 $
  $Revision: 28748 $
*/

/*+----------------------------------------------------------------------------- */
/*| NOTE: The code provided herein is still under development and hence          */
/*|       subject to change.                                                     */
/*+----------------------------------------------------------------------------- */

/**
 * @file
 * Implementation of User Library Functions related to RSSI measurements.
 */

/*
  Change Log:
  -----------
  2014-01-30 (MMr):
  - adapted, first functional draft
  2014-09-12 (MMr):
  - changed handling of ADC parameters: moved number of samples and spacing from
    _RSSI_ADCInit to each measurement function.
  2015-06-19 (MMr):
  - Changes in phcaiKEyLLGenFunc_RSSI_ADCInit:
    moved in code for RSSI start-up from application, added result code,
    moved constant D_RSSICON_STUP to header file.
  - Removed RSSI_HOLD = 0 in phcaiKEyLLGenFunc_RSSI_SelectRange.
  2015-12-08 (NBn):
  - Modified default ADCCON setting for RSSI (D_ADCCON_RSSI) such that
    REFSEL = 100bin as recommended in data sheet.
  2016-04-15 (MMr):
  - removed parameter u16_ADC_samplinginterval_us in functions
    phcaiKEyLLGenFunc_RSSI_MeasOffset and phcaiKEyLLGenFunc_RSSI_MeasChannel.
  2017-01-26 (MMr):
  - started port to TOKEN-PLUS/ACTIC5G-PLUS, only switches adapted. Register handling to be
    reviewed.
  - added parameter e_samplingtime in phcaiKEyLLGenFunc_RSSI_ADCInit
  - removed compile switches for syscalls not present in obsolete ROM codes (TOKEN before RC004).
  2017-03-02 (MMr):
  - use syscall phcaiKEyLLGenFunc_CS_ADC_poweron only on TOKEN platform
  - do not set ADCCON.POWERON to 1 for PLUS/SRX(v0) since hardware summation does not require it.
  2017-03-23 (NBn):
  - Changes in phcaiKEyLLGenFunc_RSSI_Convert:
    Range information masked with 0x07 to handle added information (bit 7) indication use of LFSHCON
  2017-11-24 (MMr):
  - Added compile switches for supporting Narrowband RSSI (PLATFORM_HAS_NBRSSI)
  2017-12-14 (MMr):
  - phcaiKEyLLGenFunc_RSSI_Convert: fixed conversion of 16-bit unsigned ADC values from NBRSSI
  2018-01-09 (NBn):
  - Module "phcaiKEyLLGenFunc_RSSI.c" split in standard RSSI (..._STDRSSI) and narrow band RSSI
    (..._NBRSSI) modules.
    Module "phcaiKEyLLGenFunc_RSSI.c" will include wrapper functions choosing the required RSSI
    function (standard or narrow band).
  2019-02-06 (MMr):
  - phcaiKEyLLGenFunc_RSSI_Convert: for SRX types, scaling by NBRSSI_STDRSSI_SCALEFACTOR
    rather than 1/64.
  2019-02-06 (MMr):
  - phcaiKEyLLGenFunc_RSSI_Convert: for SRX types, add switch NBRSSI_NO_COMPAT_SCALING
    to avoid scaling by NBRSSI_STDRSSI_SCALEFACTOR.


 */

#include "phcaiKEyLLGenFunc.h"
#include "phcaiKEyLLGenFunc_CS.h"
#include "phcaiKEyLLGenFunc_STDRSSI.h"
#include "phcaiKEyLLGenFunc_NBRSSI.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @addtogroup RSSI
 * @{
 */


/*-----------------------------------------------------------------------------------------------*/
error_t phcaiKEyLLGenFunc_RSSI_ADCInit( bool_t b_EnableRangeExt, ADC_SAMTIM_t e_samplingtime )
/*-----------------------------------------------------------------------------------------------*/
{
#ifndef PLATFORM_HAS_NBRSSI
  return phcaiKEyLLGenFunc_STDRSSI_ADCInit( b_EnableRangeExt, e_samplingtime );

#elif defined(PLATFORM_HAS_NBRSSI)
  chess_dont_warn_dead( b_EnableRangeExt );
  chess_dont_warn_dead( e_samplingtime );
  return phcaiKEyLLGenFunc_NBRSSI_ADCInit();

#else
#error "RSSI mode not supported"
#endif
}

/*-----------------------------------------------------------------------------------------------*/
void phcaiKEyLLGenFunc_RSSI_ADCStop( void )
/*-----------------------------------------------------------------------------------------------*/
{
#ifndef PLATFORM_HAS_NBRSSI
  phcaiKEyLLGenFunc_STDRSSI_ADCStop();

#elif defined(PLATFORM_HAS_NBRSSI)
  phcaiKEyLLGenFunc_NBRSSI_ADCStop();

#else
#error "RSSI mode not supported"
#endif
}

/*-----------------------------------------------------------------------------------------------*/
uint16_t phcaiKEyLLGenFunc_RSSI_MeasOffset( uint16_t u16_num_ADC_samples )
/*-----------------------------------------------------------------------------------------------*/
{
#ifndef PLATFORM_HAS_NBRSSI
  return phcaiKEyLLGenFunc_STDRSSI_MeasOffset( u16_num_ADC_samples, 0u );

#elif defined(PLATFORM_HAS_NBRSSI)
  // For NBRSSI, no offset measurement is required.
  // Return 0 for compatibility with existing code.
  chess_dont_warn_dead( u16_num_ADC_samples );
  return 0u;

#else
#error "RSSI mode not supported"
#endif
}

/*-----------------------------------------------------------------------------------------------*/
uint16_t phcaiKEyLLGenFunc_RSSI_MeasOffset_FractBits( uint16_t u16_num_ADC_samples, uint16_t u16_NumFractionalBits )
/*-----------------------------------------------------------------------------------------------*/
{
#ifndef PLATFORM_HAS_NBRSSI
  return phcaiKEyLLGenFunc_STDRSSI_MeasOffset( u16_num_ADC_samples, u16_NumFractionalBits );

#elif defined(PLATFORM_HAS_NBRSSI)
  // For NBRSSI, no offset measurement is required.
  // Return 0 for compatibility with existing code.
  chess_dont_warn_dead( u16_num_ADC_samples );
  chess_dont_warn_dead( u16_NumFractionalBits );

  return 0u;

#else
#error "RSSI mode not supported"
#endif
}

/*-----------------------------------------------------------------------------------------------*/
uint16_t phcaiKEyLLGenFunc_RSSI_MeasChannel( uint16_t u16_num_ADC_samples )
/*-----------------------------------------------------------------------------------------------*/
{
#ifndef PLATFORM_HAS_NBRSSI
  return phcaiKEyLLGenFunc_STDRSSI_MeasChannel( u16_num_ADC_samples, 0u );

#elif defined(PLATFORM_HAS_NBRSSI)
  return phcaiKEyLLGenFunc_NBRSSI_MeasChannel( u16_num_ADC_samples );

#else
#error "RSSI mode not supported"
#endif
}

/*-----------------------------------------------------------------------------------------------*/
uint16_t phcaiKEyLLGenFunc_RSSI_MeasChannel_FractBits( uint16_t u16_num_ADC_samples, uint16_t u16_NumFractionalBits )
/*-----------------------------------------------------------------------------------------------*/
{
#ifndef PLATFORM_HAS_NBRSSI
  return phcaiKEyLLGenFunc_STDRSSI_MeasChannel( u16_num_ADC_samples, u16_NumFractionalBits );

#elif defined(PLATFORM_HAS_NBRSSI)
  chess_dont_warn_dead( u16_NumFractionalBits );
  return phcaiKEyLLGenFunc_NBRSSI_MeasChannel( u16_num_ADC_samples );

#else
#error "RSSI mode not supported"
#endif
}


/*-----------------------------------------------------------------------------------------------*/
phcaiKEyLLGenFunc_RssiRange_t
phcaiKEyLLGenFunc_RSSI_SelectRange( phcaiKEyLLGenFunc_RssiChannel_t e_channel,
                                    phcaiKEyLLGenFunc_RssiRange_t   e_range,
                                    bool_t                          b_autoRangeInd )
/*-----------------------------------------------------------------------------------------------*/
{
#ifndef PLATFORM_HAS_NBRSSI

  return phcaiKEyLLGenFunc_STDRSSI_SelectRange( e_channel, e_range, b_autoRangeInd );

#elif defined(PLATFORM_HAS_NBRSSI)

  return phcaiKEyLLGenFunc_NBRSSI_SelectRange ( e_channel, e_range, b_autoRangeInd );

#else
#error "RSSI mode not supported"
#endif
}

/*-----------------------------------------------------------------------------------------------*/
float24 phcaiKEyLLGenFunc_RSSI_Convert( uint16_t u16_meas_res,
                                        uint16_t u16_offset,
                                        phcaiKEyLLGenFunc_RssiRange_t e_range)
/*-----------------------------------------------------------------------------------------------*/
{
  uint16_t u16_diff;

  /* Union serves to access the bit fields of float24 data */
  union
  {
    float24 FloatValue;   /* 24 bit float value (NXP format) */
    uint8_t ByteValue[3]; /* Bytes of FloatValue:  */
  } FloatTrans;

  /* ByteValue[0] : mantissa bits m7..m0 */
  /* ByteValue[1] : s = sign bit (=> sgn = (s==0)?(+1):(-1), mantissa bits m14..m8 */
  /* ByteValue[2] : exponent e7..e0 (signed, 2s complement repres.) */
  /* Actual value is */
  /* f = sgn * ( 2^(-1) + m14 * 2^(-2) + ... m0 * 2^(-16) ) * 2^e */

  PCON2.bits.R2MSDET = 1U;                       /* Dynamic reset of the 2 ms detection */

  /* extract the actual RSSI gain, clear bit 7 (may indicate Upper Range Extension)
  which is not relevant here. */
  e_range = (phcaiKEyLLGenFunc_RssiRange_t)((uint8_t)e_range & 0x07);

  /* Always do the conversion to float24, to reduce dependency of timing on data. */
  /* Notes:
   - TODO consider moving uint16_t to float24 conversion to utility function
   */
  if ( u16_meas_res < u16_offset ) {
    /* limit an unexpected negative difference to 0.
     (offset should never be larger than the ADC value.) */
    u16_diff = 0u;
  }
  else {
    u16_diff = u16_meas_res - u16_offset;
  }
  if ( u16_diff >= 32768u ) {
    /* int-to-float24 conversion works on signed 16-bit integer,
      so we must shift to 15 bit range.
      Least significant bit is lost, but float24 has only 15 bit mantissa anyway.
      Can only occur with 16-bit results from NBRSSI. */
    FloatTrans.FloatValue = (float24) (int16_t)( u16_diff / 2 );
    /* increment exponent by 1 means multiply by 2 */
    FloatTrans.ByteValue[2] += 1u;
  }
  else {
    /* Note: 0 <= u16_diff <= 32767 */
    /* Note: compiler's conversion function considers special case of input==0. */
    FloatTrans.FloatValue = (float24) (int16_t) u16_diff;
  }

  if ( e_range == RSSI_RANGE_LIM )
  {
    /* To be compatible with the pcf7953 ROM lib (function R_RSSI_CONVERT), */
    /* set the result to 2^16 = 2^(-1) * 2^17, means m=0, e= 17 = 0x11 .    */
    FloatTrans.ByteValue[0] = 0x00U;
    FloatTrans.ByteValue[1] = 0x00U;
    FloatTrans.ByteValue[2] = 0x11U;
  }
  else
  {
    /* Do the equivalent to: */
    /* FloatTrans.FloatValue = FloatTrans.FloatValue / gain; */
    switch ( e_range )
    {
      case RSSI_RANGE_54dB:
        /* Gain = 2^9 = 512; */
        /* division by 2^9  means subtract 9 from exponent. */
        FloatTrans.ByteValue[2] -= 9U;
        break;

      case RSSI_RANGE_36dB:
        /* Gain = 64; */
        /* Division by 64 = 2^6  means subtract 6 from exponent. */
        FloatTrans.ByteValue[2] -= 6U;
        break;
      case RSSI_RANGE_18dB:
        /* Gain = 8; */
        FloatTrans.ByteValue[2] -= 3U;
        break;
      case RSSI_RANGE_0dB:
        /* Gain = 1; no change */
        /* FloatTrans.ByteValue[2] += 0; */
        break;
      case RSSI_RANGE_m18dB:
        /* Gain = 0.125; */
        FloatTrans.ByteValue[2] += 3U;
        break;

      case RSSI_RANGE_LIM: /* added for MISRA-C */
      default:
        /* Gain = 1; */
        /* FloatTrans.ByteValue[2] += 0; */
        break;
    }
    #if defined(PLATFORM_HAS_NBRSSI) & !defined(NBRSSI_NO_COMPAT_SCALING)
    // compensate higher output values of NBRSSI, for compatibility with TED-Kit2
    // GUI (RSSI display).
    FloatTrans.FloatValue *= NBRSSI_STDRSSI_SCALEFACTOR;
    // Formerly:
    // roughly compensate for the difference of 16-bit to 10-bit ADC
    //FloatTrans.ByteValue[2] -= 6u;
    #endif
  }

  return FloatTrans.FloatValue;
}


/* @} */
/* @} */
