
// File generated by noodle version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:28 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/SysInit.c mrk3

[
    7 : P1INS typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P1INS_t_DM9
    8 : P1OUT typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P1OUT_t_DM9
    9 : P1DIR typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P1DIR_t_DM9
   11 : P2OUT typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P2OUT_t_DM9
   12 : P2DIR typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P2DIR_t_DM9
   26 : WDCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_WDCON_t_DM9
   29 : CLKCON2 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_CLKCON2_t_DM9
   33 : RTCCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_RTCCON_t_DM9
   34 : PRECON2 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON2_t_DM9
   35 : PRECON3 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON3_t_DM9
   36 : PRECON4 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON4_t_DM9
   37 : PRECON5 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON5_t_DM9
   38 : PRECON6 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON6_t_DM9
   39 : PRECON7 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON7_t_DM9
   40 : PRECON8 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON8_t_DM9
   41 : PRECON9 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON9_t_DM9
   42 : PREPD typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PREPD_t_DM9
   51 : PRESTAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESTAT_t_DM9
   52 : WUP1W0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP1W0_t_DM9
   53 : WUP1W1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP1W1_t_DM9
   54 : WUP2W0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP2W0_t_DM9
   55 : WUP2W1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP2W1_t_DM9
   56 : WUP3W0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP3W0_t_DM9
   57 : PRET typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRET_t_DM9
   58 : PRE3T typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRE3T_t_DM9
   60 : PRECON10 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON10_t_DM9
   61 : PRECON11 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON11_t_DM9
  102 : BATSYS0 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_BATSYS0_t_DM9
  104 : PRESWUP0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESWUP0_t_DM9
  105 : PRESWUP1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESWUP1_t_DM9
  106 : PRESWUP2 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESWUP2_t_DM9
  107 : P1WRES typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P1WRES_t_DM9
  108 : P2WRES typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P2WRES_t_DM9
  109 : USRBAT0 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_USRBATx_t_DM9
  110 : USRBAT1 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_USRBATx_t_DM9
  111 : USRBAT2 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_USRBATx_t_DM9
  112 : USRBAT3 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_USRBATx_t_DM9
  113 : USRBAT4 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_USRBATx_t_DM9
  114 : USRBAT5 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_USRBATx_t_DM9
  115 : USRBAT6 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_USRBATx_t_DM9
  116 : USRBAT7 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_USRBATx_t_DM9
  117 : LFTUNEVBAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_LFTUNEVBAT_t_DM9
  120 : PCON0 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PCON0_t_DM9
  131 : P1ALTF typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_P1ALTF_t_DM9
  132 : P2ALTF typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_P2ALTF_t_DM9
  170 : KEY_ID typ=int8_ bnd=e algn=1 stl=DM
  171 : KEY_PID typ=int8_ bnd=e algn=1 stl=DM
  173 : g_b_InCriticalSection typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=bool_t_DM9
  174 : g_b_NMI_occurred typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=bool_t_DM9
  176 : g_Vbat_Value typ=int8_ bnd=g sz=2 algn=2 stl=DM9 tref=uint16_t_DM9
]
__SysInit_sttc {
} #0
----------
----------

