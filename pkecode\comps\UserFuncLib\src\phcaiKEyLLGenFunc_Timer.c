/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_Timer.c 20054 2019-05-10 10:59:25Z dep10330 $
  $Revision: 20054 $
*/

/**
 * @file
 * Implementation of User Functions for timer related tasks.
 */

/*
  Change Log

  MMr (2010-09-01):
  - disabled timer0 IRQ in _timer0_delay_us
  - disabled timer0 IRQ before exit of _timer0_delay_ms
  MMr (2010-09-06):
  - _timer0_delay_ms: changed clock source to AUX
    (allows ~1 ms resolution)
  - _timer0_delay_us, _timer0_delay_ms : fixed reload register value
    (must be number of us/ms minus one for exact timing)
  MMr (2012-06-06):
  - _timer0_delay_ms: added clearing interrupt flag before IDLE
  MMr (2013-02-04):
  - _timer0_delay_* : bugfix: no delay if argument equals 0
  2013-07-29 (MMr):
  - Improved MISRA-C compliance
  2015-10-22 (MMr):
  - Added: functions phcaiKEyLLGenFunc_Timer1_Start_AuxClk,
    phcaiKEyLLGenFunc_Timer1_Start_1kHz, phcaiKEyLLGenFunc_Timer1_Capture,
    phcaiKEyLLGenFunc_Timer1_Stop.
  - Avoid overwriting of full CLKCON3 register, instead only write to TMUXxC bitfield.
  2016-08-09 (MMr):
  - started port to TOKEN-PLUS/ACTIC5G-PLUS, only switches adapted. Register handling to be checked
    (maybe minor changes needed).
  2019-01-10 (MMr):
  - phcaiKEyLLGenFunc_Timer0_Start_us: added.
  2019-02-26 (MMr):
  - phcaiKEyLLGenFunc_Timer1_Cmp_Wait: added.
 */


#include "phcaiKEyLLGenFunc_Timer.h"
#include "ncf29xx.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @addtogroup Timers
 * @{
 */

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_timer0_delay_us( uint16_t u16_us )
{
  if ( u16_us > 0U )
  {
    /* TMUX0C[3:0] = 1 : select AUX Clock (1 MHz) */
    CLKCON3.bits.TMUX0C = 1U;
    /* Reset timer 0. */
    T0CON0.val  = 0x02U;
    /* Select TMUX0, prescaler 2^0. */
    T0CON1.val  = 0x20U;  /* Timer 0 control register 1 [@ 0x95 (149) ] */
                          /* 7   | 6   | [5:4]         | [3:0] */
                          /* RFU | RFU | T0CLKSEL[1:0] | T0PRESC[3:0]                                 */
                          /*  -  |  -  | 2: TMUX0CLK   | T0RegClk = 1 * T0CLK --> 1 MHz = 1 / 1 us */

    /* Set reload value to cycles minus 1 (!). */
    T0RLD.val = u16_us - 1U;
    /* Disable timer0 IRQ */
    /* Note: INTEN0[7] (W0) reads '0', bitfield write allowed. */
    INTEN0.bits.IE_T0 = 0U;
    /* Clear interrupt flag. */
    INTCLR0.val = 0x08U;
    /* Start timer 0 in single shot mode. */
    T0CON0.val = 0x05U;
    /* Wait until timer 0 has expired. */
    while ( (INTFLAG0.val & 0x08U) == 0U )
    {
    }
    /* Clear interrupt flag. */
    INTCLR0.val = 0x08U;
  }
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_timer0_delay_ms( uint16_t u16_ms )
{
  if ( u16_ms > 0U )
  {
    /* TMUX0C[3:0] = 1 : select AUX Clock (1 MHz) */
    CLKCON3.bits.TMUX0C = 1U;

    T0CON0.val  = 0x02U;          /* Timer 0 control register 0 [@ 0x94] */
                                  /* [7:6]                                | 5   | 4   | 3   | 2                | 1     | 0 */
                                  /* T0OUT [1:0]                          | RFU | RFU | RFU | T0SGL            | T0RST | T0RUN */
                                  /* Output line config.: value unchanged |  -  |  -  |  -  | auto reload mode | reset | stopped  */

    /* largest clock divider is T0PRESC=12 means 4096 */
    T0CON1.val  = 0x2AU;          /* Timer 0 control register 1 [@ 0x95] */
                                  /* 7   | 6   | [5:4]         | [3:0] */
                                  /* RFU | RFU | T0CLKSEL[1:0] | T0PRESC[3:0]                                 */
                                  /*  -  |  -  | 2: TMUX0CLK   | T0RegClk = 1024 * T0CLK --> ~ 1kHz = 1 / 1 ms */

    /* Set reload value to cycles minus 1 (!). */
    T0RLD.val  = u16_ms - 1U;     /* Timer 0 reload register [@ 0x98] */
                                  /* [15:8]       | [7:0] */
                                  /* T0RLDH [7:0] | T0RLDL [7:0] */
    /* Clear interrupt flag. */
    INTCLR0.bits.IC_T0 = 1U;

    /* Enable timer 0 interrupt */
    /* Note: INTEN0[7] (W0) reads '0', bitfield write allowed. */
    INTEN0.bits.IE_T0 = 1U;      /* User interrupt enable register 0 [@ 0xB3] */
                                 /* 7   | 6   | 5        | 4        | 3     | 2       | 1        | 0 */
                                 /* RFU | RFU | IE_T1CAP | IE_T1CMP | IE_T0 | IE_PORT | IE_HFNMI | IE_LFNMI */

    T0CON0.val = 0x05U;          /* Start timer 0 in single shot mode */

    /* Enter IDLE mode, repeat while expected flag not set. */
    do
    {
      go_idle();
    }
    while ( (INTFLAG0.val & 0x08U) == 0U );

    INTCLR0.bits.IC_T0 = 1U;     /* Interrupt acknowledge (all bits read '0')*/
    INTEN0.bits.IE_T0  = 0U;     /* Disable timer0 IRQ */
  }
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Timer0_Start_us( uint16_t u16_us )
{
  if ( u16_us > 0u )
  {
    /* TMUX0C[3:0] = 1 : select AUX Clock (1 MHz) */
    CLKCON3.bits.TMUX0C = 1u;
    /* Reset timer 0. */
    T0CON0.val  = 0x02u;
    /* Select TMUX0, prescaler 2^0. */
    T0CON1.val  = 0x20u;  /* Timer 0 control register 1 [@ 0x95 (149) ] */
                          /* 7   | 6   | [5:4]         | [3:0] */
                          /* RFU | RFU | T0CLKSEL[1:0] | T0PRESC[3:0]                                 */
                          /*  -  |  -  | 2: TMUX0CLK   | T0RegClk = 1 * T0CLK --> 1 MHz = 1 / 1 us */

    /* Set reload value to cycles minus 1 (!). */
    T0RLD.val = u16_us - 1u;
    /* Enable timer0 IRQ */
    /* Note: INTEN0[7] (W0) reads '0', bitfield write allowed. */
    INTEN0.bits.IE_T0 = 1u;
    /* Clear interrupt flag. */
    INTCLR0.val = 0x08u;
    /* Start timer 0 in single shot mode (T0SGL=1, T0RUN=1). */
    T0CON0.val = 0x05u;
  }
}

/*-----------------------------------------------------------------------------------------------*/

void  phcaiKEyLLGenFunc_Timer0_Wait(void)
{
  /* If timer 0 is running. */
  if ( (T0CON0.val & 0x01U) != 0x00U )
  {
    /* Wait until timer 0 has expired. */
    while((INTFLAG0.val & 0x08U) == 0x00U)
    {
    }
  }
  /* Clear interrupt flag. */
  INTCLR0.bits.IC_T0 = 1U;
}

/*-----------------------------------------------------------------------------------------------*/

void  phcaiKEyLLGenFunc_Timer1_Start_AuxClk( void )
{
  /* TMUX1C[2:0] = 1 : select AUX Clock (1 MHz) */
  CLKCON3.bits.TMUX1C = 1u;
  /* Reset timer 1, set mode 0. */
  T1CON0.val  = 0x02U;
  /* Select TMUX1, prescaler 2^0. */
  T1CON1.val  = 0x30U;  /* Timer 1 control register 1 */
                        /* 7   | 6   | [5:4]         | [3:0] */
                        /* RFU | RFU | T1CLKSEL[1:0] | T1PRESC[3:0]                                  */
                        /*  -  |  -  | 3: TMUX1CLK   | 0: T1RegClk = 1 * T1CLK --> 1 MHz = 1 / 1 us  */

  //T1CMP.val = 0xFFFFU; //needed?
  /* Clear interrupt flag T1CAP. */
  INTCLR0.val = 0x20U;
  /* Start timer 1. */
  T1CON0.bits.T1RUN = 1U;
}

/*-----------------------------------------------------------------------------------------------*/

void  phcaiKEyLLGenFunc_Timer1_Start_1kHz( void )
{
  /* TMUX1C[2:0] = 1 : select AUX Clock (1 MHz) */
  CLKCON3.bits.TMUX1C = 1u;
  /* Reset timer 1, set mode 0. */
  T1CON0.val  = 0x02U;
  /* Select TMUX1, prescaler 2^10 = 1024. */
  T1CON1.val  = 0x3AU;  /* Timer 1 control register 1 */
                        /* 7   | 6   | [5:4]         | [3:0] */
                        /* RFU | RFU | T1CLKSEL[1:0] | T1PRESC[3:0]                                  */
                        /*  -  |  -  | 3: TMUX1CLK   | 10: T1RegClk = 1024 * T1CLK means 1 MHz/1024 ~ 1 / 1 ms  */

  /* Clear interrupt flag T1CAP. */
  INTCLR0.val = 0x20U;
  /* Start timer 1. */
  T1CON0.bits.T1RUN = 1U;
}

/*-----------------------------------------------------------------------------------------------*/

uint16_t phcaiKEyLLGenFunc_Timer1_Capture( void )
{
  uint16_t u16_res;
  if ( 0u != T1CON0.bits.T1RUN )
  {
    T1CON2.bits.T1MANCAP = 1u;
    while ( T1CON2.bits.T1MANCAP != 0u ) {}  // wait until capture completed.
    u16_res = T1CAP.val;
  }
  else
  {
    u16_res = 0u;
  }
  return u16_res;
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Timer1_Stop( void )
{
  T1CON0.bits.T1RUN = 0u;
  while ( T1CON0.bits.T1RUN != 0u ) {}  // wait until stopped.
}

/*-----------------------------------------------------------------------------------------------*/

void  phcaiKEyLLGenFunc_Timer1_Cmp_Wait(void)
{
  /* If timer 1 is running. */
  if ( (T1CON0.val & 0x01u) != 0x00u )
  {
    /* Wait until timer 1 compare event. */
    while ( (INTFLAG0.val & 0x10u ) == 0u )
    {
    }
  }
  /* Clear interrupt flag. */
  INTCLR0.bits.IC_T1CMP = 1u;
}


/*-----------------------------------------------------------------------------------------------*/

/* @} */
/* @} */

/* eof */
