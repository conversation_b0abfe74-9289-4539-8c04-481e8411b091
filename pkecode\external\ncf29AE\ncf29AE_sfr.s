/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: ncf29AE_sfr.s 22437 2019-10-02 15:23:00Z dep10330 $
  $Revision: 22437 $
*/


/**
 * @file
 * Declaration of the special function registers (SFR) of NCF29AE (TOKEN-SRX2)
 * (User mode) for assembly language.
 */

#ifndef NCF29AE_SFR_S
#define NCF29AE_SFR_S

.undef global data CXPC
.undef global data CXSW
.undef global data P1INS
.undef global data P1OUT
.undef global data P1DIR
.undef global data P1INTDIS
.undef global data P2INS
.undef global data P2OUT
.undef global data P2DIR
.undef global data P2INTDIS
.undef global data P3INS
.undef global data P3OUT
.undef global data P3DIR
.undef global data P3INTDIS
.undef global data IIUCON0
.undef global data IIUSTAT
.undef global data IIUCON1
.undef global data IIUDAT
.undef global data IIUSTATE
.undef global data IIUCON2
.undef global data HTCON
.undef global data I3DCON
.undef global data AESDAT
.undef global data AESCON
.undef global data CRCDAT
.undef global data CRC8DIN
.undef global data GCRCCON0
.undef global data GCRCPOLY
.undef global data GCRCDAT
.undef global data GCRCDIN
.undef global data CPUMCCCNT0
.undef global data CPUMCCCNT1
.undef global data CPUMCCCON
.undef global data WDCON
.undef global data CLKCON0
.undef global data CLKCON1
.undef global data CLKCON2
.undef global data CLKCON3
.undef global data CLKCON4
.undef global data ADCDAT
.undef global data ADCSUM
.undef global data ADCCON
.undef global data RSSICON
.undef global data ULPADDR
.undef global data ULPSEL
.undef global data ULPCON0
.undef global data ULPDAT
.undef global data ULPCON1
.undef global data T0CON0
.undef global data T0CON1
.undef global data T0REG
.undef global data T0RLD
.undef global data T1CON0
.undef global data T1CON1
.undef global data T1CON2
.undef global data T1REG
.undef global data T1CAP
.undef global data T1CMP
.undef global data T2CON0
.undef global data T2CON1
.undef global data T2REG
.undef global data T2RLD
.undef global data RNGDAT
.undef global data RNGCON
.undef global data INTCON
.undef global data INTFLAG0
.undef global data INTFLAG1
.undef global data INTFLAG2
.undef global data INTEN0
.undef global data INTEN1
.undef global data INTEN2
.undef global data SYSINTEN0
.undef global data SYSINTEN1
.undef global data INTSET0
.undef global data INTSET1
.undef global data INTSET2
.undef global data INTCLR0
.undef global data INTCLR1
.undef global data INTCLR2
.undef global data INTVEC
.undef global data LFSHCON
.undef global data PCON0
.undef global data PCON1
.undef global data PCON2
.undef global data PCON5
.undef global data BATSYS0
.undef global data BATSYS1
.undef global data PRESWUP0
.undef global data PRESWUP1
.undef global data PRESWUP2
.undef global data PRESWUP3
.undef global data PRESWUP4
.undef global data P1WRES
.undef global data P2WRES
.undef global data P3WRES
.undef global data LFTUNECH1ACT
.undef global data LFTUNECH2ACT
.undef global data LFTUNECH3ACT
.undef global data USRBAT0
.undef global data USRBAT1
.undef global data USRBAT2
.undef global data USRBAT3
.undef global data USRBAT4
.undef global data USRBAT5
.undef global data USRBAT6
.undef global data USRBAT7
.undef global data LFTUNECH1IMMO
.undef global data LFTUNECH2IMMO
.undef global data LFTUNECH3IMMO
.undef global data SPI0CON0
.undef global data SPI0CON1
.undef global data SPI0DAT
.undef global data SPI0STAT
.undef global data SPI1CON0
.undef global data SPI1CON1
.undef global data SPI1DAT
.undef global data SPI1STAT
.undef global data P1ALTF
.undef global data P2ALTF
.undef global data BITCNT
.undef global data BITSWAP
.undef global data LEDCON
.undef global data INTFLAG3
.undef global data INTEN3
.undef global data INTSET3
.undef global data INTCLR3
.undef global data TXPCON
.undef global data CLKRSTCON
.undef global data VCOCALCON
.undef global data PLLCON
.undef global data TXDAT
.undef global data TXSPC
.undef global data ENCCON0
.undef global data ENCCON1
.undef global data FREQCON0
.undef global data FREQCON1
.undef global data BRGCON
.undef global data FSKCON
.undef global data FSKRMP
.undef global data ASKCON
.undef global data ASKRMP
.undef global data PACON
.undef global data PAPWR
.undef global data PACAPTRIM
.undef global data PALIMIT
.undef global data IDENT
.undef global data LFAEN0
.undef global data LFAEN1
.undef global data LFAEN2
.undef global data LFAEN3
.undef global data LFAEN4
.undef global data LFACON0
.undef global data LFACON1
.undef global data LFACON2
.undef global data LFACON3
.undef global data LFACON4
.undef global data LFACON5
.undef global data LFACON6
.undef global data LFACON7
.undef global data LFACON8
.undef global data LFACON9
.undef global data LFACON10
.undef global data LFACON11
.undef global data LFACON12
.undef global data LFASTATUS
.undef global data LFASENSE
.undef global data WUPCON
.undef global data WUPPATEVN0
.undef global data WUPPATEVN1
.undef global data WUPPATEVN2
.undef global data WUPPATODD0
.undef global data WUPPATODD1
.undef global data WUPPATODD2
.undef global data WUPPATCON
.undef global data PAYRXCON
.undef global data PREDAT
.undef global data RTCCON
.undef global data PRECON2
.undef global data PRECON3
.undef global data PRECON4
.undef global data PRECON5
.undef global data PRESTAT
.undef global data RTCDAT
.undef global data USRBATRGL0
.undef global data USRBATRGL1
.undef global data USRBATRGL2
.undef global data USRBATRGL3
.undef global data USRBATRGL4
.undef global data USRBATRGL5
.undef global data USRBATRGL6
.undef global data USRBATRGL7
.undef global data MSICON0
.undef global data MSICON1
.undef global data MSISTAT0
.undef global data MSISTAT1
.undef global data MSICON2
.undef global data POSTWUPCON
.undef global data POSTWUPCOMP
.undef global data DCDCCON0
.undef global data DCDCCON1
.undef global data DCDCCON2
.undef global data DCDCCON3
.undef global data DCDCCON4
.undef global data RSSICON1
.undef global data RSSICON2
.undef global data RSSICON3
.undef global data RSSICON4
.undef global data RSSICON5
.undef global data RSSICON6
.undef global data RSSIVAL
.undef global data RSSICON7
#endif

// eof
