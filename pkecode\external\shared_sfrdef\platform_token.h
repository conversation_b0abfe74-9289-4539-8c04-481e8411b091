/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: ncf29A1.h 20054 2019-05-10 10:59:25Z dep10330 $
  $Revision: 20054 $
*/

/**
 * @file
 * Declaration of the special function registers (SFR) of the TOKEN
 * platform, including bitfield definitions for many registers.
 */

#ifndef _PLATFORM_TOKEN_
#define _PLATFORM_TOKEN_

/**
 * @defgroup tokenhw TOKEN Hardware Interface
 * Contains the definition of all TOKEN platform Special Function Registers
 * (SFR, also referred to as Hardware Registers), which are documented in the
 * data sheet.
 * Supports these products:
 * NCF29A1 (TOKEN 3D), NCF29A2 (TOKEN 1D),
 * NCF29A3 (TOKEN-LITE 3D), NCF29A4 (TOKEN-LITE 1D),
 * NCF2953 (ACTIC5G 3D),    NCF2954 (ACTIC5G 1D),
 * NCF21A2 (TOKEN-CustomSpec).
 * Note: GPIO ports P22,P23,P24 are only bonded out in ACTIC5G types.
 * Note: Addresses are set by the SFR definitions module (e.g. external\ncf29Ax\ncf29A1.c)
 * which is available in file libncf29A1.a .
 * CHANGE LOG: see NCF29A1.c
 *
 * @{
 */

#include "sfr_types.h"

#ifndef __tct_mrk3e__
#error wrong tool chain version used! please use an mrk3-e version.
#endif


/*---------------------------------------------------------------------------*/
/* Type definitions for each register                                        */
/* Note: in bitfields, LSB first                                             */
/*---------------------------------------------------------------------------*/

/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_CXPC_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t XINSTR         : 1;
    bitfield_t XSTACK         : 1;
    bitfield_t XPMEM          : 1;
    bitfield_t INTLEV         : 4;
    bitfield_t SYSMODE        : 1;
  } bits;
} SFR_CXSW_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10S           : 1;
    bitfield_t P11S           : 1;
    bitfield_t P12S           : 1;
    bitfield_t P13S           : 1;
    bitfield_t P14S           : 1;
    bitfield_t P15S           : 1;
    bitfield_t P16S           : 1;
    bitfield_t P17S           : 1;
  } bits;
} SFR_P1INS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10O           : 1;
    bitfield_t P11O           : 1;
    bitfield_t P12O           : 1;
    bitfield_t P13O           : 1;
    bitfield_t P14O           : 1;
    bitfield_t P15O           : 1;
    bitfield_t P16O           : 1;
    bitfield_t P17O           : 1;
  } bits;
} SFR_P1OUT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10D           : 1;
    bitfield_t P11D           : 1;
    bitfield_t P12D           : 1;
    bitfield_t P13D           : 1;
    bitfield_t P14D           : 1;
    bitfield_t P15D           : 1;
    bitfield_t P16D           : 1;
    bitfield_t P17D           : 1;
  } bits;
} SFR_P1DIR_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20S           : 1;
    bitfield_t P21S           : 1;
    bitfield_t P22S           : 1;
    bitfield_t P23S           : 1;
    bitfield_t P24S           : 1;
    bitfield_t                : 3;
  } bits;
} SFR_P2INS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20O           : 1;
    bitfield_t P21O           : 1;
    bitfield_t P22O           : 1;
    bitfield_t P23O           : 1;
    bitfield_t P24O           : 1;
    bitfield_t                : 3;
  } bits;
} SFR_P2OUT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20D           : 1;
    bitfield_t P21D           : 1;
    bitfield_t P22D           : 1;
    bitfield_t P23D           : 1;
    bitfield_t P24D           : 1;
    bitfield_t                : 3;
  } bits;
} SFR_P2DIR_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10INTDIS      : 1;
    bitfield_t P11INTDIS      : 1;
    bitfield_t P12INTDIS      : 1;
    bitfield_t P13INTDIS      : 1;
    bitfield_t P14INTDIS      : 1;
    bitfield_t P15INTDIS      : 1;
    bitfield_t P16INTDIS      : 1;
    bitfield_t P17INTDIS      : 1;
  } bits;
} SFR_P1INTDIS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20INTDIS      : 1;
    bitfield_t P21INTDIS      : 1;
    bitfield_t P22INTDIS      : 1;
    bitfield_t P23INTDIS      : 1;
    bitfield_t P24INTDIS      : 1;
    bitfield_t                : 3;
  } bits;
} SFR_P2INTDIS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_OBCSEN     : 1;
    bitfield_t                : 7;
  } bits;
} SFR_IIUCON2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_CLKSEL       : 2;
    bitfield_t IIU_DPSEL        : 3;
    bitfield_t IIU_MODSEL       : 2;
    bitfield_t IIU_LSBF         : 1;
  } bits;
} SFR_IIUCON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_BCNTZERO     : 1;
    bitfield_t IIU_FINISHED     : 1;
    bitfield_t IIU_TXWAIT       : 1;
    bitfield_t IIU_ENCSEL       : 2;
    bitfield_t IIU_RST          : 1;
    bitfield_t IIU_LFDEMEN      : 1;
    bitfield_t IIU_LFDATA       : 1;
  } bits;
} SFR_IIUSTAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_BITCNT     : 4;
    bitfield_t                : 3;
    bitfield_t IIU_PRIO       : 1;
  } bits;
} SFR_IIUCON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_IIUDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_STATE      : 3;
    bitfield_t                : 5;
  } bits;
} SFR_IIUSTATE_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t HTMODE         : 3;
    bitfield_t HTOSEL         : 2;
    bitfield_t HTEN           : 1;
    bitfield_t                : 1;
    bitfield_t                : 1;
  } bits;
} SFR_HTCON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_AESDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t AESBC          : 4;
    bitfield_t AESACC         : 1;
    bitfield_t                : 1;
    bitfield_t AESRST         : 1;
    bitfield_t AESRUN         : 1;
  } bits;
} SFR_AESCON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_CRCDAT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_CRC8DIN_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t WDCLR          : 1;
    bitfield_t WDTRIG         : 1;
    bitfield_t WDMODE         : 2;
    bitfield_t WDTIM          : 4;
  } bits;
} SFR_WDCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t CPUCLKCYC      : 5;
    bitfield_t CPUCLKSEL      : 2;
    bitfield_t                : 1;
  } bits;
} SFR_CLKCON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RER_COMP       : 1;
    bitfield_t                : 3;
    bitfield_t MDICLKSEL      : 2;
    bitfield_t LFCLKX2DIS     : 1;
    bitfield_t                : 1;
  } bits;
} SFR_CLKCON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MRCOSC_EN        : 1;
    bitfield_t AUXRCOSC_EN      : 1;
    bitfield_t AUXRCOSC_OUTDIS  : 1;
    bitfield_t                  : 1;
    bitfield_t AESCLKSEL        : 3;
    bitfield_t ADCCLKSEL        : 1;
  } bits;
} SFR_CLKCON2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t TMUX0C         : 4;
    bitfield_t TMUX1C         : 3;
    bitfield_t                : 1;
  } bits;
} SFR_CLKCON3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t CPUAUXDIVSEL     : 2;
    bitfield_t CGAESDIS         : 1;
    bitfield_t                  : 5;
  } bits;
} SFR_CLKCON4_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PREDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IT_MODE           : 2;
    bitfield_t IT_SEL            : 3;
    bitfield_t RTC_SEL           : 2;
    bitfield_t ITRST             : 1;
  } bits;
} SFR_RTCCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LPRC_EN           : 1;
    bitfield_t                   : 1;
    bitfield_t BDRATE            : 2;
    bitfield_t                   : 1;
    bitfield_t PDLFACT           : 1;
    bitfield_t PRERST            : 1;
    bitfield_t DIGFILRST         : 1;
  } bits;
} SFR_PRECON2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t QFACT1            : 6;
    bitfield_t PD1_6DB           : 1;
    bitfield_t DISCH1ACT         : 1;
  } bits;
} SFR_PRECON3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t QFACT2            : 6;
    bitfield_t PD2_6DB           : 1;
    bitfield_t DISCH2ACT         : 1;
  } bits;
} SFR_PRECON4_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t QFACT3            : 6;
    bitfield_t PD3_6DB           : 1;
    bitfield_t DISCH3ACT         : 1;
  } bits;
} SFR_PRECON5_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LPRC_CAL          : 4;
    bitfield_t                   : 1;
    bitfield_t CVTYPE            : 1;
    bitfield_t ERRTOLEN          : 1;
    bitfield_t                   : 1;
  } bits;
} SFR_PRECON6_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t WUP1_LEN          : 5;
    bitfield_t WUPSEL            : 3;
  } bits;
} SFR_PRECON7_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t WUP2_LEN          : 5;
    bitfield_t PDIREFSTUP        : 1;
    bitfield_t                   : 1;
    bitfield_t NEWBYTEOVFHOLD    : 1;
  } bits;
} SFR_PRECON8_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t WUP3_LEN          : 4;
    bitfield_t FIFOEN            : 1;
    bitfield_t DIGFILMODE        : 2;
    bitfield_t                   : 1;
  } bits;
} SFR_PRECON9_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PREPD_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_USRBATRGLx_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t WUP1M          : 1;
    bitfield_t WUP2M          : 1;
    bitfield_t WUP3M          : 1;
    bitfield_t NEWBYTE        : 1;
    bitfield_t BITFAIL        : 1;
    bitfield_t RTC_WUP        : 1;
    bitfield_t IT_WUP         : 1;
    bitfield_t ERRTOL         : 1;
    bitfield_t WUP1MH         : 1;
    bitfield_t WUP2MH         : 1;
    bitfield_t WUP3MH         : 1;
    bitfield_t NEWBYTE_OVF    : 1;
    bitfield_t NEXTFIFODATOK  : 1;
    bitfield_t RTC_WUP_OVF    : 1;
    bitfield_t MODE           : 2;
  } bits;
} SFR_PRESTAT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUP1W0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUP1W1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUP2W0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUP2W1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUP3W0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_PRET_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_PRE3T_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t FSEC          : 4;
    bitfield_t SEC           : 6;
    bitfield_t MIN           : 6;
  } bits;
} SFR_RTCDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t                : 2;
    bitfield_t AGCLOWBWEN     : 1;
    bitfield_t                : 2;
    bitfield_t QLIM           : 3;
  } bits;
} SFR_PRECON10_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RINMODE        : 1;
    bitfield_t                : 1;
    bitfield_t GAINCH1        : 2;
    bitfield_t GAINCH2        : 2;
    bitfield_t GAINCH3        : 2;
  } bits;
} SFR_PRECON11_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct
  {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t ADCDATA        : 10;  /* might have to be treated as signed depending on measurement mode */
    bitfield_t                :  4;
    bitfield_t CONV_OV        :  1;  /* indicate that previous ADCDATA has not been read when new data was generated */
    bitfield_t                :  1;
  } bits;
} SFR_ADCDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct
  {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t CONVSTART      : 1;
    bitfield_t CONVRESET      : 1;
    bitfield_t                : 2;
    bitfield_t BG1V2EN        : 1;
    bitfield_t BG1V2BUFEN     : 1;
    bitfield_t BATMEASEN      : 1;
    bitfield_t TSENSEN        : 1;
    bitfield_t SAMTIM         : 2;
    bitfield_t REFSEL         : 3;
    bitfield_t INSEL          : 3;
  } bits;
} SFR_ADCCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t RSSI_RST            : 1;
    bitfield_t RSSI_OVF            : 3;
    bitfield_t RSSI_HOLD           : 1;
    bitfield_t RSSI_CHANSEL        : 2;
    bitfield_t RSSI_PON            : 1;
    bitfield_t RSSI_RANGEEXTDIS    : 1;
    bitfield_t RSSI_RANGE          : 3;
    bitfield_t                     : 4;
  } bits;
} SFR_RSSICON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_ULPADDR_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_ULPSEL_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t ULPBITAMOUNT   : 3;
    bitfield_t ULPWR_RD       : 1;
    bitfield_t ULPPON         : 1;
    bitfield_t ULPRST         : 1;
    bitfield_t ULPPROGERR     : 1;
    bitfield_t ULPRUN         : 1;
  } bits;
}
SFR_ULPCON0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_ULPDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t                : 7; /* RDT */
    bitfield_t BUSYPROG       : 1; /* read-only */
  } bits;
} SFR_ULPCON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T0RUN          : 1;
    bitfield_t T0RST          : 1;
    bitfield_t T0SGL          : 1;
    bitfield_t                : 3;
    bitfield_t T0OUT          : 2;
  } bits;
} SFR_T0CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T0PRESC        : 4;
    bitfield_t T0CLKSEL       : 2;
    bitfield_t                : 2;
  } bits;
} SFR_T0CON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T0REG_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T0RLD_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T1RUN          : 1;
    bitfield_t T1RST          : 1;
    bitfield_t T1MODE         : 2;
    bitfield_t T1RSTCMP       : 1;
    bitfield_t T1RSTCAP       : 1;
    bitfield_t T1OUT          : 2;
  } bits;
} SFR_T1CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T1PRESC        : 4;
    bitfield_t T1CLKSEL       : 2;
    bitfield_t                : 2;

  } bits;
} SFR_T1CON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T1CAPSRC       : 4;
    bitfield_t T1CAPMODE      : 3;
    bitfield_t T1MANCAP       : 1;
  } bits;
} SFR_T1CON2_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T1REG_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T1CAP_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T1CMP_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T2RUN          : 1;
    bitfield_t T2RST          : 1;
    bitfield_t T2SGL          : 1;
    bitfield_t                : 3;
    bitfield_t T2OUT          : 2;
  } bits;
} SFR_T2CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T2PRESC        : 4;
    bitfield_t T2CLKSEL       : 2;
    bitfield_t                : 2;
  } bits;
} SFR_T2CON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T2REG_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T2RLD_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_RNGDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RNGRST         : 1;
    bitfield_t RNGEN          : 1;
    bitfield_t RNGTRIMOSC     : 1;
    bitfield_t RNGCONFIG      : 2;
    bitfield_t RNGBITSHIFT    : 2;
    bitfield_t RNGRUN         : 1;
  } bits;
} SFR_RNGCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t                : 4;
    bitfield_t SYSINTPRIO     : 2;
    bitfield_t GLOBSYSINTEN   : 1;
    bitfield_t LINTSWCON      : 1;
  } bits;
} SFR_INTCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IF_LF          : 1;
    bitfield_t IF_CX          : 1;
    bitfield_t IF_PORT        : 1;
    bitfield_t IF_T0          : 1;
    bitfield_t IF_T1CMP       : 1;
    bitfield_t IF_T1CAP       : 1;
    bitfield_t IF_ALTPORT     : 1;
    bitfield_t IF_ST0         : 1;
  } bits;
} SFR_INTFLAG0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IF_IIU         : 1;
    bitfield_t IF_ULP         : 1;
    bitfield_t                : 1;
    bitfield_t IF_ADC         : 1;
    bitfield_t IF_AES         : 1;
    bitfield_t IF_RNG         : 1;
    bitfield_t                : 2;
  } bits;
} SFR_INTFLAG1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IF_IT          : 1;
    bitfield_t IF_PP          : 1;
    bitfield_t IF_SP0         : 1;
    bitfield_t IF_SP1         : 1;
    bitfield_t IF_T2          : 1;
    bitfield_t                : 1;
    bitfield_t IF_MSI         : 1;
    bitfield_t IF_VBATBRN     : 1;
  } bits;
} SFR_INTFLAG2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IE_LFNMI       : 1;
    bitfield_t IE_CXNMI       : 1;
    bitfield_t IE_PORT        : 1;
    bitfield_t IE_T0          : 1;
    bitfield_t IE_T1CMP       : 1;
    bitfield_t IE_T1CAP       : 1;
    bitfield_t IE_ALTPORT     : 1;
    bitfield_t                : 1;
  } bits;
} SFR_INTEN0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IE_IIU         : 1;
    bitfield_t IE_ULP         : 1;
    bitfield_t                : 1;
    bitfield_t IE_ADC         : 1;
    bitfield_t IE_AES         : 1;
    bitfield_t IE_RNG         : 1;
    bitfield_t                : 2;
  } bits;
} SFR_INTEN1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IE_IT          : 1;
    bitfield_t IE_PP          : 1;
    bitfield_t IE_SP0         : 1;
    bitfield_t IE_SP1         : 1;
    bitfield_t IE_T2          : 1;
    bitfield_t                : 1;
    bitfield_t IE_MSI         : 1;
    bitfield_t IE_VBATBRN     : 1;
  } bits;
} SFR_INTEN2_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_SYSINTEN0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_SYSINTEN1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IS_LF          : 1;
    bitfield_t IS_CX          : 1;
    bitfield_t IS_PORT        : 1;
    bitfield_t IS_T0          : 1;
    bitfield_t IS_T1CMP       : 1;
    bitfield_t IS_T1CAP       : 1;
    bitfield_t IS_ALTPORT     : 1;
    bitfield_t IS_ST0         : 1;
  } bits;
} SFR_INTSET0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IS_IIU         : 1;
    bitfield_t IS_ULP         : 1;
    bitfield_t                : 1;
    bitfield_t IS_ADC         : 1;
    bitfield_t IS_AES         : 1;
    bitfield_t IS_RNG         : 1;
    bitfield_t                : 2;
  } bits;
} SFR_INTSET1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IS_IT          : 1;
    bitfield_t IS_PP          : 1;
    bitfield_t IS_SP0         : 1;
    bitfield_t IS_SP1         : 1;
    bitfield_t IS_T2          : 1;
    bitfield_t                : 1;
    bitfield_t IS_MSI         : 1;
    bitfield_t IS_VBATBRN     : 1;
  } bits;
} SFR_INTSET2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IC_LF          : 1;
    bitfield_t IC_CX          : 1;
    bitfield_t IC_PORT        : 1;
    bitfield_t IC_T0          : 1;
    bitfield_t IC_T1CMP       : 1;
    bitfield_t IC_T1CAP       : 1;
    bitfield_t IC_ALTPORT     : 1;
    bitfield_t IC_ST0         : 1;
  } bits;
} SFR_INTCLR0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IC_IIU         : 1;
    bitfield_t IC_ULP         : 1;
    bitfield_t                : 1;
    bitfield_t IC_ADC         : 1;
    bitfield_t IC_AES         : 1;
    bitfield_t IC_RNG         : 1;
    bitfield_t                : 2;
  } bits;
} SFR_INTCLR1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IC_IT          : 1;
    bitfield_t IC_PP          : 1;
    bitfield_t IC_SP0         : 1;
    bitfield_t IC_SP1         : 1;
    bitfield_t IC_T2          : 1;
    bitfield_t                : 1;
    bitfield_t IC_MSI         : 1;
    bitfield_t IC_VBATBRN     : 1;
  } bits;
} SFR_INTCLR2_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_INTVEC_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t BATPORFLAG     : 1;
    bitfield_t BATRGLEN       : 1;
    bitfield_t BATRGLRST      : 1;
    bitfield_t                : 5;
  } bits;
} SFR_BATSYS0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t BATRST         : 1;
    bitfield_t                : 7;
  } bits;
} SFR_BATSYS1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t WDTOF          : 1;
    bitfield_t P12C           : 3;
    bitfield_t                : 1;
    bitfield_t P13C           : 3;
    bitfield_t                : 1;
    bitfield_t P14C           : 3;
    bitfield_t                : 1;
    bitfield_t P15C           : 3;
  } bits;
} SFR_PRESWUP0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t                : 1;
    bitfield_t P16C           : 3;
    bitfield_t                : 1;
    bitfield_t P17C           : 3;
    bitfield_t                : 1;
    bitfield_t P20C           : 3;
    bitfield_t                : 1;
    bitfield_t P21C           : 3;
  } bits;
} SFR_PRESWUP1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_PRESWUP2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10WRES        : 1;
    bitfield_t P11WRES        : 1;
    bitfield_t P12WRES        : 1;
    bitfield_t P13WRES        : 1;
    bitfield_t P14WRES        : 1;
    bitfield_t P15WRES        : 1;
    bitfield_t P16WRES        : 1;
    bitfield_t P17WRES        : 1;
  } bits;
} SFR_P1WRES_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20WRES        : 1;
    bitfield_t P21WRES        : 1;
    bitfield_t                : 3;
    bitfield_t P21MRES        : 1;
    bitfield_t                : 2;
  } bits;
} SFR_P2WRES_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_USRBATx_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t LF_CAP_CH1     : 4;
    bitfield_t LF_CAP_CH2     : 4;
    bitfield_t LF_CAP_CH3     : 4;
    bitfield_t LF_CAP_CH1_DIS : 1;
    bitfield_t LF_CAP_CH2_DIS : 1;
    bitfield_t LF_CAP_CH3_DIS : 1;
    bitfield_t                : 1;
  } bits;
} SFR_LFTUNEVBAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t LF_CAP_CH1     : 4;
    bitfield_t LF_CAP_CH2     : 4;
    bitfield_t LF_CAP_CH3     : 4;
    bitfield_t LF_CAP_CH1_DIS : 1;
    bitfield_t LF_CAP_CH2_DIS : 1;
    bitfield_t LF_CAP_CH3_DIS : 1;
    bitfield_t                : 1;
  } bits;
} SFR_LFTUNEVDD_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t STDIS          : 1;
    bitfield_t SHCH1          : 1;
    bitfield_t SHCH2          : 1;
    bitfield_t SHCH3          : 1;
    bitfield_t                : 4;
  } bits;
} SFR_LFSHCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {

    bitfield_t PWRFLD         : 1;
    bitfield_t PWRBAT         : 1;
    bitfield_t PRESW_LF       : 1;
    bitfield_t VBATBRNIND     : 1;
    bitfield_t PRESW_MODE     : 1;
    bitfield_t VDDARGLEN      : 1;
    bitfield_t VDDARST        : 1;
    bitfield_t VDDRST         : 1;
  } bits;
} SFR_PCON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t PWUPIND           : 1;
    bitfield_t PMODE             : 1;
    bitfield_t LFFLD             : 1;
    bitfield_t VBATBRNFLAG       : 1;
    bitfield_t PWRMANLFSTATE     : 1;
    bitfield_t VBATMONEN         : 1;
    bitfield_t VDDABRNFLAG       : 1;
    bitfield_t VDDBRNFLAG        : 1;
  } bits;
} SFR_PCON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LOCKP             : 1;
    bitfield_t R2MSDET           : 1;
    bitfield_t VBATBRNEXT        : 1;
    bitfield_t VBATBRNREG        : 1;
    bitfield_t VBATBRNINDEN      : 1;
    bitfield_t                   : 1;
    bitfield_t VDDABRNREG        : 1;
    bitfield_t VDDBRNREG         : 1;
  } bits;
} SFR_PCON2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t DUPLEX         : 2;
    bitfield_t MODE           : 2;
    bitfield_t CLKPHA         : 1;
    bitfield_t CLKPOL         : 1;
    bitfield_t LSBF           : 1;
    bitfield_t                : 1;
  } bits;
} SFR_SPI0CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t EN             : 1;
    bitfield_t RST            : 1;
    bitfield_t STOP           : 1;
    bitfield_t INTSS          : 1;
    bitfield_t CLOCK          : 3;
    bitfield_t                : 1;
  } bits;
} SFR_SPI0CON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_SPI0DAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t BIT            : 3;
    bitfield_t                : 1;
    bitfield_t RXBFOVF        : 1;
    bitfield_t RXBF           : 1;
    bitfield_t TXBE           : 1;
    bitfield_t BUSY           : 1;
  } bits;
} SFR_SPI0STAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t DUPLEX         : 2;
    bitfield_t MODE           : 2;
    bitfield_t CLKPHA         : 1;
    bitfield_t CLKPOL         : 1;
    bitfield_t LSBF           : 1;
    bitfield_t                : 1;
  } bits;
} SFR_SPI1CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t EN             : 1;
    bitfield_t RST            : 1;
    bitfield_t STOP           : 1;
    bitfield_t INTSS          : 1;
    bitfield_t CLOCK          : 3;
    bitfield_t                : 1;
  } bits;
} SFR_SPI1CON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_SPI1DAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t BIT            : 3;
    bitfield_t                : 1;
    bitfield_t RXBFOVF        : 1;
    bitfield_t RXBF           : 1;
    bitfield_t TXBE           : 1;
    bitfield_t BUSY           : 1;
  } bits;
} SFR_SPI1STAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t P10AF          : 2;
    bitfield_t P11AF          : 2;
    bitfield_t P12AF          : 2;
    bitfield_t P13AF          : 2;
    bitfield_t P14AF          : 2;
    bitfield_t P15AF          : 2;
    bitfield_t P16AF          : 2;
    bitfield_t P17AF          : 2;
  } bits;
} SFR_P1ALTF_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t P20AF          : 2;
    bitfield_t P21AF          : 2;
    bitfield_t P22AF          : 2;
    bitfield_t P23AF          : 2;
    bitfield_t P24AF          : 2;
    bitfield_t                : 6;
  } bits;
} SFR_P2ALTF_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_BITCNT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_BITSWAP_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PREPOLL0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PREPOLL1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PRECON12_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MSI_TLIM    : 6;
    bitfield_t MSI_TCLR    : 1;
    bitfield_t             : 1;
  } bits;
} SFR_MSICON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MSI_OVF_INT_EN  : 1;
    bitfield_t MSI_MOT_INT_EN  : 1;
    bitfield_t MSI_OVF_WUP_EN  : 1;
    bitfield_t MSI_MOT_WUP_EN  : 1;
    bitfield_t MSI_LFA_PU_EN   : 1;
    bitfield_t MSI_LFA_PD_EN   : 1;
    bitfield_t MSI_MODE        : 1;
    bitfield_t MSI_EN          : 1;
  } bits;
} SFR_MSICON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MSI_TREG        : 6;
    bitfield_t                 : 2;
  } bits;
} SFR_MSISTAT0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MSI_OVF_INT     : 1;
    bitfield_t MSI_MOT_INT     : 1;
    bitfield_t MSI_OVF         : 1;
    bitfield_t                 : 2;
    bitfield_t MSI_LFA_PD      : 1;
    bitfield_t                 : 2;
  } bits;
} SFR_MSISTAT1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IF_XORDY       : 1;
    bitfield_t IF_VCOCAL      : 1;
    bitfield_t IF_PLLLOCK     : 1;
    bitfield_t IF_PLLUNLOCK   : 1;
    bitfield_t IF_PARDY       : 1;
    bitfield_t IF_PAILIM      : 1;
    bitfield_t IF_TXBE        : 1;
    bitfield_t IF_TXFIN       : 1;
  } bits;
} SFR_INTFLAG3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IE_XORDY       : 1;
    bitfield_t IE_VCOCAL      : 1;
    bitfield_t IE_PLLLOCK     : 1;
    bitfield_t IE_PLLUNLOCK   : 1;
    bitfield_t IE_PARDY       : 1;
    bitfield_t IE_PAILIM      : 1;
    bitfield_t IE_TXBE        : 1;
    bitfield_t IE_TXFIN       : 1;
  } bits;
} SFR_INTEN3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IS_XORDY       : 1;
    bitfield_t IS_VCOCAL      : 1;
    bitfield_t IS_PLLLOCK     : 1;
    bitfield_t IS_PLLUNLOCK   : 1;
    bitfield_t IS_PARDY       : 1;
    bitfield_t IS_PAILIM      : 1;
    bitfield_t IS_TXBE        : 1;
    bitfield_t IS_TXFIN       : 1;
  } bits;
} SFR_INTSET3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IC_XORDY       : 1;
    bitfield_t IC_VCOCAL      : 1;
    bitfield_t IC_PLLLOCK     : 1;
    bitfield_t IC_PLLUNLOCK   : 1;
    bitfield_t IC_PARDY       : 1;
    bitfield_t IC_PAILIM      : 1;
    bitfield_t IC_TXBE        : 1;
    bitfield_t IC_TXFIN       : 1;
  } bits;
} SFR_INTCLR3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t VDDXOEN         : 1;
    bitfield_t VDDPLLEN        : 1;
    bitfield_t VDDHSEN         : 1;
    bitfield_t XOEN            : 1;
    bitfield_t PLLEN           : 1;
    bitfield_t PAEN            : 1;
    bitfield_t XO_READY        : 1;
    bitfield_t XO_READY_EN     : 1;
  } bits;
} SFR_TXPCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t XODIV2CLKDIS   : 1;
    bitfield_t                : 7;
  } bits;
} SFR_CLKRSTCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t CALRUN             : 1;
    bitfield_t CALRESET           : 1;
    bitfield_t CAL_IDAC_FILT_EN   : 1;
    bitfield_t CAL_IDAC_CTRL      : 5;
  } bits;
} SFR_VCOCALCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t FIXDIV_DIV2_EN         : 1;
    bitfield_t PLL_LOCK_DETECT_EN     : 1;
    bitfield_t PLL_LOCK_DETECT_MODE   : 1;
    bitfield_t PLL_LOCK_DETECT_BLOCK  : 1;
    bitfield_t PLL_LOCK_DETECT_TIME   : 2;
    bitfield_t PLL_UNLOCK_DETECTED    : 1;
    bitfield_t PLL_LOCK_DETECTED      : 1;
  } bits;
} SFR_PLLCON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_TXDAT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_TXSPC_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t TXSTOP         : 1;
    bitfield_t TXRESET        : 1;
    bitfield_t TXPRBSPOLY     : 2;
    bitfield_t TXPRBSINIT     : 1;
    bitfield_t TXBUSY         : 1;
    bitfield_t TXBE           : 1;
    bitfield_t TXBU           : 1;
    bitfield_t DATSRC         : 2;
    bitfield_t DATLSBF        : 1;
    bitfield_t DATINV         : 1;
    bitfield_t DATENC         : 3;
    bitfield_t DATLAST        : 1;
  } bits;
} SFR_ENCCON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t BITCNT         : 4;
    bitfield_t                : 4;
    bitfield_t RPTCNT         : 7;
    bitfield_t RPTFE          : 1;
  } bits;
} SFR_ENCCON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_FREQCON0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_FREQCON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t MAINSC         : 11;
    bitfield_t PRESC          :  3;
    bitfield_t                :  2;
  } bits;
} SFR_BRGCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t FDEVMANT        : 5;
    bitfield_t FDEVEXP         : 3;
  } bits;
} SFR_FSKCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t FRMPMANT        : 5;
    bitfield_t FRMPEXP         : 3;
  } bits;
} SFR_FSKRMP_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t AMH            : 5;
    bitfield_t                : 1;
    bitfield_t RF_MUTE_EN     : 1;
    bitfield_t ASK            : 1;
    bitfield_t AML            : 5;
    bitfield_t                : 3;
 } bits;
} SFR_ASKCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t ARMPMANT        : 6;
    bitfield_t ARMPEXP         : 2;
  } bits;
} SFR_ASKRMP_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t PA_LPF          : 2;
    bitfield_t                 : 1;
    bitfield_t PA_10DBM_EN     : 1;
    bitfield_t PA_CLK_OFF_TIME : 2;
    bitfield_t PA_0DBM_EN      : 1;
    bitfield_t PA_READY        : 1;
  } bits;
} SFR_PACON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PAPWR_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PATRIM_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t PA_IMAX         : 5;
    bitfield_t                 : 1;
    bitfield_t PA_ILIM_EN      : 1;
    bitfield_t PA_ILIM         : 1;
  } bits;
} SFR_PALIMIT_t;
/*---------------------------------------------------------------------------*/


/*---------------------------------------------------------------------------*/
/* Register declarations                                                     */
/*---------------------------------------------------------------------------*/

extern volatile SFR_CXPC_t       chess_storage(DM9/*0x014*/)  CXPC;
extern volatile SFR_CXSW_t       chess_storage(DM9/*0x016*/)  CXSW;
extern volatile SFR_P1INS_t      chess_storage(DM9/*0x019*/)  P1INS;
extern volatile SFR_P1OUT_t      chess_storage(DM9/*0x01A*/)  P1OUT;
extern volatile SFR_P1DIR_t      chess_storage(DM9/*0x01B*/)  P1DIR;
extern volatile SFR_P2INS_t      chess_storage(DM9/*0x01C*/)  P2INS;
extern volatile SFR_P2OUT_t      chess_storage(DM9/*0x01D*/)  P2OUT;
extern volatile SFR_P2DIR_t      chess_storage(DM9/*0x01E*/)  P2DIR;
extern volatile SFR_P1INTDIS_t   chess_storage(DM9/*0x01F*/)  P1INTDIS;
extern volatile SFR_P2INTDIS_t   chess_storage(DM9/*0x020*/)  P2INTDIS;
extern volatile SFR_IIUCON2_t    chess_storage(DM9/*0x021*/)  IIUCON2;
extern volatile SFR_IIUCON0_t    chess_storage(DM9/*0x022*/)  IIUCON0;
extern volatile SFR_IIUSTAT_t    chess_storage(DM9/*0x023*/)  IIUSTAT;
extern volatile SFR_IIUCON1_t    chess_storage(DM9/*0x024*/)  IIUCON1;
extern volatile SFR_IIUDAT_t     chess_storage(DM9/*0x025*/)  IIUDAT;
extern volatile SFR_IIUSTATE_t   chess_storage(DM9/*0x026*/)  IIUSTATE;
extern volatile SFR_HTCON_t      chess_storage(DM9/*0x027*/)  HTCON;
extern volatile SFR_AESDAT_t     chess_storage(DM9/*0x028*/)  AESDAT;
extern volatile SFR_AESCON_t     chess_storage(DM9/*0x02A*/)  AESCON;
extern volatile SFR_CRCDAT_t     chess_storage(DM9/*0x02C*/)  CRCDAT;
extern volatile SFR_CRC8DIN_t    chess_storage(DM9/*0x02E*/)  CRC8DIN;
extern volatile SFR_WDCON_t      chess_storage(DM9/*0x037*/)  WDCON;
extern volatile SFR_CLKCON0_t    chess_storage(DM9/*0x038*/)  CLKCON0;
extern volatile SFR_CLKCON1_t    chess_storage(DM9/*0x039*/)  CLKCON1;
extern volatile SFR_CLKCON2_t    chess_storage(DM9/*0x03A*/)  CLKCON2;
extern volatile SFR_CLKCON3_t    chess_storage(DM9/*0x03D*/)  CLKCON3;
extern volatile SFR_CLKCON4_t    chess_storage(DM9/*0x03F*/)  CLKCON4;
extern volatile SFR_PREDAT_t     chess_storage(DM9/*0x040*/)  PREDAT;
extern volatile SFR_RTCCON_t     chess_storage(DM9/*0x041*/)  RTCCON;
extern volatile SFR_PRECON2_t    chess_storage(DM9/*0x042*/)  PRECON2;
extern volatile SFR_PRECON3_t    chess_storage(DM9/*0x043*/)  PRECON3;
extern volatile SFR_PRECON4_t    chess_storage(DM9/*0x044*/)  PRECON4;
extern volatile SFR_PRECON5_t    chess_storage(DM9/*0x045*/)  PRECON5;
extern volatile SFR_PRECON6_t    chess_storage(DM9/*0x046*/)  PRECON6;
extern volatile SFR_PRECON7_t    chess_storage(DM9/*0x047*/)  PRECON7;
extern volatile SFR_PRECON8_t    chess_storage(DM9/*0x048*/)  PRECON8;
extern volatile SFR_PRECON9_t    chess_storage(DM9/*0x049*/)  PRECON9;
extern volatile SFR_PREPD_t      chess_storage(DM9/*0x04A*/)  PREPD;
extern volatile SFR_USRBATRGLx_t chess_storage(DM9/*0x04C*/)  USRBATRGL0;
extern volatile SFR_USRBATRGLx_t chess_storage(DM9/*0x04D*/)  USRBATRGL1;
extern volatile SFR_USRBATRGLx_t chess_storage(DM9/*0x04E*/)  USRBATRGL2;
extern volatile SFR_USRBATRGLx_t chess_storage(DM9/*0x04F*/)  USRBATRGL3;
extern volatile SFR_USRBATRGLx_t chess_storage(DM9/*0x050*/)  USRBATRGL4;
extern volatile SFR_USRBATRGLx_t chess_storage(DM9/*0x051*/)  USRBATRGL5;
extern volatile SFR_USRBATRGLx_t chess_storage(DM9/*0x052*/)  USRBATRGL6;
extern volatile SFR_USRBATRGLx_t chess_storage(DM9/*0x053*/)  USRBATRGL7;
extern volatile SFR_PRESTAT_t    chess_storage(DM9/*0x05C*/)  PRESTAT;
extern volatile SFR_WUP1W0_t     chess_storage(DM9/*0x05E*/)  WUP1W0;
extern volatile SFR_WUP1W1_t     chess_storage(DM9/*0x060*/)  WUP1W1;
extern volatile SFR_WUP2W0_t     chess_storage(DM9/*0x062*/)  WUP2W0;
extern volatile SFR_WUP2W1_t     chess_storage(DM9/*0x064*/)  WUP2W1;
extern volatile SFR_WUP3W0_t     chess_storage(DM9/*0x066*/)  WUP3W0;
extern volatile SFR_PRET_t       chess_storage(DM9/*0x068*/)  PRET;
extern volatile SFR_PRE3T_t      chess_storage(DM9/*0x06A*/)  PRE3T;
extern volatile SFR_RTCDAT_t     chess_storage(DM9/*0x06C*/)  RTCDAT;
extern volatile SFR_PRECON10_t   chess_storage(DM9/*0x06E*/)  PRECON10;
extern volatile SFR_PRECON11_t   chess_storage(DM9/*0x06F*/)  PRECON11;
extern volatile SFR_ADCDAT_t     chess_storage(DM9/*0x072*/)  ADCDAT;
extern volatile SFR_ADCCON_t     chess_storage(DM9/*0x074*/)  ADCCON;
extern volatile SFR_RSSICON_t    chess_storage(DM9/*0x07C*/)  RSSICON;
extern volatile SFR_ULPADDR_t    chess_storage(DM9/*0x080*/)  ULPADDR;
extern volatile SFR_ULPSEL_t     chess_storage(DM9/*0x082*/)  ULPSEL;
extern volatile SFR_ULPCON0_t    chess_storage(DM9/*0x084*/)  ULPCON0;
extern volatile SFR_ULPDAT_t     chess_storage(DM9/*0x085*/)  ULPDAT;
extern volatile SFR_ULPCON1_t    chess_storage(DM9/*0x08A*/)  ULPCON1;
extern volatile SFR_T0CON0_t     chess_storage(DM9/*0x094*/)  T0CON0;
extern volatile SFR_T0CON1_t     chess_storage(DM9/*0x095*/)  T0CON1;
extern volatile SFR_T0REG_t      chess_storage(DM9/*0x096*/)  T0REG;
extern volatile SFR_T0RLD_t      chess_storage(DM9/*0x098*/)  T0RLD;
extern volatile SFR_T1CON0_t     chess_storage(DM9/*0x09A*/)  T1CON0;
extern volatile SFR_T1CON1_t     chess_storage(DM9/*0x09B*/)  T1CON1;
extern volatile SFR_T1CON2_t     chess_storage(DM9/*0x09C*/)  T1CON2;
extern volatile SFR_T1REG_t      chess_storage(DM9/*0x09E*/)  T1REG;
extern volatile SFR_T1CAP_t      chess_storage(DM9/*0x0A0*/)  T1CAP;
extern volatile SFR_T1CMP_t      chess_storage(DM9/*0x0A2*/)  T1CMP;
extern volatile SFR_T2CON0_t     chess_storage(DM9/*0x0A4*/)  T2CON0;
extern volatile SFR_T2CON1_t     chess_storage(DM9/*0x0A5*/)  T2CON1;
extern volatile SFR_T2REG_t      chess_storage(DM9/*0x0A6*/)  T2REG;
extern volatile SFR_T2RLD_t      chess_storage(DM9/*0x0A8*/)  T2RLD;
extern volatile SFR_RNGDAT_t     chess_storage(DM9/*0x0AA*/)  RNGDAT;
extern volatile SFR_RNGCON_t     chess_storage(DM9/*0x0AC*/)  RNGCON;
extern volatile SFR_INTCON_t     chess_storage(DM9/*0x0AF*/)  INTCON;
extern volatile SFR_INTFLAG0_t   chess_storage(DM9/*0x0B0*/)  INTFLAG0;
extern volatile SFR_INTFLAG1_t   chess_storage(DM9/*0x0B1*/)  INTFLAG1;
extern volatile SFR_INTFLAG2_t   chess_storage(DM9/*0x0B2*/)  INTFLAG2;
extern volatile SFR_INTEN0_t     chess_storage(DM9/*0x0B3*/)  INTEN0;
extern volatile SFR_INTEN1_t     chess_storage(DM9/*0x0B4*/)  INTEN1;
extern volatile SFR_INTEN2_t     chess_storage(DM9/*0x0B5*/)  INTEN2;
extern volatile SFR_SYSINTEN0_t  chess_storage(DM9/*0x0B6*/)  SYSINTEN0;
extern volatile SFR_SYSINTEN1_t  chess_storage(DM9/*0x0B7*/)  SYSINTEN1;
extern volatile SFR_INTSET0_t    chess_storage(DM9/*0x0B8*/)  INTSET0;
extern volatile SFR_INTSET1_t    chess_storage(DM9/*0x0B9*/)  INTSET1;
extern volatile SFR_INTSET2_t    chess_storage(DM9/*0x0BA*/)  INTSET2;
extern volatile SFR_INTCLR0_t    chess_storage(DM9/*0x0BB*/)  INTCLR0;
extern volatile SFR_INTCLR1_t    chess_storage(DM9/*0x0BC*/)  INTCLR1;
extern volatile SFR_INTCLR2_t    chess_storage(DM9/*0x0BD*/)  INTCLR2;
extern volatile SFR_INTVEC_t     chess_storage(DM9/*0x0BE*/)  INTVEC;
extern volatile SFR_BATSYS0_t    chess_storage(DM9/*0x0C0*/)  BATSYS0;
extern volatile SFR_BATSYS1_t    chess_storage(DM9/*0x0C1*/)  BATSYS1;
extern volatile SFR_PRESWUP0_t   chess_storage(DM9/*0x0C2*/)  PRESWUP0;
extern volatile SFR_PRESWUP1_t   chess_storage(DM9/*0x0C4*/)  PRESWUP1;
extern volatile SFR_PRESWUP2_t   chess_storage(DM9/*0x0C6*/)  PRESWUP2;
extern volatile SFR_P1WRES_t     chess_storage(DM9/*0x0C8*/)  P1WRES;
extern volatile SFR_P2WRES_t     chess_storage(DM9/*0x0C9*/)  P2WRES;
extern volatile SFR_USRBATx_t    chess_storage(DM9/*0x0CA*/)  USRBAT0;
extern volatile SFR_USRBATx_t    chess_storage(DM9/*0x0CB*/)  USRBAT1;
extern volatile SFR_USRBATx_t    chess_storage(DM9/*0x0CC*/)  USRBAT2;
extern volatile SFR_USRBATx_t    chess_storage(DM9/*0x0CD*/)  USRBAT3;
extern volatile SFR_USRBATx_t    chess_storage(DM9/*0x0CE*/)  USRBAT4;
extern volatile SFR_USRBATx_t    chess_storage(DM9/*0x0CF*/)  USRBAT5;
extern volatile SFR_USRBATx_t    chess_storage(DM9/*0x0D0*/)  USRBAT6;
extern volatile SFR_USRBATx_t    chess_storage(DM9/*0x0D1*/)  USRBAT7;
extern volatile SFR_LFTUNEVBAT_t chess_storage(DM9/*0x0D2*/)  LFTUNEVBAT;
extern volatile SFR_LFTUNEVDD_t  chess_storage(DM9/*0x0D4*/)  LFTUNEVDD;
extern volatile SFR_LFSHCON_t    chess_storage(DM9/*0x0D6*/)  LFSHCON;
extern volatile SFR_PCON0_t      chess_storage(DM9/*0x0D8*/)  PCON0;
extern volatile SFR_PCON1_t      chess_storage(DM9/*0x0D9*/)  PCON1;
extern volatile SFR_PCON2_t      chess_storage(DM9/*0x0DA*/)  PCON2;
extern volatile SFR_SPI0CON0_t   chess_storage(DM9/*0x0E0*/)  SPI0CON0;
extern volatile SFR_SPI0CON1_t   chess_storage(DM9/*0x0E1*/)  SPI0CON1;
extern volatile SFR_SPI0DAT_t    chess_storage(DM9/*0x0E2*/)  SPI0DAT;
extern volatile SFR_SPI0STAT_t   chess_storage(DM9/*0x0E3*/)  SPI0STAT;
extern volatile SFR_SPI1CON0_t   chess_storage(DM9/*0x0E4*/)  SPI1CON0;
extern volatile SFR_SPI1CON1_t   chess_storage(DM9/*0x0E5*/)  SPI1CON1;
extern volatile SFR_SPI1DAT_t    chess_storage(DM9/*0x0E6*/)  SPI1DAT;
extern volatile SFR_SPI1STAT_t   chess_storage(DM9/*0x0E7*/)  SPI1STAT;
extern volatile SFR_P1ALTF_t     chess_storage(DM9/*0x0EC*/)  P1ALTF;
extern volatile SFR_P2ALTF_t     chess_storage(DM9/*0x0EE*/)  P2ALTF;
extern volatile SFR_BITCNT_t     chess_storage(DM9/*0x0F0*/)  BITCNT;
extern volatile SFR_BITSWAP_t    chess_storage(DM9/*0x0F2*/)  BITSWAP;
extern volatile SFR_PREPOLL0_t   chess_storage(DM9/*0x0F7*/)  PREPOLL0;
extern volatile SFR_PREPOLL1_t   chess_storage(DM9/*0x0F8*/)  PREPOLL1;
extern volatile SFR_PRECON12_t   chess_storage(DM9/*0x0F9*/)  PRECON12;
extern volatile SFR_MSICON0_t    chess_storage(DM9/*0x0FA*/)  MSICON0;
extern volatile SFR_MSICON1_t    chess_storage(DM9/*0x0FB*/)  MSICON1;
extern volatile SFR_MSISTAT0_t   chess_storage(DM9/*0x0FC*/)  MSISTAT0;
extern volatile SFR_MSISTAT1_t   chess_storage(DM9/*0x0FD*/)  MSISTAT1;
extern volatile SFR_INTFLAG3_t   chess_storage(DM9/*0x100*/)  INTFLAG3;
extern volatile SFR_INTEN3_t     chess_storage(DM9/*0x101*/)  INTEN3;
extern volatile SFR_INTSET3_t    chess_storage(DM9/*0x102*/)  INTSET3;
extern volatile SFR_INTCLR3_t    chess_storage(DM9/*0x103*/)  INTCLR3;
extern volatile SFR_TXPCON_t     chess_storage(DM9/*0x104*/)  TXPCON;
extern volatile SFR_CLKRSTCON_t  chess_storage(DM9/*0x105*/)  CLKRSTCON;
extern volatile SFR_VCOCALCON_t  chess_storage(DM9/*0x106*/)  VCOCALCON;
extern volatile SFR_PLLCON_t     chess_storage(DM9/*0x107*/)  PLLCON;
extern volatile SFR_TXDAT_t      chess_storage(DM9/*0x108*/)  TXDAT;
extern volatile SFR_TXSPC_t      chess_storage(DM9/*0x10A*/)  TXSPC;
extern volatile SFR_ENCCON0_t    chess_storage(DM9/*0x10C*/)  ENCCON0;
extern volatile SFR_ENCCON1_t    chess_storage(DM9/*0x10E*/)  ENCCON1;
extern volatile SFR_FREQCON0_t   chess_storage(DM9/*0x110*/)  FREQCON0;
extern volatile SFR_FREQCON1_t   chess_storage(DM9/*0x112*/)  FREQCON1;
extern volatile SFR_BRGCON_t     chess_storage(DM9/*0x114*/)  BRGCON;
extern volatile SFR_FSKCON_t     chess_storage(DM9/*0x116*/)  FSKCON;
extern volatile SFR_FSKRMP_t     chess_storage(DM9/*0x117*/)  FSKRMP;
extern volatile SFR_ASKCON_t     chess_storage(DM9/*0x118*/)  ASKCON;
extern volatile SFR_ASKRMP_t     chess_storage(DM9/*0x11A*/)  ASKRMP;
extern volatile SFR_PACON_t      chess_storage(DM9/*0x11B*/)  PACON;
extern volatile SFR_PAPWR_t      chess_storage(DM9/*0x11C*/)  PAPWR;
extern volatile SFR_PATRIM_t     chess_storage(DM9/*0x11D*/)  PATRIM;
extern volatile SFR_PALIMIT_t    chess_storage(DM9/*0x11E*/)  PALIMIT;

/**@}*/

#endif /* ifdef _PLATFORM_TOKEN_ */
