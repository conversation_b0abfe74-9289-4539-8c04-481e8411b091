/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_HITAG.h 14551 2018-07-09 14:15:46Z dep10330 $
  $Revision: 14551 $
*/

/**
 * @file
 * Declarations of User Functions to support the calculation unit in the IIU
 * (Immobilizer Interface Unit).
 * @todo improve doxygen documentation of constants
 */

#ifndef PHCAIKEYLLGENFUNC_HITAG_H
#define PHCAIKEYLLGENFUNC_HITAG_H

#include "types.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup HITAG  Support for calculation unit in IIU (HITAG shift register)
 * Support functions to access the calculation unit in the Immobilizer
 * Interface Unit (IIU).
 * @{
 */


/*-------------------------------------------------------------------------------*/
/* Definitions                                                                   */
/*-------------------------------------------------------------------------------*/

/* Definition for HTMODE[2:0] - HITAG crypto mode and function selection */
#define HT2_Load16  0U
#define HT3_Load16  1U
#define HT2_Load0   2U
#define HT3_Load0   3U
#define HT2_LF      4U
#define HT3_LF      5U
#define HT2_OWF     6U
#define HT3_OWF     7U


/*-------------------------------------------------------------------------------*/
/* Functions declaration                                                         */
/*-------------------------------------------------------------------------------*/

/**
 * Initializes the IIU to be used in general purpose mode and enables the HITAG crypto unit.
 * Sets Peripheral Clock (PCLK) as clock source, means same clock source as the MRK3 core.
 */
void phcaiKEyLLGenFunc_HITAG_CryptoInit( void );


/**
 * Disables the HITAG crypto unit.
 */
void phcaiKEyLLGenFunc_HITAG_CryptoStop( void );


/**
 * Loads a number of bits from RAM to the calculation unit of the NCF29xx.
 * This is used to initialize the calculation unit according to the
 * specification of the crypto algorithm.
 * The processing of the bits within the calculation unit of the NCF29xx
 * is controlled by the crypto function code, which is given by the corresponding
 * HTMODE encoding (for details see also NCF29xx data sheet).
 * IDLE mode is used to wait for IIU interrupt flag IIU_FINISHED.
 * If less than 8 bits are used in the last byte of the input buffer,
 * it must be MSB aligned.
 *
 * Note: The user is responsible to apply a valid function code to this procedure.
 *       Applicable function codes are: HITAG2: Load 0, HITAG3: Load 0,
 *       HITAG2: Load 16, HITAG3: Load 16, HITAG2: OWF, HITAG3: OWF
 *
 * Note: calculation takes one PCLK clock cycle per bit.
 *
 * @param[in]   pu8_data        Pointer to RAM address where the data to be loaded into the crypto unit is stored.
 * @param[in]   u16_NumBits     Number of bits to be loaded into the crypto unit.
 * @param[in]   u8_cryptoFunc   Function code (HTMODE) to be used.
 *
 * @return      result          TRUE -> execution completed, FALSE-> error
 *
 */
bool_t phcaiKEyLLGenFunc_HITAG_CryptoLoad(
    const uint8_t*  const pu8_data,
    uint16_t        u16_NumBits,
    uint8_t         u8_cryptoFunc );


/**
 * Reads a number of cipher bits from the calculation unit of the NCF29xx.
 * These bits are used either:
 *  - to encrypt/decrypt data bits stored in RAM directly by EXORing
 *  - or to store the cipher bits in RAM.
 *
 * The processing of the bits within the calculation unit of the NCF29xx
 * is controlled by the crypto function code, which is given by the corresponding
 * HTMODE encoding (for details see also NCF29xx data sheet).
 * IDLE mode is used to wait for IIU interrupt flag IIU_FINISHED.
 * If less than 8 bits are used in the last byte of the input or output buffer,
 * it must be MSB aligned.
 *
 * Note: The user is responsible to apply a valid function code to this procedure.
 *       Applicable function codes are: HITAG2: LF, HITAG3: LF
 *
 * Note: calculation takes one PCLK clock cycle per bit.
 *
 * @param[out]  pu8_data        Pointer to RAM address where the received data will be modified.
 * @param[in]   u16_NumBits     Number of bits to be read from the crypto unit.
 * @param[in]   u8_cryptoFunc   Function code (HTMODE) to be used.
 * @param[in]   b_EXOR          Ex-OR cypher bits with RAM data pointed to by 'pu8_data'
 *
 * @return      result  TRUE -> execution completed, FALSE-> error
 *
 */
bool_t phcaiKEyLLGenFunc_HITAG_CryptoGet(
    uint8_t*  pu8_data,
    uint16_t  u16_NumBits,
    uint8_t   u8_cryptoFunc,
    bool_t    b_EXOR );



/**
 * Performs HITAG-3 authentication using the calculation unit.
 * Parameters used as in HITAG-3 transponders.
 * Reads a 96 bit (12 bytes) secret key stored in ULPEE.
 * Outputs are the 16 bit MAC and the 48 bit RESPONSE.
 * Measured execution time with Release build:
 * at 2 MHz CPU clock: 1.03 ms,
 * at 4 MHz CPU clock: 0.65 ms.
 *
 * @param[in]   u16_SK_base_page   First ULPEE page (of three) containing the 12 secret key bytes
 * @param[in]   u8arr_Identifier   Four bytes identifier
 * @param[in]   u8arr_Challenge    Eight bytes challenge (random number) from BS
 * @param[out]  u8arr_MAC          Two bytes Message Authenticator from BS
 * @param[out]  u8arr_Response     Six bytes response towards BS
 *
 */

void phcaiKEyLLGenFunc_HITAG_3_authent(
    /* inputs: */
    const uint16_t u16_SK_base_page,
    const uint8_t  u8arr_Identifier[4],
    const uint8_t  u8arr_Challenge[8],
    /* outputs: */
    uint8_t u8arr_MAC[2],
    uint8_t u8arr_Response[6] );



/*@}*/
/*@}*/

#endif
