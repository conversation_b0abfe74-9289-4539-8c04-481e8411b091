<project name="Project" processor="mrk3" lib="..">
    <file type="c" name="ncf29A1.c" path=""/>
    <file type="a" name="ncf29A1_vector_table_default.s" path=""/>
    <option id="cpp.include" value="../ ../../types/" inherit="1"/>
    <option id="ear.mur" value="on"/>
    <option id="project.dir" value=""/>
    <option id="project.name" value="libncf29A1.a"/>
    <option id="project.postbuild" value="../copy_archive.bat ncf29A1 libncf29A1.a " inherit="1"/>
    <option id="project.type" value="arch"/>
</project>
