/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: ncf29AA.h 20054 2019-05-10 10:59:25Z dep10330 $
  $Revision: 20054 $
*/

/**
 * @file
 * Declaration of the special function registers (SFR) of the TOKEN-SRX
 * (NCF29AA/29AB) platform, including bitfield definitions for many registers.
 * (hardware revision B0 or later, production version: C2)
 */

#ifndef _PLATFORM_SRX_
#define _PLATFORM_SRX_

/**
 * @defgroup tokensrxhw TOKEN-SRX Hardware Interface
 * Contains the definition of all TOKEN-SRX family Special Function Registers
 * (SFR, also referred to as Hardware Registers), which are documented in the
 * data sheet.
 * Supports these products:
 * NCF29AA (TOKEN-SRX 3D), NCF29AB (TOKEN-SRX 1D),
 * NCF215A (ACTIC-SRX-CustomSpec),
 * Note: Certain GPIO ports are only bonded out in ACTIC-SRX types.
 * Note: Addresses are set by the SFR definitions module (e.g. external\ncf29AA\ncf29AA.c)
 * which is available in file libncf29AA.a .
 * CHANGE LOG: see ncf29AA.c
 *
 * @{
 */

#include "sfr_types.h"

#ifndef __tct_mrk3e__
#error wrong tool chain version used! please use an mrk3-e version.
#endif

/*---------------------------------------------------------------------------*/
/* Type definitions for each register                                        */
/* Note: in bitfields, LSB first                                             */
/*---------------------------------------------------------------------------*/

/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_CXPC_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t XINSTR         : 1;
    bitfield_t XSTACK         : 1;
    bitfield_t XPMEM          : 1;
    bitfield_t INTLEV         : 4;
    bitfield_t SYSMODE        : 1;
  } bits;
} SFR_CXSW_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10S           : 1;
    bitfield_t P11S           : 1;
    bitfield_t P12S           : 1;
    bitfield_t P13S           : 1;
    bitfield_t P14S           : 1;
    bitfield_t P15S           : 1;
    bitfield_t P16S           : 1;
    bitfield_t P17S           : 1;
  } bits;
} SFR_P1INS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10O           : 1;
    bitfield_t P11O           : 1;
    bitfield_t P12O           : 1;
    bitfield_t P13O           : 1;
    bitfield_t P14O           : 1;
    bitfield_t P15O           : 1;
    bitfield_t P16O           : 1;
    bitfield_t P17O           : 1;
  } bits;
} SFR_P1OUT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10D           : 1;
    bitfield_t P11D           : 1;
    bitfield_t P12D           : 1;
    bitfield_t P13D           : 1;
    bitfield_t P14D           : 1;
    bitfield_t P15D           : 1;
    bitfield_t P16D           : 1;
    bitfield_t P17D           : 1;
  } bits;
} SFR_P1DIR_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10INTDIS      : 1;
    bitfield_t P11INTDIS      : 1;
    bitfield_t P12INTDIS      : 1;
    bitfield_t P13INTDIS      : 1;
    bitfield_t P14INTDIS      : 1;
    bitfield_t P15INTDIS      : 1;
    bitfield_t P16INTDIS      : 1;
    bitfield_t P17INTDIS      : 1;
  } bits;
} SFR_P1INTDIS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20S           : 1;
    bitfield_t P21S           : 1;
    bitfield_t P22S           : 1;
    bitfield_t P23S           : 1;
    bitfield_t P24S           : 1;
    bitfield_t P25S           : 1;
    bitfield_t P26S           : 1;
    bitfield_t P27S           : 1;
  } bits;
} SFR_P2INS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20O           : 1;
    bitfield_t P21O           : 1;
    bitfield_t P22O           : 1;
    bitfield_t P23O           : 1;
    bitfield_t P24O           : 1;
    bitfield_t P25O           : 1;
    bitfield_t P26O           : 1;
    bitfield_t P27O           : 1;
  } bits;
} SFR_P2OUT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20D           : 1;
    bitfield_t P21D           : 1;
    bitfield_t P22D           : 1;
    bitfield_t P23D           : 1;
    bitfield_t P24D           : 1;
    bitfield_t P25D           : 1;
    bitfield_t P26D           : 1;
    bitfield_t P27D           : 1;
  } bits;
} SFR_P2DIR_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20INTDIS      : 1;
    bitfield_t P21INTDIS      : 1;
    bitfield_t P22INTDIS      : 1;
    bitfield_t P23INTDIS      : 1;
    bitfield_t P24INTDIS      : 1;
    bitfield_t P25INTDIS      : 1;
    bitfield_t P26INTDIS      : 1;
    bitfield_t P27INTDIS      : 1;
  } bits;
} SFR_P2INTDIS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  { /* Note: P31 is no longer available since pin 20 is used as VLFA.        */
    /* Note: P34,P35 are no longer available due to 32kHz XO at pins 26,27.  */
    bitfield_t P30S           : 1;
    bitfield_t                : 1;
    bitfield_t P32S           : 1;
    bitfield_t P33S           : 1;
    bitfield_t                : 4;
  } bits;
} SFR_P3INS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P30O           : 1;
    bitfield_t                : 1;
    bitfield_t P32O           : 1;
    bitfield_t P33O           : 1;
    bitfield_t                : 4;
  } bits;
} SFR_P3OUT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P30D           : 1;
    bitfield_t                : 1;
    bitfield_t P32D           : 1;
    bitfield_t P33D           : 1;
    bitfield_t                : 4;
  } bits;
} SFR_P3DIR_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P30INTDIS      : 1;
    bitfield_t                : 1;
    bitfield_t P32INTDIS      : 1;
    bitfield_t P33INTDIS      : 1;
    bitfield_t                : 4;
  } bits;
} SFR_P3INTDIS_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_CLKSEL       : 2;
    bitfield_t IIU_DPSEL        : 3;
    bitfield_t IIU_MODSEL       : 3;  /* size increased from 2 to 3 bits, bit7 was IIU_LSBF, moved to IIUCON2 */
  } bits;
} SFR_IIUCON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_BCNTZERO     : 1;
    bitfield_t IIU_FINISHED     : 1;
    bitfield_t IIU_TXWAIT       : 1;
    bitfield_t IIU_ENCSEL       : 2;
    bitfield_t IIU_RST          : 1;
    bitfield_t IIU_LFDEMEN      : 1;
    bitfield_t IIU_LFDATA       : 1;
  } bits;
} SFR_IIUSTAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_BITCNT     : 4;
    bitfield_t                : 3;
    bitfield_t IIU_PRIO       : 1;
  } bits;
} SFR_IIUCON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_IIUDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_STATE      : 3;
    bitfield_t                : 5;
  } bits;
} SFR_IIUSTATE_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IIU_OBCSEN     : 1;
    bitfield_t IIU_LSBF       : 1;
    bitfield_t                : 6;
  } bits;
} SFR_IIUCON2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t HTMODE         : 3;
    bitfield_t HTOSEL         : 2;
    bitfield_t HTEN           : 1;
    bitfield_t                : 1;
    bitfield_t                : 1;
  } bits;
} SFR_HTCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LF1FD          : 1;
    bitfield_t LF2FD          : 1;
    bitfield_t LF3FD          : 1;
    bitfield_t LFCLKAUTOSEL   : 1;
    bitfield_t LFCLKSEL       : 2;
    bitfield_t                : 1;
    bitfield_t IMMO3DDIS      : 1;   /* DS name 3DIMMODIS cannot be used as C identifier */
  } bits;
} SFR_I3DCON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_AESDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t AESBC          : 4;
    bitfield_t AESACC         : 1;
    bitfield_t                : 1;
    bitfield_t AESRST         : 1;
    bitfield_t AESRUN         : 1;
  } bits;
} SFR_AESCON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_CRCDAT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_CRC8DIN_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t GCRCBITCNT     : 3;
    bitfield_t                : 1;
    bitfield_t GCRCMSBFIRST   : 1;
    bitfield_t GCRCLEFTALIGN  : 1;
    bitfield_t                : 2;
  } bits;
} SFR_GCRCCON0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_GCRCPOLY_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_GCRCDAT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_GCRCDIN_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_CPUMCCCNT0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_CPUMCCCNT1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RSTCNT         : 1;
    bitfield_t ENCNT          : 1;
    bitfield_t CLKSELCNT      : 1;
    bitfield_t                : 5;
  } bits;
} SFR_CPUMCCCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t WDCLR          : 1;
    bitfield_t WDTRIG         : 1;
    bitfield_t WDMODE         : 2;
    bitfield_t WDTIM          : 4;
  } bits;
} SFR_WDCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t CPUCLKCYC      : 5;
    bitfield_t CPUCLKSEL      : 2;
    bitfield_t                : 1;
  } bits;
} SFR_CLKCON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RER_COMP       : 1;
    bitfield_t                : 3;
    bitfield_t MDICLKSEL      : 2;
    bitfield_t                : 2;
  } bits;
} SFR_CLKCON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MRCOSC_EN        : 1;
    bitfield_t AUXRCOSC_EN      : 1;
    bitfield_t AUXRCOSC_OUTDIS  : 1;
    bitfield_t                  : 1;
    bitfield_t AESCLKSEL        : 3;
    bitfield_t ADCCLKSEL        : 1;
  } bits;
} SFR_CLKCON2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t TMUX0C         : 4;  /* changed */
    bitfield_t TMUX1C         : 3;  /* changed */
    bitfield_t                : 1;
  } bits;
} SFR_CLKCON3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t CPUAUXDIVSEL     : 2;
    bitfield_t CGAESDIS         : 1;
    bitfield_t RTCAUXCLKSEL     : 1;  /* new */
    bitfield_t RTCAUXCLKSELSTAT : 1;  /* new */
    bitfield_t                  : 3;
  } bits;
} SFR_CLKCON4_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct
  {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    /** Note: kept for compatibility. Actually it is 16 bits wide, for using with hardware summation. */
    bitfield_t ADCDATA        : 10;  /* might have to be treated as signed depending on measurement mode */
    bitfield_t                :  6;
  } bits;
} SFR_ADCDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct
  {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t ADCSUMMSB      :  2;  /* Bits 17..16 of summation result, bits 15..0 are the ADCDAT register contents. */
    bitfield_t                :  6;
    bitfield_t SUMCNT         :  8;
  } bits;
} SFR_ADCSUM_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct
  {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t CONVSTART      : 1;
    bitfield_t CONVRESET      : 1;
    bitfield_t POWERON        : 1;
    bitfield_t SAMTIMEXT      : 1;
    bitfield_t BG1V2EN        : 1;
    bitfield_t BG1V2BUFEN     : 1;
    bitfield_t BATMEASEN      : 1;
    bitfield_t TSENSEN        : 1;
    bitfield_t SAMTIM         : 2;
    bitfield_t REFSEL         : 3;
    bitfield_t INSEL          : 3;
  } bits;
} SFR_ADCCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  { /* New SFR structure for narrow-band RSSI unit */
    bitfield_t RSSI_RST            : 1;  /* bits  0.. 0                     */
    bitfield_t RSSI_OVF            : 3;  /* bits  4.. 1                     */
    bitfield_t                     : 3;  /* bits  6.. 4 : RFU -/W0          */
    bitfield_t RSSI_PON            : 1;  /* bits  7.. 7                     */
    bitfield_t                     : 1;  /* bits  8.. 8 : RDT R/W0          */
    bitfield_t RSSI_RANGE          : 3;  /* bits 11.. 9                     */
    bitfield_t                     : 1;  /* bits 12..12                     */
    bitfield_t RSSI_CHANSEL        : 3;  /* bits 15..13                     */
  } bits;
} SFR_RSSICON0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_ULPADDR_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_ULPSEL_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t ULPBITAMOUNT   : 3;
    bitfield_t ULPWR_RD       : 1;
    bitfield_t ULPPON         : 1;
    bitfield_t ULPRST         : 1;
    bitfield_t ULPPROGERR     : 1;
    bitfield_t ULPRUN         : 1;
  } bits;
}
SFR_ULPCON0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_ULPDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t                : 7; /* RDT */
    bitfield_t BUSYPROG       : 1; /* read-only */
  } bits;
} SFR_ULPCON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T0RUN          : 1;
    bitfield_t T0RST          : 1;
    bitfield_t T0SGL          : 1;
    bitfield_t                : 3;
    bitfield_t T0OUT          : 2;
  } bits;
} SFR_T0CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T0PRESC        : 4;
    bitfield_t T0CLKSEL       : 2;
    bitfield_t                : 2;
  } bits;
} SFR_T0CON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T0REG_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T0RLD_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T1RUN          : 1;
    bitfield_t T1RST          : 1;
    bitfield_t T1MODE         : 2;
    bitfield_t T1RSTCMP       : 1;
    bitfield_t T1RSTCAP       : 1;
    bitfield_t T1OUT          : 2;
  } bits;
} SFR_T1CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T1PRESC        : 4;
    bitfield_t T1CLKSEL       : 2;
    bitfield_t                : 2;

  } bits;
} SFR_T1CON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T1CAPSRC       : 4;
    bitfield_t T1CAPMODE      : 3;
    bitfield_t T1MANCAP       : 1;
  } bits;
} SFR_T1CON2_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T1REG_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T1CAP_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T1CMP_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T2RUN          : 1;
    bitfield_t T2RST          : 1;
    bitfield_t T2SGL          : 1;
    bitfield_t                : 3;
    bitfield_t T2OUT          : 2;
  } bits;
} SFR_T2CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t T2PRESC        : 4;
    bitfield_t T2CLKSEL       : 2;
    bitfield_t                : 2;
  } bits;
} SFR_T2CON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T2REG_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_T2RLD_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_RNGDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RNGRST         : 1;
    bitfield_t RNGEN          : 1;
    bitfield_t RNGTRIMOSC     : 1;
    bitfield_t RNGCONFIG      : 2;
    bitfield_t RNGBITSHIFT    : 2;
    bitfield_t RNGRUN         : 1;
  } bits;
} SFR_RNGCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t                : 4;
    bitfield_t SYSINTPRIO     : 2;
    bitfield_t GLOBSYSINTEN   : 1;
    bitfield_t LINTSWCON      : 1;
  } bits;
} SFR_INTCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IF_LF          : 1;
    bitfield_t IF_CX          : 1;
    bitfield_t IF_PORT        : 1;
    bitfield_t IF_T0          : 1;
    bitfield_t IF_T1CMP       : 1;
    bitfield_t IF_T1CAP       : 1;
    bitfield_t IF_ALTPORT     : 1;
    bitfield_t IF_ST0         : 1;
  } bits;
} SFR_INTFLAG0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IF_IIU         : 1;
    bitfield_t IF_ULP         : 1;
    bitfield_t                : 1;
    bitfield_t IF_ADC         : 1;
    bitfield_t IF_AES         : 1;
    bitfield_t IF_RNG         : 1;
    bitfield_t IF_RTCMISC     : 1;
    bitfield_t IF_RSSI        : 1;  /* new */
  } bits;
} SFR_INTFLAG1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IF_IT          : 1;
    bitfield_t IF_PP          : 1;
    bitfield_t IF_SP0         : 1;
    bitfield_t IF_SP1         : 1;
    bitfield_t IF_T2          : 1;
    bitfield_t IF_LFAMON      : 1;  /* new */
    bitfield_t IF_MSI         : 1;
    bitfield_t IF_VBATBRN     : 1;
  } bits;
} SFR_INTFLAG2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IE_LFNMI       : 1;
    bitfield_t IE_CXNMI       : 1;
    bitfield_t IE_PORT        : 1;
    bitfield_t IE_T0          : 1;
    bitfield_t IE_T1CMP       : 1;
    bitfield_t IE_T1CAP       : 1;
    bitfield_t IE_ALTPORT     : 1;
    bitfield_t                : 1;
  } bits;
} SFR_INTEN0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IE_IIU         : 1;
    bitfield_t IE_ULP         : 1;
    bitfield_t                : 1;
    bitfield_t IE_ADC         : 1;
    bitfield_t IE_AES         : 1;
    bitfield_t IE_RNG         : 1;
    bitfield_t IE_RTCMISC     : 1;
    bitfield_t IE_RSSI        : 1;  /* new */
  } bits;
} SFR_INTEN1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IE_IT          : 1;
    bitfield_t IE_PP          : 1;
    bitfield_t IE_SP0         : 1;
    bitfield_t IE_SP1         : 1;
    bitfield_t IE_T2          : 1;
    bitfield_t IE_LFAMON      : 1;  /* new */
    bitfield_t IE_MSI         : 1;
    bitfield_t IE_VBATBRN     : 1;
  } bits;
} SFR_INTEN2_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_SYSINTEN0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_SYSINTEN1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IS_LF          : 1;
    bitfield_t IS_CX          : 1;
    bitfield_t IS_PORT        : 1;
    bitfield_t IS_T0          : 1;
    bitfield_t IS_T1CMP       : 1;
    bitfield_t IS_T1CAP       : 1;
    bitfield_t IS_ALTPORT     : 1;
    bitfield_t IS_ST0         : 1;
  } bits;
} SFR_INTSET0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IS_IIU         : 1;
    bitfield_t IS_ULP         : 1;
    bitfield_t                : 1;
    bitfield_t IS_ADC         : 1;
    bitfield_t IS_AES         : 1;
    bitfield_t IS_RNG         : 1;
    bitfield_t IS_RTCMISC     : 1;
    bitfield_t IS_RSSI        : 1;  /* new */
  } bits;
} SFR_INTSET1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IS_IT          : 1;
    bitfield_t IS_PP          : 1;
    bitfield_t IS_SP0         : 1;
    bitfield_t IS_SP1         : 1;
    bitfield_t IS_T2          : 1;
    bitfield_t IS_LFAMON      : 1;  /* new */
    bitfield_t IS_MSI         : 1;
    bitfield_t IS_VBATBRN     : 1;
  } bits;
} SFR_INTSET2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IC_LF          : 1;
    bitfield_t IC_CX          : 1;
    bitfield_t IC_PORT        : 1;
    bitfield_t IC_T0          : 1;
    bitfield_t IC_T1CMP       : 1;
    bitfield_t IC_T1CAP       : 1;
    bitfield_t IC_ALTPORT     : 1;
    bitfield_t IC_ST0         : 1;
  } bits;
} SFR_INTCLR0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IC_IIU         : 1;
    bitfield_t IC_ULP         : 1;
    bitfield_t                : 1;
    bitfield_t IC_ADC         : 1;
    bitfield_t IC_AES         : 1;
    bitfield_t IC_RNG         : 1;
    bitfield_t IC_RTCMISC     : 1;
    bitfield_t IC_RSSI        : 1;  /* new */
  } bits;
} SFR_INTCLR1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IC_IT          : 1;
    bitfield_t IC_PP          : 1;
    bitfield_t IC_SP0         : 1;
    bitfield_t IC_SP1         : 1;
    bitfield_t IC_T2          : 1;
    bitfield_t IC_LFAMON      : 1;  /* new */
    bitfield_t IC_MSI         : 1;
    bitfield_t IC_VBATBRN     : 1;
  } bits;
} SFR_INTCLR2_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_INTVEC_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t STDIS          : 1;
    bitfield_t SHCH1          : 1;
    bitfield_t SHCH2          : 1;
    bitfield_t SHCH3          : 1;
    bitfield_t                : 4;
  } bits;
} SFR_LFSHCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {

    bitfield_t PWRFLD         : 1;
    bitfield_t PWRBAT         : 1;
    bitfield_t PRESW_LF       : 1;
    bitfield_t VBATBRNIND     : 1;
    bitfield_t PRESW_MODE     : 1;
    bitfield_t VDDARGLEN      : 1;
    bitfield_t VDDARST        : 1;
    bitfield_t VDDRST         : 1;
  } bits;
} SFR_PCON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t PWUPIND           : 1;
    bitfield_t PMODE             : 1;
    bitfield_t LFFLD             : 1;
    bitfield_t VBATBRNFLAG       : 1;
    bitfield_t PWRMANLFSTATE     : 1;
    bitfield_t VBATMONEN         : 1;
    bitfield_t VDDABRNFLAG       : 1;
    bitfield_t VDDBRNFLAG        : 1;
  } bits;
} SFR_PCON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LOCKP             : 1;
    bitfield_t R2MSDET           : 1;
    bitfield_t VBATBRNEXT        : 1;
    bitfield_t VBATBRNREG        : 1;
    bitfield_t VBATBRNINDEN      : 1;
    bitfield_t                   : 1;
    bitfield_t VDDABRNREG        : 1;
    bitfield_t VDDBRNREG         : 1;
  } bits;
} SFR_PCON2_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PCON5_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t BATPORFLAG     : 1;
    bitfield_t BATRGLEN       : 1;
    bitfield_t BATRGLRST      : 1;
    bitfield_t                : 5;
  } bits;
} SFR_BATSYS0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t BATRST         : 1;
    bitfield_t BATDRGLDIS1U   : 1;  /* new */
    bitfield_t                : 1;
    bitfield_t BATDRGLFBEN    : 1;  /* new */
    bitfield_t                : 4;
  } bits;
} SFR_BATSYS1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t WDTOF          : 1;
    bitfield_t P12C           : 3;
    bitfield_t WDTOFWUPEN     : 1;  /* new */
    bitfield_t P13C           : 3;
    bitfield_t                : 1;
    bitfield_t P14C           : 3;
    bitfield_t                : 1;
    bitfield_t P15C           : 3;
  } bits;
} SFR_PRESWUP0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t                : 1;
    bitfield_t P16C           : 3;
    bitfield_t                : 1;
    bitfield_t P17C           : 3;
    bitfield_t P21MRES0       : 1;
    bitfield_t P20C           : 3;
    bitfield_t P21MRES1       : 1;
    bitfield_t P21C           : 3;
  } bits;
} SFR_PRESWUP1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t                : 1;
    bitfield_t P22C           : 3;
    bitfield_t                : 1;
    bitfield_t P23C           : 3;
    bitfield_t                : 1;
    bitfield_t P24C           : 3;
    bitfield_t                : 1;
    bitfield_t P25C           : 3;
  } bits;
} SFR_PRESWUP2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t                : 1;
    bitfield_t P26C           : 3;
    bitfield_t                : 1;
    bitfield_t P27C           : 3;
    bitfield_t                : 1;
    bitfield_t P30C           : 3;
    bitfield_t                : 4;  /* was P31C */
  } bits;
} SFR_PRESWUP3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t                : 1;
    bitfield_t P32C           : 3;
    bitfield_t                : 1;
    bitfield_t P33C           : 3;
    bitfield_t                : 1;
    bitfield_t P34C           : 3;
    bitfield_t                : 1;
    bitfield_t P35C           : 3;
  } bits;
} SFR_PRESWUP4_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P10WRES        : 1;
    bitfield_t P11WRES        : 1;
    bitfield_t P12WRES        : 1;
    bitfield_t P13WRES        : 1;
    bitfield_t P14WRES        : 1;
    bitfield_t P15WRES        : 1;
    bitfield_t P16WRES        : 1;
    bitfield_t P17WRES        : 1;
  } bits;
} SFR_P1WRES_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P20WRES        : 1;
    bitfield_t P21WRES        : 1;
    bitfield_t P22WRES        : 1;
    bitfield_t P23WRES        : 1;
    bitfield_t P24WRES        : 1;
    bitfield_t P25WRES        : 1;
    bitfield_t P26WRES        : 1;
    bitfield_t P27WRES        : 1;
  } bits;
} SFR_P2WRES_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t P30WRES        : 1;
    bitfield_t                : 1;
    bitfield_t P32WRES        : 1;
    bitfield_t P33WRES        : 1;
    bitfield_t P34WRES        : 1;
    bitfield_t P35WRES        : 1;
    bitfield_t                : 2;
  } bits;
} SFR_P3WRES_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFTUNECH1ACT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFTUNECH2ACT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFTUNECH3ACT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_USRBATx_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFTUNECH1IMMO_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFTUNECH2IMMO_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFTUNECH3IMMO_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t DUPLEX         : 2;
    bitfield_t MODE           : 2;
    bitfield_t CLKPHA         : 1;
    bitfield_t CLKPOL         : 1;
    bitfield_t LSBF           : 1;
    bitfield_t                : 1;
  } bits;
} SFR_SPI0CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t EN             : 1;
    bitfield_t RST            : 1;
    bitfield_t STOP           : 1;
    bitfield_t INTSS          : 1;
    bitfield_t CLOCK          : 3;
    bitfield_t                : 1;
  } bits;
} SFR_SPI0CON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_SPI0DAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t BIT            : 3;
    bitfield_t                : 1;
    bitfield_t RXBFOVF        : 1;
    bitfield_t RXBF           : 1;
    bitfield_t TXBE           : 1;
    bitfield_t BUSY           : 1;
  } bits;
} SFR_SPI0STAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t DUPLEX         : 2;
    bitfield_t MODE           : 2;
    bitfield_t CLKPHA         : 1;
    bitfield_t CLKPOL         : 1;
    bitfield_t LSBF           : 1;
    bitfield_t                : 1;
  } bits;
} SFR_SPI1CON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t EN             : 1;
    bitfield_t RST            : 1;
    bitfield_t STOP           : 1;
    bitfield_t INTSS          : 1;
    bitfield_t CLOCK          : 3;
    bitfield_t                : 1;
  } bits;
} SFR_SPI1CON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_SPI1DAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t BIT            : 3;
    bitfield_t                : 1;
    bitfield_t RXBFOVF        : 1;
    bitfield_t RXBF           : 1;
    bitfield_t TXBE           : 1;
    bitfield_t BUSY           : 1;
  } bits;
} SFR_SPI1STAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t P10AF          : 2;
    bitfield_t P11AF          : 2;
    bitfield_t P12AF          : 2;
    bitfield_t P13AF          : 2;
    bitfield_t P14AF          : 2;
    bitfield_t P15AF          : 2;
    bitfield_t P16AF          : 2;
    bitfield_t P17AF          : 2;
  } bits;
} SFR_P1ALTF_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t P20AF          : 2;
    bitfield_t P21AF          : 2;
    bitfield_t P22AF          : 2;
    bitfield_t P23AF          : 2;
    bitfield_t P24AF          : 2;
    bitfield_t P25AF          : 2;
    bitfield_t P26AF          : 2;
    bitfield_t P27AF          : 2;
  } bits;
} SFR_P2ALTF_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_BITCNT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_BITSWAP_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LEDRANGE0      : 1;
    bitfield_t LEDRANGE1      : 1;
    bitfield_t LEDRANGE2      : 1;
    bitfield_t LEDRANGE3      : 1;
    bitfield_t                : 4;
  } bits;
} SFR_LEDCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IF_XORDY       : 1;
    bitfield_t IF_VCOCAL      : 1;
    bitfield_t IF_PLLLOCK     : 1;
    bitfield_t IF_PLLUNLOCK   : 1;
    bitfield_t IF_PARDY       : 1;
    bitfield_t IF_PAILIM      : 1;
    bitfield_t IF_TXBE        : 1;
    bitfield_t IF_TXFIN       : 1;
  } bits;
} SFR_INTFLAG3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IE_XORDY       : 1;
    bitfield_t IE_VCOCAL      : 1;
    bitfield_t IE_PLLLOCK     : 1;
    bitfield_t IE_PLLUNLOCK   : 1;
    bitfield_t IE_PARDY       : 1;
    bitfield_t IE_PAILIM      : 1;
    bitfield_t IE_TXBE        : 1;
    bitfield_t IE_TXFIN       : 1;
  } bits;
} SFR_INTEN3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IS_XORDY       : 1;
    bitfield_t IS_VCOCAL      : 1;
    bitfield_t IS_PLLLOCK     : 1;
    bitfield_t IS_PLLUNLOCK   : 1;
    bitfield_t IS_PARDY       : 1;
    bitfield_t IS_PAILIM      : 1;
    bitfield_t IS_TXBE        : 1;
    bitfield_t IS_TXFIN       : 1;
  } bits;
} SFR_INTSET3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IC_XORDY       : 1;
    bitfield_t IC_VCOCAL      : 1;
    bitfield_t IC_PLLLOCK     : 1;
    bitfield_t IC_PLLUNLOCK   : 1;
    bitfield_t IC_PARDY       : 1;
    bitfield_t IC_PAILIM      : 1;
    bitfield_t IC_TXBE        : 1;
    bitfield_t IC_TXFIN       : 1;
  } bits;
} SFR_INTCLR3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t VDDXOEN         : 1;
    bitfield_t VDDPLLEN        : 1;
    bitfield_t VDDHSEN         : 1;
    bitfield_t XOEN            : 1;
    bitfield_t PLLEN           : 1;
    bitfield_t PAEN            : 1;
    bitfield_t XO_READY        : 1;
    bitfield_t XO_READY_EN     : 1;
  } bits;
} SFR_TXPCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t XODIV2CLKDIS   : 1;
    bitfield_t                : 7;
  } bits;
} SFR_CLKRSTCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t CALRUN             : 1;
    bitfield_t CALRESET           : 1;
    bitfield_t CAL_IDAC_FILT_EN   : 1;
    bitfield_t CAL_IDAC_CTRL      : 5;
  } bits;
} SFR_VCOCALCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t FIXDIV_DIV2_EN         : 1;
    bitfield_t PLL_LOCK_DETECT_EN     : 1;
    bitfield_t PLL_LOCK_DETECT_MODE   : 1;
    bitfield_t PLL_LOCK_DETECT_BLOCK  : 1;
    bitfield_t PLL_LOCK_DETECT_TIME   : 2;
    bitfield_t PLL_UNLOCK_DETECTED    : 1;
    bitfield_t PLL_LOCK_DETECTED      : 1;
  } bits;
} SFR_PLLCON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_TXDAT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_TXSPC_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t TXSTOP         : 1;
    bitfield_t TXRESET        : 1;
    bitfield_t TXPRBSPOLY     : 2;
    bitfield_t TXPRBSINIT     : 1;
    bitfield_t TXBUSY         : 1;
    bitfield_t TXBE           : 1;
    bitfield_t TXBU           : 1;
    bitfield_t DATSRC         : 2;
    bitfield_t DATLSBF        : 1;
    bitfield_t DATINV         : 1;
    bitfield_t DATENC         : 3;
    bitfield_t DATLAST        : 1;
  } bits;
} SFR_ENCCON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t BITCNT         : 4;
    bitfield_t TXCLKEN        : 1;
    bitfield_t TXCLKSRC       : 2;
    bitfield_t TXCLKINV       : 1;
    bitfield_t RPTCNT         : 7;
    bitfield_t RPTFE          : 1;
  } bits;
} SFR_ENCCON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_FREQCON0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_FREQCON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t MAINSC         : 11;
    bitfield_t PRESC          :  3;
    bitfield_t                :  2;
  } bits;
} SFR_BRGCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t FDEVMANT        : 5;
    bitfield_t FDEVEXP         : 3;
  } bits;
} SFR_FSKCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t FRMPMANT        : 5;
    bitfield_t FRMPEXP         : 3;
  } bits;
} SFR_FSKRMP_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t AMH            : 5;
    bitfield_t                : 1;
    bitfield_t RF_MUTE_EN     : 1;
    bitfield_t ASK            : 1;
    bitfield_t AML            : 5;
    bitfield_t                : 3;
 } bits;
} SFR_ASKCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t ARMPMANT        : 6;
    bitfield_t ARMPEXP         : 2;
  } bits;
} SFR_ASKRMP_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t PA_LPF          : 2;
    bitfield_t                 : 1;
    bitfield_t PA_12DBM_EN     : 1;
    bitfield_t PA_CLK_OFF_TIME : 2;
    bitfield_t PA_6DBM_EN      : 1;
    bitfield_t PA_READY        : 1;
  } bits;
} SFR_PACON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PAPWR_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t PA_CAP_FINE     : 3;
    bitfield_t PA_CAP_COARSE   : 2;
    bitfield_t                 : 3;
  } bits;
} SFR_PACAPTRIM_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t PA_IMAX         : 5;
    bitfield_t                 : 1;
    bitfield_t PA_ILIM_EN      : 1;
    bitfield_t PA_ILIM         : 1;
  } bits;
} SFR_PALIMIT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_IDENT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LFA_EN_LDO              : 1;
    bitfield_t                         : 1;   /* R/W0 */
    bitfield_t LFA_EN_XTAL_32K         : 1;
    bitfield_t LFA_EN_XTAL_32K_OK_FLAG : 1;
    bitfield_t LFA_EN_IFFILT_CAL       : 1;
    bitfield_t                         : 1;   /* -/W0, was LFA_EN_DEMOD_CMF_SLICER, removed for current SRX(1) */
    bitfield_t LFA_EN_DEMOD_MMF_SLICER : 1;
    bitfield_t LFA_EN_DEMOD_MMF        : 1;
  } bits;
} SFR_LFAEN0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LFA_EN_PLL_PFD              : 1;
    bitfield_t LFA_EN_PLL_VCO              : 1;
    bitfield_t LFA_EN_PLL_NDIV             : 1;
    bitfield_t LFA_EN_PLL_LO               : 1;
    bitfield_t LFA_EN_PLL_CLK_IFFILT_CAL   : 1;
    bitfield_t LFA_EN_PLL_DCDC_CLK         : 1;
    bitfield_t LFA_EN_PLL_CLK_TIMER        : 1;
    bitfield_t LFA_EN_XTAL_CLK_DEMOD       : 1;
  } bits;
} SFR_LFAEN1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LFA_CH1_EN_AGC_RF           : 1;
    bitfield_t LFA_CH1_EN_IF_MIXER         : 1;
    bitfield_t LFA_CH1_EN_IFFILT           : 1;
    bitfield_t LFA_CH1_EN_AGC1_IF          : 1;
    bitfield_t LFA_CH1_EN_AGC2_IF          : 1;
    bitfield_t LFA_CH1_EN_RECT_I           : 1;
    bitfield_t LFA_CH1_EN_RECT_Q           : 1;
    bitfield_t                             : 1;  /* RFU -/W0 */
  } bits;
} SFR_LFAEN2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LFA_CH2_EN_AGC_RF           : 1;
    bitfield_t LFA_CH2_EN_IF_MIXER         : 1;
    bitfield_t LFA_CH2_EN_IFFILT           : 1;
    bitfield_t LFA_CH2_EN_AGC1_IF          : 1;
    bitfield_t LFA_CH2_EN_AGC2_IF          : 1;
    bitfield_t LFA_CH2_EN_RECT_I           : 1;
    bitfield_t LFA_CH2_EN_RECT_Q           : 1;
    bitfield_t                             : 1;  /* RFU -/W0 */
  } bits;
} SFR_LFAEN3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LFA_CH3_EN_AGC_RF           : 1;
    bitfield_t LFA_CH3_EN_IF_MIXER         : 1;
    bitfield_t LFA_CH3_EN_IFFILT           : 1;
    bitfield_t LFA_CH3_EN_AGC1_IF          : 1;
    bitfield_t LFA_CH3_EN_AGC2_IF          : 1;
    bitfield_t LFA_CH3_EN_RECT_I           : 1;
    bitfield_t LFA_CH3_EN_RECT_Q           : 1;
    bitfield_t                             : 1;  /* RFU -/W0 */
  } bits;
} SFR_LFAEN4_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t LFA_IF_AGC1_RESET           : 1;
    bitfield_t LFA_IF_AGC2_RESET           : 1;
    bitfield_t LFA_BRNEN                   : 1;
    bitfield_t LFA_BRNFLAG                 : 1;
    bitfield_t LFA_BRNREG                  : 1;
    bitfield_t                             : 1;  /* RFU R/W0 */
    bitfield_t LFA_RF_AGC_RESET            : 1;
    bitfield_t                             : 1;  /* RFU R/W0 */
  } bits;
} SFR_LFACON0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFACON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFACON2_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFACON4_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFACON5_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t                               : 2;  /* bits 1..0 : RDT R/W0 */
    bitfield_t LFA_SET_XTAL_GM               : 2;  /* bits 3..2            */
    bitfield_t                               : 2;  /* bits 5..4 : RDT R/W0 */
    bitfield_t LFA_ISO_RELEASE               : 1;  /* bit  6               */
    bitfield_t LFA_EN_AGC_FASTDECAY          : 1;  /* bit  7               */
  } bits;
} SFR_LFACON6_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFACON7_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFACON8_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct {
    bitfield_t                       : 4;  /* RFU */
    bitfield_t LFA_XO32K_FAIL        : 1;
    bitfield_t LFA_PLL_LD            : 1;
    bitfield_t LFA_XO32K_FAILREG     : 1;
    bitfield_t LFA_DCDC_FAILREG      : 1;
  } bits;
} SFR_LFASTATUS_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_LFASENSE_t;  /* LFA_SET_SENS_MODE[7..0] */
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct {
    bitfield_t                       : 1;  /* bit 0 : RDT R/W0       */
    bitfield_t EN_WUPA               : 1;
    bitfield_t EN_WUPB               : 1;
    bitfield_t EN_WUPC               : 1;
    bitfield_t EN_CORR               : 1;
    bitfield_t WUPRX_EN_PAYLOAD      : 1;
    bitfield_t WUPPAT_SEL            : 2;  /* bits  7.. 6            */
    bitfield_t SCM_CFG               : 4;  /* bits 11.. 8            */
    bitfield_t                       : 4;  /* bits 15..12 : RFU -/W0 */
  } bits;
} SFR_WUPCON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUPPATEVN0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUPPATEVN1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUPPATEVN2_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUPPATODD0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUPPATODD1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_WUPPATODD2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t WUPLEN                : 6;  /* bits  5.. 0 (mask=003F) */
    bitfield_t EN_SEG_CORR           : 1;  /* bits      6 (mask=0040) */
    bitfield_t EN_STRICT_CORR        : 1;  /* bits      7 (mask=0080) */
    bitfield_t ERRTOL                : 3;  /* bits 10.. 8 (mask=0700) */
    bitfield_t MMF                   : 1;  /* bits     11 (mask=0800) */
    bitfield_t CV2LEN                : 4;  /* bits 15..12 (mask=F000) */
  } bits;
} SFR_WUPPATCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t                       : 2;  /* bits  1.. 0 : set to 11b       */
    bitfield_t WUPRXSELA             : 1;  /* bits      2                    */
    bitfield_t WUPRXSELB             : 1;  /* bits      3                    */
    bitfield_t WUPRXSELC             : 1;  /* bits      4                    */
    bitfield_t                       : 3;  /* bits  7.. 5 : set to 100b      */
    bitfield_t RESTARTONWUP          : 1;  /* bits      8                    */
    bitfield_t                       : 1;  /* bits      9 : RDT R/W0         */
    bitfield_t                       : 6;  /* bits 15..10 : RDT              */
  } bits;
} SFR_PAYRXCON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_PREDAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t IT_MODE           : 2;
    bitfield_t IT_SEL            : 3;
    bitfield_t RTC_SEL           : 2;
    bitfield_t ITRST             : 1;
  } bits;
} SFR_RTCCON_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t XO32K_FAIL_MASK   : 1;   /* changed */
    bitfield_t RINMMODE          : 1;
    bitfield_t                   : 4;   /* changed */
    bitfield_t PRERST            : 1;
    bitfield_t                   : 1;
  } bits;
} SFR_PRECON2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t QFACT1            : 6;
    bitfield_t                   : 2;
  } bits;
} SFR_PRECON3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t QFACT2            : 6;
    bitfield_t                   : 2;
  } bits;
} SFR_PRECON4_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t QFACT3            : 6;
    bitfield_t                   : 2;
  } bits;
} SFR_PRECON5_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {                                 /* In TOKEN[-PLUS]:                      */
    bitfield_t WUPAM          : 1;  /* was WUP1M                             */
    bitfield_t WUPBM          : 1;  /* was WUP2M                             */
    bitfield_t WUPCM          : 1;  /* was WUP3M                             */
    bitfield_t NEWBYTE        : 1;  /* was NEWBYTE                           */
    bitfield_t                : 1;  /* was BITFAIL (SRX: PMCV flag removed)  */
    bitfield_t RTC_WUP        : 1;  /* was RTC_WUP                           */
    bitfield_t IT_WUP         : 1;  /* was IT_WUP                            */
    bitfield_t                : 1;  /* was ERRTOL                            */
    bitfield_t WUPAMH         : 1;  /* was WUP1MH                            */
    bitfield_t WUPBMH         : 1;  /* was WUP2MH                            */
    bitfield_t WUPCMH         : 1;  /* was WUP3MH                            */
    bitfield_t NEWBYTE_OVF    : 1;  /* was NEWBYTE_OVF                       */
    bitfield_t                : 1;  /* was NEXTFIFODATOK                     */
    bitfield_t RTC_WUP_OVF    : 1;  /* was RTC_WUP_OVF                       */
    bitfield_t MODE           : 1;  /* was MODE[0]                           */
    bitfield_t STOPRX         : 1;  /* was MODE[1]                           */
  } bits;
} SFR_PRESTAT_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t FSEC          : 4;
    bitfield_t SEC           : 6;
    bitfield_t MIN           : 6;
  } bits;
} SFR_RTCDAT_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_USRBATRGLx_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MSI_TLIM    : 6;
    bitfield_t MSI_TCLR    : 1;
    bitfield_t             : 1;
  } bits;
} SFR_MSICON0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MSI_OVF_INT_EN  : 1;
    bitfield_t MSI_MOT_INT_EN  : 1;
    bitfield_t MSI_OVF_WUP_EN  : 1;
    bitfield_t MSI_MOT_WUP_EN  : 1;
    bitfield_t MSI_LFA_PU_EN   : 1;
    bitfield_t MSI_LFA_PD_EN   : 1;
    bitfield_t MSI_MODE        : 1;
    bitfield_t MSI_EN          : 1;
  } bits;
} SFR_MSICON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MSI_TREG        : 6;
    bitfield_t                 : 2;
  } bits;
} SFR_MSISTAT0_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MSI_OVF_INT      : 1;
    bitfield_t MSI_MOT_INT      : 1;
    bitfield_t MSI_OVF          : 1;
    bitfield_t MSI_MEMSBOOT_INT : 1;
    bitfield_t MSI_MEMSBOOT_OVF : 1;
    bitfield_t MSI_LFA_PD       : 1;
    bitfield_t                  : 2;
  } bits;
} SFR_MSISTAT1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t MSI_MEMSBOOT_INT_EN : 1;
    bitfield_t MSI_MEMSBOOT_WUP_EN : 1;
    bitfield_t MSI_SPIKEREJECT     : 1;
    bitfield_t                     : 1; /* changed */
    bitfield_t MSI_RANGE           : 3; /* changed */
    bitfield_t MSI_MEMS_MODE       : 1;
  } bits;
} SFR_MSICON2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t CMPMATCH        : 1;
    bitfield_t CMPINTEN        : 1;
    bitfield_t RUN             : 1;
    bitfield_t NBRESTART       : 1;
    bitfield_t WUPRESTART      : 1;
    bitfield_t CMPWRPENDING    : 1;
    bitfield_t MULTISHOT       : 1;
    bitfield_t                 : 1;
  } bits;
} SFR_POSTWUPCON_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_POSTWUPCOMP_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t DCDC_EN         : 1;
    bitfield_t DCDC_CKSEL      : 2;
    bitfield_t                 : 3;  /* RDT */
    bitfield_t DCDC_STUP_DONE  : 1;
    bitfield_t DCDC_RAMP_DONE  : 1;
  } bits;
} SFR_DCDCCON0_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_DCDCCON1_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_DCDCCON3_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_DCDCCON4_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_DCDCCON6_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_DCDCCON7_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t               : 6;  /* RDT R/W0 */
    bitfield_t BG_OK         : 1;
    bitfield_t BG_EN         : 1;
  } bits;
} SFR_DCDCCON8_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RSSI_OVF0DB_LVL  : 4;
    bitfield_t RSSI_OVF18DB_LVL : 4;
  } bits;
} SFR_RSSICON1_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RSSI_OVF36DB_LVL : 4;
    bitfield_t                  : 3;  /* RDT R/W0*/
    bitfield_t RSSI_RVV_INTEN   : 1;
  } bits;
} SFR_RSSICON2_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t SDADC_OVL_CFG    : 3;  /* bits 2..0 : set to 001b             */
    bitfield_t SDADC_OVERLOAD   : 1;  /* bits 3..3 : R/-                     */
    bitfield_t RSSI_FILT_CFG    : 3;  /* bits 6..4                           */
    bitfield_t                  : 1;  /* bits 7..7 : RDT  -/W0               */
  } bits;
} SFR_RSSICON3_t;
/*---------------------------------------------------------------------------*/
typedef SFR_byte SFR_RSSICON4_t; /* RSSI_AVG_CFG[7..0] */
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RSSI_LDO_EN        : 1;  /* bits 0..0                         */
    bitfield_t                    : 1;  /* bits 1..1 : RDT -/W0              */
    bitfield_t RSSI_OSC_EN        : 1;  /* bits 2..2                         */
    bitfield_t                    : 1;  /* bits 3..3 : R/W1                  */
    bitfield_t RSSI_OSC_CALSTART  : 1;  /* bits 4..4                         */
    bitfield_t RSSI_OSC_CALDONE   : 1;  /* bits 5..5 : R/W1->0               */
    bitfield_t                    : 2;  /* bits 7..6 : RDT R/W0              */
  } bits;
} SFR_RSSICON5_t;
/*---------------------------------------------------------------------------*/
typedef SFR_word SFR_RSSIVAL_t;
/* RSSI_VAL15 .. RSSI_VAL0 */
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t RSSI_START         : 1;
    bitfield_t SDADC_ENABLE       : 1;
    bitfield_t RSSI_BUSY          : 1;
    bitfield_t RSSI_RAW_VALID     : 1; /* R/W1->0 */
    bitfield_t RSSI_VALID         : 1; /* R/W1->0 */
    bitfield_t RSSI_VAL_SEL       : 3;
  } bits;
} SFR_RSSICON7_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint8_t val;
  struct
  {
    bitfield_t                    : 1;
    bitfield_t PF2_EN             : 1;  /* system register, read-only for user */
    bitfield_t                    : 6;
  } bits;
} SFR_PFCON3_t;
/*---------------------------------------------------------------------------*/
typedef union
{
  uint16_t val;
  struct {
    uint8_t lo;
    uint8_t hi;
  } byte;
  struct
  {
    bitfield_t                       : 7;
    bitfield_t DCBUSEN               : 1;
    bitfield_t                       : 8;
  } bits;
} SFR_BISTCON_t;
/*---------------------------------------------------------------------------*/


/*---------------------------------------------------------------------------*/
/* Register declarations                                                     */
/*---------------------------------------------------------------------------*/

extern volatile SFR_CXPC_t          chess_storage(DM9/*0x0014*/)  CXPC;
extern volatile SFR_CXSW_t          chess_storage(DM9/*0x0016*/)  CXSW;
extern volatile SFR_P1INS_t         chess_storage(DM9/*0x0018*/)  P1INS;
extern volatile SFR_P1OUT_t         chess_storage(DM9/*0x0019*/)  P1OUT;
extern volatile SFR_P1DIR_t         chess_storage(DM9/*0x001A*/)  P1DIR;
extern volatile SFR_P1INTDIS_t      chess_storage(DM9/*0x001B*/)  P1INTDIS;
extern volatile SFR_P2INS_t         chess_storage(DM9/*0x001C*/)  P2INS;
extern volatile SFR_P2OUT_t         chess_storage(DM9/*0x001D*/)  P2OUT;
extern volatile SFR_P2DIR_t         chess_storage(DM9/*0x001E*/)  P2DIR;
extern volatile SFR_P2INTDIS_t      chess_storage(DM9/*0x001F*/)  P2INTDIS;
extern volatile SFR_P3INS_t         chess_storage(DM9/*0x0020*/)  P3INS;
extern volatile SFR_P3OUT_t         chess_storage(DM9/*0x0021*/)  P3OUT;
extern volatile SFR_P3DIR_t         chess_storage(DM9/*0x0022*/)  P3DIR;
extern volatile SFR_P3INTDIS_t      chess_storage(DM9/*0x0023*/)  P3INTDIS;
extern volatile SFR_IIUCON0_t       chess_storage(DM9/*0x0024*/)  IIUCON0;
extern volatile SFR_IIUSTAT_t       chess_storage(DM9/*0x0025*/)  IIUSTAT;
extern volatile SFR_IIUCON1_t       chess_storage(DM9/*0x0026*/)  IIUCON1;
extern volatile SFR_IIUDAT_t        chess_storage(DM9/*0x0027*/)  IIUDAT;
extern volatile SFR_IIUSTATE_t      chess_storage(DM9/*0x0028*/)  IIUSTATE;
extern volatile SFR_IIUCON2_t       chess_storage(DM9/*0x0029*/)  IIUCON2;
extern volatile SFR_HTCON_t         chess_storage(DM9/*0x002A*/)  HTCON;
extern volatile SFR_I3DCON_t        chess_storage(DM9/*0x0031*/)  I3DCON;
extern volatile SFR_AESDAT_t        chess_storage(DM9/*0x0032*/)  AESDAT;
extern volatile SFR_AESCON_t        chess_storage(DM9/*0x0034*/)  AESCON;
extern volatile SFR_CRCDAT_t        chess_storage(DM9/*0x0035*/)  CRCDAT;
extern volatile SFR_CRC8DIN_t       chess_storage(DM9/*0x0036*/)  CRC8DIN;
extern volatile SFR_GCRCCON0_t      chess_storage(DM9/*0x0037*/)  GCRCCON0;
extern volatile SFR_GCRCPOLY_t      chess_storage(DM9/*0x0038*/)  GCRCPOLY;
extern volatile SFR_GCRCDAT_t       chess_storage(DM9/*0x003A*/)  GCRCDAT;
extern volatile SFR_GCRCDIN_t       chess_storage(DM9/*0x003C*/)  GCRCDIN;
extern volatile SFR_CPUMCCCNT0_t    chess_storage(DM9/*0x004C*/)  CPUMCCCNT0;
extern volatile SFR_CPUMCCCNT1_t    chess_storage(DM9/*0x004E*/)  CPUMCCCNT1;
extern volatile SFR_CPUMCCCON_t     chess_storage(DM9/*0x0050*/)  CPUMCCCON;
extern volatile SFR_WDCON_t         chess_storage(DM9/*0x0051*/)  WDCON;
extern volatile SFR_CLKCON0_t       chess_storage(DM9/*0x0052*/)  CLKCON0;
extern volatile SFR_CLKCON1_t       chess_storage(DM9/*0x0053*/)  CLKCON1;
extern volatile SFR_CLKCON2_t       chess_storage(DM9/*0x0054*/)  CLKCON2;
extern volatile SFR_CLKCON3_t       chess_storage(DM9/*0x0055*/)  CLKCON3;
extern volatile SFR_CLKCON4_t       chess_storage(DM9/*0x0057*/)  CLKCON4;
extern volatile SFR_ADCDAT_t        chess_storage(DM9/*0x005A*/)  ADCDAT;
extern volatile SFR_ADCSUM_t        chess_storage(DM9/*0x005C*/)  ADCSUM;
extern volatile SFR_ADCCON_t        chess_storage(DM9/*0x005E*/)  ADCCON;
extern volatile SFR_RSSICON0_t      chess_storage(DM9/*0x0060*/)  RSSICON0;
extern volatile SFR_ULPADDR_t       chess_storage(DM9/*0x0064*/)  ULPADDR;
extern volatile SFR_ULPSEL_t        chess_storage(DM9/*0x0066*/)  ULPSEL;
extern volatile SFR_ULPCON0_t       chess_storage(DM9/*0x0068*/)  ULPCON0;
extern volatile SFR_ULPDAT_t        chess_storage(DM9/*0x0069*/)  ULPDAT;
extern volatile SFR_ULPCON1_t       chess_storage(DM9/*0x006D*/)  ULPCON1;
extern volatile SFR_T0CON0_t        chess_storage(DM9/*0x006E*/)  T0CON0;
extern volatile SFR_T0CON1_t        chess_storage(DM9/*0x006F*/)  T0CON1;
extern volatile SFR_T0REG_t         chess_storage(DM9/*0x0070*/)  T0REG;
extern volatile SFR_T0RLD_t         chess_storage(DM9/*0x0072*/)  T0RLD;
extern volatile SFR_T1CON0_t        chess_storage(DM9/*0x0074*/)  T1CON0;
extern volatile SFR_T1CON1_t        chess_storage(DM9/*0x0075*/)  T1CON1;
extern volatile SFR_T1CON2_t        chess_storage(DM9/*0x0076*/)  T1CON2;
extern volatile SFR_T1REG_t         chess_storage(DM9/*0x0078*/)  T1REG;
extern volatile SFR_T1CAP_t         chess_storage(DM9/*0x007A*/)  T1CAP;
extern volatile SFR_T1CMP_t         chess_storage(DM9/*0x007C*/)  T1CMP;
extern volatile SFR_T2CON0_t        chess_storage(DM9/*0x007E*/)  T2CON0;
extern volatile SFR_T2CON1_t        chess_storage(DM9/*0x007F*/)  T2CON1;
extern volatile SFR_T2REG_t         chess_storage(DM9/*0x0080*/)  T2REG;
extern volatile SFR_T2RLD_t         chess_storage(DM9/*0x0082*/)  T2RLD;
extern volatile SFR_RNGDAT_t        chess_storage(DM9/*0x0084*/)  RNGDAT;
extern volatile SFR_RNGCON_t        chess_storage(DM9/*0x0086*/)  RNGCON;
extern volatile SFR_INTCON_t        chess_storage(DM9/*0x0087*/)  INTCON;
extern volatile SFR_INTFLAG0_t      chess_storage(DM9/*0x0088*/)  INTFLAG0;
extern volatile SFR_INTFLAG1_t      chess_storage(DM9/*0x0089*/)  INTFLAG1;
extern volatile SFR_INTFLAG2_t      chess_storage(DM9/*0x008A*/)  INTFLAG2;
extern volatile SFR_INTEN0_t        chess_storage(DM9/*0x008B*/)  INTEN0;
extern volatile SFR_INTEN1_t        chess_storage(DM9/*0x008C*/)  INTEN1;
extern volatile SFR_INTEN2_t        chess_storage(DM9/*0x008D*/)  INTEN2;
extern volatile SFR_SYSINTEN0_t     chess_storage(DM9/*0x008E*/)  SYSINTEN0;
extern volatile SFR_SYSINTEN1_t     chess_storage(DM9/*0x008F*/)  SYSINTEN1;
extern volatile SFR_INTSET0_t       chess_storage(DM9/*0x0090*/)  INTSET0;
extern volatile SFR_INTSET1_t       chess_storage(DM9/*0x0091*/)  INTSET1;
extern volatile SFR_INTSET2_t       chess_storage(DM9/*0x0092*/)  INTSET2;
extern volatile SFR_INTCLR0_t       chess_storage(DM9/*0x0093*/)  INTCLR0;
extern volatile SFR_INTCLR1_t       chess_storage(DM9/*0x0094*/)  INTCLR1;
extern volatile SFR_INTCLR2_t       chess_storage(DM9/*0x0095*/)  INTCLR2;
extern volatile SFR_INTVEC_t        chess_storage(DM9/*0x0096*/)  INTVEC;
extern volatile SFR_LFSHCON_t       chess_storage(DM9/*0x0098*/)  LFSHCON;
extern volatile SFR_PCON0_t         chess_storage(DM9/*0x0099*/)  PCON0;
extern volatile SFR_PCON1_t         chess_storage(DM9/*0x009A*/)  PCON1;
extern volatile SFR_PCON2_t         chess_storage(DM9/*0x009B*/)  PCON2;
extern volatile SFR_PCON5_t         chess_storage(DM9/*0x009E*/)  PCON5;
extern volatile SFR_BATSYS0_t       chess_storage(DM9/*0x00A0*/)  BATSYS0;
extern volatile SFR_BATSYS1_t       chess_storage(DM9/*0x00A1*/)  BATSYS1;
extern volatile SFR_PRESWUP0_t      chess_storage(DM9/*0x00A2*/)  PRESWUP0;
extern volatile SFR_PRESWUP1_t      chess_storage(DM9/*0x00A4*/)  PRESWUP1;
extern volatile SFR_PRESWUP2_t      chess_storage(DM9/*0x00A6*/)  PRESWUP2;
extern volatile SFR_PRESWUP3_t      chess_storage(DM9/*0x00A8*/)  PRESWUP3;
extern volatile SFR_PRESWUP4_t      chess_storage(DM9/*0x00AA*/)  PRESWUP4;
extern volatile SFR_P1WRES_t        chess_storage(DM9/*0x00AC*/)  P1WRES;
extern volatile SFR_P2WRES_t        chess_storage(DM9/*0x00AD*/)  P2WRES;
extern volatile SFR_P3WRES_t        chess_storage(DM9/*0x00AE*/)  P3WRES;
extern volatile SFR_LFTUNECH1ACT_t  chess_storage(DM9/*0x00AF*/)  LFTUNECH1ACT;
extern volatile SFR_LFTUNECH2ACT_t  chess_storage(DM9/*0x00B0*/)  LFTUNECH2ACT;
extern volatile SFR_LFTUNECH3ACT_t  chess_storage(DM9/*0x00B1*/)  LFTUNECH3ACT;
extern volatile SFR_USRBATx_t       chess_storage(DM9/*0x00B2*/)  USRBAT0;
extern volatile SFR_USRBATx_t       chess_storage(DM9/*0x00B3*/)  USRBAT1;
extern volatile SFR_USRBATx_t       chess_storage(DM9/*0x00B4*/)  USRBAT2;
extern volatile SFR_USRBATx_t       chess_storage(DM9/*0x00B5*/)  USRBAT3;
extern volatile SFR_USRBATx_t       chess_storage(DM9/*0x00B6*/)  USRBAT4;
extern volatile SFR_USRBATx_t       chess_storage(DM9/*0x00B7*/)  USRBAT5;
extern volatile SFR_USRBATx_t       chess_storage(DM9/*0x00B8*/)  USRBAT6;
extern volatile SFR_USRBATx_t       chess_storage(DM9/*0x00B9*/)  USRBAT7;
extern volatile SFR_LFTUNECH1IMMO_t chess_storage(DM9/*0x00BA*/)  LFTUNECH1IMMO;
extern volatile SFR_LFTUNECH2IMMO_t chess_storage(DM9/*0x00BB*/)  LFTUNECH2IMMO;
extern volatile SFR_LFTUNECH3IMMO_t chess_storage(DM9/*0x00BC*/)  LFTUNECH3IMMO;
extern volatile SFR_SPI0CON0_t      chess_storage(DM9/*0x00C0*/)  SPI0CON0;
extern volatile SFR_SPI0CON1_t      chess_storage(DM9/*0x00C1*/)  SPI0CON1;
extern volatile SFR_SPI0DAT_t       chess_storage(DM9/*0x00C2*/)  SPI0DAT;
extern volatile SFR_SPI0STAT_t      chess_storage(DM9/*0x00C3*/)  SPI0STAT;
extern volatile SFR_SPI1CON0_t      chess_storage(DM9/*0x00C4*/)  SPI1CON0;
extern volatile SFR_SPI1CON1_t      chess_storage(DM9/*0x00C5*/)  SPI1CON1;
extern volatile SFR_SPI1DAT_t       chess_storage(DM9/*0x00C6*/)  SPI1DAT;
extern volatile SFR_SPI1STAT_t      chess_storage(DM9/*0x00C7*/)  SPI1STAT;
extern volatile SFR_P1ALTF_t        chess_storage(DM9/*0x00C8*/)  P1ALTF;
extern volatile SFR_P2ALTF_t        chess_storage(DM9/*0x00CA*/)  P2ALTF;
extern volatile SFR_BITCNT_t        chess_storage(DM9/*0x00CC*/)  BITCNT;
extern volatile SFR_BITSWAP_t       chess_storage(DM9/*0x00CE*/)  BITSWAP;
extern volatile SFR_LEDCON_t        chess_storage(DM9/*0x00CF*/)  LEDCON;
extern volatile SFR_INTFLAG3_t      chess_storage(DM9/*0x00D0*/)  INTFLAG3;
extern volatile SFR_INTEN3_t        chess_storage(DM9/*0x00D1*/)  INTEN3;
extern volatile SFR_INTSET3_t       chess_storage(DM9/*0x00D2*/)  INTSET3;
extern volatile SFR_INTCLR3_t       chess_storage(DM9/*0x00D3*/)  INTCLR3;
extern volatile SFR_TXPCON_t        chess_storage(DM9/*0x00D4*/)  TXPCON;
extern volatile SFR_CLKRSTCON_t     chess_storage(DM9/*0x00D5*/)  CLKRSTCON;
extern volatile SFR_VCOCALCON_t     chess_storage(DM9/*0x00D6*/)  VCOCALCON;
extern volatile SFR_PLLCON_t        chess_storage(DM9/*0x00D7*/)  PLLCON;
extern volatile SFR_TXDAT_t         chess_storage(DM9/*0x00D8*/)  TXDAT;
extern volatile SFR_TXSPC_t         chess_storage(DM9/*0x00DA*/)  TXSPC;
extern volatile SFR_ENCCON0_t       chess_storage(DM9/*0x00DC*/)  ENCCON0;
extern volatile SFR_ENCCON1_t       chess_storage(DM9/*0x00DE*/)  ENCCON1;
extern volatile SFR_FREQCON0_t      chess_storage(DM9/*0x00E0*/)  FREQCON0;
extern volatile SFR_FREQCON1_t      chess_storage(DM9/*0x00E2*/)  FREQCON1;
extern volatile SFR_BRGCON_t        chess_storage(DM9/*0x00E4*/)  BRGCON;
extern volatile SFR_FSKCON_t        chess_storage(DM9/*0x00E6*/)  FSKCON;
extern volatile SFR_FSKRMP_t        chess_storage(DM9/*0x00E7*/)  FSKRMP;
extern volatile SFR_ASKCON_t        chess_storage(DM9/*0x00E8*/)  ASKCON;
extern volatile SFR_ASKRMP_t        chess_storage(DM9/*0x00EA*/)  ASKRMP;
extern volatile SFR_PACON_t         chess_storage(DM9/*0x00EB*/)  PACON;
extern volatile SFR_PAPWR_t         chess_storage(DM9/*0x00EC*/)  PAPWR;
extern volatile SFR_PACAPTRIM_t     chess_storage(DM9/*0x00ED*/)  PACAPTRIM;
extern volatile SFR_PALIMIT_t       chess_storage(DM9/*0x00EE*/)  PALIMIT;
extern volatile SFR_IDENT_t         chess_storage(DM9/*0x00FE*/)  IDENT;
extern volatile SFR_LFAEN0_t        chess_storage(DM9/*0x0100*/)  LFAEN0;
extern volatile SFR_LFAEN1_t        chess_storage(DM9/*0x0101*/)  LFAEN1;
extern volatile SFR_LFAEN2_t        chess_storage(DM9/*0x0102*/)  LFAEN2;
extern volatile SFR_LFAEN3_t        chess_storage(DM9/*0x0103*/)  LFAEN3;
extern volatile SFR_LFAEN4_t        chess_storage(DM9/*0x0104*/)  LFAEN4;
extern volatile SFR_LFACON0_t       chess_storage(DM9/*0x0105*/)  LFACON0;
extern volatile SFR_LFACON1_t       chess_storage(DM9/*0x0106*/)  LFACON1;
extern volatile SFR_LFACON2_t       chess_storage(DM9/*0x0107*/)  LFACON2;
extern volatile SFR_LFACON4_t       chess_storage(DM9/*0x0109*/)  LFACON4;
extern volatile SFR_LFACON5_t       chess_storage(DM9/*0x010A*/)  LFACON5;
extern volatile SFR_LFACON6_t       chess_storage(DM9/*0x010B*/)  LFACON6;
extern volatile SFR_LFACON7_t       chess_storage(DM9/*0x0134*/)  LFACON7;
extern volatile SFR_LFACON8_t       chess_storage(DM9/*0x013B*/)  LFACON8;
extern volatile SFR_LFASTATUS_t     chess_storage(DM9/*0x010C*/)  LFASTATUS;
extern volatile SFR_LFASENSE_t      chess_storage(DM9/*0x010D*/)  LFASENSE;
extern volatile SFR_WUPCON_t        chess_storage(DM9/*0x010E*/)  WUPCON;
extern volatile SFR_WUPPATEVN0_t    chess_storage(DM9/*0x0110*/)  WUPPATEVN0;
extern volatile SFR_WUPPATEVN1_t    chess_storage(DM9/*0x0112*/)  WUPPATEVN1;
extern volatile SFR_WUPPATEVN2_t    chess_storage(DM9/*0x0114*/)  WUPPATEVN2;
extern volatile SFR_WUPPATODD0_t    chess_storage(DM9/*0x0116*/)  WUPPATODD0;
extern volatile SFR_WUPPATODD1_t    chess_storage(DM9/*0x0118*/)  WUPPATODD1;
extern volatile SFR_WUPPATODD2_t    chess_storage(DM9/*0x011A*/)  WUPPATODD2;
extern volatile SFR_WUPPATCON_t     chess_storage(DM9/*0x011C*/)  WUPPATCON;
extern volatile SFR_PAYRXCON_t      chess_storage(DM9/*0x011E*/)  PAYRXCON;
extern volatile SFR_PREDAT_t        chess_storage(DM9/*0x0120*/)  PREDAT;
extern volatile SFR_RTCCON_t        chess_storage(DM9/*0x0121*/)  RTCCON;
extern volatile SFR_PRECON2_t       chess_storage(DM9/*0x0122*/)  PRECON2;
extern volatile SFR_PRECON3_t       chess_storage(DM9/*0x0123*/)  PRECON3;
extern volatile SFR_PRECON4_t       chess_storage(DM9/*0x0124*/)  PRECON4;
extern volatile SFR_PRECON5_t       chess_storage(DM9/*0x0125*/)  PRECON5;
extern volatile SFR_PRESTAT_t       chess_storage(DM9/*0x0126*/)  PRESTAT;
extern volatile SFR_RTCDAT_t        chess_storage(DM9/*0x0128*/)  RTCDAT;
extern volatile SFR_USRBATRGLx_t    chess_storage(DM9/*0x012A*/)  USRBATRGL0;
extern volatile SFR_USRBATRGLx_t    chess_storage(DM9/*0x012B*/)  USRBATRGL1;
extern volatile SFR_USRBATRGLx_t    chess_storage(DM9/*0x012C*/)  USRBATRGL2;
extern volatile SFR_USRBATRGLx_t    chess_storage(DM9/*0x012D*/)  USRBATRGL3;
extern volatile SFR_USRBATRGLx_t    chess_storage(DM9/*0x012E*/)  USRBATRGL4;
extern volatile SFR_USRBATRGLx_t    chess_storage(DM9/*0x012F*/)  USRBATRGL5;
extern volatile SFR_USRBATRGLx_t    chess_storage(DM9/*0x0130*/)  USRBATRGL6;
extern volatile SFR_USRBATRGLx_t    chess_storage(DM9/*0x0131*/)  USRBATRGL7;
extern volatile SFR_DCDCCON8_t      chess_storage(DM9/*0x013E*/)  DCDCCON8;
extern volatile SFR_MSICON0_t       chess_storage(DM9/*0x0142*/)  MSICON0;
extern volatile SFR_MSICON1_t       chess_storage(DM9/*0x0143*/)  MSICON1;
extern volatile SFR_MSISTAT0_t      chess_storage(DM9/*0x0144*/)  MSISTAT0;
extern volatile SFR_MSISTAT1_t      chess_storage(DM9/*0x0145*/)  MSISTAT1;
extern volatile SFR_MSICON2_t       chess_storage(DM9/*0x0146*/)  MSICON2;
extern volatile SFR_POSTWUPCON_t    chess_storage(DM9/*0x0147*/)  POSTWUPCON;
extern volatile SFR_POSTWUPCOMP_t   chess_storage(DM9/*0x0148*/)  POSTWUPCOMP;
extern volatile SFR_DCDCCON0_t      chess_storage(DM9/*0x014A*/)  DCDCCON0;
extern volatile SFR_DCDCCON1_t      chess_storage(DM9/*0x014B*/)  DCDCCON1;
extern volatile SFR_DCDCCON3_t      chess_storage(DM9/*0x014D*/)  DCDCCON3;
extern volatile SFR_DCDCCON4_t      chess_storage(DM9/*0x014E*/)  DCDCCON4;
extern volatile SFR_DCDCCON6_t      chess_storage(DM9/*0x0150*/)  DCDCCON6;
extern volatile SFR_DCDCCON7_t      chess_storage(DM9/*0x0151*/)  DCDCCON7;
extern volatile SFR_RSSICON1_t      chess_storage(DM9/*0x0152*/)  RSSICON1;
extern volatile SFR_RSSICON2_t      chess_storage(DM9/*0x0153*/)  RSSICON2;
extern volatile SFR_RSSICON3_t      chess_storage(DM9/*0x0154*/)  RSSICON3;
extern volatile SFR_RSSICON4_t      chess_storage(DM9/*0x0155*/)  RSSICON4;
extern volatile SFR_RSSICON5_t      chess_storage(DM9/*0x0156*/)  RSSICON5;
extern volatile SFR_RSSIVAL_t       chess_storage(DM9/*0x0158*/)  RSSIVAL;
extern volatile SFR_RSSICON7_t      chess_storage(DM9/*0x015A*/)  RSSICON7;
extern volatile SFR_PFCON3_t        chess_storage(DM9/*0x015F*/)  PFCON3;
extern volatile SFR_BISTCON_t       chess_storage(DM9/*0x016C*/)  BISTCON;

/**@}*/

#endif /* _PLATFORM_SRX_ */
