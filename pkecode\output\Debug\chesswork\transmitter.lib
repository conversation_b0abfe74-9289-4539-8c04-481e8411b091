
// File generated by noodle version P-2019.09#78e58cd307#210222, Sat Mar  2 20:51:11 2024
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/transmitter.c mrk3

toolrelease _19R3;


// additional
prop gp_offset_type = ( __sint );

// void phcaiKEyLLGenFunc_CS_SetClkCon(const uint8_t)
Fvoid_phcaiKEyLLGenFunc_CS_SetClkCon___uchar : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_SetClkCon" 'void phcaiKEyLLGenFunc_CS_SetClkCon(const uint8_t)'; 
    arg : ( int8_:i );
    loc : ( RbL[0] );
}

// error_t phcaiKEyLLGenFunc_CS_UHF_XoStartUp(bool_t, bool_t)
Ferror_t_phcaiKEyLLGenFunc_CS_UHF_XoStartUp_bool_t_bool_t : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_UHF_XoStartUp" 'error_t phcaiKEyLLGenFunc_CS_UHF_XoStartUp(bool_t, bool_t)'; 
    arg : ( int8_:r int8_:i int8_:i );
    loc : ( RbL[0] RbL[0] RbH[0] );
}

// error_t phcaiKEyLLGenFunc_CS_UHF_PllStartUp(bool_t, bool_t, uint8_t)
Ferror_t_phcaiKEyLLGenFunc_CS_UHF_PllStartUp_bool_t_bool_t___uchar : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_UHF_PllStartUp" 'error_t phcaiKEyLLGenFunc_CS_UHF_PllStartUp(bool_t, bool_t, uint8_t)'; 
    arg : ( int8_:r int8_:i int8_:i int8_:i );
    loc : ( RbL[0] RbL[0] RbH[0] RbL[1] );
}

// error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(const uint8_t * const, const uint16_t)
Ferror_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage___P__uchar___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage" 'error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(const uint8_t * const, const uint16_t)'; 
    arg : ( int8_:r int16_:i int16_:i );
    loc : ( RbL[0] R46[0] RwL[0] );
}

// void phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(uint8_t * const, const uint16_t)
Fvoid_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_ULPEE_ReadPage" 'void phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(uint8_t * const, const uint16_t)'; 
    arg : ( int16_:i int16_:i );
    loc : ( R46[0] RwL[0] );
}

// uint8_t phcaiKEyLLGenFunc_ULPEE_ReadOneByte(const uint16_t)
F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_ULPEE_ReadOneByte" 'uint8_t phcaiKEyLLGenFunc_ULPEE_ReadOneByte(const uint16_t)'; 
    arg : ( int8_:r int16_:i );
    loc : ( RbL[0] RwL[0] );
}

// uint16_t phcaiKEyLLGenFunc_ULPEE_ReadOneWord(const uint16_t)
F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_ULPEE_ReadOneWord" 'uint16_t phcaiKEyLLGenFunc_ULPEE_ReadOneWord(const uint16_t)'; 
    arg : ( int16_:r int16_:i );
    loc : ( RwL[0] RwL[0] );
}

// void phcaiKEyLLGenFunc_timer0_delay_us(uint16_t)
Fvoid_phcaiKEyLLGenFunc_timer0_delay_us___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_timer0_delay_us" 'void phcaiKEyLLGenFunc_timer0_delay_us(uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
}

// void tx_read_configuration(uint16_t, TxRegSettings_t *)
Fvoid_tx_read_configuration___ushort___PTxRegSettings_t : user_defined, called {
    fnm : "tx_read_configuration" 'void tx_read_configuration(uint16_t, TxRegSettings_t *)'; 
    arg : ( int16_:i int16_:i );
    loc : ( RwL[0] R46[0] );
    frm : ( y=2 l=6 );
    llv : 0 0 * * 0 ;
}

// void tx_apply_configuration(const TxRegSettings_t *)
Fvoid_tx_apply_configuration___PTxRegSettings_t : user_defined, called {
    fnm : "tx_apply_configuration" 'void tx_apply_configuration(const TxRegSettings_t *)'; 
    arg : ( int16_:i );
    loc : ( R46[0] );
    frm : ( y=2 l=2 );
    llv : 0 0 0 0 0 ;
}

// error_t tx_enable_Xtal_oscillator(bool_t)
Ferror_t_tx_enable_Xtal_oscillator_bool_t : user_defined, called {
    fnm : "tx_enable_Xtal_oscillator" 'error_t tx_enable_Xtal_oscillator(bool_t)'; 
    arg : ( int8_:r int8_:i );
    loc : ( RbL[0] RbL[0] );
    frm : ( y=2 l=2 );
    llv : 0 0 * * 0 ;
}

// error_t tx_enable_PLL(bool_t)
Ferror_t_tx_enable_PLL_bool_t : user_defined, called {
    fnm : "tx_enable_PLL" 'error_t tx_enable_PLL(bool_t)'; 
    arg : ( int8_:r int8_:i );
    loc : ( RbL[0] RbL[0] );
    frm : ( y=2 l=2 );
    llv : 0 0 * * 0 ;
}

// error_t tx_enable_PA(bool_t)
Ferror_t_tx_enable_PA_bool_t : user_defined, called {
    fnm : "tx_enable_PA" 'error_t tx_enable_PA(bool_t)'; 
    arg : ( int8_:r int8_:i );
    loc : ( RbL[0] RbL[0] );
    frm : ( y=2 l=2 );
    llv : 0 0 * * 0 ;
}

// void tx_shutdown()
Fvoid_tx_shutdown : user_defined, called {
    fnm : "tx_shutdown" 'void tx_shutdown()'; 
    frm : ( y=2 );
    llv : 0 0 * * 0 ;
}

// void tx_transmit_buffer_encoded_bytes(DataEnc_t, uint16_t, const uint8_t *)
Fvoid_tx_transmit_buffer_encoded_bytes_DataEnc_t___ushort___P__uchar : user_defined, called {
    fnm : "tx_transmit_buffer_encoded_bytes" 'void tx_transmit_buffer_encoded_bytes(DataEnc_t, uint16_t, const uint8_t *)'; 
    arg : ( int8_:i int16_:i int16_:i );
    loc : ( RbL[0] RwL[1] R46[0] );
    frm : ( y=2 l=10 );
    llv : 0 0 0 0 0 ;
}

// void tx_transmit_buffer_encoded_bits(DataEnc_t, uint16_t, const uint8_t *)
Fvoid_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar : user_defined, called {
    fnm : "tx_transmit_buffer_encoded_bits" 'void tx_transmit_buffer_encoded_bits(DataEnc_t, uint16_t, const uint8_t *)'; 
    arg : ( int8_:i int16_:i int16_:i );
    loc : ( RbL[0] RwL[1] R46[0] );
    frm : ( y=2 l=12 );
    llv : 0 0 0 0 0 ;
}

// void tx_transmit_encoded_bits(DataEnc_t, uint16_t, uint16_t)
Fvoid_tx_transmit_encoded_bits_DataEnc_t___ushort___ushort : user_defined, called {
    fnm : "tx_transmit_encoded_bits" 'void tx_transmit_encoded_bits(DataEnc_t, uint16_t, uint16_t)'; 
    arg : ( int8_:i int16_:i int16_:i );
    loc : ( RbL[0] RwL[1] RwL[2] );
    frm : ( y=2 l=6 );
    llv : 0 0 0 0 0 ;
}

// void Rf_TXFream(uint8_t *, uint8_t)
Fvoid_Rf_TXFream___P__uchar___uchar : user_defined, called {
    fnm : "Rf_TXFream" 'void Rf_TXFream(uint8_t *, uint8_t)'; 
    arg : ( int16_:i int8_:i );
    loc : ( R46[0] RbL[0] );
    frm : ( y=2 l=10 );
    llv : 0 0 * * 0 ;
}

// void FHSS_FreqCfg(uint8_t, uint8_t)
Fvoid_FHSS_FreqCfg___uchar___uchar : user_defined, called {
    fnm : "FHSS_FreqCfg" 'void FHSS_FreqCfg(uint8_t, uint8_t)'; 
    arg : ( int8_:i int8_:i );
    loc : ( RbL[0] RbH[0] );
    frm : ( y=2 l=8 );
    llv : 0 0 * * 0 ;
}

