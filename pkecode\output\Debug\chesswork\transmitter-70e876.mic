
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:19 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-70e876 mrk3


// m3;   next: m4 (next offset: 3)
000000  1 0  "0000010010010111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000001  2 0  "0110101001000011"   // (DM[0]) = _pl_rd_res_reg_const_store_1_B1 (RbL[0],0,DM[0],R7); 
000002  0 0  "0000000000000000"   // /

// m4 chess_separator_scheduler;   next: m42 (next offset: 3)

// m42;   next: m44, jump target: m9 (next offset: 6)
000003  2 0  "0100001010001011"   // (nz_flag,c_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B2 (0,DM[0],R7); 
000004  0 0  "0000000000000000"   // /
000005  1 0  "0101000000000101"   // () = cc_eq__jump_const_1_B1 (nz_flag,5); 

// m44, jump target: m21 (next offset: 10)
000006  2 0  "0010000000100000"   // (DM9,PM,nz_flag) = load_const__ad_const_store_1_B1 (0,DM9,PM); 
000007  0 0  "0000000011101111"   // /
000008  1 0  "0110100010000000"   // (RbL[0]) = const_1_B2 (); 
000009  1 0  "0101101000000111"   // () = jump_const_1_B1 (7); 

// m9;   next: m10 (next offset: 16)
000010  1 0  "0110100010001000"   // (RbL[0]) = const_2_B2 (); 
000011  1 0  "0110100010000100"   // (RbH[0]) = const_1_B2 (); 
000012  2 0  "0110100000000001"   // (RbL[1]) = const_3_B1 (); 
000013  0 0  "0000000011101110"   // /
000014  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000015  0 0  "0000000000000000"   // /

// m10 subroutine call;   next: m45 (next offset: 16)

// m45;   next: m21 (next offset: 16)

// m21 (next offset: /)
000016  1 0  "0001010010010111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000017  1 0  "0001101111000100"   // () = ret_1_B1 (); 

