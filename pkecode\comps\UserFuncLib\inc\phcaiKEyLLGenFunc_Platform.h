/*
  -----------------------------------------------------------------------------
  (c) Copyright 2010 - 2020  NXP
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, <PERSON>FF<PERSON>IATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_Platform.h 31225 2021-03-10 14:44:11Z dep10330 $
  $Revision: 31225 $
*/

/**
 * @file
 * Definitions of features avaiable for each type of the TOKEN product family.
 * Includes SMART2A, but not (yet) KeyLink/ACTIC4G.
 */

/*
  Change Log:
  2018-07-25 (MMr):
  - added PLATFORM_HAS_??KB_EROM and PLATFORM_HAS_?KB_RAM for better support of SRX types.
  2019-02-08 (MMr):
  - added symbol PLATFORM_INITIAL_STACKPTR.
  - added support for RC901.
  2019-02-22 (MMr):
  - moved NCF215A RC901 to NCF215C RC901.
  2019-07-18 (MMr):
  - added SRX2 support (preliminary, keeping PLATFORM_TOKENSRX for compatib.)
  2019-08-15 (MMr):
  - improved SRX2 support: introduced switch PLATFORM_TOKENSRX_COMMON, defined
    for SRX, SRX2 and IMPACT types.
    PLATFORM_TOKENSRX  shall be defined only on the actual SRX(1) type.
    PLATFORM_TOKENSRX2 shall be defined only on the actual SRX2 and IMPACT types.
  2020-01-03 (MMr):
  - added PLATFORM_HAS_GENERICCRC.
  2020-09-28 (MMr):
  - for IMPACT, sets PLATFORM_HAS_RFXO.
  - updated comments to SRX2.
  2020-10-22 (MMr):
  - For IMPACT types, changed PLATFORM_ID from PF_TOKENSRX to PF_TOKENSRX2.
 */

#ifndef PHCAIKEYLLGENFUNC_PLATFORM_H
#define PHCAIKEYLLGENFUNC_PLATFORM_H


/*
 * Platform ID codes.
 */
#define PF_UNDEF        0u   /* Avoid use of 0 for ID code.                                      */
#define PF_KEYLINK      1u   /* Note: KEYLINK is not yet supported in unified demo code.         */
#define PF_SMART2A      2u   /* Note: SMART2 (NCF2960) is no longer supported.                   */
#define PF_TOKEN        3u
#define PF_TOKENPLUS    4u
#define PF_TOKENSRXv0   5u   /* was used for test chips NCF29AAv0 or NCF295Av0, obsoleted.       */
#define PF_TOKENSRX     6u   /* NCF29AA, NCF295A, NCF215A, NCF215C                               */
#define PF_TOKENSRX2    7u   /* NCF29AE, NCF295E, NCF29AC                                        */



/* SMART2A types */
#if defined(NCF2961) || defined(NCF2161)

#define PLATFORM_ID                      PF_SMART2A
#define PLATFORM_SMART2A                 1
#define PLATFORM_HAS_PATRIM_SFR          1
#define PLATFORM_HAS_16KB_EROM           1
#define PLATFORM_HAS_2KB_RAM             1
#define PLATFORM_INITIAL_STACKPTR   0x0880
#define PLATFORM_BASE_EROM_IN_DM    0x0980  /* Lowest EROM (byte) address visible in Data Memory */

#endif

/* TOKEN or ACTIC5G types */
#if  defined(NCF29A1) || defined(NCF29A2) || defined(NCF21A2) \
  || defined(NCF2953) || defined(NCF2954)

#define PLATFORM_ID                      PF_TOKEN
#define PLATFORM_TOKEN                   1
#define PLATFORM_HAS_VDDA_RESET          1
#define PLATFORM_HAS_VDDA_BROWNOUT       1
#define PLATFORM_HAS_VBATREG             1
#define PLATFORM_HAS_MSI                 1
#define PLATFORM_HAS_LFTUNE              1
#define PLATFORM_HAS_PORT2x              1
#define PLATFORM_HAS_PATRIM_SFR          1
#define PLATFORM_HAS_32KB_EROM           1
#define PLATFORM_HAS_2KB_RAM             1
#define PLATFORM_INITIAL_STACKPTR   0x0880
#define PLATFORM_BASE_EROM_IN_DM    0x0980

#endif

/* TOKEN-LITE types */
#if  defined(NCF29A3) || defined(NCF29A4)

#define PLATFORM_ID                      PF_TOKEN
#define PLATFORM_TOKEN                   1
#define PLATFORM_HAS_VDDA_RESET          1
#define PLATFORM_HAS_VDDA_BROWNOUT       1
#define PLATFORM_HAS_VBATREG             1
#define PLATFORM_HAS_PORT2x              1
#define PLATFORM_HAS_PATRIM_SFR          1
#define PLATFORM_HAS_32KB_EROM           1
#define PLATFORM_HAS_2KB_RAM             1
#define PLATFORM_INITIAL_STACKPTR   0x0880
#define PLATFORM_BASE_EROM_IN_DM    0x0980

#endif


/* TOKEN-PLUS or ACTIC5G-PLUS types */
#if  defined(NCF29A7) || defined(NCF29A8) \
  || defined(NCF2957) || defined(NCF2958) \
  || defined(NCF2157) || defined(NCF2158)

#define PLATFORM_ID                      PF_TOKENPLUS
#define PLATFORM_TOKENPLUS               1
#define PLATFORM_HAS_VDDA_RESET          1
#define PLATFORM_HAS_VDDA_BROWNOUT       1
#define PLATFORM_HAS_VBATREG             1
#define PLATFORM_HAS_MSI                 1
#define PLATFORM_HAS_MSI_EXT             1
#define PLATFORM_HAS_LFTUNE_32           1
#define PLATFORM_HAS_PORT2x              1
#define PLATFORM_HAS_PORT3x              1
#define PLATFORM_HAS_LEDCON              1
#define PLATFORM_HAS_ADC_EXT_SAMTIM      1
#define PLATFORM_HAS_ADC_HW_SUMMATION    1
#define PLATFORM_HAS_PACAPTRIM_SFR       1
#define PLATFORM_HAS_VPA_2V2MAX          1
#define PLATFORM_HAS_VBATREGREFRESH      1
#define PLATFORM_HAS_LFPOSTWUPTIMER      1
#define PLATFORM_HAS_P30                 1
#define PLATFORM_HAS_P31                 1
#define PLATFORM_HAS_RSSICHAINCOMP       1
#define PLATFORM_HAS_CPUMCCNT            1
#define PLATFORM_HAS_GENERICCRC          1
#define PLATFORM_HAS_32KB_EROM           1
#define PLATFORM_HAS_2KB_RAM             1
#define PLATFORM_INITIAL_STACKPTR   0x0880
#define PLATFORM_BASE_EROM_IN_DM    0x0980

#endif


/* TOKEN-SRX or ACTIC-SRX types (C3 silicon is in production) */
#if  defined(NCF29AA) || defined(NCF29AB) \
  || defined(NCF295A) || defined(NCF295B) \
  || defined(NCF215A) || defined(NCF215B)

#define PLATFORM_ID                      PF_TOKENSRX
#define PLATFORM_TOKENSRX                1
#define PLATFORM_TOKENSRX_COMMON         1     /* defined for SRX, SRX2 and IMPACT types */
#define PLATFORM_HAS_VDDA_RESET          1
#define PLATFORM_HAS_VDDA_BROWNOUT       1
#define PLATFORM_HAS_VBATREG             1
#define PLATFORM_HAS_MSI                 1
#define PLATFORM_HAS_MSI_EXT             1
#define PLATFORM_HAS_LFTUNE_32           1
#define PLATFORM_HAS_PORT2x              1
#define PLATFORM_HAS_PORT3x              1
#define PLATFORM_HAS_LEDCON              1
#define PLATFORM_HAS_ADC_EXT_SAMTIM      1
#define PLATFORM_HAS_ADC_HW_SUMMATION    1
#define PLATFORM_HAS_PACAPTRIM_SFR       1
#define PLATFORM_HAS_VPA_2V2MAX          1
#define PLATFORM_HAS_VBATREGREFRESH      1
#define PLATFORM_HAS_LFPOSTWUPTIMER      1
#define PLATFORM_HAS_XO32KHZ             1
#define PLATFORM_HAS_P30                 1
#define PLATFORM_HAS_NBRSSI              1
#define PLATFORM_HAS_RSSICHAINCOMP       1
#define PLATFORM_HAS_CPUMCCNT            1
#define PLATFORM_HAS_GENERICCRC          1
#define PLATFORM_HAS_64KB_EROM           1
#define PLATFORM_HAS_4KB_RAM             1
/* Stack pointer for 4 kB total RAM minus 256 bytes system RAM */
#define PLATFORM_INITIAL_STACKPTR   0x1080
#define PLATFORM_BASE_EROM_IN_DM    0x1180

#endif


/* IMPACT types (supporting CaT): - in production (D0 silicon) */
#if  defined(NCF29AC) || defined(NCF29AD) \
  || defined(NCF295C) || defined(NCF295D) \
  || defined(NCF215C) || defined(NCF215D)

#define PLATFORM_ID                      PF_TOKENSRX2
#define PLATFORM_TOKENSRX2               1
#define PLATFORM_TOKENSRX_COMMON         1     /* defined for SRX, SRX2 and IMPACT types */
#define PLATFORM_HAS_VDDA_RESET          1
#define PLATFORM_HAS_VDDA_BROWNOUT       1
#define PLATFORM_HAS_VBATREG             1
#define PLATFORM_HAS_MSI                 1
#define PLATFORM_HAS_MSI_EXT             1
#define PLATFORM_HAS_LFTUNE_32           1
#define PLATFORM_HAS_PORT2x              1
#define PLATFORM_HAS_PORT3x              1
#define PLATFORM_HAS_LEDCON              1
#define PLATFORM_HAS_ADC_EXT_SAMTIM      1
#define PLATFORM_HAS_ADC_HW_SUMMATION    1
#define PLATFORM_HAS_PACAPTRIM_SFR       1
#define PLATFORM_HAS_VPA_2V2MAX          1
#define PLATFORM_HAS_VBATREGREFRESH      1
#define PLATFORM_HAS_LFPOSTWUPTIMER      1
#define PLATFORM_HAS_XO32KHZ             1
/*      PLATFORM_HAS_P30                 not set since pin 11 is bonded as VDDC2 */
#define PLATFORM_HAS_NBRSSI              1
#define PLATFORM_HAS_RSSICHAINCOMP       1
#define PLATFORM_HAS_CPUMCCNT            1
#define PLATFORM_HAS_GENERICCRC          1
#define PLATFORM_HAS_64KB_EROM           1
#define PLATFORM_HAS_4KB_RAM             1
#define PLATFORM_HAS_CHARGEANDTALK       1  /* NEW with IMPACT types */
/* Stack pointer for 4 kB total RAM minus 320 bytes system RAM */
#define PLATFORM_INITIAL_STACKPTR   0x1040
#define PLATFORM_BASE_EROM_IN_DM    0x1180

#endif


/* TOKEN-SRX2 or ACTIC-SRX2 type - in production (D0 silicon) */
#if  defined(NCF29AE) || defined(NCF29AF) \
  || defined(NCF295E) || defined(NCF295F) \
  || defined(NCF215E) || defined(NCF215F)

#define PLATFORM_ID                      PF_TOKENSRX2
#define PLATFORM_TOKENSRX2               1
#define PLATFORM_TOKENSRX_COMMON         1     /* defined for SRX, SRX2 and IMPACT types */
#define PLATFORM_HAS_VDDA_RESET          1
#define PLATFORM_HAS_VDDA_BROWNOUT       1
#define PLATFORM_HAS_VBATREG             1
#define PLATFORM_HAS_MSI                 1
#define PLATFORM_HAS_MSI_EXT             1
#define PLATFORM_HAS_LFTUNE_32           1
#define PLATFORM_HAS_PORT2x              1
#define PLATFORM_HAS_PORT3x              1
#define PLATFORM_HAS_LEDCON              1
#define PLATFORM_HAS_ADC_EXT_SAMTIM      1
#define PLATFORM_HAS_ADC_HW_SUMMATION    1
#define PLATFORM_HAS_PACAPTRIM_SFR       1
#define PLATFORM_HAS_VPA_2V2MAX          1
#define PLATFORM_HAS_VBATREGREFRESH      1
#define PLATFORM_HAS_LFPOSTWUPTIMER      1
#define PLATFORM_HAS_XO32KHZ             1
#define PLATFORM_HAS_P30                 1
#define PLATFORM_HAS_NBRSSI              1
#define PLATFORM_HAS_RSSICHAINCOMP       1
#define PLATFORM_HAS_CPUMCCNT            1
#define PLATFORM_HAS_GENERICCRC          1
#define PLATFORM_HAS_64KB_EROM           1
#define PLATFORM_HAS_4KB_RAM             1
/* Stack pointer for 4 kB total RAM minus 320 bytes system RAM */
#define PLATFORM_INITIAL_STACKPTR   0x1040
#define PLATFORM_BASE_EROM_IN_DM    0x1180

#endif


/*
  Types with on-chip XO for RF (27.6 MHz):
  SMART2A, TOKEN, TOKEN-PLUS, TOKEN-SRX[2], IMPACT, but not the ACTIC* types.
 */
#if defined(NCF2961)   || defined(NCF2922) || defined(NCF2161) \
 || defined(NCF29A1)   || defined(NCF29A2) || defined(NCF21A2) \
 || defined(NCF29A3)   || defined(NCF29A4) \
 || defined(NCF29A7)   || defined(NCF29A8) \
 || defined(NCF29AA)   || defined(NCF29AB) \
 || defined(NCF29AE)   || defined(NCF29AF) \
 || defined(NCF29AC)   || defined(NCF29AD)

#define PLATFORM_HAS_RFXO              1

#endif

/**
 * PLATFORM_PORT_MAJOR_MAX indicates the highest x of
 * all hardware registers PxyINS existing on the selected platform.
 */
#if defined( PLATFORM_HAS_PORT3x )
#define PLATFORM_PORT_MAJOR_MAX        3u
#elif defined( PLATFORM_HAS_PORT2x )
#define PLATFORM_PORT_MAJOR_MAX        2u
#else
/* only SMART2A */
#define PLATFORM_PORT_MAJOR_MAX        1u
#endif

#endif /* ifndef PHCAIKEYLLGENFUNC_PLATFORM_H */

/* eof */
