
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:19 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-d7a399 mrk3


// m3;   next: m4 (next offset: 3)
000000  1 0  "0000010010110111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (6,R7,R7); 
000001  2 0  "0110111001100011"   // (DM[0]) = _pl_rd_res_reg_const_store_1_B1 (R46[0],0,DM[0],R7); 
000002  0 0  "0000000000000000"   // /

// m4 chess_separator_scheduler;   next: m5 (next offset: 3)

// m5;   next: m6 (next offset: 4)
000003  1 0  "0110111101000000"   // (DM[2]) = _pl_rd_res_reg_const_store_1_B2 (RwL[0],2,DM[2],R7); 

// m6 chess_separator_scheduler;   next: m65 (next offset: 4)

// m65;   next: m8 (next offset: 7)
000004  1 0  "0110111100000000"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (2,DM[2],R7); 
000005  1 0  "0111110010010000"   // (RwL[0],c_flag,nz_flag) = shl_const_1_B1 (RwL[0]); 
000006  1 0  "0110111101000001"   // (DM[4]) = _pl_rd_res_reg_const_store_1_B2 (RwL[0],4,DM[4],R7); 

// m8 chess_separator_scheduler;   next: m9 (next offset: 7)

// m9;   next: m10 (next offset: 10)
000007  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000008  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000009  0 0  "0000000000000000"   // /

// m10 subroutine call;   next: m11 (next offset: 10)

// m11;   next: m12 (next offset: 13)
000010  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000011  0 0  "0000000000000000"   // /
000012  1 0  "0110110101000000"   // (DM) = store_1_B1 (RwL[0],R46[0],DM); 

// m12 chess_separator_scheduler;   next: m13 (next offset: 13)

// m13;   next: m14 (next offset: 17)
000013  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000014  1 0  "0001010010010000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_12_B2 (RwL[0]); 
000015  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000016  0 0  "0000000000000000"   // /

// m14 subroutine call;   next: m15 (next offset: 17)

// m15;   next: m16 (next offset: 21)
000017  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000018  0 0  "0000000000000000"   // /
000019  2 0  "0110111001000000"   // (DM) = _pl_const_store_12_B1 (RwL[0],R46[0],DM); 
000020  0 0  "0000000000000010"   // /

// m16 chess_separator_scheduler;   next: m17 (next offset: 21)

// m17;   next: m18 (next offset: 25)
000021  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000022  1 0  "0001010010100000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_11_B2 (RwL[0]); 
000023  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000024  0 0  "0000000000000000"   // /

// m18 subroutine call;   next: m19 (next offset: 25)

// m19;   next: m20 (next offset: 29)
000025  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000026  0 0  "0000000000000000"   // /
000027  2 0  "0110101001000000"   // (DM) = _pl_const_store_11_B1 (RbL[0],R46[0],DM); 
000028  0 0  "0000000000000100"   // /

// m20 chess_separator_scheduler;   next: m21 (next offset: 29)

// m21;   next: m22 (next offset: 33)
000029  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000030  1 0  "0001010010110000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_10_B2 (RwL[0]); 
000031  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000032  0 0  "0000000000000000"   // /

// m22 subroutine call;   next: m23 (next offset: 33)

// m23;   next: m24 (next offset: 37)
000033  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000034  0 0  "0000000000000000"   // /
000035  2 0  "0110111001000000"   // (DM) = _pl_const_store_10_B1 (RwL[0],R46[0],DM); 
000036  0 0  "0000000000000110"   // /

// m24 chess_separator_scheduler;   next: m25 (next offset: 37)

// m25;   next: m26 (next offset: 41)
000037  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000038  1 0  "0001010010101000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_9_B2 (RwL[0]); 
000039  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000040  0 0  "0000000000000000"   // /

// m26 subroutine call;   next: m27 (next offset: 41)

// m27;   next: m28 (next offset: 45)
000041  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000042  0 0  "0000000000000000"   // /
000043  2 0  "0110101001000000"   // (DM) = _pl_const_store_9_B1 (RbL[0],R46[0],DM); 
000044  0 0  "0000000000001000"   // /

// m28 chess_separator_scheduler;   next: m29 (next offset: 45)

// m29;   next: m30 (next offset: 49)
000045  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000046  1 0  "0001010011000000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_8_B2 (RwL[0]); 
000047  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000048  0 0  "0000000000000000"   // /

// m30 subroutine call;   next: m31 (next offset: 49)

// m31;   next: m32 (next offset: 53)
000049  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000050  0 0  "0000000000000000"   // /
000051  2 0  "0110111001000000"   // (DM) = _pl_const_store_8_B1 (RwL[0],R46[0],DM); 
000052  0 0  "0000000000001010"   // /

// m32 chess_separator_scheduler;   next: m33 (next offset: 53)

// m33;   next: m34 (next offset: 57)
000053  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000054  1 0  "0001010011010000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_7_B2 (RwL[0]); 
000055  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000056  0 0  "0000000000000000"   // /

// m34 subroutine call;   next: m35 (next offset: 57)

// m35;   next: m36 (next offset: 61)
000057  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000058  0 0  "0000000000000000"   // /
000059  2 0  "0110101001000000"   // (DM) = _pl_const_store_7_B1 (RbL[0],R46[0],DM); 
000060  0 0  "0000000000001100"   // /

// m36 chess_separator_scheduler;   next: m37 (next offset: 61)

// m37;   next: m38 (next offset: 66)
000061  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000062  2 0  "0001010000000000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_6_B1 (RwL[0]); 
000063  0 0  "0000000000001011"   // /
000064  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000065  0 0  "0000000000000000"   // /

// m38 subroutine call;   next: m39 (next offset: 66)

// m39;   next: m40 (next offset: 70)
000066  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000067  0 0  "0000000000000000"   // /
000068  2 0  "0110101001000000"   // (DM) = _pl_const_store_6_B1 (RbL[0],R46[0],DM); 
000069  0 0  "0000000000001101"   // /

// m40 chess_separator_scheduler;   next: m41 (next offset: 70)

// m41;   next: m42 (next offset: 75)
000070  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000071  2 0  "0001010000000000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_5_B1 (RwL[0]); 
000072  0 0  "0000000000001100"   // /
000073  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000074  0 0  "0000000000000000"   // /

// m42 subroutine call;   next: m43 (next offset: 75)

// m43;   next: m44 (next offset: 79)
000075  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000076  0 0  "0000000000000000"   // /
000077  2 0  "0110101001000000"   // (DM) = _pl_const_store_5_B1 (RbL[0],R46[0],DM); 
000078  0 0  "0000000000001110"   // /

// m44 chess_separator_scheduler;   next: m45 (next offset: 79)

// m45;   next: m46 (next offset: 84)
000079  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000080  2 0  "0001010000000000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_4_B1 (RwL[0]); 
000081  0 0  "0000000000001101"   // /
000082  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000083  0 0  "0000000000000000"   // /

// m46 subroutine call;   next: m47 (next offset: 84)

// m47;   next: m48 (next offset: 88)
000084  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000085  0 0  "0000000000000000"   // /
000086  2 0  "0110101001000000"   // (DM) = _pl_const_store_4_B1 (RbL[0],R46[0],DM); 
000087  0 0  "0000000000001111"   // /

// m48 chess_separator_scheduler;   next: m49 (next offset: 88)

// m49;   next: m50 (next offset: 93)
000088  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000089  2 0  "0001010000000000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_3_B1 (RwL[0]); 
000090  0 0  "0000000000001110"   // /
000091  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000092  0 0  "0000000000000000"   // /

// m50 subroutine call;   next: m51 (next offset: 93)

// m51;   next: m52 (next offset: 97)
000093  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000094  0 0  "0000000000000000"   // /
000095  2 0  "0110101001000000"   // (DM) = _pl_const_store_3_B1 (RbL[0],R46[0],DM); 
000096  0 0  "0000000000010000"   // /

// m52 chess_separator_scheduler;   next: m53 (next offset: 97)

// m53;   next: m54 (next offset: 102)
000097  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000098  2 0  "0001010000000000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_2_B1 (RwL[0]); 
000099  0 0  "0000000000001111"   // /
000100  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000101  0 0  "0000000000000000"   // /

// m54 subroutine call;   next: m55 (next offset: 102)

// m55;   next: m56 (next offset: 106)
000102  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000103  0 0  "0000000000000000"   // /
000104  2 0  "0110101001000000"   // (DM) = _pl_const_store_2_B1 (RbL[0],R46[0],DM); 
000105  0 0  "0000000000010001"   // /

// m56 chess_separator_scheduler;   next: m57 (next offset: 106)

// m57;   next: m58 (next offset: 110)
000106  1 0  "0110111100000001"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000107  1 0  "0001010011100000"   // (RwL[0],c_flag,nz_flag,o_flag) = _pl_const_1_B2 (RwL[0]); 
000108  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000109  0 0  "0000000000000000"   // /

// m58 subroutine call;   next: m59 (next offset: 110)

// m59;   next: m60 (next offset: 114)
000110  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000111  0 0  "0000000000000000"   // /
000112  2 0  "0110111001000000"   // (DM) = _pl_const_store_1_B1 (RwL[0],R46[0],DM); 
000113  0 0  "0000000000010010"   // /

// m60 chess_separator_scheduler;   next: m62 (next offset: 114)

// m62 (next offset: /)
000114  1 0  "0001010010110111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (6,R7,R7); 
000115  1 0  "0001101111000100"   // () = ret_1_B1 (); 

