
// File generated by mist version P-2019.09#78e58cd307#210222, Tue Sep 26 11:12:54 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-e68161 mrk3


// m3;   next: m4 (next offset: 3)
000000  1 0  "0000010010010111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000001  2 0  "0110111001100011"   // (DM[0]) = _pl_rd_res_reg_const_store_1_B1 (R46[0],0,DM[0],R7); 
000002  0 0  "0000000000000000"   // /

// m4 chess_separator_scheduler;   next: m5 (next offset: 3)

// m5;   next: m6 (next offset: 7)
000003  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000004  0 0  "0000000000000000"   // /
000005  1 0  "0110110100100000"   // (R46[0]) = load_1_B1 (R46[0],DM); 
000006  1 0  "1100010000001100"   // (DM9,PM) = store_const_1_B2 (R46[0],0,DM9,PM); 

// m6 chess_separator_scheduler;   next: m7 (next offset: 7)

// m7;   next: m8 (next offset: 12)
000007  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000008  0 0  "0000000000000000"   // /
000009  2 0  "0110111000100000"   // (R46[0]) = _pl_const_load_11_B1 (R46[0],DM); 
000010  0 0  "0000000000000010"   // /
000011  1 0  "1100010000001100"   // (DM9,PM) = store_const_1_B2 (R46[0],0,DM9,PM); 

// m8 chess_separator_scheduler;   next: m9 (next offset: 12)

// m9;   next: m10 (next offset: 17)
000012  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000013  0 0  "0000000000000000"   // /
000014  2 0  "0110111000100000"   // (R46[0]) = _pl_const_load_10_B1 (R46[0],DM); 
000015  0 0  "0000000000000110"   // /
000016  1 0  "1100010000001100"   // (DM9,PM) = store_const_1_B2 (R46[0],0,DM9,PM); 

// m10 chess_separator_scheduler;   next: m11 (next offset: 17)

// m11;   next: m12 (next offset: 22)
000017  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000018  0 0  "0000000000000000"   // /
000019  2 0  "0110101000000000"   // (RbL[0]) = _pl_const_load_9_B1 (R46[0],DM); 
000020  0 0  "0000000000001000"   // /
000021  1 0  "1100000000001000"   // (DM9,PM) = store_const_2_B2 (RbL[0],0,DM9,PM); 

// m12 chess_separator_scheduler;   next: m13 (next offset: 22)

// m13;   next: m14 (next offset: 27)
000022  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000023  0 0  "0000000000000000"   // /
000024  2 0  "0110111000100000"   // (R46[0]) = _pl_const_load_8_B1 (R46[0],DM); 
000025  0 0  "0000000000001010"   // /
000026  1 0  "1100010000001100"   // (DM9,PM) = store_const_1_B2 (R46[0],0,DM9,PM); 

// m14 chess_separator_scheduler;   next: m15 (next offset: 27)

// m15;   next: m16 (next offset: 32)
000027  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000028  0 0  "0000000000000000"   // /
000029  2 0  "0110101000000000"   // (RbL[0]) = _pl_const_load_7_B1 (R46[0],DM); 
000030  0 0  "0000000000001100"   // /
000031  1 0  "1100000000001000"   // (DM9,PM) = store_const_2_B2 (RbL[0],0,DM9,PM); 

// m16 chess_separator_scheduler;   next: m17 (next offset: 32)

// m17;   next: m18 (next offset: 37)
000032  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000033  0 0  "0000000000000000"   // /
000034  2 0  "0110101000000000"   // (RbL[0]) = _pl_const_load_6_B1 (R46[0],DM); 
000035  0 0  "0000000000001101"   // /
000036  1 0  "1100000000001000"   // (DM9,PM) = store_const_2_B2 (RbL[0],0,DM9,PM); 

// m18 chess_separator_scheduler;   next: m19 (next offset: 37)

// m19;   next: m20 (next offset: 42)
000037  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000038  0 0  "0000000000000000"   // /
000039  2 0  "0110101000000000"   // (RbL[0]) = _pl_const_load_5_B1 (R46[0],DM); 
000040  0 0  "0000000000001110"   // /
000041  1 0  "1100000000001000"   // (DM9,PM) = store_const_2_B2 (RbL[0],0,DM9,PM); 

// m20 chess_separator_scheduler;   next: m21 (next offset: 42)

// m21;   next: m22 (next offset: 47)
000042  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000043  0 0  "0000000000000000"   // /
000044  2 0  "0110101000000000"   // (RbL[0]) = _pl_const_load_4_B1 (R46[0],DM); 
000045  0 0  "0000000000001111"   // /
000046  1 0  "1100000000001000"   // (DM9,PM) = store_const_2_B2 (RbL[0],0,DM9,PM); 

// m22 chess_separator_scheduler;   next: m23 (next offset: 47)

// m23;   next: m24 (next offset: 52)
000047  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000048  0 0  "0000000000000000"   // /
000049  2 0  "0110101000000000"   // (RbL[0]) = _pl_const_load_3_B1 (R46[0],DM); 
000050  0 0  "0000000000010000"   // /
000051  1 0  "1100000000001000"   // (DM9,PM) = store_const_2_B2 (RbL[0],0,DM9,PM); 

// m24 chess_separator_scheduler;   next: m25 (next offset: 52)

// m25;   next: m26 (next offset: 57)
000052  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000053  0 0  "0000000000000000"   // /
000054  2 0  "0110101000000000"   // (RbL[0]) = _pl_const_load_2_B1 (R46[0],DM); 
000055  0 0  "0000000000010001"   // /
000056  1 0  "1100000000001000"   // (DM9,PM) = store_const_2_B2 (RbL[0],0,DM9,PM); 

// m26 chess_separator_scheduler;   next: m27 (next offset: 57)

// m27;   next: m28 (next offset: 62)
000057  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000058  0 0  "0000000000000000"   // /
000059  2 0  "0110111000100000"   // (R46[0]) = _pl_const_load_1_B1 (R46[0],DM); 
000060  0 0  "0000000000010010"   // /
000061  1 0  "1100010000001100"   // (DM9,PM) = store_const_1_B2 (R46[0],0,DM9,PM); 

// m28 chess_separator_scheduler;   next: m30 (next offset: 62)

// m30 (next offset: /)
000062  1 0  "0001010010010111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000063  1 0  "0001101111000100"   // () = ret_1_B1 (); 

