
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-51aaef mrk3


// m5;   next: m6 (next offset: 4)
000000  2 0  "0001011111111111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (10,R7,R7); 
000001  0 0  "1111111111110110"   // /
000002  2 0  "0110111001100011"   // (DM[0]) = _pl_rd_res_reg_const_store_2_B1 (R46[0],0,DM[0],R7); 
000003  0 0  "0000000000000000"   // /

// m6 chess_separator_scheduler;   next: m7 (next offset: 4)

// m7;   next: m8 (next offset: 5)
000004  1 0  "0110111101001000"   // (DM[2]) = _pl_rd_res_reg_const_store_2_B2 (RwL[1],2,DM[2],R7); 

// m8 chess_separator_scheduler;   next: m9 (next offset: 5)

// m9;   next: m10 (next offset: 6)
000005  1 0  "0110101101000011"   // (DM[4]) = _pl_rd_res_reg_const_store_1_B2 (RbL[0],4,DM[4],R7); 

// m10 chess_separator_scheduler;   next: m11 (next offset: 6)

// m11;   next: m14 (next offset: 10)
000006  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (8,R7); 
000007  0 0  "0000000000001000"   // /
000008  2 0  "0110110000001000"   // (DM[8]) = store_const_1_B1 (R46[0],DM[8]); 
000009  0 0  "0000101110111000"   // /

// m14 chess_separator_scheduler;   next: m421 (next offset: 10)

// m421;   next: m18 (next offset: 14)
000010  1 0  "0110101100000011"   // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B2 (4,DM[4],R7); 
000011  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000012  2 0  "0110111111110000"   // (DM9,PM) = load_const_bf_mov_const_const_store_2_B1 (RwL[0],0,DM9,PM); 
000013  0 0  "0011110000000000"   // /

// m18 chess_separator_scheduler;   next: m429 (next offset: 14)

// m429;   next: m20 (next offset: 17)
000014  1 0  "0110110011000101"   // (R46[1]) = const_1_B2 (); 
000015  2 0  "0110111111110101"   // (DM9,PM) = load_const_bf_mov_const_const_store_1_B1 (R46[1],0,DM9,PM); 
000016  0 0  "0100000000000000"   // /

// m20 chess_separator_scheduler;   next: m21 (next offset: 17)

// m21;   next: m22 (next offset: 18)
000017  1 0  "0110111110000010"   // (DM[6]) = _pl_rd_res_reg_const_store_const_1_B3 (6,DM[6],R7); 

// m22 chess_separator_scheduler;   next: m64 (next offset: 18)

// m64, jump target: m2 (next offset: 22)
000018  2 0  "0110111000101011"   // (R46[1]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000019  0 0  "0000000000000000"   // /
000020  1 0  "0110111100000000"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (2,DM[2],R7); 
000021  1 0  "0101101000001110"   // () = jump_const_1_B1 (14); 

// m52, jump target: m1 (next offset: 27)
000022  1 0  "0110110001101110" .loop_nesting 1    // R46[2] = R46[1]; 
000023  1 0  "0001011100110010"   // (R46[2],c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const__pl_1_B2 (R46[2],6,DM[6],R7); 
000024  1 0  "0110100100001010"   // (RbL[1]) = load_1_B1 (R46[2],DM9,DM9,DM9,DM,DM,DM,DM,DM,DM); 
000025  1 0  "1100000000011001"   // (DM9,PM) = store_const_2_B2 (RbL[1],1,DM9,PM); 
000026  1 0  "0101101000000010"   // () = jump_const_1_B1 (2); 

// m41;   next: m1 (next offset: 28)
000027  1 0  "0000011110001011" .loop_nesting 2    // (DM[8],c_flag,nz_flag,o_flag) = _mi_load_const__pl_rd_res_reg_const_store_1_B3 (8,DM[8],DM[8],R7); 

// m1;   next: m320 (next offset: 28)

// m320;   next: m325, jump target: m54 (next offset: 30)
000028  2 0  "0100111000000100"   // (DM9,PM,nz_flag) = load_const__ad_const_cmp_const_cc_ne__jump_const_1_B1 (0,4,DM9,PM); 
000029  0 0  "0000000001000000"   // /

// m325;   next: m54, jump target: m41 (next offset: 32)
000030  1 0  "0100011110000011"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B3 (8,DM[8],R7); 
000031  1 0  "0101011111111100"   // () = cc_a__jump_const_1_B1 (c_flag,nz_flag,-4); 

// m54;   next: m57 (next offset: 34)
000032  2 0  "0110110000001000" .loop_nesting 1    // (DM[8]) = store_const_1_B1 (R46[0],DM[8]); 
000033  0 0  "0000101110111000"   // /

// m57 chess_separator_scheduler;   next: m58 (next offset: 34)

// m58;   next: m2 (next offset: 35)
000034  1 0  "0001011110001010"   // (DM[6],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B3 (6,DM[6],DM[6],R7); 

// m2;   next: m330 (next offset: 35)

// m330;   next: m67, jump target: m52 (next offset: 37)
000035  1 0  "0100011101000010"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_1_B2 (RwL[0],6,DM[6],R7); 
000036  1 0  "0101100011110010"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-14); 

// m67 (next offset: /)
000037  2 0  "0001011111111111" .loop_nesting 0    // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (10,R7,R7); 
000038  0 0  "0000000000001010"   // /
000039  1 0  "0001101111000100"   // () = ret_1_B1 (); 

