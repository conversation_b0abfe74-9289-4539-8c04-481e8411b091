
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:30 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i SysInit-db3ca8 mrk3


// m3;   next: m4 (next offset: 4)
000000  2 0  "0001011111111111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (12,R7,R7); 
000001  0 0  "1111111111110100"   // /
000002  2 0  "0110101010000011"   // (DM[0]) = store_const__pl_rd_res_reg_const_1_B2 (0,DM[0],R7); 
000003  0 0  "0000000000000000"   // /

// m4 chess_separator_scheduler;   next: m5 (next offset: 4)

// m5;   next: m6 (next offset: 5)
000004  1 0  "0110101110000000"   // (DM[1]) = store_const__pl_rd_res_reg_const_1_B3 (1,DM[1],R7); 

// m6 chess_separator_scheduler;   next: m7 (next offset: 5)

// m7;   next: m8 (next offset: 6)
000005  1 0  "0110101110000001"   // (DM[1]) = store_const__pl_rd_res_reg_const_1_B3 (2,DM[1],R7); 

// m8 chess_separator_scheduler;   next: m9 (next offset: 6)

// m9;   next: m10 (next offset: 7)
000006  1 0  "0110101110000010"   // (DM[1]) = store_const__pl_rd_res_reg_const_1_B3 (3,DM[1],R7); 

// m10 chess_separator_scheduler;   next: m11 (next offset: 7)

// m11;   next: m13 (next offset: 8)
000007  1 0  "0110101110000011"   // (DM[1]) = store_const__pl_rd_res_reg_const_1_B3 (4,DM[1],R7); 

// m13 chess_separator_scheduler;   next: m14 (next offset: 8)

// m14;   next: m15 (next offset: 9)
000008  1 0  "0110111110000010"   // (DM[6]) = _pl_rd_res_reg_const_store_const_1_B3 (6,DM[6],R7); 

// m15 chess_separator_scheduler;   next: m16 (next offset: 9)

// m16;   next: m17 (next offset: 12)
000009  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (8,R7); 
000010  0 0  "0000000000001000"   // /
000011  1 0  "0110100110000000"   // (DM[8]) = store_const_4_B2 (R46[0],DM[8]); 

// m17 chess_separator_scheduler;   next: m18 (next offset: 12)

// m18;   next: m19 (next offset: 13)
000012  1 0  "0110111110000100"   // (DM[10]) = _pl_rd_res_reg_const_store_const_1_B3 (10,DM[10],R7); 

// m19 chess_separator_scheduler;   next: m406 (next offset: 13)

// m406;   next: m24, jump target: m27 (next offset: 15)
000013  2 0  "0100110000000100"   // (DM9,PM,nz_flag) = load_const__ad_const_cmp_const_cc_eq__jump_const_2_B1 (0,4,DM9,PM); 
000014  0 0  "0000000000000001"   // /

// m24;   next: m25 (next offset: 17)
000015  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000016  0 0  "0000000000000000"   // /

// m25 subroutine call;   next: m26 (next offset: 17)

// m26 (next offset: 17)

// m27;   next: m29 (next offset: 17)

// m29;   next: m30 (next offset: 19)
000017  2 0  "0110100000100000"   // (DM9) = store_const_const_8_B1 (0,DM9); 
000018  0 0  "0000000000000001"   // /

// m30 chess_separator_scheduler;   next: m31 (next offset: 19)

// m31;   next: m32 (next offset: 22)
000019  1 0  "0110100010001000"   // (RbL[0]) = const_15_B2 (); 
000020  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000021  0 0  "0000000000000000"   // /

// m32 subroutine call;   next: m34 (next offset: 22)

// m34;   next: m35 (next offset: 25)
000022  2 0  "0110110000000100"   // (R46[0]) = const_14_B1 (); 
000023  0 0  "0100010001001000"   // /
000024  1 0  "1100010000001100"   // (DM9,PM) = store_const_2_B2 (R46[0],0,DM9,PM); 

// m35 chess_separator_scheduler;   next: m36 (next offset: 25)

// m36;   next: m37 (next offset: 28)
000025  2 0  "0110110000000100"   // (R46[0]) = const_13_B1 (); 
000026  0 0  "1100010001000100"   // /
000027  1 0  "1100010000001100"   // (DM9,PM) = store_const_3_B2 (R46[0],0,DM9,PM); 

// m37 chess_separator_scheduler;   next: m38 (next offset: 28)

// m38;   next: m39 (next offset: 31)
000028  2 0  "0110110000000100"   // (R46[0]) = const_12_B1 (); 
000029  0 0  "0000100010001000"   // /
000030  1 0  "1100010000001100"   // (DM9,PM) = store_const_2_B2 (R46[0],0,DM9,PM); 

// m39 chess_separator_scheduler;   next: m40 (next offset: 31)

// m40;   next: m41 (next offset: 34)
000031  2 0  "0110110000000100"   // (R46[0]) = const_11_B1 (); 
000032  0 0  "0000000100010001"   // /
000033  1 0  "1100010000001100"   // (DM9,PM) = store_const_2_B2 (R46[0],0,DM9,PM); 

// m41 chess_separator_scheduler;   next: m42 (next offset: 34)

// m42;   next: m43 (next offset: 38)
000034  1 0  "1100000000000000"   // (RbL[0],DM9,PM) = load_const_2_B2 (0,DM9,PM); 
000035  1 0  "0010000010010000"   // (RbL[0],nz_flag) = _ad_const_3_B2 (RbL[0]); 
000036  2 0  "0110101001000011"   // (DM[0]) = store__pl_rd_res_reg_const_2_B1 (RbL[0],0,DM[0],R7); 
000037  0 0  "0000000000000000"   // /

// m43 chess_separator_scheduler;   next: m44 (next offset: 38)

// m44;   next: m45 (next offset: 43)
000038  2 0  "0110101000000011"   // (RbL[0]) = load__pl_rd_res_reg_const_2_B1 (0,DM[0],R7); 
000039  0 0  "0000000000000000"   // /
000040  2 0  "0010100000000000"   // (RbL[0],nz_flag) = _or_const_2_B1 (RbL[0]); 
000041  0 0  "0000000011000101"   // /
000042  1 0  "1100000000001000"   // (DM9,PM) = store_const_1_B2 (RbL[0],0,DM9,PM); 

// m45 chess_separator_scheduler;   next: m46 (next offset: 43)

// m46;   next: m47 (next offset: 45)
000043  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_3_B1 (0,DM9,PM); 
000044  0 0  "0000000000000000"   // /

// m47 chess_separator_scheduler;   next: m48 (next offset: 45)

// m48;   next: m49 (next offset: 47)
000045  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_3_B1 (0,DM9,PM); 
000046  0 0  "0000000000000000"   // /

// m49 chess_separator_scheduler;   next: m50 (next offset: 47)

// m50;   next: m51 (next offset: 49)
000047  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_3_B1 (0,DM9,PM); 
000048  0 0  "0000000000000000"   // /

// m51 chess_separator_scheduler;   next: m52 (next offset: 49)

// m52;   next: m53 (next offset: 54)
000049  1 0  "1100000000000000"   // (RbL[0],DM9,PM) = load_const_2_B2 (0,DM9,PM); 
000050  2 0  "0010000000000000"   // (RbL[0],nz_flag) = _ad_const_2_B1 (RbL[0]); 
000051  0 0  "0000000000011111"   // /
000052  2 0  "0110101001000011"   // (DM[0]) = store__pl_rd_res_reg_const_2_B1 (RbL[0],0,DM[0],R7); 
000053  0 0  "0000000000000000"   // /

// m53 chess_separator_scheduler;   next: m54 (next offset: 54)

// m54;   next: m55 (next offset: 57)
000054  2 0  "0110101000000011"   // (RbL[0]) = load__pl_rd_res_reg_const_2_B1 (0,DM[0],R7); 
000055  0 0  "0000000000000000"   // /
000056  1 0  "1100000000001000"   // (DM9,PM) = store_const_1_B2 (RbL[0],0,DM9,PM); 

// m55 chess_separator_scheduler;   next: m56 (next offset: 57)

// m56;   next: m57 (next offset: 59)
000057  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_7_B1 (0,DM9,PM); 
000058  0 0  "0000000011111111"   // /

// m57 chess_separator_scheduler;   next: m58 (next offset: 59)

// m58;   next: m59 (next offset: 61)
000059  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_6_B1 (0,DM9,PM); 
000060  0 0  "0000000000011111"   // /

// m59 chess_separator_scheduler;   next: m60 (next offset: 61)

// m60;   next: m61 (next offset: 63)
000061  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_6_B1 (0,DM9,PM); 
000062  0 0  "0000000000011111"   // /

// m61 chess_separator_scheduler;   next: m62 (next offset: 63)

// m62;   next: m63 (next offset: 65)
000063  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_3_B1 (0,DM9,PM); 
000064  0 0  "0000000000000000"   // /

// m63 chess_separator_scheduler;   next: m64 (next offset: 65)

// m64;   next: m65 (next offset: 69)
000065  2 0  "0110110000000000"   // (RwL[0]) = const_10_B1 (); 
000066  0 0  "0000000011010001"   // /
000067  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000068  0 0  "0000000000000000"   // /

// m65 subroutine call;   next: m66 (next offset: 69)

// m66;   next: m67 (next offset: 72)
000069  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (8,R7); 
000070  0 0  "0000000000001000"   // /
000071  1 0  "0110100101000000"   // (DM[8]) = store_1_B1 (RbL[0],R46[0],DM[8]); 

// m67 chess_separator_scheduler;   next: m372 (next offset: 72)

// m372;   next: m75, jump target: m72 (next offset: 75)
000072  2 0  "0100000000110011"   // (nz_flag,c_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B1 (8,DM[8],R7); 
000073  0 0  "0000100001010101"   // /
000074  1 0  "0101000000000100"   // () = cc_eq__jump_const_1_B1 (nz_flag,4); 

// m75, jump target: m78 (next offset: 78)
000075  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_4_B1 (0,DM9,PM); 
000076  0 0  "0000000000000001"   // /
000077  1 0  "0101101000000011"   // () = jump_const_1_B1 (3); 

// m72;   next: m78 (next offset: 80)
000078  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_5_B1 (0,DM9,PM); 
000079  0 0  "0000000001010101"   // /

// m78;   next: m79 (next offset: 82)
000080  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_3_B1 (0,DM9,PM); 
000081  0 0  "0000000000000000"   // /

// m79 chess_separator_scheduler;   next: m80 (next offset: 82)

// m80;   next: m81 (next offset: 85)
000082  2 0  "0110110000000100"   // (R46[0]) = const_9_B1 (); 
000083  0 0  "0001110100010000"   // /
000084  1 0  "1100010000001100"   // (DM9,PM) = store_const_2_B2 (R46[0],0,DM9,PM); 

// m81 chess_separator_scheduler;   next: m82 (next offset: 85)

// m82;   next: m83 (next offset: 88)
000085  2 0  "0110110000000100"   // (R46[0]) = const_8_B1 (); 
000086  0 0  "0110000000111000"   // /
000087  1 0  "1100010000001100"   // (DM9,PM) = store_const_2_B2 (R46[0],0,DM9,PM); 

// m83 chess_separator_scheduler;   next: m84 (next offset: 88)

// m84;   next: m85 (next offset: 91)
000088  2 0  "0110110000000100"   // (R46[0]) = const_7_B1 (); 
000089  0 0  "0100010001011111"   // /
000090  1 0  "1100010000001100"   // (DM9,PM) = store_const_2_B2 (R46[0],0,DM9,PM); 

// m85 chess_separator_scheduler;   next: m86 (next offset: 91)

// m86;   next: m87 (next offset: 94)
000091  2 0  "0110110000000100"   // (R46[0]) = const_6_B1 (); 
000092  0 0  "0010001000110011"   // /
000093  1 0  "1100010000001100"   // (DM9,PM) = store_const_2_B2 (R46[0],0,DM9,PM); 

// m87 chess_separator_scheduler;   next: m325 (next offset: 94)

// m325;   next: m89 (next offset: 102)
000094  2 0  "0110100000010000"   // (RbL[0]) = load_const_1_B1 (3,DM); 
000095  0 0  "0000000000000011"   // /
000096  2 0  "0010000000000000"   // (RbL[0],nz_flag) = _ad_const_1_B1 (RbL[0]); 
000097  0 0  "0000000000001111"   // /
000098  1 0  "0111100010100000"   // (RbL[0],c_flag,nz_flag) = shl_const_1_B1 (RbL[0]); 
000099  2 0  "0010100000000000"   // (RbL[0],nz_flag) = _or_const_1_B1 (RbL[0]); 
000100  0 0  "0000000000001111"   // /
000101  1 0  "1100000000001000"   // (DM9,PM) = store_const_1_B2 (RbL[0],0,DM9,PM); 

// m89 chess_separator_scheduler;   next: m90 (next offset: 102)

// m90;   next: m91 (next offset: 105)
000102  2 0  "0110100000010000"   // (RbL[0]) = load_const_1_B1 (2,DM); 
000103  0 0  "0000000000000010"   // /
000104  1 0  "1100000000011000"   // (DM9,PM) = store_const_1_B2 (RbL[0],1,DM9,PM); 

// m91 chess_separator_scheduler;   next: m92 (next offset: 105)

// m92;   next: m93 (next offset: 108)
000105  2 0  "0110100000010000"   // (RbL[0]) = load_const_1_B1 (1,DM); 
000106  0 0  "0000000000000001"   // /
000107  1 0  "1100000000001000"   // (DM9,PM) = store_const_1_B2 (RbL[0],0,DM9,PM); 

// m93 chess_separator_scheduler;   next: m94 (next offset: 108)

// m94;   next: m95 (next offset: 111)
000108  2 0  "0110100000010000"   // (RbL[0]) = load_const_1_B1 (0,DM); 
000109  0 0  "0000000000000000"   // /
000110  1 0  "1100000000011000"   // (DM9,PM) = store_const_1_B2 (RbL[0],1,DM9,PM); 

// m95 chess_separator_scheduler;   next: m341 (next offset: 111)

// m341;   next: m97 (next offset: 119)
000111  2 0  "0110100000010000"   // (RbL[0]) = load_const_1_B1 (3,DM); 
000112  0 0  "0000000000000011"   // /
000113  2 0  "0010000000000000"   // (RbL[0],nz_flag) = _ad_const_1_B1 (RbL[0]); 
000114  0 0  "0000000000001111"   // /
000115  1 0  "0111100010100000"   // (RbL[0],c_flag,nz_flag) = shl_const_1_B1 (RbL[0]); 
000116  2 0  "0010100000000000"   // (RbL[0],nz_flag) = _or_const_1_B1 (RbL[0]); 
000117  0 0  "0000000000001111"   // /
000118  1 0  "1100000000001000"   // (DM9,PM) = store_const_1_B2 (RbL[0],0,DM9,PM); 

// m97 chess_separator_scheduler;   next: m98 (next offset: 119)

// m98;   next: m99 (next offset: 122)
000119  2 0  "0110100000010000"   // (RbL[0]) = load_const_1_B1 (2,DM); 
000120  0 0  "0000000000000010"   // /
000121  1 0  "1100000000011000"   // (DM9,PM) = store_const_1_B2 (RbL[0],1,DM9,PM); 

// m99 chess_separator_scheduler;   next: m100 (next offset: 122)

// m100;   next: m101 (next offset: 125)
000122  2 0  "0110100000010000"   // (RbL[0]) = load_const_1_B1 (1,DM); 
000123  0 0  "0000000000000001"   // /
000124  1 0  "1100000000001000"   // (DM9,PM) = store_const_1_B2 (RbL[0],0,DM9,PM); 

// m101 chess_separator_scheduler;   next: m102 (next offset: 125)

// m102;   next: m103 (next offset: 128)
000125  2 0  "0110100000010000"   // (RbL[0]) = load_const_1_B1 (0,DM); 
000126  0 0  "0000000000000000"   // /
000127  1 0  "1100000000011000"   // (DM9,PM) = store_const_1_B2 (RbL[0],1,DM9,PM); 

// m103 chess_separator_scheduler;   next: m104 (next offset: 128)

// m104;   next: m105 (next offset: 132)
000128  2 0  "0110110000000000"   // (RwL[0]) = const_5_B1 (); 
000129  0 0  "0000000000011010"   // /
000130  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000131  0 0  "0000000000000000"   // /

// m105 subroutine call;   next: m106 (next offset: 132)

// m106;   next: m107 (next offset: 133)
000132  1 0  "0110111101000010"   // (DM[6]) = store__pl_rd_res_reg_const_1_B2 (RwL[0],6,DM[6],R7); 

// m107 chess_separator_scheduler;   next: m358 (next offset: 133)

// m358;   next: m109 (next offset: 140)
000133  1 0  "0110101100000101"   // (RbL[0]) = load__pl_rd_res_reg_const___uchar_1_B2 (6,DM[6],R7); 
000134  2 0  "0010000000000000"   // (RbL[0],nz_flag) = _ad_const_1_B1 (RbL[0]); 
000135  0 0  "0000000000001111"   // /
000136  1 0  "0111100010100000"   // (RbL[0],c_flag,nz_flag) = shl_const_1_B1 (RbL[0]); 
000137  2 0  "0010100000000000"   // (RbL[0],nz_flag) = _or_const_1_B1 (RbL[0]); 
000138  0 0  "0000000000001111"   // /
000139  1 0  "1100000000001000"   // (DM9,PM) = store_const_1_B2 (RbL[0],0,DM9,PM); 

// m109 chess_separator_scheduler;   next: m262 (next offset: 140)

// m262;   next: m113 (next offset: 142)
000140  1 0  "0110111100000010"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B2 (6,DM[6],R7); 
000141  1 0  "1100000000011100"   // (DM9,PM) = store_extract_hi_const_1_B2 (RwL[0],1,DM9,PM); 

// m113 chess_separator_scheduler;   next: m289 (next offset: 142)

// m289;   next: m117 (next offset: 144)
000142  2 0  "0010000000100000"   // (DM9,PM,nz_flag) = load_const__ad_const_store_2_B1 (0,DM9,PM); 
000143  0 0  "0000000010111111"   // /

// m117 chess_separator_scheduler;   next: m296 (next offset: 144)

// m296;   next: m119 (next offset: 146)
000144  2 0  "0010000000100000"   // (DM9,PM,nz_flag) = load_const__ad_const_store_1_B1 (0,DM9,PM); 
000145  0 0  "0000000001111111"   // /

// m119 chess_separator_scheduler;   next: m120 (next offset: 146)

// m120;   next: m121 (next offset: 148)
000146  2 0  "0110110000100000"   // (DM9,PM) = store_const_const_2_B1 (0,DM9,PM); 
000147  0 0  "0000000001111111"   // /

// m121 chess_separator_scheduler;   next: m398 (next offset: 148)

// m398;   next: m378, jump target: m132 (next offset: 150)
000148  2 0  "0100110000000110"   // (DM9,PM,nz_flag) = load_const__ad_const_cmp_const_cc_eq__jump_const_1_B1 (0,6,DM9,PM); 
000149  0 0  "0000000000000010"   // /

// m378;   next: m132, jump target: m135 (next offset: 154)
000150  2 0  "0110101111010000"   // (RbL[0],DM9,PM) = load_const_mov_bf_const_const_1_B1 (0,DM9,PM); 
000151  0 0  "0001001000000000"   // /
000152  1 0  "0100000010001000"   // (nz_flag,c_flag,o_flag) = cmp_const_1_B2 (RbL[0]); 
000153  1 0  "0101000100000011"   // () = cc_ne__jump_const_1_B1 (nz_flag,3); 

// m132;   next: m133 (next offset: 156)
000154  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000155  0 0  "0000000000000000"   // /

// m133 subroutine call;   next: m134 (next offset: 156)

// m134 (next offset: 156)

// m135;   next: m137 (next offset: 156)

// m137;   next: m138 (next offset: 159)
000156  1 0  "0110100010000000"   // (RbL[0]) = const_4_B2 (); 
000157  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000158  0 0  "0000000000000000"   // /

// m138 subroutine call;   next: m140 (next offset: 159)

// m140;   next: m141 (next offset: 163)
000159  2 0  "0110110000000000"   // (RwL[0]) = const_3_B1 (); 
000160  0 0  "0000000000001110"   // /
000161  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000162  0 0  "0000000000000000"   // /

// m141 subroutine call;   next: m385 (next offset: 163)

// m385;   next: m146, jump target: m160 (next offset: 166)
000163  2 0  "0110010000000000"   // (nz_flag) = _ad_const_cmp_const_1_B1 (RwL[0]); 
000164  0 0  "0000001100000000"   // /
000165  1 0  "0101000000010011"   // () = cc_eq__jump_const_1_B1 (nz_flag,19); 

// m146;   next: m147 (next offset: 171)
000166  1 0  "0110110010011000"   // (RwL[0]) = const_2_B2 (); 
000167  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (1,R7); 
000168  0 0  "0000000000000001"   // /
000169  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000170  0 0  "0000000000000000"   // /

// m147 subroutine call;   next: m149 (next offset: 171)

// m149;   next: m150 (next offset: 174)
000171  1 0  "0110110010100000"   // (RwL[0]) = const_1_B2 (); 
000172  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000173  0 0  "0000000000000000"   // /

// m150 subroutine call;   next: m152 (next offset: 174)

// m152;   next: m153 (next offset: 178)
000174  2 0  "0010000000110011"   // (DM[1],nz_flag) = load__pl_rd_res_reg_const__ad_const_store_1_B1 (1,DM[1],DM[1],R7); 
000175  0 0  "0000000111111100"   // /
000176  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (1,R7); 
000177  0 0  "0000000000000001"   // /

// m153 chess_separator_scheduler;   next: m154 (next offset: 178)

// m154;   next: m155 (next offset: 181)
000178  1 0  "0110110010011000"   // (RwL[0]) = const_2_B2 (); 
000179  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000180  0 0  "0000000000000000"   // /

// m155 subroutine call;   next: m157 (next offset: 181)

// m157;   next: m158 (next offset: 184)
000181  1 0  "0110110010100000"   // (RwL[0]) = const_1_B2 (); 
000182  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000183  0 0  "0000000000000000"   // /

// m158 subroutine call;   next: m410 (next offset: 184)

// m410;   next: m392 (next offset: 184)

// m160;   next: m392 (next offset: 184)

// m392;   next: m302, jump target: m175 (next offset: 188)
000184  2 0  "0110100000100000"   // (DM9) = store_const_const_1_B1 (0,DM9); 
000185  0 0  "0000000000000000"   // /
000186  2 0  "0100100000000100"   // (c_flag,nz_flag,o_flag) = load_const_cmp_const_cc_eq__jump_const_1_B1 (0,4,DM9); 
000187  0 0  "0000000000000000"   // /

// m302;   next: m180 (next offset: 190)
000188  2 0  "0010100000100000"   // (DM9,PM,nz_flag) = load_const__or_const_store_1_B1 (0,DM9,PM); 
000189  0 0  "0000000010000000"   // /

// m175;   next: m180 (next offset: 190)

// m180 (next offset: /)
000190  2 0  "0001011111111111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (12,R7,R7); 
000191  0 0  "0000000000001100"   // /
000192  1 0  "0001101111000100"   // () = ret_1_B1 (); 

