/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_IIU.h 21074 2019-07-22 13:39:34Z dep10330 $
  $Revision: 21074 $
*/

/**
 * @file
 * Declarations of the stubs to call IIU specific system functions (ROM library).
 */

#ifndef PHCAIKEYLLGENFUNC_CS_IIU_H
#define PHCAIKEYLLGENFUNC_CS_IIU_H

#include "types.h"

/**
 * @defgroup genfunclib_stubs_iiu IIU
 * Caller stubs for IIU specific system functions (ROM library).
 * @{
 */

/**
 * Receive data from the Passive LF interface (IIU, Immobilizer Interface Unit).
 *
 * @param[out] data_buffer Byte buffer where the system call will store the received data.
 * @param[in]  number_of_bytes Size of the data buffer in bytes.
 * @param[out] received_bits The number of bits received from the IIU hardware
 * (might be less than the size of the buffer, or 0 indicating an error).
 * @return ::SUCCESS in case of success, ::ERROR otherwise. Typical error cases
 * are incorrect @p data_buffer pointer in terms of address, boundary, or a
 * failure in the reception.
 *
 * @note uses timer 0 to implement Twait,BS. At entry, this function waits until timer 0 is expired.
 * @note the data_buffer pointer is checked against location (it must be a user address), and boundaries
 * meaning that the data_buffer+number_of_bytes pointer must be within the user RAM bounds.
 * @see phcaiKEyLLGenFunc_CS_IIU_send
 */
error_t phcaiKEyLLGenFunc_CS_IIU_receive(uint8_t* const data_buffer, const uint8_t number_of_bytes, uint8_t * received_bits);

/**
 * Transmit data using the Passive LF interface (IIU, Immobilizer Interface Unit).
 *
 * @param[out] data_buffer A byte buffer containing the data to be transmitted.
 * @param[in]  number_of_bytes Number of bytes to be sent.
 * @return ::SUCCESS in case of success, ::ERROR otherwise. Typical error cases
 * are incorrect @p data_buffer pointer in terms of address, boundary.
 *
 * @note uses timer 0 to implement Twait,BS. Timer 0 is started before return from this function.
 * @note the data_buffer pointer is checked against location (it must be a user address), and boundaries
 * meaning that the data_buffer+number_of_bytes pointer must be within the user RAM bounds.
 * @see phcaiKEyLLGenFunc_CS_IIU_receive
 */
error_t phcaiKEyLLGenFunc_CS_IIU_send(uint8_t* const data_buffer, const uint8_t number_of_bytes);

/**
 * Set the value of a certain IIUTIMx SFR.
 *
 * @param[in]   timx_index
 *              index of the IIUTIMx SFR (index 5 corresponds to IIUTIMP)
 * @param[in]   value
 *              desired SFR value
 *
 * @return SUCCESS if the timx_index was updated, ERROR otherwise.
 *
 * @note If timx_index corresponds to IIUTIMP, only the LSBit of value is
 * considered.
 */
error_t phcaiKeyLLHal_CS_IIU_TIMX_SFR_set(const uint8_t timx_index, const uint8_t value);

/**
 * @brief Initialize the C&T transmit unit by loading trim and timing
 * settings.
 *
 * - Initialize C&T specific timing values.
 * - Initialize CTDRV, LFTUNCHxTX.
 *
 * @pre ULP15 must be enabled and ready for reading.
 * @post The C&T module can be used for transmission.
 */
void phcaiKEyLLGenFunc_CS_IIU_CatInit(void);

/**
 * @brief Send the given data via C&T.
 *
 * @param[in] data_buffer Byte buffer containing the data to be transmitted.
 * @param[in] number_of_bytes Number of bytes to be sent.
 * The given value must be >0.
 * @param[in] t_ch_resp 14-bit counter value corresponding to the expected
 * carrier-off event time (LFCLK cycles).
 * @return ::SUCCESS in case of success, ::ERROR in case the parameters are
 * invalid or the transmit sequence was aborted by the base station.
 *
 * @pre The IIU state must be set to TXWAIT.
 * @note The caller is responsible for ensuring that the 14-bit counter value
 * does not exceed t_ch_resp before the transmit routine is ready for
 * transmission.
 */
error_t phcaiKEyLLGenFunc_CS_IIU_CatSend(const uint8_t* const data_buffer,
  const uint8_t number_of_bytes, const uint16_t t_ch_resp);

/**
 * @brief Execute a muted C&T transmission for a given number of payload bytes.
 *
 * Execute a C&T transmission without actually sending any data.
 *
 * @param[in] number_of_bytes Number of payload bytes the transmit routine
 * should be executed for. The given value must be >0.
 * @param[in] t_ch_resp 14-bit counter value corresponding to the expected
 * carrier-off event time (LFCLK cycles).
 *
 * @return ::SUCCESS in case of success, ::ERROR in case the parameters are
 * invalid or the transmit sequence was aborted by the base station.
 *
 * @see phcaiKEyLLGenFunc_CS_IIU_CatSend
 */
error_t phcaiKEyLLGenFunc_CS_IIU_CatSendMuted(const uint8_t number_of_bytes,
  const uint16_t t_ch_resp);
  
/*@}*/
/*@}*/

#endif
