/*
  -----------------------------------------------------------------------------
  Copyright 2010 - 2020  NXP
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, <PERSON>FF<PERSON>IATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: ncf29xx_sfrdef.s 24956 2020-01-31 09:22:44Z dep10330 $
  $Revision: 24956 $
*/

/**
 * @file
 * Include the SFR declarations header file for the device selected by preprocessor symbol (macro),
 * e.g. NCF29A1. Assembly language only.
 */

#if defined( NCF29A1 ) || defined( NCF29A2 ) || defined( NCF29A3 ) || defined( NCF29A4 )
#include "ncf29A1/ncf29A1_sfr.s"

#elif defined( NCF2953 ) || defined( NCF2954 )
#include "ncf2953/ncf2953_sfr.s"

#elif defined( NCF21A2 )
#include "ncf21A2/ncf21A2_sfr.s"

#elif defined( NCF2961 )
#include "ncf2961/ncf2961_sfr.s"

#elif defined( NCF2161 )
#include "ncf2161/ncf2161_sfr.s"

#elif defined( NCF29A7 ) || defined( NCF29A8 )
#include "ncf29A7/ncf29A7_sfr.s"

#elif defined( NCF2957 ) || defined( NCF2958 )
#include "ncf2957/ncf2957_sfr.s"

#elif defined( NCF2157 )
#include "ncf2157/ncf2157_sfr.s"

#elif defined(NCF29AA) || defined(NCF29AB)
#include "ncf29AA/ncf29AA_sfr.s"

#elif defined(NCF295A) || defined(NCF295B)
#include "ncf295A/ncf295A_sfr.s"

#elif defined( NCF215A )
#include "ncf215A/ncf215A_sfr.s"

// NCF29AC / NCF29AD are redirected to NCF29AE
#elif defined(NCF29AE) || defined(NCF29AF) || defined(NCF29AC) || defined(NCF29AD)
#include "ncf29AE/ncf29AE_sfr.s"

// NCF295C / NCF295D are redirected to NCF295E
#elif defined( NCF295E ) || defined( NCF295F ) || defined( NCF295C ) || defined( NCF295D )
#include "ncf295E/ncf295E_sfr.s"

#elif defined( NCF215C )
#include "ncf215C/ncf215C_sfr.s"

#elif defined( NCF215E )
#include "ncf215E/ncf215E_sfr.s"

#else

#error "device not (yet) supported or not defined."

#endif

/* eof */
