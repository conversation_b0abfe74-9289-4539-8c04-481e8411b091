
#include "ncf29xx.h"
#include "phcaiKEyLLGenFunc.h"

#include "buttons.h"
#include "timer.h"
#include "defs.h"

//******************************************************************************
// local define
//******************************************************************************
// Only this Bits are mask out from Button Port
//#define BUTTON_SHIFT          4
// Demo board LID1962
#define BUTTON_SHIFT           0

//******************************************************************************
// local prototypes
//******************************************************************************
uint8_t btn_get_debounced_buttons (void);
uint8_t btn_debounce_buttons (void);
uint8_t btn_detect_buttons (void);

//******************************************************************************
// global function
//******************************************************************************
uint8_t btn_get_bid (void)
{
  uint8_t u8_ButtonCode;

  u8_ButtonCode = 0x00;

  u8_ButtonCode = btn_debounce_buttons();

  return (u8_ButtonCode);
}

//******************************************************************************
// local functions
//******************************************************************************


//******************************************************************************
// get debounced button code with equidistant sampling period (general function library)
//******************************************************************************
uint8_t btn_get_debounced_buttons (void)
{
  uint8_t  u8_ButtonCode;           // Local Variable for button code

  // use general debounce function with equidistant debounce sampling
  phcaiKEyLLGenFunc_Util_Debounce
  (                     
    &u8_ButtonCode,    // This is the return Value of Buttun debouncing, One result bit per port
    1000,              // Delay between samplings in microseconds
    10,                // Number of samples to take
    3                  // threshold, Minimum number of '1' samples required to give a result of '1'
  );   
  
  /* 
  * delivered: 
  * u8_ButtonCode = 0 sw3 sw2 sw1 0  0   0   0 
  * buttons low active (pressed = logical zero)
  * needed:    
  * u8_ButtonCode = 0  0   0   0  0 sw3 sw2 sw1
  * buttons high active (pressed = logical one)
  */
  return (((~u8_ButtonCode) & BUTTON_MASK ) >> BUTTON_SHIFT); 
}

//******************************************************************************
// detect the button input port and deliver the button code (BID)
// pressed button is set to logical ONE
//******************************************************************************
uint8_t btn_detect_buttons (void)
{
  uint8_t u8_ButtonCode;

  u8_ButtonCode = 0x00;

  /* Button ports are low active, thus a pressed button delivers a logical ZERO

  * NCF2960 Demo Board:
  * SW1 = P14 = P1INS.4
  * SW2 = P15 = P1INS.5
  * SW3 = P16 = P1INS.6
  * P1INS = [0 sw3 sw2 sw1 0 0 0 0]

  * get button code and shift to LSB position
  * a pressed button now delivers a logical ONE
  * u8_ButtonCode = [0 0 0 0  0 sw3 sw2 sw1]
  */
  u8_ButtonCode  = (((~P1INS.val) & BUTTON_MASK )>> BUTTON_SHIFT);

  return (u8_ButtonCode);
}

//******************************************************************************
// use alternative concept to debounce buttons depending on BDEB settings from EEPROM.
//******************************************************************************
uint8_t btn_debounce_buttons (void)
{
  // prime value scaling factor (unit: us)
  //uint8_t   u8arr_PrimeDetect[]={2,3,5,7,9,11,13,17,23,29,31,37,41,47,53,59};
  uint8_t   u8_NumOfDetect;                      // number of samples points in a single loop
  uint8_t   u8_CurBID;                           // current button code (BID)
  uint8_t   u8_Error;                            // error counter for bouncing events
  uint8_t   u8_TimeFactor;
  uint8_t   u8_DebounceLoop;                     // overall debouncing loops
  uint8_t   u8_ErrorLimit;                       // max. number of debouncing events (errors)
  uint8_t   i,k;                                 // indeces
  uint8_t   u8arr_bdeb[2];                       // settings array (read from EEPROM)
  uint16_t  u16_TimerDelay;
  
  uint8_t   m = 0;
  uint8_t   n = 0;
  uint8_t   CurBIDVal = 0; 
 #if 0 
  /*
  BDEB Coding:
  EEPROM: EE_BDEB [1,0] is in RKE config page 7, bit [31:16]

  The data is read from EEPROM EE_BDEB[1,0] to an array u8arr_bdeb[0,1] MSB first!
  Thus the array indexing is equal to the definition in the specification.

  => EE_BDEB[1] bit [7:4] => u8arr_bdeb[0] bit [7:4] => number of allowed errors [1:16](error threshold)
     EE_BDEB[1] bit [3:0] => u8arr_bdeb[0] bit [3:0] => number of detection loops = number of single values
                                                        taken from prime value array [1:16]
                                                        [1 => 2us / 2 => 2us,3us / 3 => 2us,3us,5us...]
     EE_BDEB[0] bit [7:4] => u8arr_bdeb[1] bit [7:4] => number of debouncing loops [1:16]
     EE_BDEB[0] bit [3:0] => u8arr_bdeb[1] bit [3:0] => time scaling factor for single prime value from array [1:16]
                                                        time factor * prime detect value[i]
                                                        [1 => 1*2us,1*3us,1*5us...] /
                                                        [2 => 2*2us,2*3us,2*5us...] /
                                                        [3 => 3*2us,3*3us,3*5us...]...

  u8_NumOfDetect:
  => max. time delay take from prime array: [2us+3us+5us+...+59us = 397us]
  u8_TimeFactor:
  => max. time delay using scaling factor: (time scaling factor * [2us+3us+5us+...+59us = 397us]) => 16 * 397us = 6352us
  u8_DebounceLoop:
  => max. time delay using loop counter: 16 * 6352us = 101.632us ~= 100ms
  */

  // get button depending data from dispatcher
  // take care: BDEB[0] gets the MSB (byte 1)
  
//  dat_get_bdeb (u8arr_bdeb);++********
  u8arr_bdeb[0] = 0xF9;
  u8arr_bdeb[1] = 0x0B;
  // get number of detection loops, index on prime value array
  // 0: 1 samples, F: 16 samples (EE_BDEB byte 1, LSB)
  u8_NumOfDetect = (u8arr_bdeb[0] & 0x0F) + 1;

  // get time factor to spread single prime detect value
  // mask valid bits (EE_BDEB byte 0, LSB) 0: factor 1 , F: factor 16
  u8_TimeFactor = (u8arr_bdeb[1] & 0x0F) + 1;

  // get total number of debouncing loops 0: 1 loop, F: 16 loops (EE_BDEB byte 0, MSB)
  u8_DebounceLoop = ((u8arr_bdeb[1] & 0xF0) >> 4) + 1;

  // get max. number of exceptable errors (EE_BDEB byte 1, MSB)++
  u8_ErrorLimit = (u8arr_bdeb[0] & 0xF0) >> 4;

  // to get an overall error threshold, the given error limit
  // is multiplied to the number of debouncing loops
  u8_ErrorLimit *= u8_DebounceLoop;                        // calculate overall error limit (N loops * max. number of errors)
  u8_Error = 0;                                            // reset error counter
  
  // button debouncing and detection loop
  for (k=0; k<u8_DebounceLoop; k++)           // debouncing loop, depend on loop counter BDEB[1] bit [7:4]
  {
    for (i=0; i<0x0A; i++)          // detection loop, depends on set number of detection samples BDEB[0] bit [3:0]
    {
      // range: time factor * prime detect => ([1:16]*2us -> [1:16]*59us) / max: 16 * SUM(PrimeDetect[i])
  //    u16_TimerDelay = (uint16_t)u8arr_PrimeDetect[i] * (uint16_t)u8_TimeFactor;
      
      // non equidistant delay before button sampling
      timer_delay_us(u16_TimerDelay);
    timer_delay_us(10); 
      // get current button code
      cycle_delay_ms(1); 
      u8_CurBID = btn_detect_buttons();       // a pressed button delivers a logical ONE
     if( u8_CurBID != 0 )
     {
         timer_delay_us(10); 
         u8_CurBID = btn_detect_buttons();
     }
     
     if (u8_CurBID == 0)                     // if button pressed, bid >0, if bid=0, no button is pressed or button is bouncing
      {
        u8_Error++;                           // increment error counter until upper error limit (threshold) is reached
      }
      
      if (u8_Error > u8_ErrorLimit)          // error limit exceeded?
      {
        return 0;                            //  return value to ensure power-off on error
      }
    }
  }
  #endif
 ////////// get debounced BID////////////////////////////   ///////////////////////////////////////////////////////////////////////////
   for (m=0; m<0x0A; m++)          
    {
      cycle_delay_ms(1);
      CurBIDVal = btn_detect_buttons(); 
      if( (CurBIDVal&BUTTON_MASK)!=0)
      {
          n=n+1;
      }
    }
    if(n>=4)
    {
       return CurBIDVal;
    }
    else
    {
       return 0;
    }
}

//******************************************************************************