/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_ULPEE.h 18366 2019-02-19 15:01:29Z dep10330 $
  $Revision: 18366 $
*/

/**
 * @file
 * Declarations of the stubs to call ULPEE specific system functions (ROM library).
 */

#ifndef PHCAIKEYLLGENFUNC_CS_ULPEE_H
#define PHCAIKEYLLGENFUNC_CS_ULPEE_H

#include "types.h"

/**
 * @defgroup genfunclib_stubs_ulpee ULP
 * Caller stubs for ULP specific API functions placed
 * in ROM and executed in system mode.
 * @{
 */

/**
 * Programs one page of the ULP EEPROM (data in big endianness).
 * Only pages in modules 0 to 7 (page numbers 000h to 1FFh) can be written.
 * From TOKEN-PLUS RC001 onwards, also device configuration (DCFG) pages 3D3h to 3D5h 
 * can be written.
 * Byte data[0] is being written to the most significant byte of page.
 *
 * @param[in] data Pointer to the first of the 4 data bytes.
 * @param[in] page_address Page address of the desired target location in the
 *                         ULP EEPROM.
 * @return Error indicator (::SUCCESS in case of success, ::ERROR in case of
 * failure to write the ULPEE).
 *
 * @note ULPEE must be powered on (ULPPON=1) and the module must be enabled
 * (ULPSEL register) at least t_ULP_PON_us before calling this function.
 * @note Interrupt flag IF_ULP is '1' at return from system mode.
 *
 * @see phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage_LE()
 */
error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(
  const uint8_t* const data, const uint16_t page_address);

/**
 * Programs one page in the ULP EEPROM (data in little endianness).
 * Only pages in modules 0 to 7 (page numbers 000h to 1FFh) can be written.
 * From TOKEN-PLUS RC001 onwards, also device configuration (DCFG) pages 3D3h to 3D5h 
 * can be written.
 * Byte data[0] is being written to the least significant byte of page.
 *
 * @param[in] data Pointer to the first of the 4 data bytes.
 * @param[in] page_address Page address of the desired target location in the
 *                         ULP EEPROM.
 * @return Error indicator (::SUCCESS in case of success, ::ERROR in case of
 * failure to write the ULPEE).
 *
 * @note ULPEE must be powered on (ULPPON=1) and the module must be enabled
 * (ULPSEL register) at least t_ULP_PON_us before calling this function.
 * @note Interrupt flag IF_ULP is '1' at return from system mode.
 *
 * @see phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage()
 */
error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage_LE(
  const uint8_t* const data, const uint16_t page_address);

/**
 * Programs one page of the ULP EEPROM (data in big endianness) and returns
 * immediately after programming has started (does not wait until programming
 * has ended).
 * Only pages in modules 0 to 7 (page numbers 000h to 1FFh) can be written.
 * From TOKEN-PLUS RC001 onwards, also device configuration (DCFG) pages 3D3h to 3D5h 
 * can be written.
 *
 * @param[in] data Pointer to the first of the 4 data bytes.
 * @param[in] page_address Page address of the desired target location in the
 *                         ULP EEPROM.
 * @return Error indicator (always ::SUCCESS)
 *
 * @note ULPEE must be powered on (ULPPON=1) and the module must be enabled
 * (ULPSEL register) at least t_ULP_PON_us before calling this function.
 *
 * @see phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage_LE()
 */
error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait(
  const uint8_t * const data, const uint16_t page_address);

/**
 * Programs one page of the ULP EEPROM (data in little endianness) and returns
 * immediately after programming has started (does not wait until programming
 * has ended).
 * Only pages in modules 0 to 7 (page numbers 000h to 1FFh) can be written.
 * From TOKEN-PLUS RC001 onwards, also device configuration (DCFG) pages 3D3h to 3D5h 
 * can be written.
 *
 * @param[in] data Pointer to the first of the 4 data bytes.
 * @param[in] page_address Page address of the desired target location in the
 *                         ULP EEPROM.
 * @return Error indicator (always ::SUCCESS)
 *
 * @note ULPEE must be powered on (ULPPON=1) and the module must be enabled
 * (ULPSEL register) at least t_ULP_PON_us before calling this function.
 *
 * @see phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage_LE()
 */
error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait_LE(
  const uint8_t * const data, const uint16_t page_address);

/**
 * Reads a page from ULPEE starting from the given page address in big endian
 * byte order, that is, the most significant byte of the 32-bit page value
 * is stored in destination buffer index 0 (lowest RAM address).
 *
 * @param[out] data Target buffer in User RAM (with a size of at least 4 bytes).
 * @param[in] page_address Page address of desired source location in ULP EEPROM.
 *
 * @note ULPEE must be powered on (ULPPON=1) and the module must be enabled
 * (ULPSEL register) at least t_ULP_PON_us before calling this function.
 * @note Interrupt flag IF_ULP is '1' at return from system mode.
 *
 * @see phcaiKEyLLGenFunc_CS_ULPEE_ReadPage_LE()
 */
void phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(
  uint8_t* const data, const uint16_t page_address);

/**
 * Reads a page from ULPEE starting from the given page address in little endian
 * byte order, that is, the least significant byte of the 32-bit page value
 * is stored in destination buffer index 0 (lowest RAM address).
 *
 * @param[out] data Target buffer in User RAM (with a size of at least 4 bytes).
 * @param[in] page_address Page address of desired source location in ULP EEPROM.
 *
 * @note ULPEE must be powered on (ULPPON=1) and the module must be enabled
 * (ULPSEL register) at least t_ULP_PON_us before calling this function.
 * @note Interrupt flag IF_ULP is '1' at return from system mode.
 *
 * @see phcaiKEyLLGenFunc_CS_ULPEE_ReadPage()
 */
void phcaiKEyLLGenFunc_CS_ULPEE_ReadPage_LE(
  uint8_t* const data, const uint16_t page_address);

/**
 * Reads a certain number of bytes from ULP.
 * The least significant byte of each 32-bit page is read first (in the same
 * order as read from the ULPDATA register).
 *
 * @param[out] data Target buffer in User RAM (with a size of at least
 *                  @p num_bytes bytes)
 * @param[in] byte_address Start address (in bytes) of desired source location
 *                         in ULP EEPROM.
 * @param[in] num_bytes Number of bytes to read.
 *
 * @note ULPEE must be powered on (ULPPON=1) and the module must be enabled
 * (ULPSEL register) at least t_ULP_PON_us before calling this function.
 * @note The interrupt flag IF_ULP is '1' at return from system mode.
 */
void phcaiKEyLLGenFunc_CS_ULPEE_ReadBytes(
  uint8_t* const data, const uint16_t byte_address,
  const uint16_t num_bytes);

/**
 * Set the BUSYPROG bit in ULPCON1 (bit 7, internal name PROGEN), in order to start the ULPEE programming process.
 */
void phcaiKEyLLGenFunc_CS_ULPEE_SetPROGEN(void);

/*@}*/

#endif
