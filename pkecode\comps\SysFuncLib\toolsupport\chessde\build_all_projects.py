# build_all_projects.py : search subfolders for ChessDE project files (.prx)
# and run build in Debug and Release configuration.
# Output goes to stdout (console if not otherwise redirected).
# encoding: UTF-8

import os, subprocess, code

#--------------------------------------------------------------------------------------------------
# Set your tool path here!
# Backslashes must be escaped to avoid interpretation.

#CHESSMAKE="C:\\Program Files (x86)\\Synopsys\\ASIP\\mrk3-e-13R1.4\\bin\\WINbin\\chessmk.exe"
CHESSMAKE="C:\\Synopsys\\ipp-mrk3-e\\M-2017.03\\win64\\bin\\WINbin\\chessmk.exe"

# select which configurations shall be built
#BUILD_CONFIGS = [ "Debug", "Release" ]
#BUILD_CONFIGS = [ "Debug" ]
BUILD_CONFIGS = [ "Release" ]

# select additional options for make tool
OPTIONS       = "+P 4"

#--------------------------------------------------------------------------------------------------

def build_project(s_path, s_config):
    params = [ CHESSMAKE ] + OPTIONS.split(" ") + [ "-C", s_config, s_path ]
    #print(params)
    #ocp = subprocess.run([CHESSMAKE, "+v"])
    ocp = subprocess.run(params)
    return ocp.returncode

#--------------------------------------------------------------------------------------------------

print("================================================================")
print("  Build ALL ChessDE projects in sub folders of work directory   ")
print("================================================================")
print("  Selected make tool: {}".format(CHESSMAKE))
print("  Working directory : {}".format(os.getcwd()))
print("  Options: {}".format(OPTIONS))
print("  Configurations: ",end="")
for s_cfg in BUILD_CONFIGS:
    print(s_cfg, end=",")
print("")
print("================================================================")

i_res = 0

IC = code.InteractiveConsole()
ui = IC.raw_input("Building multiple projects may take some time and cause high CPU load. " +
                  "Press enter to start.")
print("================================================================\n\n")

for root, dirs, files in os.walk(os.getcwd()):
    for s_path in files:
        if s_path.endswith(".prx"):
            os.chdir(root)
            for s_cfg in BUILD_CONFIGS:
                print("Building project {}\n".format(s_path,s_cfg))
                i_res = build_project(s_path, s_cfg)
                if i_res != 0: # build error?
                    break
                print("========================================================\n\n")
            if i_res != 0:
                break
    if i_res != 0:
        break
if i_res == 0:
    print("\nCompleted successfully.")
else:
    print("\n!!! Build stopped with errors !!!")
    print("Project: {}, configuration: {}".format(s_path,s_cfg))

ui = IC.raw_input("\nPress enter to close.")

# eof


