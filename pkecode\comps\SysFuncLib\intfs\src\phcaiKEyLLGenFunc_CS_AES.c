/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_AES.c 11676 2018-01-02 13:17:01Z dep17622 $
  $Revision: 11676 $
*/

/**
 * @file
 * Implementation of the stubs to call AES specific KEyLink Lite General Functions
 * placed in ROM.
 */

#include "phcaiKEyLLGenFunc_CS_AES.h"
#include "phcaiKEyLLGenFunc_CS_Utils.h"
#include "phcaiKEyLLGenFunc_SCI.h"

void phcaiKEyLLGenFunc_CS_AES_LoadData(
  const uint8_t* const input_vector, const uint8_t len)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_AES_LOAD_DATA;
  phcaiKEyLLGenFunc_Func_Params.params.aes_load_data.data = input_vector;
  phcaiKEyLLGenFunc_Func_Params.params.aes_load_data.len = len;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_AES_LoadKey(const uint8_t* const key)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_AES_LOAD_KEY;
  phcaiKEyLLGenFunc_Func_Params.params.aes_load_key.key = key;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_AES_Encrypt(
  uint8_t* const output_vector)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_AES_ENCRYPT;
  phcaiKEyLLGenFunc_Func_Params.params.aes_encrypt.output_vector
    = output_vector;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_AES_Init(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_AES_INIT;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_AES_disable(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_AES_DISABLE;
  call_syscall(5);
}
