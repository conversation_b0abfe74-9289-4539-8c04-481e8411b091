# -----------------------------------------------------------------------------
# (c) NXP B.V. All rights reserved.
# Disclaimer
# 1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
#    warranties of any kind. NXP makes no warranties to Licensee and shall not
#    indemnify Licensee or hold it harmless for any reason related to the NXP
#    Software/Source Code or otherwise be liable to the NXP customer. The NXP
#    customer acknowledges and agrees that the NXP Software/Source Code is
#    provided AS-IS and accepts all risks of utilizing the NXP Software under
#    the conditions set forth according to this disclaimer.
#
# 2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
#    BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
#    FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
#    RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
#    SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
#    INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
#    RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
#    CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
#    RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
#    THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
#    INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
#    (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
#    AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
#    SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
#    SUCH DAMAGES.
#
# 3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
#    time, also without informing customer.
#
# 4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
#    companies from and against any claims, suits, losses, damages,
#    liabilities, costs and expenses (including reasonable attorney's fees)
#    resulting from Licensee's and/or Licensee customer's/licensee's use of the
#    NXP Software/Source Code.
# -----------------------------------------------------------------------------

# build_all_projects.py : search subfolders for ChessDE project files (.prx)
# and run build in Debug and Release configuration.
# Output goes to stdout (console if not otherwise redirected).
# encoding: UTF-8

import os, subprocess, code

#--------------------------------------------------------------------------------------------------
# Set your tool path here!
# Backslashes must be escaped to avoid interpretation.

#CHESSMAKE="C:\\Program Files (x86)\\Synopsys\\ASIP\\mrk3-e-13R1.4\\bin\\WINbin\\chessmk.exe"
CHESSMAKE="C:\\Synopsys\\ipp-mrk3-e\\M-2017.03\\win64\\bin\\WINbin\\chessmk.exe"

# select which configurations shall be built
#BUILD_CONFIGS = [ "Debug", "Release" ]
BUILD_CONFIGS = [ "Debug" ]
#BUILD_CONFIGS = [ "Release" ]

# select additional options for make tool
OPTIONS       = "+P 4"

#--------------------------------------------------------------------------------------------------

def build_project(s_path, s_config):
    params = [ CHESSMAKE ] + OPTIONS.split(" ") + [ "-C", s_config, s_path ]
    #print(params)
    #ocp = subprocess.run([CHESSMAKE, "+v"])
    ocp = subprocess.run(params)
    return ocp.returncode

#--------------------------------------------------------------------------------------------------

print("================================================================")
print("  Build ALL ChessDE projects in sub folders of work directory   ")
print("================================================================")
print("  Selected make tool: {}".format(CHESSMAKE))
print("  Working directory : {}".format(os.getcwd()))
print("  Options: {}".format(OPTIONS))
print("  Configurations: ",end="")
for s_cfg in BUILD_CONFIGS:
    print(s_cfg, end=",")
print("")
print("================================================================")

i_res = 0

IC = code.InteractiveConsole()
ui = IC.raw_input("Building multiple projects may take some time and cause high CPU load. " +
                  "Press enter to start.")
print("================================================================\n\n")

for root, dirs, files in os.walk(os.getcwd()):
    for s_path in files:
        if s_path.endswith(".prx"):
            os.chdir(root)
            for s_cfg in BUILD_CONFIGS:
                print("Building project {}\n".format(s_path,s_cfg))
                i_res = build_project(s_path, s_cfg)
                if i_res != 0: # build error?
                    break
                print("========================================================\n\n")
            if i_res != 0:
                break
    if i_res != 0:
        break
if i_res == 0:
    print("\nCompleted successfully.")
else:
    print("\n!!! Build stopped with errors !!!")
    print("Project: {}, configuration: {}".format(s_path,s_cfg))

ui = IC.raw_input("\nPress enter to close.")

# eof


