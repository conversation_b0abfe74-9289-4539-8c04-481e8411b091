
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:24 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i timer-258389 mrk3


// m3;   next: m4 (next offset: 3)
000000  1 0  "0000010010010111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000001  2 0  "0110111001000011"   // (DM[0]) = _pl_rd_res_reg_const_store_1_B1 (RwL[0],0,DM[0],R7); 
000002  0 0  "0000000000000000"   // /

// m4 chess_separator_scheduler;   next: m5 (next offset: 3)

// m5;   next: m6 (next offset: 7)
000003  2 0  "0110111000000011"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000004  0 0  "0000000000000000"   // /
000005  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000006  0 0  "0000000000000000"   // /

// m6 subroutine call;   next: m9 (next offset: 7)

// m9 (next offset: /)
000007  1 0  "0001010010010111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000008  1 0  "0001101111000100"   // () = ret_1_B1 (); 

