
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:19 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-d7a399 mrk3

[
    0 : void_tx_read_configuration___ushort___PTxRegSettings_t typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : ps_ActTxRegSettings typ=int8_ val=0t0 bnd=a sz=2 algn=2 stl=DM tref=__PTxRegSettings_t_DM
   21 : u16_EeBasePage typ=int8_ val=2t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   22 : u16_baseaddr typ=int8_ val=4t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   23 : __extDM_TxRegSettings_t typ=int8_ bnd=b stl=DM
   24 : __extDM_TxRegSettings_t_Cfg_FREQCON0 typ=int8_ bnd=b stl=DM
   25 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   26 : __extPM_void typ=uint16_ bnd=b stl=PM
   27 : __extDM_void typ=int8_ bnd=b stl=DM
   28 : __extULP_void typ=uint32_ bnd=b stl=ULP
   29 : __extDM_TxRegSettings_t_Cfg_FREQCON1 typ=int8_ bnd=b stl=DM
   30 : __extDM_TxRegSettings_t_Cfg_PLLCON typ=int8_ bnd=b stl=DM
   31 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   32 : __extDM_TxRegSettings_t_Cfg_ASKCON typ=int8_ bnd=b stl=DM
   33 : __extDM_TxRegSettings_t_Cfg_FSKCON typ=int8_ bnd=b stl=DM
   34 : __extDM_TxRegSettings_t_Cfg_BRGCON typ=int8_ bnd=b stl=DM
   35 : __extDM_TxRegSettings_t_Cfg_ASKRMP typ=int8_ bnd=b stl=DM
   36 : __extDM_TxRegSettings_t_Cfg_FSKRMP typ=int8_ bnd=b stl=DM
   37 : __extDM_TxRegSettings_t_Cfg_PACON typ=int8_ bnd=b stl=DM
   38 : __extDM_TxRegSettings_t_Cfg_PAPWR typ=int8_ bnd=b stl=DM
   39 : __extDM_TxRegSettings_t_Cfg_PATRIM typ=int8_ bnd=b stl=DM
   40 : __extDM_TxRegSettings_t_Cfg_PALIMIT typ=int8_ bnd=b stl=DM
   41 : __extDM_TxRegSettings_t_Cfg_ENCCON0 typ=int8_ bnd=b stl=DM
   43 : __arg_u16_EeBasePage typ=int16_ bnd=p tref=uint16_t__
   44 : __arg_ps_ActTxRegSettings typ=int16_ bnd=p tref=__PTxRegSettings_t__
   49 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   53 : __ct_2t0 typ=int16_ val=2t0 bnd=m
   57 : __ct_4t0 typ=int16_ val=4t0 bnd=m
   60 : __fch_u16_EeBasePage typ=int16_ bnd=m
   63 : __tmp typ=int16_ bnd=m
   64 : __fch_u16_baseaddr typ=int16_ bnd=m
   68 : __ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort typ=int16_ val=0r bnd=m
   70 : __tmp typ=int16_ bnd=m
   71 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
   75 : __fch_u16_baseaddr typ=int16_ bnd=m
   78 : __tmp typ=int16_ bnd=m
   81 : __tmp typ=int16_ bnd=m
   82 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
   86 : __fch_u16_baseaddr typ=int16_ bnd=m
   89 : __tmp typ=int16_ bnd=m
   90 : __uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort typ=int16_ val=0r bnd=m
   92 : __tmp typ=int8_ bnd=m
   93 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
   97 : __fch_u16_baseaddr typ=int16_ bnd=m
  100 : __tmp typ=int16_ bnd=m
  103 : __tmp typ=int16_ bnd=m
  104 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  108 : __fch_u16_baseaddr typ=int16_ bnd=m
  111 : __tmp typ=int16_ bnd=m
  114 : __tmp typ=int8_ bnd=m
  115 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  119 : __fch_u16_baseaddr typ=int16_ bnd=m
  122 : __tmp typ=int16_ bnd=m
  125 : __tmp typ=int16_ bnd=m
  126 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  130 : __fch_u16_baseaddr typ=int16_ bnd=m
  133 : __tmp typ=int16_ bnd=m
  136 : __tmp typ=int8_ bnd=m
  137 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  141 : __fch_u16_baseaddr typ=int16_ bnd=m
  144 : __tmp typ=int16_ bnd=m
  147 : __tmp typ=int8_ bnd=m
  148 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  152 : __fch_u16_baseaddr typ=int16_ bnd=m
  155 : __tmp typ=int16_ bnd=m
  158 : __tmp typ=int8_ bnd=m
  159 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  163 : __fch_u16_baseaddr typ=int16_ bnd=m
  166 : __tmp typ=int16_ bnd=m
  169 : __tmp typ=int8_ bnd=m
  170 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  174 : __fch_u16_baseaddr typ=int16_ bnd=m
  177 : __tmp typ=int16_ bnd=m
  180 : __tmp typ=int8_ bnd=m
  181 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  185 : __fch_u16_baseaddr typ=int16_ bnd=m
  188 : __tmp typ=int16_ bnd=m
  191 : __tmp typ=int8_ bnd=m
  192 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  196 : __fch_u16_baseaddr typ=int16_ bnd=m
  199 : __tmp typ=int16_ bnd=m
  202 : __tmp typ=int16_ bnd=m
  203 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  207 : __ct_6s0 typ=int16_ val=6s0 bnd=m
  214 : __ct_6s0 typ=int16_ val=6s0 bnd=m
  222 : __seff typ=any bnd=m
  223 : __seff typ=any bnd=m
  224 : __seff typ=any bnd=m
  225 : __seff typ=any bnd=m
  226 : __seff typ=any bnd=m
  227 : __seff typ=any bnd=m
  228 : __seff typ=any bnd=m
  229 : __seff typ=any bnd=m
  230 : __seff typ=any bnd=m
  231 : __seff typ=any bnd=m
  232 : __seff typ=any bnd=m
  233 : __seff typ=any bnd=m
  234 : __seff typ=any bnd=m
  235 : __seff typ=any bnd=m
  236 : __seff typ=any bnd=m
  237 : __seff typ=any bnd=m
  238 : __seff typ=any bnd=m
  239 : __seff typ=any bnd=m
  240 : __seff typ=any bnd=m
  241 : __seff typ=any bnd=m
  242 : __seff typ=any bnd=m
  243 : __seff typ=any bnd=m
  244 : __seff typ=any bnd=m
  245 : __seff typ=any bnd=m
  246 : __seff typ=any bnd=m
  247 : __seff typ=any bnd=m
  248 : __seff typ=any bnd=m
  249 : __seff typ=any bnd=m
  250 : __seff typ=any bnd=m
  251 : __seff typ=any bnd=m
  252 : __seff typ=any bnd=m
  253 : __seff typ=any bnd=m
  254 : __seff typ=any bnd=m
  255 : __seff typ=any bnd=m
  256 : __seff typ=any bnd=m
  257 : __seff typ=any bnd=m
  258 : __seff typ=any bnd=m
  259 : __seff typ=any bnd=m
  260 : __seff typ=any bnd=m
  261 : __seff typ=any bnd=m
  262 : __seff typ=any bnd=m
  263 : __seff typ=any bnd=m
  264 : __seff typ=any bnd=m
  265 : __seff typ=any bnd=m
]
Fvoid_tx_read_configuration___ushort___PTxRegSettings_t {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (ps_ActTxRegSettings.19 var=20) source ()  <30>;
    (u16_EeBasePage.20 var=21) source ()  <31>;
    (u16_baseaddr.21 var=22) source ()  <32>;
    (__extDM_TxRegSettings_t.22 var=23) source ()  <33>;
    (__extDM_TxRegSettings_t_Cfg_FREQCON0.23 var=24) source ()  <34>;
    (__extDM_int16_.24 var=25) source ()  <35>;
    (__extPM_void.25 var=26) source ()  <36>;
    (__extDM_void.26 var=27) source ()  <37>;
    (__extULP_void.27 var=28) source ()  <38>;
    (__extDM_TxRegSettings_t_Cfg_FREQCON1.28 var=29) source ()  <39>;
    (__extDM_TxRegSettings_t_Cfg_PLLCON.29 var=30) source ()  <40>;
    (__extDM_int8_.30 var=31) source ()  <41>;
    (__extDM_TxRegSettings_t_Cfg_ASKCON.31 var=32) source ()  <42>;
    (__extDM_TxRegSettings_t_Cfg_FSKCON.32 var=33) source ()  <43>;
    (__extDM_TxRegSettings_t_Cfg_BRGCON.33 var=34) source ()  <44>;
    (__extDM_TxRegSettings_t_Cfg_ASKRMP.34 var=35) source ()  <45>;
    (__extDM_TxRegSettings_t_Cfg_FSKRMP.35 var=36) source ()  <46>;
    (__extDM_TxRegSettings_t_Cfg_PACON.36 var=37) source ()  <47>;
    (__extDM_TxRegSettings_t_Cfg_PAPWR.37 var=38) source ()  <48>;
    (__extDM_TxRegSettings_t_Cfg_PATRIM.38 var=39) source ()  <49>;
    (__extDM_TxRegSettings_t_Cfg_PALIMIT.39 var=40) source ()  <50>;
    (__extDM_TxRegSettings_t_Cfg_ENCCON0.40 var=41) source ()  <51>;
    (__arg_u16_EeBasePage.42 var=43 stl=RwL off=0) inp ()  <53>;
    (__arg_ps_ActTxRegSettings.45 var=44 stl=R46 off=0) inp ()  <56>;
    (__ct_0t0.638 var=49) const_inp ()  <400>;
    (__ct_2t0.639 var=53) const_inp ()  <401>;
    (__ct_4t0.640 var=57) const_inp ()  <402>;
    (__ct_6s0.644 var=214) const_inp ()  <406>;
    <175> {
      (__sp.53 var=19 __seff.768 var=263 stl=c_flag_w __seff.769 var=264 stl=nz_flag_w __seff.770 var=265 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_6s0.644 __sp.18 __sp.18)  <527>;
      (__seff.822 var=263 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.768)  <808>;
      (__seff.823 var=264 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.769)  <809>;
      (__seff.824 var=265 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.770)  <810>;
    } stp=0;
    <176> {
      (ps_ActTxRegSettings.70 var=20) _pl_rd_res_reg_const_store_1_B1 (__arg_ps_ActTxRegSettings.821 __ct_0t0.638 ps_ActTxRegSettings.19 __sp.53)  <528>;
      (__arg_ps_ActTxRegSettings.821 var=44 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__arg_ps_ActTxRegSettings.45)  <807>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <81>;
    } #4 off=3 nxt=5
    #5 off=3 nxt=6
    <174> {
      (u16_EeBasePage.72 var=21) _pl_rd_res_reg_const_store_1_B2 (__arg_u16_EeBasePage.813 __ct_2t0.639 u16_EeBasePage.20 __sp.53)  <526>;
      (__arg_u16_EeBasePage.813 var=43 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__arg_u16_EeBasePage.42)  <800>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <83>;
    } #6 off=4 nxt=65
    #65 off=4 nxt=8
    <171> {
      (__fch_u16_EeBasePage.73 var=60 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_2t0.639 u16_EeBasePage.72 __sp.53)  <523>;
      (__fch_u16_EeBasePage.815 var=60 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_EeBasePage.73)  <802>;
    } stp=0;
    <172> {
      (__tmp.636 var=63 stl=a_w2 __seff.763 var=261 stl=c_flag_w __seff.764 var=262 stl=nz_flag_w) shl_const_1_B1 (__fch_u16_EeBasePage.814)  <524>;
      (__fch_u16_EeBasePage.814 var=60 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_EeBasePage.815)  <801>;
      (__seff.816 var=262 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.764)  <803>;
      (__seff.817 var=261 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.763)  <804>;
      (__tmp.819 var=63 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.636)  <806>;
    } stp=1;
    <173> {
      (u16_baseaddr.78 var=22) _pl_rd_res_reg_const_store_1_B2 (__tmp.818 __ct_4t0.640 u16_baseaddr.21 __sp.53)  <525>;
      (__tmp.818 var=63 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__tmp.819)  <805>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <89>;
    } #8 off=7 nxt=9
    #9 off=7 nxt=10
    (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.641 var=68) const_inp ()  <403>;
    <169> {
      (__fch_u16_baseaddr.79 var=64 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <521>;
      (__fch_u16_baseaddr.969 var=64 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.79)  <930>;
    } stp=0;
    <170> {
      () call_const_1_B1 (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.641)  <522>;
    } stp=1;
    call {
        (__tmp.86 var=70 stl=RwL off=0 __extDM.89 var=17 __extDM_TxRegSettings_t.90 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.91 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.92 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.93 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.94 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.95 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.96 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.97 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.98 var=36 __extDM_TxRegSettings_t_Cfg_PACON.99 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.100 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.101 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.102 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.103 var=30 __extDM_int16_.104 var=25 __extDM_int8_.105 var=31 __extDM_void.106 var=27 __extPM.107 var=16 __extPM_void.108 var=26 __extULP.109 var=18 __extULP_void.110 var=28 __vola.111 var=13) F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort (__fch_u16_baseaddr.969 __extDM.16 __extDM_TxRegSettings_t.22 __extDM_TxRegSettings_t_Cfg_ASKCON.31 __extDM_TxRegSettings_t_Cfg_ASKRMP.34 __extDM_TxRegSettings_t_Cfg_BRGCON.33 __extDM_TxRegSettings_t_Cfg_ENCCON0.40 __extDM_TxRegSettings_t_Cfg_FREQCON0.23 __extDM_TxRegSettings_t_Cfg_FREQCON1.28 __extDM_TxRegSettings_t_Cfg_FSKCON.32 __extDM_TxRegSettings_t_Cfg_FSKRMP.35 __extDM_TxRegSettings_t_Cfg_PACON.36 __extDM_TxRegSettings_t_Cfg_PALIMIT.39 __extDM_TxRegSettings_t_Cfg_PAPWR.37 __extDM_TxRegSettings_t_Cfg_PATRIM.38 __extDM_TxRegSettings_t_Cfg_PLLCON.29 __extDM_int16_.24 __extDM_int8_.30 __extDM_void.26 __extPM.15 __extPM_void.25 __extULP.17 __extULP_void.27 __vola.12)  <98>;
    } #10 off=10 nxt=11
    #11 off=10 nxt=12
    <167> {
      (__fch_ps_ActTxRegSettings.112 var=71 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <519>;
      (__fch_ps_ActTxRegSettings.833 var=71 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.112)  <818>;
    } stp=0;
    <168> {
      (__extDM_TxRegSettings_t_Cfg_FREQCON0.117 var=24) store_1_B1 (__tmp.831 __fch_ps_ActTxRegSettings.832 __extDM_TxRegSettings_t_Cfg_FREQCON0.95)  <520>;
      (__tmp.831 var=70 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__tmp.86)  <816>;
      (__fch_ps_ActTxRegSettings.832 var=71 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.833)  <817>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <106>;
    } #12 off=13 nxt=13
    #13 off=13 nxt=14
    <164> {
      (__fch_u16_baseaddr.118 var=75 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <516>;
      (__fch_u16_baseaddr.826 var=75 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.118)  <812>;
    } stp=0;
    <165> {
      (__tmp.121 var=78 stl=a_w2 __seff.755 var=258 stl=c_flag_w __seff.756 var=259 stl=nz_flag_w __seff.757 var=260 stl=o_flag_w) _pl_const_12_B2 (__fch_u16_baseaddr.825)  <517>;
      (__fch_u16_baseaddr.825 var=75 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.826)  <811>;
      (__seff.827 var=260 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.757)  <813>;
      (__seff.828 var=259 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.756)  <814>;
      (__seff.829 var=258 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.755)  <815>;
      (__tmp.967 var=78 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.121)  <928>;
    } stp=1;
    <166> {
      () call_const_1_B1 (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.641)  <518>;
    } stp=2;
    call {
        (__tmp.125 var=81 stl=RwL off=0 __extDM.128 var=17 __extDM_TxRegSettings_t.129 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.130 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.131 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.132 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.133 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.134 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.135 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.136 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.137 var=36 __extDM_TxRegSettings_t_Cfg_PACON.138 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.139 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.140 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.141 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.142 var=30 __extDM_int16_.143 var=25 __extDM_int8_.144 var=31 __extDM_void.145 var=27 __extPM.146 var=16 __extPM_void.147 var=26 __extULP.148 var=18 __extULP_void.149 var=28 __vola.150 var=13) F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort (__tmp.967 __extDM.89 __extDM_TxRegSettings_t.90 __extDM_TxRegSettings_t_Cfg_ASKCON.91 __extDM_TxRegSettings_t_Cfg_ASKRMP.92 __extDM_TxRegSettings_t_Cfg_BRGCON.93 __extDM_TxRegSettings_t_Cfg_ENCCON0.94 __extDM_TxRegSettings_t_Cfg_FREQCON0.117 __extDM_TxRegSettings_t_Cfg_FREQCON1.96 __extDM_TxRegSettings_t_Cfg_FSKCON.97 __extDM_TxRegSettings_t_Cfg_FSKRMP.98 __extDM_TxRegSettings_t_Cfg_PACON.99 __extDM_TxRegSettings_t_Cfg_PALIMIT.100 __extDM_TxRegSettings_t_Cfg_PAPWR.101 __extDM_TxRegSettings_t_Cfg_PATRIM.102 __extDM_TxRegSettings_t_Cfg_PLLCON.103 __extDM_int16_.104 __extDM_int8_.105 __extDM_void.106 __extPM.107 __extPM_void.108 __extULP.109 __extULP_void.110 __vola.111)  <115>;
    } #14 off=17 nxt=15
    #15 off=17 nxt=16
    <162> {
      (__fch_ps_ActTxRegSettings.151 var=82 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <514>;
      (__fch_ps_ActTxRegSettings.843 var=82 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.151)  <826>;
    } stp=0;
    <163> {
      (__extDM_TxRegSettings_t_Cfg_FREQCON1.156 var=29) _pl_const_store_12_B1 (__tmp.841 __fch_ps_ActTxRegSettings.842 __extDM_TxRegSettings_t_Cfg_FREQCON1.135)  <515>;
      (__tmp.841 var=81 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__tmp.125)  <824>;
      (__fch_ps_ActTxRegSettings.842 var=82 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.843)  <825>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <123>;
    } #16 off=21 nxt=17
    #17 off=21 nxt=18
    (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.642 var=90) const_inp ()  <404>;
    <159> {
      (__fch_u16_baseaddr.157 var=86 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <511>;
      (__fch_u16_baseaddr.836 var=86 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.157)  <820>;
    } stp=0;
    <160> {
      (__tmp.160 var=89 stl=a_w2 __seff.748 var=255 stl=c_flag_w __seff.749 var=256 stl=nz_flag_w __seff.750 var=257 stl=o_flag_w) _pl_const_11_B2 (__fch_u16_baseaddr.835)  <512>;
      (__fch_u16_baseaddr.835 var=86 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.836)  <819>;
      (__seff.837 var=257 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.750)  <821>;
      (__seff.838 var=256 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.749)  <822>;
      (__seff.839 var=255 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.748)  <823>;
      (__tmp.965 var=89 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.160)  <926>;
    } stp=1;
    <161> {
      () call_const_1_B1 (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.642)  <513>;
    } stp=2;
    call {
        (__tmp.164 var=92 stl=RbL off=0 __extDM.167 var=17 __extDM_TxRegSettings_t.168 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.169 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.170 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.171 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.172 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.173 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.174 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.175 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.176 var=36 __extDM_TxRegSettings_t_Cfg_PACON.177 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.178 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.179 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.180 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.181 var=30 __extDM_int16_.182 var=25 __extDM_int8_.183 var=31 __extDM_void.184 var=27 __extPM.185 var=16 __extPM_void.186 var=26 __extULP.187 var=18 __extULP_void.188 var=28 __vola.189 var=13) F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort (__tmp.965 __extDM.128 __extDM_TxRegSettings_t.129 __extDM_TxRegSettings_t_Cfg_ASKCON.130 __extDM_TxRegSettings_t_Cfg_ASKRMP.131 __extDM_TxRegSettings_t_Cfg_BRGCON.132 __extDM_TxRegSettings_t_Cfg_ENCCON0.133 __extDM_TxRegSettings_t_Cfg_FREQCON0.134 __extDM_TxRegSettings_t_Cfg_FREQCON1.156 __extDM_TxRegSettings_t_Cfg_FSKCON.136 __extDM_TxRegSettings_t_Cfg_FSKRMP.137 __extDM_TxRegSettings_t_Cfg_PACON.138 __extDM_TxRegSettings_t_Cfg_PALIMIT.139 __extDM_TxRegSettings_t_Cfg_PAPWR.140 __extDM_TxRegSettings_t_Cfg_PATRIM.141 __extDM_TxRegSettings_t_Cfg_PLLCON.142 __extDM_int16_.143 __extDM_int8_.144 __extDM_void.145 __extPM.146 __extPM_void.147 __extULP.148 __extULP_void.149 __vola.150)  <132>;
    } #18 off=25 nxt=19
    #19 off=25 nxt=20
    <157> {
      (__fch_ps_ActTxRegSettings.190 var=93 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <509>;
      (__fch_ps_ActTxRegSettings.853 var=93 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.190)  <834>;
    } stp=0;
    <158> {
      (__extDM_TxRegSettings_t_Cfg_PLLCON.195 var=30) _pl_const_store_11_B1 (__tmp.851 __fch_ps_ActTxRegSettings.852 __extDM_TxRegSettings_t_Cfg_PLLCON.181)  <510>;
      (__tmp.851 var=92 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.164)  <832>;
      (__fch_ps_ActTxRegSettings.852 var=93 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.853)  <833>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <140>;
    } #20 off=29 nxt=21
    #21 off=29 nxt=22
    <154> {
      (__fch_u16_baseaddr.196 var=97 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <506>;
      (__fch_u16_baseaddr.846 var=97 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.196)  <828>;
    } stp=0;
    <155> {
      (__tmp.199 var=100 stl=a_w2 __seff.741 var=252 stl=c_flag_w __seff.742 var=253 stl=nz_flag_w __seff.743 var=254 stl=o_flag_w) _pl_const_10_B2 (__fch_u16_baseaddr.845)  <507>;
      (__fch_u16_baseaddr.845 var=97 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.846)  <827>;
      (__seff.847 var=254 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.743)  <829>;
      (__seff.848 var=253 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.742)  <830>;
      (__seff.849 var=252 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.741)  <831>;
      (__tmp.964 var=100 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.199)  <925>;
    } stp=1;
    <156> {
      () call_const_1_B1 (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.641)  <508>;
    } stp=2;
    call {
        (__tmp.203 var=103 stl=RwL off=0 __extDM.206 var=17 __extDM_TxRegSettings_t.207 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.208 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.209 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.210 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.211 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.212 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.213 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.214 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.215 var=36 __extDM_TxRegSettings_t_Cfg_PACON.216 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.217 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.218 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.219 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.220 var=30 __extDM_int16_.221 var=25 __extDM_int8_.222 var=31 __extDM_void.223 var=27 __extPM.224 var=16 __extPM_void.225 var=26 __extULP.226 var=18 __extULP_void.227 var=28 __vola.228 var=13) F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort (__tmp.964 __extDM.167 __extDM_TxRegSettings_t.168 __extDM_TxRegSettings_t_Cfg_ASKCON.169 __extDM_TxRegSettings_t_Cfg_ASKRMP.170 __extDM_TxRegSettings_t_Cfg_BRGCON.171 __extDM_TxRegSettings_t_Cfg_ENCCON0.172 __extDM_TxRegSettings_t_Cfg_FREQCON0.173 __extDM_TxRegSettings_t_Cfg_FREQCON1.174 __extDM_TxRegSettings_t_Cfg_FSKCON.175 __extDM_TxRegSettings_t_Cfg_FSKRMP.176 __extDM_TxRegSettings_t_Cfg_PACON.177 __extDM_TxRegSettings_t_Cfg_PALIMIT.178 __extDM_TxRegSettings_t_Cfg_PAPWR.179 __extDM_TxRegSettings_t_Cfg_PATRIM.180 __extDM_TxRegSettings_t_Cfg_PLLCON.195 __extDM_int16_.182 __extDM_int8_.183 __extDM_void.184 __extPM.185 __extPM_void.186 __extULP.187 __extULP_void.188 __vola.189)  <149>;
    } #22 off=33 nxt=23
    #23 off=33 nxt=24
    <152> {
      (__fch_ps_ActTxRegSettings.229 var=104 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <504>;
      (__fch_ps_ActTxRegSettings.863 var=104 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.229)  <842>;
    } stp=0;
    <153> {
      (__extDM_TxRegSettings_t_Cfg_ASKCON.234 var=32) _pl_const_store_10_B1 (__tmp.861 __fch_ps_ActTxRegSettings.862 __extDM_TxRegSettings_t_Cfg_ASKCON.208)  <505>;
      (__tmp.861 var=103 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__tmp.203)  <840>;
      (__fch_ps_ActTxRegSettings.862 var=104 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.863)  <841>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <157>;
    } #24 off=37 nxt=25
    #25 off=37 nxt=26
    <149> {
      (__fch_u16_baseaddr.235 var=108 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <501>;
      (__fch_u16_baseaddr.856 var=108 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.235)  <836>;
    } stp=0;
    <150> {
      (__tmp.238 var=111 stl=a_w2 __seff.734 var=249 stl=c_flag_w __seff.735 var=250 stl=nz_flag_w __seff.736 var=251 stl=o_flag_w) _pl_const_9_B2 (__fch_u16_baseaddr.855)  <502>;
      (__fch_u16_baseaddr.855 var=108 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.856)  <835>;
      (__seff.857 var=251 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.736)  <837>;
      (__seff.858 var=250 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.735)  <838>;
      (__seff.859 var=249 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.734)  <839>;
      (__tmp.962 var=111 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.238)  <923>;
    } stp=1;
    <151> {
      () call_const_1_B1 (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.642)  <503>;
    } stp=2;
    call {
        (__tmp.242 var=114 stl=RbL off=0 __extDM.245 var=17 __extDM_TxRegSettings_t.246 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.247 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.248 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.249 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.250 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.251 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.252 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.253 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.254 var=36 __extDM_TxRegSettings_t_Cfg_PACON.255 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.256 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.257 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.258 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.259 var=30 __extDM_int16_.260 var=25 __extDM_int8_.261 var=31 __extDM_void.262 var=27 __extPM.263 var=16 __extPM_void.264 var=26 __extULP.265 var=18 __extULP_void.266 var=28 __vola.267 var=13) F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort (__tmp.962 __extDM.206 __extDM_TxRegSettings_t.207 __extDM_TxRegSettings_t_Cfg_ASKCON.234 __extDM_TxRegSettings_t_Cfg_ASKRMP.209 __extDM_TxRegSettings_t_Cfg_BRGCON.210 __extDM_TxRegSettings_t_Cfg_ENCCON0.211 __extDM_TxRegSettings_t_Cfg_FREQCON0.212 __extDM_TxRegSettings_t_Cfg_FREQCON1.213 __extDM_TxRegSettings_t_Cfg_FSKCON.214 __extDM_TxRegSettings_t_Cfg_FSKRMP.215 __extDM_TxRegSettings_t_Cfg_PACON.216 __extDM_TxRegSettings_t_Cfg_PALIMIT.217 __extDM_TxRegSettings_t_Cfg_PAPWR.218 __extDM_TxRegSettings_t_Cfg_PATRIM.219 __extDM_TxRegSettings_t_Cfg_PLLCON.220 __extDM_int16_.221 __extDM_int8_.222 __extDM_void.223 __extPM.224 __extPM_void.225 __extULP.226 __extULP_void.227 __vola.228)  <166>;
    } #26 off=41 nxt=27
    #27 off=41 nxt=28
    <147> {
      (__fch_ps_ActTxRegSettings.268 var=115 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <499>;
      (__fch_ps_ActTxRegSettings.873 var=115 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.268)  <850>;
    } stp=0;
    <148> {
      (__extDM_TxRegSettings_t_Cfg_FSKCON.273 var=33) _pl_const_store_9_B1 (__tmp.871 __fch_ps_ActTxRegSettings.872 __extDM_TxRegSettings_t_Cfg_FSKCON.253)  <500>;
      (__tmp.871 var=114 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.242)  <848>;
      (__fch_ps_ActTxRegSettings.872 var=115 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.873)  <849>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <174>;
    } #28 off=45 nxt=29
    #29 off=45 nxt=30
    <144> {
      (__fch_u16_baseaddr.274 var=119 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <496>;
      (__fch_u16_baseaddr.866 var=119 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.274)  <844>;
    } stp=0;
    <145> {
      (__tmp.277 var=122 stl=a_w2 __seff.727 var=246 stl=c_flag_w __seff.728 var=247 stl=nz_flag_w __seff.729 var=248 stl=o_flag_w) _pl_const_8_B2 (__fch_u16_baseaddr.865)  <497>;
      (__fch_u16_baseaddr.865 var=119 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.866)  <843>;
      (__seff.867 var=248 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.729)  <845>;
      (__seff.868 var=247 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.728)  <846>;
      (__seff.869 var=246 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.727)  <847>;
      (__tmp.961 var=122 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.277)  <922>;
    } stp=1;
    <146> {
      () call_const_1_B1 (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.641)  <498>;
    } stp=2;
    call {
        (__tmp.281 var=125 stl=RwL off=0 __extDM.284 var=17 __extDM_TxRegSettings_t.285 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.286 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.287 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.288 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.289 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.290 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.291 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.292 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.293 var=36 __extDM_TxRegSettings_t_Cfg_PACON.294 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.295 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.296 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.297 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.298 var=30 __extDM_int16_.299 var=25 __extDM_int8_.300 var=31 __extDM_void.301 var=27 __extPM.302 var=16 __extPM_void.303 var=26 __extULP.304 var=18 __extULP_void.305 var=28 __vola.306 var=13) F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort (__tmp.961 __extDM.245 __extDM_TxRegSettings_t.246 __extDM_TxRegSettings_t_Cfg_ASKCON.247 __extDM_TxRegSettings_t_Cfg_ASKRMP.248 __extDM_TxRegSettings_t_Cfg_BRGCON.249 __extDM_TxRegSettings_t_Cfg_ENCCON0.250 __extDM_TxRegSettings_t_Cfg_FREQCON0.251 __extDM_TxRegSettings_t_Cfg_FREQCON1.252 __extDM_TxRegSettings_t_Cfg_FSKCON.273 __extDM_TxRegSettings_t_Cfg_FSKRMP.254 __extDM_TxRegSettings_t_Cfg_PACON.255 __extDM_TxRegSettings_t_Cfg_PALIMIT.256 __extDM_TxRegSettings_t_Cfg_PAPWR.257 __extDM_TxRegSettings_t_Cfg_PATRIM.258 __extDM_TxRegSettings_t_Cfg_PLLCON.259 __extDM_int16_.260 __extDM_int8_.261 __extDM_void.262 __extPM.263 __extPM_void.264 __extULP.265 __extULP_void.266 __vola.267)  <183>;
    } #30 off=49 nxt=31
    #31 off=49 nxt=32
    <142> {
      (__fch_ps_ActTxRegSettings.307 var=126 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <494>;
      (__fch_ps_ActTxRegSettings.883 var=126 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.307)  <858>;
    } stp=0;
    <143> {
      (__extDM_TxRegSettings_t_Cfg_BRGCON.312 var=34) _pl_const_store_8_B1 (__tmp.881 __fch_ps_ActTxRegSettings.882 __extDM_TxRegSettings_t_Cfg_BRGCON.288)  <495>;
      (__tmp.881 var=125 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__tmp.281)  <856>;
      (__fch_ps_ActTxRegSettings.882 var=126 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.883)  <857>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <191>;
    } #32 off=53 nxt=33
    #33 off=53 nxt=34
    <139> {
      (__fch_u16_baseaddr.313 var=130 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <491>;
      (__fch_u16_baseaddr.876 var=130 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.313)  <852>;
    } stp=0;
    <140> {
      (__tmp.316 var=133 stl=a_w2 __seff.720 var=243 stl=c_flag_w __seff.721 var=244 stl=nz_flag_w __seff.722 var=245 stl=o_flag_w) _pl_const_7_B2 (__fch_u16_baseaddr.875)  <492>;
      (__fch_u16_baseaddr.875 var=130 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.876)  <851>;
      (__seff.877 var=245 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.722)  <853>;
      (__seff.878 var=244 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.721)  <854>;
      (__seff.879 var=243 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.720)  <855>;
      (__tmp.959 var=133 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.316)  <920>;
    } stp=1;
    <141> {
      () call_const_1_B1 (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.642)  <493>;
    } stp=2;
    call {
        (__tmp.320 var=136 stl=RbL off=0 __extDM.323 var=17 __extDM_TxRegSettings_t.324 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.325 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.326 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.327 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.328 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.329 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.330 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.331 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.332 var=36 __extDM_TxRegSettings_t_Cfg_PACON.333 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.334 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.335 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.336 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.337 var=30 __extDM_int16_.338 var=25 __extDM_int8_.339 var=31 __extDM_void.340 var=27 __extPM.341 var=16 __extPM_void.342 var=26 __extULP.343 var=18 __extULP_void.344 var=28 __vola.345 var=13) F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort (__tmp.959 __extDM.284 __extDM_TxRegSettings_t.285 __extDM_TxRegSettings_t_Cfg_ASKCON.286 __extDM_TxRegSettings_t_Cfg_ASKRMP.287 __extDM_TxRegSettings_t_Cfg_BRGCON.312 __extDM_TxRegSettings_t_Cfg_ENCCON0.289 __extDM_TxRegSettings_t_Cfg_FREQCON0.290 __extDM_TxRegSettings_t_Cfg_FREQCON1.291 __extDM_TxRegSettings_t_Cfg_FSKCON.292 __extDM_TxRegSettings_t_Cfg_FSKRMP.293 __extDM_TxRegSettings_t_Cfg_PACON.294 __extDM_TxRegSettings_t_Cfg_PALIMIT.295 __extDM_TxRegSettings_t_Cfg_PAPWR.296 __extDM_TxRegSettings_t_Cfg_PATRIM.297 __extDM_TxRegSettings_t_Cfg_PLLCON.298 __extDM_int16_.299 __extDM_int8_.300 __extDM_void.301 __extPM.302 __extPM_void.303 __extULP.304 __extULP_void.305 __vola.306)  <200>;
    } #34 off=57 nxt=35
    #35 off=57 nxt=36
    <137> {
      (__fch_ps_ActTxRegSettings.346 var=137 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <489>;
      (__fch_ps_ActTxRegSettings.893 var=137 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.346)  <866>;
    } stp=0;
    <138> {
      (__extDM_TxRegSettings_t_Cfg_ASKRMP.351 var=35) _pl_const_store_7_B1 (__tmp.891 __fch_ps_ActTxRegSettings.892 __extDM_TxRegSettings_t_Cfg_ASKRMP.326)  <490>;
      (__tmp.891 var=136 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.320)  <864>;
      (__fch_ps_ActTxRegSettings.892 var=137 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.893)  <865>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <208>;
    } #36 off=61 nxt=37
    #37 off=61 nxt=38
    <134> {
      (__fch_u16_baseaddr.352 var=141 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <486>;
      (__fch_u16_baseaddr.886 var=141 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.352)  <860>;
    } stp=0;
    <135> {
      (__tmp.355 var=144 stl=a_w2 __seff.713 var=240 stl=c_flag_w __seff.714 var=241 stl=nz_flag_w __seff.715 var=242 stl=o_flag_w) _pl_const_6_B1 (__fch_u16_baseaddr.885)  <487>;
      (__fch_u16_baseaddr.885 var=141 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.886)  <859>;
      (__seff.887 var=242 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.715)  <861>;
      (__seff.888 var=241 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.714)  <862>;
      (__seff.889 var=240 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.713)  <863>;
      (__tmp.958 var=144 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.355)  <919>;
    } stp=1;
    <136> {
      () call_const_1_B1 (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.642)  <488>;
    } stp=3;
    call {
        (__tmp.359 var=147 stl=RbL off=0 __extDM.362 var=17 __extDM_TxRegSettings_t.363 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.364 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.365 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.366 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.367 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.368 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.369 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.370 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.371 var=36 __extDM_TxRegSettings_t_Cfg_PACON.372 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.373 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.374 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.375 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.376 var=30 __extDM_int16_.377 var=25 __extDM_int8_.378 var=31 __extDM_void.379 var=27 __extPM.380 var=16 __extPM_void.381 var=26 __extULP.382 var=18 __extULP_void.383 var=28 __vola.384 var=13) F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort (__tmp.958 __extDM.323 __extDM_TxRegSettings_t.324 __extDM_TxRegSettings_t_Cfg_ASKCON.325 __extDM_TxRegSettings_t_Cfg_ASKRMP.351 __extDM_TxRegSettings_t_Cfg_BRGCON.327 __extDM_TxRegSettings_t_Cfg_ENCCON0.328 __extDM_TxRegSettings_t_Cfg_FREQCON0.329 __extDM_TxRegSettings_t_Cfg_FREQCON1.330 __extDM_TxRegSettings_t_Cfg_FSKCON.331 __extDM_TxRegSettings_t_Cfg_FSKRMP.332 __extDM_TxRegSettings_t_Cfg_PACON.333 __extDM_TxRegSettings_t_Cfg_PALIMIT.334 __extDM_TxRegSettings_t_Cfg_PAPWR.335 __extDM_TxRegSettings_t_Cfg_PATRIM.336 __extDM_TxRegSettings_t_Cfg_PLLCON.337 __extDM_int16_.338 __extDM_int8_.339 __extDM_void.340 __extPM.341 __extPM_void.342 __extULP.343 __extULP_void.344 __vola.345)  <217>;
    } #38 off=66 nxt=39
    #39 off=66 nxt=40
    <132> {
      (__fch_ps_ActTxRegSettings.385 var=148 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <484>;
      (__fch_ps_ActTxRegSettings.903 var=148 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.385)  <874>;
    } stp=0;
    <133> {
      (__extDM_TxRegSettings_t_Cfg_FSKRMP.390 var=36) _pl_const_store_6_B1 (__tmp.901 __fch_ps_ActTxRegSettings.902 __extDM_TxRegSettings_t_Cfg_FSKRMP.371)  <485>;
      (__tmp.901 var=147 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.359)  <872>;
      (__fch_ps_ActTxRegSettings.902 var=148 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.903)  <873>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <225>;
    } #40 off=70 nxt=41
    #41 off=70 nxt=42
    <129> {
      (__fch_u16_baseaddr.391 var=152 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <481>;
      (__fch_u16_baseaddr.896 var=152 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.391)  <868>;
    } stp=0;
    <130> {
      (__tmp.394 var=155 stl=a_w2 __seff.706 var=237 stl=c_flag_w __seff.707 var=238 stl=nz_flag_w __seff.708 var=239 stl=o_flag_w) _pl_const_5_B1 (__fch_u16_baseaddr.895)  <482>;
      (__fch_u16_baseaddr.895 var=152 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.896)  <867>;
      (__seff.897 var=239 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.708)  <869>;
      (__seff.898 var=238 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.707)  <870>;
      (__seff.899 var=237 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.706)  <871>;
      (__tmp.957 var=155 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.394)  <918>;
    } stp=1;
    <131> {
      () call_const_1_B1 (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.642)  <483>;
    } stp=3;
    call {
        (__tmp.398 var=158 stl=RbL off=0 __extDM.401 var=17 __extDM_TxRegSettings_t.402 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.403 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.404 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.405 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.406 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.407 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.408 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.409 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.410 var=36 __extDM_TxRegSettings_t_Cfg_PACON.411 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.412 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.413 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.414 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.415 var=30 __extDM_int16_.416 var=25 __extDM_int8_.417 var=31 __extDM_void.418 var=27 __extPM.419 var=16 __extPM_void.420 var=26 __extULP.421 var=18 __extULP_void.422 var=28 __vola.423 var=13) F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort (__tmp.957 __extDM.362 __extDM_TxRegSettings_t.363 __extDM_TxRegSettings_t_Cfg_ASKCON.364 __extDM_TxRegSettings_t_Cfg_ASKRMP.365 __extDM_TxRegSettings_t_Cfg_BRGCON.366 __extDM_TxRegSettings_t_Cfg_ENCCON0.367 __extDM_TxRegSettings_t_Cfg_FREQCON0.368 __extDM_TxRegSettings_t_Cfg_FREQCON1.369 __extDM_TxRegSettings_t_Cfg_FSKCON.370 __extDM_TxRegSettings_t_Cfg_FSKRMP.390 __extDM_TxRegSettings_t_Cfg_PACON.372 __extDM_TxRegSettings_t_Cfg_PALIMIT.373 __extDM_TxRegSettings_t_Cfg_PAPWR.374 __extDM_TxRegSettings_t_Cfg_PATRIM.375 __extDM_TxRegSettings_t_Cfg_PLLCON.376 __extDM_int16_.377 __extDM_int8_.378 __extDM_void.379 __extPM.380 __extPM_void.381 __extULP.382 __extULP_void.383 __vola.384)  <234>;
    } #42 off=75 nxt=43
    #43 off=75 nxt=44
    <127> {
      (__fch_ps_ActTxRegSettings.424 var=159 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <479>;
      (__fch_ps_ActTxRegSettings.913 var=159 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.424)  <882>;
    } stp=0;
    <128> {
      (__extDM_TxRegSettings_t_Cfg_PACON.429 var=37) _pl_const_store_5_B1 (__tmp.911 __fch_ps_ActTxRegSettings.912 __extDM_TxRegSettings_t_Cfg_PACON.411)  <480>;
      (__tmp.911 var=158 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.398)  <880>;
      (__fch_ps_ActTxRegSettings.912 var=159 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.913)  <881>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <242>;
    } #44 off=79 nxt=45
    #45 off=79 nxt=46
    <124> {
      (__fch_u16_baseaddr.430 var=163 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <476>;
      (__fch_u16_baseaddr.906 var=163 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.430)  <876>;
    } stp=0;
    <125> {
      (__tmp.433 var=166 stl=a_w2 __seff.699 var=234 stl=c_flag_w __seff.700 var=235 stl=nz_flag_w __seff.701 var=236 stl=o_flag_w) _pl_const_4_B1 (__fch_u16_baseaddr.905)  <477>;
      (__fch_u16_baseaddr.905 var=163 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.906)  <875>;
      (__seff.907 var=236 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.701)  <877>;
      (__seff.908 var=235 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.700)  <878>;
      (__seff.909 var=234 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.699)  <879>;
      (__tmp.956 var=166 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.433)  <917>;
    } stp=1;
    <126> {
      () call_const_1_B1 (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.642)  <478>;
    } stp=3;
    call {
        (__tmp.437 var=169 stl=RbL off=0 __extDM.440 var=17 __extDM_TxRegSettings_t.441 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.442 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.443 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.444 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.445 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.446 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.447 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.448 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.449 var=36 __extDM_TxRegSettings_t_Cfg_PACON.450 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.451 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.452 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.453 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.454 var=30 __extDM_int16_.455 var=25 __extDM_int8_.456 var=31 __extDM_void.457 var=27 __extPM.458 var=16 __extPM_void.459 var=26 __extULP.460 var=18 __extULP_void.461 var=28 __vola.462 var=13) F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort (__tmp.956 __extDM.401 __extDM_TxRegSettings_t.402 __extDM_TxRegSettings_t_Cfg_ASKCON.403 __extDM_TxRegSettings_t_Cfg_ASKRMP.404 __extDM_TxRegSettings_t_Cfg_BRGCON.405 __extDM_TxRegSettings_t_Cfg_ENCCON0.406 __extDM_TxRegSettings_t_Cfg_FREQCON0.407 __extDM_TxRegSettings_t_Cfg_FREQCON1.408 __extDM_TxRegSettings_t_Cfg_FSKCON.409 __extDM_TxRegSettings_t_Cfg_FSKRMP.410 __extDM_TxRegSettings_t_Cfg_PACON.429 __extDM_TxRegSettings_t_Cfg_PALIMIT.412 __extDM_TxRegSettings_t_Cfg_PAPWR.413 __extDM_TxRegSettings_t_Cfg_PATRIM.414 __extDM_TxRegSettings_t_Cfg_PLLCON.415 __extDM_int16_.416 __extDM_int8_.417 __extDM_void.418 __extPM.419 __extPM_void.420 __extULP.421 __extULP_void.422 __vola.423)  <251>;
    } #46 off=84 nxt=47
    #47 off=84 nxt=48
    <122> {
      (__fch_ps_ActTxRegSettings.463 var=170 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <474>;
      (__fch_ps_ActTxRegSettings.923 var=170 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.463)  <890>;
    } stp=0;
    <123> {
      (__extDM_TxRegSettings_t_Cfg_PAPWR.468 var=38) _pl_const_store_4_B1 (__tmp.921 __fch_ps_ActTxRegSettings.922 __extDM_TxRegSettings_t_Cfg_PAPWR.452)  <475>;
      (__tmp.921 var=169 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.437)  <888>;
      (__fch_ps_ActTxRegSettings.922 var=170 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.923)  <889>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <259>;
    } #48 off=88 nxt=49
    #49 off=88 nxt=50
    <119> {
      (__fch_u16_baseaddr.469 var=174 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <471>;
      (__fch_u16_baseaddr.916 var=174 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.469)  <884>;
    } stp=0;
    <120> {
      (__tmp.472 var=177 stl=a_w2 __seff.692 var=231 stl=c_flag_w __seff.693 var=232 stl=nz_flag_w __seff.694 var=233 stl=o_flag_w) _pl_const_3_B1 (__fch_u16_baseaddr.915)  <472>;
      (__fch_u16_baseaddr.915 var=174 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.916)  <883>;
      (__seff.917 var=233 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.694)  <885>;
      (__seff.918 var=232 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.693)  <886>;
      (__seff.919 var=231 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.692)  <887>;
      (__tmp.955 var=177 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.472)  <916>;
    } stp=1;
    <121> {
      () call_const_1_B1 (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.642)  <473>;
    } stp=3;
    call {
        (__tmp.476 var=180 stl=RbL off=0 __extDM.479 var=17 __extDM_TxRegSettings_t.480 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.481 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.482 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.483 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.484 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.485 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.486 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.487 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.488 var=36 __extDM_TxRegSettings_t_Cfg_PACON.489 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.490 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.491 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.492 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.493 var=30 __extDM_int16_.494 var=25 __extDM_int8_.495 var=31 __extDM_void.496 var=27 __extPM.497 var=16 __extPM_void.498 var=26 __extULP.499 var=18 __extULP_void.500 var=28 __vola.501 var=13) F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort (__tmp.955 __extDM.440 __extDM_TxRegSettings_t.441 __extDM_TxRegSettings_t_Cfg_ASKCON.442 __extDM_TxRegSettings_t_Cfg_ASKRMP.443 __extDM_TxRegSettings_t_Cfg_BRGCON.444 __extDM_TxRegSettings_t_Cfg_ENCCON0.445 __extDM_TxRegSettings_t_Cfg_FREQCON0.446 __extDM_TxRegSettings_t_Cfg_FREQCON1.447 __extDM_TxRegSettings_t_Cfg_FSKCON.448 __extDM_TxRegSettings_t_Cfg_FSKRMP.449 __extDM_TxRegSettings_t_Cfg_PACON.450 __extDM_TxRegSettings_t_Cfg_PALIMIT.451 __extDM_TxRegSettings_t_Cfg_PAPWR.468 __extDM_TxRegSettings_t_Cfg_PATRIM.453 __extDM_TxRegSettings_t_Cfg_PLLCON.454 __extDM_int16_.455 __extDM_int8_.456 __extDM_void.457 __extPM.458 __extPM_void.459 __extULP.460 __extULP_void.461 __vola.462)  <268>;
    } #50 off=93 nxt=51
    #51 off=93 nxt=52
    <117> {
      (__fch_ps_ActTxRegSettings.502 var=181 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <469>;
      (__fch_ps_ActTxRegSettings.933 var=181 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.502)  <898>;
    } stp=0;
    <118> {
      (__extDM_TxRegSettings_t_Cfg_PATRIM.507 var=39) _pl_const_store_3_B1 (__tmp.931 __fch_ps_ActTxRegSettings.932 __extDM_TxRegSettings_t_Cfg_PATRIM.492)  <470>;
      (__tmp.931 var=180 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.476)  <896>;
      (__fch_ps_ActTxRegSettings.932 var=181 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.933)  <897>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <276>;
    } #52 off=97 nxt=53
    #53 off=97 nxt=54
    <114> {
      (__fch_u16_baseaddr.508 var=185 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <466>;
      (__fch_u16_baseaddr.926 var=185 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.508)  <892>;
    } stp=0;
    <115> {
      (__tmp.511 var=188 stl=a_w2 __seff.685 var=228 stl=c_flag_w __seff.686 var=229 stl=nz_flag_w __seff.687 var=230 stl=o_flag_w) _pl_const_2_B1 (__fch_u16_baseaddr.925)  <467>;
      (__fch_u16_baseaddr.925 var=185 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.926)  <891>;
      (__seff.927 var=230 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.687)  <893>;
      (__seff.928 var=229 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.686)  <894>;
      (__seff.929 var=228 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.685)  <895>;
      (__tmp.954 var=188 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.511)  <915>;
    } stp=1;
    <116> {
      () call_const_1_B1 (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.642)  <468>;
    } stp=3;
    call {
        (__tmp.515 var=191 stl=RbL off=0 __extDM.518 var=17 __extDM_TxRegSettings_t.519 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.520 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.521 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.522 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.523 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.524 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.525 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.526 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.527 var=36 __extDM_TxRegSettings_t_Cfg_PACON.528 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.529 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.530 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.531 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.532 var=30 __extDM_int16_.533 var=25 __extDM_int8_.534 var=31 __extDM_void.535 var=27 __extPM.536 var=16 __extPM_void.537 var=26 __extULP.538 var=18 __extULP_void.539 var=28 __vola.540 var=13) F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort (__tmp.954 __extDM.479 __extDM_TxRegSettings_t.480 __extDM_TxRegSettings_t_Cfg_ASKCON.481 __extDM_TxRegSettings_t_Cfg_ASKRMP.482 __extDM_TxRegSettings_t_Cfg_BRGCON.483 __extDM_TxRegSettings_t_Cfg_ENCCON0.484 __extDM_TxRegSettings_t_Cfg_FREQCON0.485 __extDM_TxRegSettings_t_Cfg_FREQCON1.486 __extDM_TxRegSettings_t_Cfg_FSKCON.487 __extDM_TxRegSettings_t_Cfg_FSKRMP.488 __extDM_TxRegSettings_t_Cfg_PACON.489 __extDM_TxRegSettings_t_Cfg_PALIMIT.490 __extDM_TxRegSettings_t_Cfg_PAPWR.491 __extDM_TxRegSettings_t_Cfg_PATRIM.507 __extDM_TxRegSettings_t_Cfg_PLLCON.493 __extDM_int16_.494 __extDM_int8_.495 __extDM_void.496 __extPM.497 __extPM_void.498 __extULP.499 __extULP_void.500 __vola.501)  <285>;
    } #54 off=102 nxt=55
    #55 off=102 nxt=56
    <112> {
      (__fch_ps_ActTxRegSettings.541 var=192 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <464>;
      (__fch_ps_ActTxRegSettings.943 var=192 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.541)  <906>;
    } stp=0;
    <113> {
      (__extDM_TxRegSettings_t_Cfg_PALIMIT.546 var=40) _pl_const_store_2_B1 (__tmp.941 __fch_ps_ActTxRegSettings.942 __extDM_TxRegSettings_t_Cfg_PALIMIT.529)  <465>;
      (__tmp.941 var=191 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.515)  <904>;
      (__fch_ps_ActTxRegSettings.942 var=192 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.943)  <905>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <293>;
    } #56 off=106 nxt=57
    #57 off=106 nxt=58
    <109> {
      (__fch_u16_baseaddr.547 var=196 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.640 u16_baseaddr.78 __sp.53)  <461>;
      (__fch_u16_baseaddr.936 var=196 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_baseaddr.547)  <900>;
    } stp=0;
    <110> {
      (__tmp.550 var=199 stl=a_w2 __seff.678 var=225 stl=c_flag_w __seff.679 var=226 stl=nz_flag_w __seff.680 var=227 stl=o_flag_w) _pl_const_1_B2 (__fch_u16_baseaddr.935)  <462>;
      (__fch_u16_baseaddr.935 var=196 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__fch_u16_baseaddr.936)  <899>;
      (__seff.937 var=227 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.680)  <901>;
      (__seff.938 var=226 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.679)  <902>;
      (__seff.939 var=225 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.678)  <903>;
      (__tmp.953 var=199 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.550)  <914>;
    } stp=1;
    <111> {
      () call_const_1_B1 (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.641)  <463>;
    } stp=2;
    call {
        (__tmp.554 var=202 stl=RwL off=0 __extDM.557 var=17 __extDM_TxRegSettings_t.558 var=23 __extDM_TxRegSettings_t_Cfg_ASKCON.559 var=32 __extDM_TxRegSettings_t_Cfg_ASKRMP.560 var=35 __extDM_TxRegSettings_t_Cfg_BRGCON.561 var=34 __extDM_TxRegSettings_t_Cfg_ENCCON0.562 var=41 __extDM_TxRegSettings_t_Cfg_FREQCON0.563 var=24 __extDM_TxRegSettings_t_Cfg_FREQCON1.564 var=29 __extDM_TxRegSettings_t_Cfg_FSKCON.565 var=33 __extDM_TxRegSettings_t_Cfg_FSKRMP.566 var=36 __extDM_TxRegSettings_t_Cfg_PACON.567 var=37 __extDM_TxRegSettings_t_Cfg_PALIMIT.568 var=40 __extDM_TxRegSettings_t_Cfg_PAPWR.569 var=38 __extDM_TxRegSettings_t_Cfg_PATRIM.570 var=39 __extDM_TxRegSettings_t_Cfg_PLLCON.571 var=30 __extDM_int16_.572 var=25 __extDM_int8_.573 var=31 __extDM_void.574 var=27 __extPM.575 var=16 __extPM_void.576 var=26 __extULP.577 var=18 __extULP_void.578 var=28 __vola.579 var=13) F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort (__tmp.953 __extDM.518 __extDM_TxRegSettings_t.519 __extDM_TxRegSettings_t_Cfg_ASKCON.520 __extDM_TxRegSettings_t_Cfg_ASKRMP.521 __extDM_TxRegSettings_t_Cfg_BRGCON.522 __extDM_TxRegSettings_t_Cfg_ENCCON0.523 __extDM_TxRegSettings_t_Cfg_FREQCON0.524 __extDM_TxRegSettings_t_Cfg_FREQCON1.525 __extDM_TxRegSettings_t_Cfg_FSKCON.526 __extDM_TxRegSettings_t_Cfg_FSKRMP.527 __extDM_TxRegSettings_t_Cfg_PACON.528 __extDM_TxRegSettings_t_Cfg_PALIMIT.546 __extDM_TxRegSettings_t_Cfg_PAPWR.530 __extDM_TxRegSettings_t_Cfg_PATRIM.531 __extDM_TxRegSettings_t_Cfg_PLLCON.532 __extDM_int16_.533 __extDM_int8_.534 __extDM_void.535 __extPM.536 __extPM_void.537 __extULP.538 __extULP_void.539 __vola.540)  <302>;
    } #58 off=110 nxt=59
    #59 off=110 nxt=60
    <107> {
      (__fch_ps_ActTxRegSettings.580 var=203 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.638 ps_ActTxRegSettings.70 __sp.53)  <459>;
      (__fch_ps_ActTxRegSettings.948 var=203 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.580)  <909>;
    } stp=0;
    <108> {
      (__extDM_TxRegSettings_t_Cfg_ENCCON0.585 var=41) _pl_const_store_1_B1 (__tmp.946 __fch_ps_ActTxRegSettings.947 __extDM_TxRegSettings_t_Cfg_ENCCON0.562)  <460>;
      (__tmp.946 var=202 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__tmp.554)  <907>;
      (__fch_ps_ActTxRegSettings.947 var=203 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.948)  <908>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <310>;
    } #60 off=114 nxt=62
    #62 off=114 nxt=-2
    () sink (__vola.579)  <317>;
    () sink (__extPM.575)  <320>;
    () sink (__extDM.557)  <321>;
    () sink (__extULP.577)  <322>;
    () sink (__sp.591)  <323>;
    () sink (ps_ActTxRegSettings.70)  <324>;
    () sink (u16_EeBasePage.72)  <325>;
    () sink (u16_baseaddr.78)  <326>;
    () sink (__extDM_TxRegSettings_t.558)  <327>;
    () sink (__extDM_TxRegSettings_t_Cfg_FREQCON0.563)  <328>;
    () sink (__extDM_int16_.572)  <329>;
    () sink (__extPM_void.576)  <330>;
    () sink (__extDM_void.574)  <331>;
    () sink (__extULP_void.578)  <332>;
    () sink (__extDM_TxRegSettings_t_Cfg_FREQCON1.564)  <333>;
    () sink (__extDM_TxRegSettings_t_Cfg_PLLCON.571)  <334>;
    () sink (__extDM_int8_.573)  <335>;
    () sink (__extDM_TxRegSettings_t_Cfg_ASKCON.559)  <336>;
    () sink (__extDM_TxRegSettings_t_Cfg_FSKCON.565)  <337>;
    () sink (__extDM_TxRegSettings_t_Cfg_BRGCON.561)  <338>;
    () sink (__extDM_TxRegSettings_t_Cfg_ASKRMP.560)  <339>;
    () sink (__extDM_TxRegSettings_t_Cfg_FSKRMP.566)  <340>;
    () sink (__extDM_TxRegSettings_t_Cfg_PACON.567)  <341>;
    () sink (__extDM_TxRegSettings_t_Cfg_PAPWR.569)  <342>;
    () sink (__extDM_TxRegSettings_t_Cfg_PATRIM.570)  <343>;
    () sink (__extDM_TxRegSettings_t_Cfg_PALIMIT.568)  <344>;
    () sink (__extDM_TxRegSettings_t_Cfg_ENCCON0.585)  <345>;
    (__ct_6s0.643 var=207) const_inp ()  <405>;
    <105> {
      (__sp.591 var=19 __seff.671 var=222 stl=c_flag_w __seff.672 var=223 stl=nz_flag_w __seff.673 var=224 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_6s0.643 __sp.53 __sp.53)  <457>;
      (__seff.949 var=222 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.671)  <910>;
      (__seff.950 var=223 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.672)  <911>;
      (__seff.951 var=224 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.673)  <912>;
    } stp=0;
    <106> {
      () ret_1_B1 ()  <458>;
    } stp=1;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,60:0,0);
3 : (0,60:56,0);
4 : (0,60:56,0);
5 : (0,60:31,0);
6 : (0,60:31,0);
8 : (0,62:41,3);
9 : (0,64:88,4);
10 : (0,64:38,4);
11 : (0,64:36,5);
12 : (0,64:36,5);
13 : (0,65:88,5);
14 : (0,65:38,5);
15 : (0,65:36,6);
16 : (0,65:36,6);
17 : (0,66:88,6);
18 : (0,66:38,6);
19 : (0,66:36,7);
20 : (0,66:36,7);
21 : (0,67:88,7);
22 : (0,67:38,7);
23 : (0,67:36,8);
24 : (0,67:36,8);
25 : (0,68:88,8);
26 : (0,68:38,8);
27 : (0,68:36,9);
28 : (0,68:36,9);
29 : (0,69:88,9);
30 : (0,69:38,9);
31 : (0,69:36,10);
32 : (0,69:36,10);
33 : (0,70:88,10);
34 : (0,70:38,10);
35 : (0,70:36,11);
36 : (0,70:36,11);
37 : (0,71:88,11);
38 : (0,71:38,11);
39 : (0,71:36,12);
40 : (0,71:36,12);
41 : (0,72:88,12);
42 : (0,72:38,12);
43 : (0,72:36,13);
44 : (0,72:36,13);
45 : (0,73:88,13);
46 : (0,73:38,13);
47 : (0,73:36,14);
48 : (0,73:36,14);
49 : (0,74:88,14);
50 : (0,74:38,14);
51 : (0,74:36,15);
52 : (0,74:36,15);
53 : (0,75:88,15);
54 : (0,75:38,15);
55 : (0,75:36,16);
56 : (0,75:36,16);
57 : (0,76:88,16);
58 : (0,76:38,16);
59 : (0,76:36,17);
60 : (0,76:36,17);
62 : (0,77:0,17);
65 : (0,62:41,3);
----------
81 : (0,60:56,0);
83 : (0,60:31,0);
89 : (0,62:41,3);
98 : (0,64:38,4);
106 : (0,64:36,5);
115 : (0,65:38,5);
123 : (0,65:36,6);
132 : (0,66:38,6);
140 : (0,66:36,7);
149 : (0,67:38,7);
157 : (0,67:36,8);
166 : (0,68:38,8);
174 : (0,68:36,9);
183 : (0,69:38,9);
191 : (0,69:36,10);
200 : (0,70:38,10);
208 : (0,70:36,11);
217 : (0,71:38,11);
225 : (0,71:36,12);
234 : (0,72:38,12);
242 : (0,72:36,13);
251 : (0,73:38,13);
259 : (0,73:36,14);
268 : (0,74:38,14);
276 : (0,74:36,15);
285 : (0,75:38,15);
293 : (0,75:36,16);
302 : (0,76:38,16);
310 : (0,76:36,17);
457 : (0,77:0,0) (0,77:0,17);
458 : (0,77:0,17);
459 : (0,76:2,16) (0,60:74,0);
460 : (0,76:21,16);
461 : (0,76:75,16) (0,62:11,0);
462 : (0,76:88,16);
463 : (0,76:38,16);
464 : (0,75:2,15) (0,60:74,0);
465 : (0,75:21,15);
466 : (0,75:75,15) (0,62:11,0);
467 : (0,75:88,15);
468 : (0,75:38,15);
469 : (0,74:2,14) (0,60:74,0);
470 : (0,74:21,14);
471 : (0,74:75,14) (0,62:11,0);
472 : (0,74:88,14);
473 : (0,74:38,14);
474 : (0,73:2,13) (0,60:74,0);
475 : (0,73:21,13);
476 : (0,73:75,13) (0,62:11,0);
477 : (0,73:88,13);
478 : (0,73:38,13);
479 : (0,72:2,12) (0,60:74,0);
480 : (0,72:21,12);
481 : (0,72:75,12) (0,62:11,0);
482 : (0,72:88,12);
483 : (0,72:38,12);
484 : (0,71:2,11) (0,60:74,0);
485 : (0,71:21,11);
486 : (0,71:75,11) (0,62:11,0);
487 : (0,71:88,11);
488 : (0,71:38,11);
489 : (0,70:2,10) (0,60:74,0);
490 : (0,70:21,10);
491 : (0,70:75,10) (0,62:11,0);
492 : (0,70:88,10);
493 : (0,70:38,10);
494 : (0,69:2,9) (0,60:74,0);
495 : (0,69:21,9);
496 : (0,69:75,9) (0,62:11,0);
497 : (0,69:88,9);
498 : (0,69:38,9);
499 : (0,68:2,8) (0,60:74,0);
500 : (0,68:21,8);
501 : (0,68:75,8) (0,62:11,0);
502 : (0,68:88,8);
503 : (0,68:38,8);
504 : (0,67:2,7) (0,60:74,0);
505 : (0,67:21,7);
506 : (0,67:75,7) (0,62:11,0);
507 : (0,67:88,7);
508 : (0,67:38,7);
509 : (0,66:2,6) (0,60:74,0);
510 : (0,66:21,6);
511 : (0,66:75,6) (0,62:11,0);
512 : (0,66:88,6);
513 : (0,66:38,6);
514 : (0,65:2,5) (0,60:74,0);
515 : (0,65:21,5);
516 : (0,65:75,5) (0,62:11,0);
517 : (0,65:88,5);
518 : (0,65:38,5);
519 : (0,64:2,4) (0,60:74,0);
520 : (0,64:21,4);
521 : (0,64:75,4) (0,62:11,0);
522 : (0,64:38,4);
523 : (0,62:26,3) (0,60:40,0);
524 : (0,62:41,3);
525 : (0,62:11,0) (0,62:41,3);
526 : (0,60:40,0) (0,60:31,0);
527 : (0,60:8,0);
528 : (0,60:74,0) (0,60:56,0);

