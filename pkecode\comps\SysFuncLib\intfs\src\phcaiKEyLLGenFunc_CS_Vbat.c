/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_Vbat.c 21074 2019-07-22 13:39:34Z dep10330 $
  $Revision: 21074 $
*/

/**
 * @file
 * Implementation of the stubs to call VBAT specific KEyLink Lite General Functions
 * placed in ROM.
 */

#include "phcaiKEyLLGenFunc_CS_Vbat.h"
#include "phcaiKEyLLGenFunc_CS_Utils.h"

void phcaiKEyLLGenFunc_CS_SetVBatRgl(const bool_t on)
{
  call_syscall_8(2U, ((on == TRUE) ? 1U : 0U));
}

void phcaiKEyLLGenFunc_CS_RefreshVBatRgl(void)
{
  call_syscall_8(7U, 0U);
}

void phcaiKEyLLGenFunc_CS_ResetVBat(void)
{
  call_syscall_8(3U, 0U);
}

void phcaiKEyLLGenFunc_CS_SetBatPORFlag(const bool_t set)
{
  call_syscall_8(4U, ((set == TRUE) ? 1U : 0U));
}

#if defined(PHFL_CONFIG_HAVE_LF_TUNING_CAPS) && (PHFL_CONFIG_HAVE_LF_TUNING_CAPS == CONFIG_YES)
// KEYLL_FUNC_CODE_WRITE_ULPEE_3D4
error_t phcaiKEyLLGenFunc_CS_ULPEE_Write_ULPEE_LFTUNE(const uint8_t * const data){
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_WRITE_ULPEE_3D4;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.data = data;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.wait = TRUE;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.return_val;
}
#endif

#if defined(PHFL_CONFIG_HAVE_LFA_TEST_SLICER_OUTPUT) && \
      (PHFL_CONFIG_HAVE_LFA_TEST_SLICER_OUTPUT == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_VbatReg_TestLfaSlicerOutput(const phcaiKEyLLGenFunc_CS_VbatReg_TestLfaSlicerOutputOption_t option)
{
  call_syscall_8(8U, (uint8_t)option);
}
#endif