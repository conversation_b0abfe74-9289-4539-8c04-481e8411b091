
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-51aaef mrk3

[
  -61 : __adr_bitcount typ=int16_ bnd=m adro=23
    0 : void_tx_transmit_buffer_encoded_bytes_DataEnc_t___ushort___P__uchar typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : pu8_buffer typ=int8_ val=0t0 bnd=a sz=2 algn=2 stl=DM tref=__P__uchar_DM
   21 : u16_numbytes typ=int8_ val=2t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   22 : e_encoding typ=int8_ val=4t0 bnd=a sz=1 algn=1 stl=DM tref=DataEnc_t_DM
   23 : bitcount typ=int8_ val=8t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   24 : ENCCON0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ENCCON0_t_DM9
   25 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   26 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   27 : __extDM_SFR_ENCCON0_t typ=int8_ bnd=b stl=DM
   28 : ENCCON1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ENCCON1_t_DM9
   29 : __extDM_SFR_ENCCON1_t typ=int8_ bnd=b stl=DM
   30 : i typ=int8_ val=6t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   31 : TXDAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_TXDAT_t_DM9
   32 : __extDM_SFR_word typ=int8_ bnd=b stl=DM
   35 : __ptr_ENCCON0 typ=int16_ val=0a bnd=m adro=24
   37 : __ptr_ENCCON1 typ=int16_ val=0a bnd=m adro=28
   40 : __arg_e_encoding typ=int8_ bnd=p tref=DataEnc_t__
   41 : __arg_u16_numbytes typ=int16_ bnd=p tref=uint16_t__
   42 : __arg_pu8_buffer typ=int16_ bnd=p tref=__P__uchar__
   47 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   51 : __ct_2t0 typ=int16_ val=2t0 bnd=m
   55 : __ct_4t0 typ=int16_ val=4t0 bnd=m
   59 : __ct_8t0 typ=int16_ val=8t0 bnd=m
   61 : __adr_bitcount typ=int16_ bnd=m adro=23
   63 : __ct_6t0 typ=int16_ val=6t0 bnd=m
   80 : __tmp typ=int16_ bnd=m
  108 : __fch_pu8_buffer typ=int16_ bnd=m
  111 : __tmp typ=int16_ bnd=m
  112 : __fchtmp typ=int8_ bnd=m
  143 : __tmp typ=bool bnd=m
  151 : __fch_u16_numbytes typ=int16_ bnd=m
  153 : __ct_10s0 typ=int16_ val=10s0 bnd=m
  158 : __ct_8 typ=uint16_1_32768_ val=8f bnd=m
  192 : __ct_10s0 typ=int16_ val=10s0 bnd=m
  195 : __ptr_TXDAT__a1 typ=int16_ val=1a bnd=m adro=31
  247 : __apl_r typ=int16_ bnd=m tref=__uint__
  252 : __apl_c typ=uint1_ bnd=m tref=uint1___
  254 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  264 : __apl_c typ=uint1_ bnd=m tref=uint1___
  266 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  324 : __false typ=bool val=0f bnd=m
  325 : __either typ=bool bnd=m
  326 : __trgt typ=rel8_ val=4j bnd=m
  327 : __trgt typ=rel8_ val=-4j bnd=m
  328 : __trgt typ=rel8_ val=-14j bnd=m
  329 : __trgt typ=rel8_ val=14j bnd=m
  330 : __trgt typ=rel8_ val=2j bnd=m
  331 : __seff typ=any bnd=m
  332 : __seff typ=any bnd=m
  333 : __seff typ=any bnd=m
  334 : __seff typ=any bnd=m
  335 : __seff typ=any bnd=m
  336 : __seff typ=any bnd=m
  337 : __seff typ=any bnd=m
  338 : __seff typ=any bnd=m
  339 : __seff typ=any bnd=m
  340 : __seff typ=any bnd=m
  341 : __seff typ=any bnd=m
  342 : __seff typ=any bnd=m
  343 : __seff typ=any bnd=m
  344 : __seff typ=any bnd=m
  345 : __seff typ=any bnd=m
  349 : __seff typ=any bnd=m
  350 : __seff typ=any bnd=m
  351 : __seff typ=any bnd=m
  353 : __side_effect typ=any bnd=m
]
Fvoid_tx_transmit_buffer_encoded_bytes_DataEnc_t___ushort___P__uchar {
    #5 off=0 nxt=6
    (__vola.12 var=13) source ()  <23>;
    (__extDM.16 var=17) source ()  <27>;
    (__sp.18 var=19) source ()  <29>;
    (pu8_buffer.19 var=20) source ()  <30>;
    (u16_numbytes.20 var=21) source ()  <31>;
    (e_encoding.21 var=22) source ()  <32>;
    (bitcount.22 var=23) source ()  <33>;
    (ENCCON0.23 var=24) source ()  <34>;
    (__extDM_int16_.24 var=25) source ()  <35>;
    (__extDM_int8_.25 var=26) source ()  <36>;
    (__extDM_SFR_ENCCON0_t.26 var=27) source ()  <37>;
    (ENCCON1.27 var=28) source ()  <38>;
    (__extDM_SFR_ENCCON1_t.28 var=29) source ()  <39>;
    (i.29 var=30) source ()  <40>;
    (TXDAT.30 var=31) source ()  <41>;
    (__extDM_SFR_word.31 var=32) source ()  <42>;
    (__arg_e_encoding.39 var=40 stl=RbL off=0) inp ()  <50>;
    (__arg_u16_numbytes.42 var=41 stl=RwL off=1) inp ()  <53>;
    (__arg_pu8_buffer.45 var=42 stl=R46 off=0) inp ()  <56>;
    (__ct_0t0.1130 var=47) const_inp ()  <1372>;
    (__ct_2t0.1131 var=51) const_inp ()  <1373>;
    (__ct_4t0.1132 var=55) const_inp ()  <1374>;
    (__ct_8t0.1133 var=59) const_inp ()  <1375>;
    (__ct_6t0.1134 var=63) const_inp ()  <1376>;
    (__ct_10s0.1136 var=192) const_inp ()  <1378>;
    <146> {
      (__sp.53 var=19 __seff.1204 var=349 stl=c_flag_w __seff.1205 var=350 stl=nz_flag_w __seff.1206 var=351 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_10s0.1136 __sp.18 __sp.18)  <1440>;
      (__seff.1325 var=350 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1205)  <1629>;
      (__seff.1326 var=349 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1204)  <1630>;
      (__seff.1338 var=351 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1206)  <1642>;
    } stp=0;
    <147> {
      (pu8_buffer.85 var=20) _pl_rd_res_reg_const_store_2_B1 (__arg_pu8_buffer.1331 __ct_0t0.1130 pu8_buffer.19 __sp.53)  <1441>;
      (__arg_pu8_buffer.1331 var=42 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__arg_pu8_buffer.45)  <1635>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <96>;
    } #6 off=4 nxt=7
    #7 off=4 nxt=8
    <145> {
      (u16_numbytes.87 var=21) _pl_rd_res_reg_const_store_2_B2 (__arg_u16_numbytes.1332 __ct_2t0.1131 u16_numbytes.20 __sp.53)  <1439>;
      (__arg_u16_numbytes.1332 var=41 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__arg_u16_numbytes.42)  <1636>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <98>;
    } #8 off=5 nxt=9
    #9 off=5 nxt=10
    <144> {
      (e_encoding.89 var=22) _pl_rd_res_reg_const_store_1_B2 (__arg_e_encoding.1333 __ct_4t0.1132 e_encoding.21 __sp.53)  <1438>;
      (__arg_e_encoding.1333 var=40 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__arg_e_encoding.39)  <1637>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <100>;
    } #10 off=6 nxt=11
    #11 off=6 nxt=14
    <143> {
      (bitcount.93 var=23) store_const_1_B1 (__adr_bitcount.1340 bitcount.22)  <1437>;
      (__adr_bitcount.1340 var=-61 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_bitcount.1341)  <1644>;
    } stp=2;
    <236> {
      (__adr_bitcount.1342 var=-61 stl=a_w2 __side_effect.1343 var=353 stl=c_flag_w __side_effect.1345 var=353 stl=nz_flag_w __side_effect.1347 var=353 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_8t0.1133 __sp.53)  <1577>;
      (__adr_bitcount.1341 var=-61 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_bitcount.1342)  <1645>;
      (__side_effect.1344 var=353 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.1343)  <1646>;
      (__side_effect.1346 var=353 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.1345)  <1647>;
      (__side_effect.1348 var=353 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.1347)  <1648>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <111>;
    } #14 off=10 nxt=421
    #421 off=10 nxt=18
    (__ptr_ENCCON0.1128 var=35) const_inp ()  <1370>;
    <139> {
      (__apl_r.937 var=247 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B2 (__ct_4t0.1132 e_encoding.89 __sp.53)  <1433>;
      (__apl_r.1335 var=247 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.937)  <1639>;
    } stp=0;
    <140> {
      (__tmp.939 var=80 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.1334)  <1434>;
      (__apl_r.1334 var=247 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.1335)  <1638>;
      (__tmp.1337 var=80 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.939)  <1641>;
    } stp=1;
    <141> {
      (ENCCON0.109 var=24 __vola.110 var=13) load_const_bf_mov_const_const_store_2_B1 (__tmp.1336 __ptr_ENCCON0.1128 ENCCON0.23 __vola.12)  <1435>;
      (__tmp.1336 var=80 stl=a_w1) a_w1_1_dr_move_Rw_1_int16__B0 (__tmp.1337)  <1640>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <129>;
    } #18 off=14 nxt=429
    #429 off=14 nxt=20
    (__ptr_ENCCON1.1129 var=37) const_inp ()  <1371>;
    <138> {
      (ENCCON1.129 var=28 __vola.130 var=13) load_const_bf_mov_const_const_store_1_B1 (__ct_8.1349 __ptr_ENCCON1.1129 ENCCON1.27 __vola.110)  <1432>;
      (__ct_8.1349 var=158 stl=a_w1) a_w1_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_8.1350)  <1649>;
    } stp=1;
    <240> {
      (__ct_8.1351 var=158 stl=a_w0) const_1_B2 ()  <1586>;
      (__ct_8.1350 var=158 stl=R46 off=1) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B3 (__ct_8.1351)  <1650>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <140>;
    } #20 off=17 nxt=21
    #21 off=17 nxt=22
    <136> {
      (i.137 var=30) _pl_rd_res_reg_const_store_const_1_B3 (__ct_6t0.1134 i.29 __sp.53)  <1430>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <144>;
    } #22 off=18 nxt=64
    #64 off=18 nxt=-3 tgt=2
    () sink (__vola.130)  <664>;
    () sink (__sp.53)  <670>;
    () sink (pu8_buffer.85)  <671>;
    () sink (u16_numbytes.87)  <672>;
    () sink (e_encoding.89)  <673>;
    () sink (bitcount.93)  <674>;
    () sink (ENCCON0.109)  <675>;
    () sink (ENCCON1.129)  <679>;
    () sink (i.137)  <681>;
    () sync_sink (__vola.130) sid=37  <684>;
    () sync_sink (bitcount.93) sid=47  <694>;
    () sync_sink (ENCCON0.109) sid=48  <695>;
    () sync_sink (i.137) sid=54  <701>;
    () sync_sink (TXDAT.30) sid=55  <702>;
    (__vola.1114 var=13) never ()  <1351>;
    (bitcount.1115 var=23) never ()  <1352>;
    (ENCCON0.1116 var=24) never ()  <1353>;
    (i.1117 var=30) never ()  <1354>;
    (TXDAT.1118 var=31) never ()  <1355>;
    (__ptr_TXDAT__a1.1137 var=195) const_inp ()  <1379>;
    (__trgt.1138 var=326) const_inp ()  <1380>;
    (__trgt.1139 var=327) const_inp ()  <1381>;
    (__trgt.1140 var=328) const_inp ()  <1382>;
    (__trgt.1141 var=329) const_inp ()  <1383>;
    (__trgt.1142 var=330) const_inp ()  <1384>;
    <133> {
      (__fch_pu8_buffer.214 var=108 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.1130 pu8_buffer.85 __sp.53)  <1427>;
      (__fch_pu8_buffer.1330 var=108 stl=R46 off=1) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_pu8_buffer.214)  <1634>;
    } stp=0;
    <134> {
      (__fch_u16_numbytes.593 var=151 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_2t0.1131 u16_numbytes.87 __sp.53)  <1428>;
      (__fch_u16_numbytes.1329 var=151 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_numbytes.593)  <1633>;
    } stp=2;
    <135> {
      () jump_const_1_B1 (__trgt.1141)  <1429>;
    } stp=3;
    do {
        {
            (__vola.178 var=13) entry (__vola.595 __vola.1114)  <186>;
            (bitcount.188 var=23) entry (bitcount.615 bitcount.1115)  <196>;
            (ENCCON0.189 var=24) entry (ENCCON0.617 ENCCON0.1116)  <197>;
            (i.195 var=30) entry (i.629 i.1117)  <203>;
            (TXDAT.196 var=31) entry (TXDAT.631 TXDAT.1118)  <204>;
        } #28
        {
            #52 off=22 nxt=-3 tgt=1
            () sink (__vola.227)  <448>;
            () sink (__sp.53)  <454>;
            () sink (pu8_buffer.85)  <455>;
            () sink (u16_numbytes.87)  <456>;
            () sink (e_encoding.89)  <457>;
            () sink (bitcount.188)  <458>;
            () sink (ENCCON0.189)  <459>;
            () sink (ENCCON1.129)  <463>;
            () sink (i.195)  <465>;
            () sink (TXDAT.226)  <466>;
            () sync_sink (__vola.227) sid=1  <468>;
            () sync_sink (bitcount.188) sid=11  <478>;
            () sync_sink (ENCCON0.189) sid=12  <479>;
            (__vola.1111 var=13) never ()  <1348>;
            (bitcount.1112 var=23) never ()  <1349>;
            (ENCCON0.1113 var=24) never ()  <1350>;
            <129> {
              (__tmp.217 var=111 stl=a_w2 __seff.1180 var=343 stl=c_flag_w __seff.1181 var=344 stl=nz_flag_w __seff.1182 var=345 stl=o_flag_w) load__pl_rd_res_reg_const__pl_1_B2 (__fch_pu8_buffer.1283 __ct_6t0.1134 i.195 __sp.53)  <1423>;
              (__seff.1268 var=344 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1181)  <1611>;
              (__seff.1269 var=343 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1180)  <1612>;
              (__tmp.1277 var=111 stl=R46 off=2) Rw_1_dr_move_a_w2_1_int16__B1 (__tmp.217)  <1620>;
              (__seff.1280 var=345 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1182)  <1623>;
              (__fch_pu8_buffer.1283 var=108 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B1 (__fch_pu8_buffer.1425)  <1626>;
            } stp=1;
            <130> {
              (__fchtmp.218 var=112 stl=DM_r) load_1_B1 (__tmp.1276 ENCCON0.189 ENCCON1.129 TXDAT.196 __extDM.16 __extDM_SFR_ENCCON0_t.26 __extDM_SFR_ENCCON1_t.28 __extDM_SFR_word.31 __extDM_int16_.24 __extDM_int8_.25)  <1424>;
              (__tmp.1276 var=111 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__tmp.1277)  <1619>;
              (__fchtmp.1279 var=112 stl=RbL off=1) Rb_1_dr_move_DM_r_1_int8__B0 (__fchtmp.218)  <1622>;
            } stp=2;
            <131> {
              (TXDAT.226 var=31 __vola.227 var=13) store_const_2_B2 (__fchtmp.1278 __ptr_TXDAT__a1.1137 TXDAT.196 __vola.178)  <1425>;
              (__fchtmp.1278 var=112 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fchtmp.1279)  <1621>;
            } stp=3;
            <132> {
              () jump_const_1_B1 (__trgt.1142)  <1426>;
            } stp=4;
            <241> {
              (__fch_pu8_buffer.1425 var=108 stl=R46 off=2) R46_Rw_ra_move_R46_Rw_int16__nguard (__fch_pu8_buffer.1330)  <1718>;
            } stp=0;
            do {
                {
                    (__vola.265 var=13) entry (__vola.402 __vola.1111)  <273>;
                    (bitcount.275 var=23) entry (bitcount.422 bitcount.1112)  <283>;
                    (ENCCON0.276 var=24) entry (ENCCON0.424 ENCCON0.1113)  <284>;
                } #37
                {
                    #41 off=27 nxt=1
                    <128> {
                      (bitcount.306 var=23 __seff.1176 var=340 stl=c_flag_w __seff.1177 var=341 stl=nz_flag_w __seff.1178 var=342 stl=o_flag_w) _mi_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_8t0.1133 bitcount.275 bitcount.275 __sp.53)  <1422>;
                      (__seff.1259 var=341 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1177)  <1602>;
                      (__seff.1260 var=340 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1176)  <1603>;
                      (__seff.1262 var=342 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1178)  <1605>;
                    } stp=0;
                    sync {
                        (__vola.307 var=13) sync_link (__vola.265) sid=1  <317>;
                        (bitcount.317 var=23) sync_link (bitcount.306) sid=11  <327>;
                        (ENCCON0.318 var=24) sync_link (ENCCON0.276) sid=12  <328>;
                    } #1 off=28 nxt=320
                    #320 off=28 nxt=325 tgt=54
                    <127> {
                      (ENCCON0.350 var=24 __vola.351 var=13 __seff.1174 var=339 stl=nz_flag_w) load_const__ad_const_cmp_const_cc_ne__jump_const_1_B1 (__ptr_ENCCON0.1128 __trgt.1138 ENCCON0.318 __vola.307)  <1421>;
                      (__seff.1261 var=339 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1174)  <1604>;
                    } stp=0;
                    if {
                        {
                            () if_expr (__either.1120)  <403>;
                            (__either.1120 var=325) undefined ()  <1358>;
                        } #46
                        {
                            (__false.1121 var=324) const ()  <1359>;
                        } #48
                        {
                            (__either.1123 var=325) undefined ()  <1362>;
                            <125> {
                              (__apl_c.954 var=252 stl=c_flag_w __apl_nz.956 var=254 stl=nz_flag_w __seff.1171 var=338 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B3 (__ct_8t0.1133 bitcount.317 __sp.53)  <1419>;
                              (__apl_nz.1264 var=254 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.956)  <1607>;
                              (__apl_c.1266 var=252 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.954)  <1609>;
                              (__seff.1267 var=338 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1171)  <1610>;
                            } stp=0;
                            <126> {
                              () cc_a__jump_const_1_B1 (__apl_c.1265 __apl_nz.1263 __trgt.1139)  <1420>;
                              (__apl_nz.1263 var=254 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.1264)  <1606>;
                              (__apl_c.1265 var=252 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.1266)  <1608>;
                            } stp=1;
                        } #325 off=30 nxt=54 tgt=41
                        {
                            (__tmp.401 var=143) merge (__false.1121 __either.1123)  <410>;
                        } #49
                    } #45
                } #38
                {
                    () while_expr (__tmp.401)  <411>;
                    (__vola.402 var=13 __vola.403 var=13) exit (__vola.351)  <412>;
                    (bitcount.422 var=23 bitcount.423 var=23) exit (bitcount.317)  <422>;
                    (ENCCON0.424 var=24 ENCCON0.425 var=24) exit (ENCCON0.350)  <423>;
                } #50
            } #36
            #54 off=32 nxt=57
            <124> {
              (bitcount.549 var=23) store_const_1_B1 (__adr_bitcount.1292 bitcount.423)  <1418>;
              (__adr_bitcount.1292 var=61 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_bitcount.1341)  <1627>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <581>;
            } #57 off=34 nxt=58
            #58 off=34 nxt=2
            <123> {
              (i.555 var=30 __seff.1165 var=335 stl=c_flag_w __seff.1166 var=336 stl=nz_flag_w __seff.1167 var=337 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_6t0.1134 i.195 i.195 __sp.53)  <1417>;
              (__seff.1270 var=336 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1166)  <1613>;
              (__seff.1271 var=335 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1165)  <1614>;
              (__seff.1281 var=337 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1167)  <1624>;
            } stp=0;
            sync {
                (__vola.556 var=13) sync_link (__vola.403) sid=37  <588>;
                (bitcount.566 var=23) sync_link (bitcount.549) sid=47  <598>;
                (ENCCON0.567 var=24) sync_link (ENCCON0.425) sid=48  <599>;
                (i.573 var=30) sync_link (i.555) sid=54  <605>;
                (TXDAT.574 var=31) sync_link (TXDAT.226) sid=55  <606>;
            } #2 off=35 nxt=330
            #330 off=35 nxt=67 tgt=52
            <121> {
              (__apl_c.960 var=264 stl=c_flag_w __apl_nz.962 var=266 stl=nz_flag_w __seff.1163 var=334 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_1_B2 (__fch_u16_numbytes.1301 __ct_6t0.1134 i.573 __sp.53)  <1415>;
              (__apl_nz.1273 var=266 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.962)  <1616>;
              (__apl_c.1275 var=264 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.960)  <1618>;
              (__seff.1282 var=334 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1163)  <1625>;
              (__fch_u16_numbytes.1301 var=151 stl=a_w1) a_w1_1_dr_move_Rw_1_int16__B0 (__fch_u16_numbytes.1329)  <1628>;
            } stp=0;
            <122> {
              () cc_b__jump_const_1_B1 (__apl_c.1274 __apl_nz.1272 __trgt.1140)  <1416>;
              (__apl_nz.1272 var=266 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.1273)  <1615>;
              (__apl_c.1274 var=264 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.1275)  <1617>;
            } stp=1;
        } #29
        {
            () while_expr (__either.1125)  <627>;
            (__vola.595 var=13 __vola.596 var=13) exit (__vola.556)  <628>;
            (bitcount.615 var=23 bitcount.616 var=23) exit (bitcount.566)  <638>;
            (ENCCON0.617 var=24 ENCCON0.618 var=24) exit (ENCCON0.567)  <639>;
            (i.629 var=30 i.630 var=30) exit (i.573)  <645>;
            (TXDAT.631 var=31 TXDAT.632 var=31) exit (TXDAT.574)  <646>;
            (__either.1125 var=325) undefined ()  <1365>;
        } #62
    } #27
    #67 off=37 nxt=-2
    () sink (__vola.596)  <798>;
    () sink (__sp.744)  <804>;
    () sink (pu8_buffer.85)  <805>;
    () sink (u16_numbytes.87)  <806>;
    () sink (e_encoding.89)  <807>;
    () sink (bitcount.616)  <808>;
    () sink (ENCCON0.618)  <809>;
    () sink (ENCCON1.129)  <813>;
    () sink (i.630)  <815>;
    () sink (TXDAT.632)  <816>;
    (__ct_10s0.1135 var=153) const_inp ()  <1377>;
    <119> {
      (__sp.744 var=19 __seff.1158 var=331 stl=c_flag_w __seff.1159 var=332 stl=nz_flag_w __seff.1160 var=333 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_10s0.1135 __sp.53 __sp.53)  <1413>;
      (__seff.1327 var=332 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1159)  <1631>;
      (__seff.1328 var=331 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1158)  <1632>;
      (__seff.1339 var=333 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1160)  <1643>;
    } stp=0;
    <120> {
      () ret_1_B1 ()  <1414>;
    } stp=2;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,176:0,0);
1 : (0,189:4,18);
2 : (0,185:2,34);
5 : (0,176:84,0);
6 : (0,176:84,0);
7 : (0,176:61,0);
8 : (0,176:61,0);
9 : (0,176:39,0);
10 : (0,176:39,0);
11 : (0,179:26,5);
14 : (0,181:14,7);
18 : (0,183:14,9);
20 : (0,183:22,10);
21 : (0,185:10,11);
22 : (0,185:10,11);
27 : (0,185:2,12);
29 : (0,185:2,12);
36 : (0,189:4,14);
38 : (0,189:4,14);
41 : (0,191:17,16);
45 : (0,189:45,20);
48 : (0,189:45,22);
52 : (0,189:4,26);
54 : (0,193:13,30);
57 : (0,185:34,0);
58 : (0,185:34,0);
64 : (0,185:2,38);
67 : (0,195:0,41);
320 : (0,189:39,20);
325 : (0,189:56,21);
330 : (0,185:17,36);
421 : (0,181:14,7);
429 : (0,183:14,9);
----------
96 : (0,176:84,0);
98 : (0,176:61,0);
100 : (0,176:39,0);
111 : (0,181:14,7);
129 : (0,183:14,9);
140 : (0,183:22,10);
144 : (0,185:10,11);
186 : (0,185:2,12);
196 : (0,185:2,12);
197 : (0,185:2,12);
203 : (0,185:2,12);
204 : (0,185:2,12);
273 : (0,189:4,14);
283 : (0,189:4,14);
284 : (0,189:4,14);
403 : (0,189:45,20);
410 : (0,189:45,23);
411 : (0,189:4,24);
412 : (0,189:4,24);
422 : (0,189:4,24);
423 : (0,189:4,24);
581 : (0,185:34,0);
627 : (0,185:2,36);
628 : (0,185:2,36);
638 : (0,185:2,36);
639 : (0,185:2,36);
645 : (0,185:2,36);
646 : (0,185:2,36);
1413 : (0,195:0,0) (0,195:0,41);
1414 : (0,195:0,41);
1415 : (0,185:15,36) (0,178:11,0) (0,185:17,36);
1416 : (0,185:17,36) (0,185:2,36);
1417 : (0,185:34,31) (0,185:33,0) (0,178:11,0);
1418 : (0,193:4,29);
1419 : (0,189:48,21) (0,179:11,0) (0,189:56,21);
1420 : (0,189:56,21) (0,189:4,24);
1421 : (0,189:26,20) (0,189:30,20) (0,189:39,20) (0,189:45,20);
1422 : (0,191:17,15) (0,191:9,0) (0,179:11,0) (0,191:9,15);
1423 : (0,187:31,12) (0,178:11,0) (0,187:30,12);
1424 : (0,187:30,12);
1425 : (0,187:14,12);
1427 : (0,187:20,12) (0,176:100,0);
1428 : (0,185:19,36) (0,176:70,0);
1430 : (0,178:11,0) (0,185:8,10);
1432 : (0,183:14,9);
1433 : (0,181:37,7) (0,176:49,0) (0,181:24,7);
1434 : (0,181:24,7);
1435 : (0,181:14,7);
1437 : (0,179:26,5);
1438 : (0,176:49,0) (0,176:39,0);
1439 : (0,176:70,0) (0,176:61,0);
1440 : (0,176:5,0);
1441 : (0,176:100,0) (0,176:84,0);
1577 : (0,179:11,0);
1586 : (0,183:24,0);
1718 : (0,187:31,0) (0,187:30,0);

