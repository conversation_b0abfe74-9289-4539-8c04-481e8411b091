/*
  -----------------------------------------------------------------------------
  Copyright 2010 - 2020  NXP
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, <PERSON>FF<PERSON>IATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: ncf29xx.h 24956 2020-01-31 09:22:44Z dep10330 $
  $Revision: 24956 $
*/

/**
 * @file
 * Include the SFR declarations header file for the device selected by preprocessor symbol (macro),
 * e.g. NCF29A1.
 */


/**
 * @defgroup NCF29xyHW NCF29xy Hardware Interface
 * Hardware interface declarations of TOKEN product family
 * @{
 */

/*
  *****      SUPPORTED TYPES              *****
  +-----------------+-------------------------+
  | Type            | SFR type defin. file    |
  +-----------------+-------------------------+
  | NCF29A1         | platform_token.h        |
  | NCF29A2         |                         |
  | NCF29A3         |                         |
  | NCF29A4         |                         |
  +-----------------+                         +
  | NCF2953         |                         |
  | NCF2954         |                         |
  +-----------------+                         +
  | NCF21A2         |                         |
  +=================+=========================+
  | NCF2961         | platform_smart2a.h      |
  +-----------------+                         +
  | NCF2161         |                         |
  +=================+=========================+
  | NCF29A7         | platform_tokenplus.h    |
  | NCF29A8         |                         |
  +-----------------+                         +
  | NCF2957         |                         |
  | NCF2958         |                         |
  +-----------------+                         +
  | NCF2157         |                         |
  +=================+=========================+
  | NCF29AA         | platform_srx.h          |
  | NCF29AB         |                         |
  +-----------------+                         +
  | NCF295A         |                         |
  | NCF295B         |                         |
  +-----------------+                         +
  | NCF215A         |                         |
  +=================+=========================+
  | NCF29AE         | platform_srx2.h         |
  | NCF29AF         |                         |
  +-----------------+                         +
  | NCF295E         |                         |
  | NCF295F         |                         |
  +-----------------+                         +
  | NCF215E         |                         |
  +-----------------+                         +
  | NCF29AC         |                         |
  +-----------------+                         +
  | NCF295C         |                         |
  +-----------------+                         +
  | NCF215C         |                         |
  +-----------------+-------------------------+

*/

/* TOKEN platform */
#if   defined( NCF29A1 ) || defined( NCF29A2 ) || defined( NCF29A3 ) || defined( NCF29A4 ) \
   || defined( NCF2953 ) || defined( NCF2954 ) \
   || defined( NCF21A2 )
#include "shared_sfrdef/platform_token.h"

/*-----------------------------------------------*/

/* SMART2A platform */
#elif defined( NCF2961 ) \
   || defined( NCF2161 )
#include "shared_sfrdef/platform_smart2a.h"

/*-----------------------------------------------*/

/* TOKEN-PLUS platform */
#elif defined( NCF29A7 ) || defined( NCF29A8 ) \
   || defined( NCF2957 ) || defined( NCF2958 ) \
   || defined( NCF2157 ) || defined( NCF2158 )
#include "shared_sfrdef/platform_tokenplus.h"

/*-----------------------------------------------*/

/* TOKEN-SRX platform */
#elif defined(NCF29AA) || defined(NCF29AB) \
   || defined(NCF295A) || defined(NCF295B) \
   || defined(NCF215A)
#include "shared_sfrdef/platform_srx.h"

/*-----------------------------------------------*/

/* TOKEN-SRX2 platform */
#elif defined(NCF29AE) || defined(NCF29AF) \
   || defined(NCF295E) || defined(NCF295F) \
   || defined(NCF215E)
#include "shared_sfrdef/platform_srx2.h"

/* IMPACT, ACTIC-I, ACTIC-I-CustomSpec,
   redirect to TOKEN-SRX2 platform */
#elif defined(NCF29AC) \
   || defined(NCF295C) \
   || defined(NCF215C)
#include "shared_sfrdef/platform_srx2.h"

#else

#error "device not (yet) supported or not defined."

#endif

/*@}*/

/* eof */
