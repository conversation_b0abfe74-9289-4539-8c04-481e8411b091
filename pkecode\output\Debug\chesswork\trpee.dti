
// File generated by noodle version P-2019.09#78e58cd307#210222, Fri Nov  3 15:38:56 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/trpee.c mrk3

chess_source_level_debug

             __schar_DM9 : _basic(DM9,1,1) __schar;
                int8_DM9 : _typedef(DM9,1,1) int8 $__schar_DM9;
                  bool__ : _basic() bool;
               __cchar__ : _basic() __cchar;
               __schar__ : _basic() __schar;
               __uchar__ : _basic() __uchar;
              __sshort__ : _basic() __sshort;
              __ushort__ : _basic() __ushort;
                __sint__ : _basic() __sint;
                __uint__ : _basic() __uint;
           __slonglong__ : _basic() __slonglong;
           __ulonglong__ : _basic() __ulonglong;
               __fhalf__ : _basic() __fhalf;
                int16___ : _basic() int16_;
                uint1___ : _basic() uint1_;
                uint2___ : _basic() uint2_;
                 int8___ : _basic() int8_;
               __Pvoid__ : _basic() __Pvoid;
               __slong__ : _basic() __slong;
               __ulong__ : _basic() __ulong;
               float24__ : _basic() float24;
               float32__ : _typedef() float32 $__ulong__;
              __ffloat__ : _basic() __ffloat;
             __fdouble__ : _basic() __fdouble;
         __flongdouble__ : _basic() __flongdouble;
             __uchar_DM9 : _basic(DM9,1,1) __uchar;
             uint8_t_DM9 : _typedef(DM9,1,1) uint8_t $__uchar_DM9;
__anonymous6__trpee__DM9 : _struct(DM9,2,1) __anonymous6__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
            __ushort_DM9 : _basic(DM9,2,2) __ushort;
            uint16_t_DM9 : _typedef(DM9,2,2) uint16_t $__ushort_DM9;
            SFR_word_DM9 : _union(DM9,2,2) SFR_word {
                     byte $__anonymous6__trpee__DM9 @0;
                     val $uint16_t_DM9 @0;
                 }
          SFR_CXPC_t_DM9 : _typedef(DM9,2,2) SFR_CXPC_t $SFR_word_DM9;
              __uint_DM9 : _basic(DM9,2,2) __uint;
          bitfield_t_DM9 : _typedef(DM9,2,2) bitfield_t $__uint_DM9;
__anonymous8__trpee__DM9 : _struct(DM9,1,1) __anonymous8__trpee_ {
                     XINSTR $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     XSTACK $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     XPMEM $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     INTLEV $bitfield_t_DM9 @0 _bf 4 @3 $__schar_DM9;
                     SYSMODE $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
          SFR_CXSW_t_DM9 : _union(DM9,1,1) SFR_CXSW_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous8__trpee__DM9 @0;
                 }
__anonymous10__trpee__DM9 : _struct(DM9,1,1) __anonymous10__trpee_ {
                     P10S $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11S $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12S $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13S $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14S $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15S $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16S $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17S $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_P1INS_t_DM9 : _union(DM9,1,1) SFR_P1INS_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous10__trpee__DM9 @0;
                 }
__anonymous12__trpee__DM9 : _struct(DM9,1,1) __anonymous12__trpee_ {
                     P10O $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11O $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12O $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13O $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14O $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15O $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16O $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17O $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_P1OUT_t_DM9 : _union(DM9,1,1) SFR_P1OUT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous12__trpee__DM9 @0;
                 }
__anonymous14__trpee__DM9 : _struct(DM9,1,1) __anonymous14__trpee_ {
                     P10D $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11D $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12D $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13D $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14D $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15D $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16D $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17D $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_P1DIR_t_DM9 : _union(DM9,1,1) SFR_P1DIR_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous14__trpee__DM9 @0;
                 }
__anonymous16__trpee__DM9 : _struct(DM9,1,1) __anonymous16__trpee_ {
                     P20S $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21S $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P22S $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P23S $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P24S $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                 }
         SFR_P2INS_t_DM9 : _union(DM9,1,1) SFR_P2INS_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous16__trpee__DM9 @0;
                 }
__anonymous18__trpee__DM9 : _struct(DM9,1,1) __anonymous18__trpee_ {
                     P20O $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21O $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P22O $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P23O $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P24O $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                 }
         SFR_P2OUT_t_DM9 : _union(DM9,1,1) SFR_P2OUT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous18__trpee__DM9 @0;
                 }
__anonymous20__trpee__DM9 : _struct(DM9,1,1) __anonymous20__trpee_ {
                     P20D $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21D $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P22D $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P23D $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P24D $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                 }
         SFR_P2DIR_t_DM9 : _union(DM9,1,1) SFR_P2DIR_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous20__trpee__DM9 @0;
                 }
__anonymous22__trpee__DM9 : _struct(DM9,1,1) __anonymous22__trpee_ {
                     P10INTDIS $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11INTDIS $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12INTDIS $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13INTDIS $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14INTDIS $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15INTDIS $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16INTDIS $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17INTDIS $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_P1INTDIS_t_DM9 : _union(DM9,1,1) SFR_P1INTDIS_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous22__trpee__DM9 @0;
                 }
__anonymous24__trpee__DM9 : _struct(DM9,1,1) __anonymous24__trpee_ {
                     P20INTDIS $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21INTDIS $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P22INTDIS $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P23INTDIS $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P24INTDIS $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                 }
      SFR_P2INTDIS_t_DM9 : _union(DM9,1,1) SFR_P2INTDIS_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous24__trpee__DM9 @0;
                 }
__anonymous26__trpee__DM9 : _struct(DM9,1,1) __anonymous26__trpee_ {
                     IIU_OBCSEN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                 }
       SFR_IIUCON2_t_DM9 : _union(DM9,1,1) SFR_IIUCON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous26__trpee__DM9 @0;
                 }
__anonymous28__trpee__DM9 : _struct(DM9,1,1) __anonymous28__trpee_ {
                     IIU_CLKSEL $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     IIU_DPSEL $bitfield_t_DM9 @0 _bf 3 @2 $__schar_DM9;
                     IIU_MODSEL $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                     IIU_LSBF $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_IIUCON0_t_DM9 : _union(DM9,1,1) SFR_IIUCON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous28__trpee__DM9 @0;
                 }
__anonymous30__trpee__DM9 : _struct(DM9,1,1) __anonymous30__trpee_ {
                     IIU_BCNTZERO $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IIU_FINISHED $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IIU_TXWAIT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IIU_ENCSEL $bitfield_t_DM9 @0 _bf 2 @3 $__schar_DM9;
                     IIU_RST $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IIU_LFDEMEN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IIU_LFDATA $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_IIUSTAT_t_DM9 : _union(DM9,1,1) SFR_IIUSTAT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous30__trpee__DM9 @0;
                 }
__anonymous32__trpee__DM9 : _struct(DM9,1,1) __anonymous32__trpee_ {
                     IIU_BITCNT $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     IIU_PRIO $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_IIUCON1_t_DM9 : _union(DM9,1,1) SFR_IIUCON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous32__trpee__DM9 @0;
                 }
            SFR_byte_DM9 : _union(DM9,1,1) SFR_byte {
                     val $uint8_t_DM9 @0;
                 }
        SFR_IIUDAT_t_DM9 : _typedef(DM9,1,1) SFR_IIUDAT_t $SFR_byte_DM9;
__anonymous34__trpee__DM9 : _struct(DM9,1,1) __anonymous34__trpee_ {
                     IIU_STATE $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                 }
      SFR_IIUSTATE_t_DM9 : _union(DM9,1,1) SFR_IIUSTATE_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous34__trpee__DM9 @0;
                 }
__anonymous36__trpee__DM9 : _struct(DM9,1,1) __anonymous36__trpee_ {
                     HTMODE $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                     HTOSEL $bitfield_t_DM9 @0 _bf 2 @3 $__schar_DM9;
                     HTEN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
         SFR_HTCON_t_DM9 : _union(DM9,1,1) SFR_HTCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous36__trpee__DM9 @0;
                 }
        SFR_AESDAT_t_DM9 : _typedef(DM9,2,2) SFR_AESDAT_t $SFR_word_DM9;
__anonymous38__trpee__DM9 : _struct(DM9,1,1) __anonymous38__trpee_ {
                     AESBC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     AESACC $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     AESRST $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     AESRUN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_AESCON_t_DM9 : _union(DM9,1,1) SFR_AESCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous38__trpee__DM9 @0;
                 }
        SFR_CRCDAT_t_DM9 : _typedef(DM9,1,1) SFR_CRCDAT_t $SFR_byte_DM9;
       SFR_CRC8DIN_t_DM9 : _typedef(DM9,1,1) SFR_CRC8DIN_t $SFR_byte_DM9;
__anonymous40__trpee__DM9 : _struct(DM9,1,1) __anonymous40__trpee_ {
                     WDCLR $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     WDTRIG $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     WDMODE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     WDTIM $bitfield_t_DM9 @0 _bf 4 @4 $__schar_DM9;
                 }
         SFR_WDCON_t_DM9 : _union(DM9,1,1) SFR_WDCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous40__trpee__DM9 @0;
                 }
__anonymous42__trpee__DM9 : _struct(DM9,1,1) __anonymous42__trpee_ {
                     CPUCLKCYC $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     CPUCLKSEL $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                 }
       SFR_CLKCON0_t_DM9 : _union(DM9,1,1) SFR_CLKCON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous42__trpee__DM9 @0;
                 }
__anonymous44__trpee__DM9 : _struct(DM9,1,1) __anonymous44__trpee_ {
                     RER_COMP $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     MDICLKSEL $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     LFCLKX2DIS $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
       SFR_CLKCON1_t_DM9 : _union(DM9,1,1) SFR_CLKCON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous44__trpee__DM9 @0;
                 }
__anonymous46__trpee__DM9 : _struct(DM9,1,1) __anonymous46__trpee_ {
                     MRCOSC_EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     AUXRCOSC_EN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     AUXRCOSC_OUTDIS $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     AESCLKSEL $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                     ADCCLKSEL $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_CLKCON2_t_DM9 : _union(DM9,1,1) SFR_CLKCON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous46__trpee__DM9 @0;
                 }
__anonymous48__trpee__DM9 : _struct(DM9,1,1) __anonymous48__trpee_ {
                     TMUX0C $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     TMUX1C $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                 }
       SFR_CLKCON3_t_DM9 : _union(DM9,1,1) SFR_CLKCON3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous48__trpee__DM9 @0;
                 }
__anonymous50__trpee__DM9 : _struct(DM9,1,1) __anonymous50__trpee_ {
                     CPUAUXDIVSEL $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     CGAESDIS $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                 }
       SFR_CLKCON4_t_DM9 : _union(DM9,1,1) SFR_CLKCON4_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous50__trpee__DM9 @0;
                 }
        SFR_PREDAT_t_DM9 : _typedef(DM9,1,1) SFR_PREDAT_t $SFR_byte_DM9;
__anonymous52__trpee__DM9 : _struct(DM9,1,1) __anonymous52__trpee_ {
                     IT_MODE $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     IT_SEL $bitfield_t_DM9 @0 _bf 3 @2 $__schar_DM9;
                     RTC_SEL $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                     ITRST $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_RTCCON_t_DM9 : _union(DM9,1,1) SFR_RTCCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous52__trpee__DM9 @0;
                 }
__anonymous54__trpee__DM9 : _struct(DM9,1,1) __anonymous54__trpee_ {
                     LPRC_EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     BDRATE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     PDLFACT $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     PRERST $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     DIGFILRST $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON2_t_DM9 : _union(DM9,1,1) SFR_PRECON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous54__trpee__DM9 @0;
                 }
__anonymous56__trpee__DM9 : _struct(DM9,1,1) __anonymous56__trpee_ {
                     QFACT1 $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     PD1_6DB $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     DISCH1ACT $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON3_t_DM9 : _union(DM9,1,1) SFR_PRECON3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous56__trpee__DM9 @0;
                 }
__anonymous58__trpee__DM9 : _struct(DM9,1,1) __anonymous58__trpee_ {
                     QFACT2 $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     PD2_6DB $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     DISCH2ACT $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON4_t_DM9 : _union(DM9,1,1) SFR_PRECON4_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous58__trpee__DM9 @0;
                 }
__anonymous60__trpee__DM9 : _struct(DM9,1,1) __anonymous60__trpee_ {
                     QFACT3 $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     PD3_6DB $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     DISCH3ACT $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON5_t_DM9 : _union(DM9,1,1) SFR_PRECON5_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous60__trpee__DM9 @0;
                 }
__anonymous62__trpee__DM9 : _struct(DM9,1,1) __anonymous62__trpee_ {
                     LPRC_CAL $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     CVTYPE $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     ERRTOLEN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
       SFR_PRECON6_t_DM9 : _union(DM9,1,1) SFR_PRECON6_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous62__trpee__DM9 @0;
                 }
__anonymous64__trpee__DM9 : _struct(DM9,1,1) __anonymous64__trpee_ {
                     WUP1_LEN $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     WUPSEL $bitfield_t_DM9 @0 _bf 3 @5 $__schar_DM9;
                 }
       SFR_PRECON7_t_DM9 : _union(DM9,1,1) SFR_PRECON7_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous64__trpee__DM9 @0;
                 }
__anonymous66__trpee__DM9 : _struct(DM9,1,1) __anonymous66__trpee_ {
                     WUP2_LEN $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     PDIREFSTUP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     NEWBYTEOVFHOLD $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON8_t_DM9 : _union(DM9,1,1) SFR_PRECON8_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous66__trpee__DM9 @0;
                 }
__anonymous68__trpee__DM9 : _struct(DM9,1,1) __anonymous68__trpee_ {
                     WUP3_LEN $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     FIFOEN $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     DIGFILMODE $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                 }
       SFR_PRECON9_t_DM9 : _union(DM9,1,1) SFR_PRECON9_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous68__trpee__DM9 @0;
                 }
         SFR_PREPD_t_DM9 : _typedef(DM9,1,1) SFR_PREPD_t $SFR_byte_DM9;
    SFR_USRBATRGLx_t_DM9 : _typedef(DM9,1,1) SFR_USRBATRGLx_t $SFR_byte_DM9;
__anonymous70__trpee__DM9 : _struct(DM9,2,1) __anonymous70__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
              __sint_DM9 : _basic(DM9,2,2) __sint;
__anonymous71__trpee__DM9 : _struct(DM9,2,2) __anonymous71__trpee_ {
                     WUP1M $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     WUP2M $bitfield_t_DM9 @0 _bf 1 @1 $__sint_DM9;
                     WUP3M $bitfield_t_DM9 @0 _bf 1 @2 $__sint_DM9;
                     NEWBYTE $bitfield_t_DM9 @0 _bf 1 @3 $__sint_DM9;
                     BITFAIL $bitfield_t_DM9 @0 _bf 1 @4 $__sint_DM9;
                     RTC_WUP $bitfield_t_DM9 @0 _bf 1 @5 $__sint_DM9;
                     IT_WUP $bitfield_t_DM9 @0 _bf 1 @6 $__sint_DM9;
                     ERRTOL $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     WUP1MH $bitfield_t_DM9 @0 _bf 1 @8 $__sint_DM9;
                     WUP2MH $bitfield_t_DM9 @0 _bf 1 @9 $__sint_DM9;
                     WUP3MH $bitfield_t_DM9 @0 _bf 1 @10 $__sint_DM9;
                     NEWBYTE_OVF $bitfield_t_DM9 @0 _bf 1 @11 $__sint_DM9;
                     NEXTFIFODATOK $bitfield_t_DM9 @0 _bf 1 @12 $__sint_DM9;
                     RTC_WUP_OVF $bitfield_t_DM9 @0 _bf 1 @13 $__sint_DM9;
                     MODE $bitfield_t_DM9 @0 _bf 2 @14 $__sint_DM9;
                 }
       SFR_PRESTAT_t_DM9 : _union(DM9,2,2) SFR_PRESTAT_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous70__trpee__DM9 @0;
                     bits $__anonymous71__trpee__DM9 @0;
                 }
        SFR_WUP1W0_t_DM9 : _typedef(DM9,2,2) SFR_WUP1W0_t $SFR_word_DM9;
        SFR_WUP1W1_t_DM9 : _typedef(DM9,2,2) SFR_WUP1W1_t $SFR_word_DM9;
        SFR_WUP2W0_t_DM9 : _typedef(DM9,2,2) SFR_WUP2W0_t $SFR_word_DM9;
        SFR_WUP2W1_t_DM9 : _typedef(DM9,2,2) SFR_WUP2W1_t $SFR_word_DM9;
        SFR_WUP3W0_t_DM9 : _typedef(DM9,2,2) SFR_WUP3W0_t $SFR_word_DM9;
          SFR_PRET_t_DM9 : _typedef(DM9,2,2) SFR_PRET_t $SFR_word_DM9;
         SFR_PRE3T_t_DM9 : _typedef(DM9,2,2) SFR_PRE3T_t $SFR_word_DM9;
__anonymous73__trpee__DM9 : _struct(DM9,2,1) __anonymous73__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous74__trpee__DM9 : _struct(DM9,2,2) __anonymous74__trpee_ {
                     FSEC $bitfield_t_DM9 @0 _bf 4 @0 $__sint_DM9;
                     SEC $bitfield_t_DM9 @0 _bf 6 @4 $__sint_DM9;
                     MIN $bitfield_t_DM9 @0 _bf 6 @10 $__sint_DM9;
                 }
        SFR_RTCDAT_t_DM9 : _union(DM9,2,2) SFR_RTCDAT_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous73__trpee__DM9 @0;
                     bits $__anonymous74__trpee__DM9 @0;
                 }
__anonymous76__trpee__DM9 : _struct(DM9,1,1) __anonymous76__trpee_ {
                     AGCLOWBWEN $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     QLIM $bitfield_t_DM9 @0 _bf 3 @5 $__schar_DM9;
                 }
      SFR_PRECON10_t_DM9 : _union(DM9,1,1) SFR_PRECON10_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous76__trpee__DM9 @0;
                 }
__anonymous78__trpee__DM9 : _struct(DM9,1,1) __anonymous78__trpee_ {
                     RINMODE $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     GAINCH1 $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     GAINCH2 $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     GAINCH3 $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
      SFR_PRECON11_t_DM9 : _union(DM9,1,1) SFR_PRECON11_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous78__trpee__DM9 @0;
                 }
__anonymous80__trpee__DM9 : _struct(DM9,2,1) __anonymous80__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous81__trpee__DM9 : _struct(DM9,2,2) __anonymous81__trpee_ {
                     ADCDATA $bitfield_t_DM9 @0 _bf 10 @0 $__sint_DM9;
                     CONV_OV $bitfield_t_DM9 @0 _bf 1 @14 $__sint_DM9;
                 }
        SFR_ADCDAT_t_DM9 : _union(DM9,2,2) SFR_ADCDAT_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous80__trpee__DM9 @0;
                     bits $__anonymous81__trpee__DM9 @0;
                 }
__anonymous83__trpee__DM9 : _struct(DM9,2,1) __anonymous83__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous84__trpee__DM9 : _struct(DM9,2,2) __anonymous84__trpee_ {
                     CONVSTART $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     CONVRESET $bitfield_t_DM9 @0 _bf 1 @1 $__sint_DM9;
                     BG1V2EN $bitfield_t_DM9 @0 _bf 1 @4 $__sint_DM9;
                     BG1V2BUFEN $bitfield_t_DM9 @0 _bf 1 @5 $__sint_DM9;
                     BATMEASEN $bitfield_t_DM9 @0 _bf 1 @6 $__sint_DM9;
                     TSENSEN $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     SAMTIM $bitfield_t_DM9 @0 _bf 2 @8 $__sint_DM9;
                     REFSEL $bitfield_t_DM9 @0 _bf 3 @10 $__sint_DM9;
                     INSEL $bitfield_t_DM9 @0 _bf 3 @13 $__sint_DM9;
                 }
        SFR_ADCCON_t_DM9 : _union(DM9,2,2) SFR_ADCCON_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous83__trpee__DM9 @0;
                     bits $__anonymous84__trpee__DM9 @0;
                 }
__anonymous86__trpee__DM9 : _struct(DM9,2,1) __anonymous86__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous87__trpee__DM9 : _struct(DM9,2,2) __anonymous87__trpee_ {
                     RSSI_RST $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     RSSI_OVF $bitfield_t_DM9 @0 _bf 3 @1 $__sint_DM9;
                     RSSI_HOLD $bitfield_t_DM9 @0 _bf 1 @4 $__sint_DM9;
                     RSSI_CHANSEL $bitfield_t_DM9 @0 _bf 2 @5 $__sint_DM9;
                     RSSI_PON $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     RSSI_RANGEEXTDIS $bitfield_t_DM9 @0 _bf 1 @8 $__sint_DM9;
                     RSSI_RANGE $bitfield_t_DM9 @0 _bf 3 @9 $__sint_DM9;
                 }
       SFR_RSSICON_t_DM9 : _union(DM9,2,2) SFR_RSSICON_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous86__trpee__DM9 @0;
                     bits $__anonymous87__trpee__DM9 @0;
                 }
       SFR_ULPADDR_t_DM9 : _typedef(DM9,2,2) SFR_ULPADDR_t $SFR_word_DM9;
        SFR_ULPSEL_t_DM9 : _typedef(DM9,2,2) SFR_ULPSEL_t $SFR_word_DM9;
__anonymous89__trpee__DM9 : _struct(DM9,1,1) __anonymous89__trpee_ {
                     ULPBITAMOUNT $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                     ULPWR_RD $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     ULPPON $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     ULPRST $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     ULPPROGERR $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     ULPRUN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_ULPCON0_t_DM9 : _union(DM9,1,1) SFR_ULPCON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous89__trpee__DM9 @0;
                 }
        SFR_ULPDAT_t_DM9 : _typedef(DM9,1,1) SFR_ULPDAT_t $SFR_byte_DM9;
__anonymous91__trpee__DM9 : _struct(DM9,1,1) __anonymous91__trpee_ {
                     BUSYPROG $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_ULPCON1_t_DM9 : _union(DM9,1,1) SFR_ULPCON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous91__trpee__DM9 @0;
                 }
__anonymous93__trpee__DM9 : _struct(DM9,1,1) __anonymous93__trpee_ {
                     T0RUN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     T0RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     T0SGL $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     T0OUT $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
        SFR_T0CON0_t_DM9 : _union(DM9,1,1) SFR_T0CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous93__trpee__DM9 @0;
                 }
__anonymous95__trpee__DM9 : _struct(DM9,1,1) __anonymous95__trpee_ {
                     T0PRESC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     T0CLKSEL $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                 }
        SFR_T0CON1_t_DM9 : _union(DM9,1,1) SFR_T0CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous95__trpee__DM9 @0;
                 }
         SFR_T0REG_t_DM9 : _typedef(DM9,2,2) SFR_T0REG_t $SFR_word_DM9;
         SFR_T0RLD_t_DM9 : _typedef(DM9,2,2) SFR_T0RLD_t $SFR_word_DM9;
__anonymous97__trpee__DM9 : _struct(DM9,1,1) __anonymous97__trpee_ {
                     T1RUN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     T1RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     T1MODE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     T1RSTCMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     T1RSTCAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     T1OUT $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
        SFR_T1CON0_t_DM9 : _union(DM9,1,1) SFR_T1CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous97__trpee__DM9 @0;
                 }
__anonymous99__trpee__DM9 : _struct(DM9,1,1) __anonymous99__trpee_ {
                     T1PRESC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     T1CLKSEL $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                 }
        SFR_T1CON1_t_DM9 : _union(DM9,1,1) SFR_T1CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous99__trpee__DM9 @0;
                 }
__anonymous101__trpee__DM9 : _struct(DM9,1,1) __anonymous101__trpee_ {
                     T1CAPSRC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     T1CAPMODE $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                     T1MANCAP $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_T1CON2_t_DM9 : _union(DM9,1,1) SFR_T1CON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous101__trpee__DM9 @0;
                 }
         SFR_T1REG_t_DM9 : _typedef(DM9,2,2) SFR_T1REG_t $SFR_word_DM9;
         SFR_T1CAP_t_DM9 : _typedef(DM9,2,2) SFR_T1CAP_t $SFR_word_DM9;
         SFR_T1CMP_t_DM9 : _typedef(DM9,2,2) SFR_T1CMP_t $SFR_word_DM9;
__anonymous103__trpee__DM9 : _struct(DM9,1,1) __anonymous103__trpee_ {
                     T2RUN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     T2RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     T2SGL $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     T2OUT $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
        SFR_T2CON0_t_DM9 : _union(DM9,1,1) SFR_T2CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous103__trpee__DM9 @0;
                 }
__anonymous105__trpee__DM9 : _struct(DM9,1,1) __anonymous105__trpee_ {
                     T2PRESC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     T2CLKSEL $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                 }
        SFR_T2CON1_t_DM9 : _union(DM9,1,1) SFR_T2CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous105__trpee__DM9 @0;
                 }
         SFR_T2REG_t_DM9 : _typedef(DM9,2,2) SFR_T2REG_t $SFR_word_DM9;
         SFR_T2RLD_t_DM9 : _typedef(DM9,2,2) SFR_T2RLD_t $SFR_word_DM9;
        SFR_RNGDAT_t_DM9 : _typedef(DM9,2,2) SFR_RNGDAT_t $SFR_word_DM9;
__anonymous107__trpee__DM9 : _struct(DM9,1,1) __anonymous107__trpee_ {
                     RNGRST $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     RNGEN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     RNGTRIMOSC $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     RNGCONFIG $bitfield_t_DM9 @0 _bf 2 @3 $__schar_DM9;
                     RNGBITSHIFT $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                     RNGRUN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_RNGCON_t_DM9 : _union(DM9,1,1) SFR_RNGCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous107__trpee__DM9 @0;
                 }
__anonymous109__trpee__DM9 : _struct(DM9,1,1) __anonymous109__trpee_ {
                     SYSINTPRIO $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     GLOBSYSINTEN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     LINTSWCON $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_INTCON_t_DM9 : _union(DM9,1,1) SFR_INTCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous109__trpee__DM9 @0;
                 }
__anonymous111__trpee__DM9 : _struct(DM9,1,1) __anonymous111__trpee_ {
                     IF_LF $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IF_CX $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IF_PORT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IF_T0 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IF_T1CMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IF_T1CAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IF_ALTPORT $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IF_ST0 $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_INTFLAG0_t_DM9 : _union(DM9,1,1) SFR_INTFLAG0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous111__trpee__DM9 @0;
                 }
__anonymous113__trpee__DM9 : _struct(DM9,1,1) __anonymous113__trpee_ {
                     IF_IIU $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IF_ULP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IF_ADC $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IF_AES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IF_RNG $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
      SFR_INTFLAG1_t_DM9 : _union(DM9,1,1) SFR_INTFLAG1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous113__trpee__DM9 @0;
                 }
__anonymous115__trpee__DM9 : _struct(DM9,1,1) __anonymous115__trpee_ {
                     IF_IT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IF_PP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IF_SP0 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IF_SP1 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IF_T2 $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IF_MSI $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IF_VBATBRN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_INTFLAG2_t_DM9 : _union(DM9,1,1) SFR_INTFLAG2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous115__trpee__DM9 @0;
                 }
__anonymous117__trpee__DM9 : _struct(DM9,1,1) __anonymous117__trpee_ {
                     IE_LFNMI $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IE_CXNMI $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IE_PORT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IE_T0 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IE_T1CMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IE_T1CAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IE_ALTPORT $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
        SFR_INTEN0_t_DM9 : _union(DM9,1,1) SFR_INTEN0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous117__trpee__DM9 @0;
                 }
__anonymous119__trpee__DM9 : _struct(DM9,1,1) __anonymous119__trpee_ {
                     IE_IIU $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IE_ULP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IE_ADC $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IE_AES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IE_RNG $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
        SFR_INTEN1_t_DM9 : _union(DM9,1,1) SFR_INTEN1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous119__trpee__DM9 @0;
                 }
__anonymous121__trpee__DM9 : _struct(DM9,1,1) __anonymous121__trpee_ {
                     IE_IT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IE_PP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IE_SP0 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IE_SP1 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IE_T2 $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IE_MSI $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IE_VBATBRN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_INTEN2_t_DM9 : _union(DM9,1,1) SFR_INTEN2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous121__trpee__DM9 @0;
                 }
     SFR_SYSINTEN0_t_DM9 : _typedef(DM9,1,1) SFR_SYSINTEN0_t $SFR_byte_DM9;
     SFR_SYSINTEN1_t_DM9 : _typedef(DM9,1,1) SFR_SYSINTEN1_t $SFR_byte_DM9;
__anonymous123__trpee__DM9 : _struct(DM9,1,1) __anonymous123__trpee_ {
                     IS_LF $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IS_CX $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IS_PORT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IS_T0 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IS_T1CMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IS_T1CAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IS_ALTPORT $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IS_ST0 $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTSET0_t_DM9 : _union(DM9,1,1) SFR_INTSET0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous123__trpee__DM9 @0;
                 }
__anonymous125__trpee__DM9 : _struct(DM9,1,1) __anonymous125__trpee_ {
                     IS_IIU $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IS_ULP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IS_ADC $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IS_AES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IS_RNG $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
       SFR_INTSET1_t_DM9 : _union(DM9,1,1) SFR_INTSET1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous125__trpee__DM9 @0;
                 }
__anonymous127__trpee__DM9 : _struct(DM9,1,1) __anonymous127__trpee_ {
                     IS_IT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IS_PP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IS_SP0 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IS_SP1 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IS_T2 $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IS_MSI $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IS_VBATBRN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTSET2_t_DM9 : _union(DM9,1,1) SFR_INTSET2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous127__trpee__DM9 @0;
                 }
__anonymous129__trpee__DM9 : _struct(DM9,1,1) __anonymous129__trpee_ {
                     IC_LF $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IC_CX $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IC_PORT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IC_T0 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IC_T1CMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IC_T1CAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IC_ALTPORT $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IC_ST0 $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTCLR0_t_DM9 : _union(DM9,1,1) SFR_INTCLR0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous129__trpee__DM9 @0;
                 }
__anonymous131__trpee__DM9 : _struct(DM9,1,1) __anonymous131__trpee_ {
                     IC_IIU $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IC_ULP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IC_ADC $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IC_AES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IC_RNG $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
       SFR_INTCLR1_t_DM9 : _union(DM9,1,1) SFR_INTCLR1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous131__trpee__DM9 @0;
                 }
__anonymous133__trpee__DM9 : _struct(DM9,1,1) __anonymous133__trpee_ {
                     IC_IT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IC_PP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IC_SP0 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IC_SP1 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IC_T2 $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IC_MSI $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IC_VBATBRN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTCLR2_t_DM9 : _union(DM9,1,1) SFR_INTCLR2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous133__trpee__DM9 @0;
                 }
        SFR_INTVEC_t_DM9 : _typedef(DM9,2,2) SFR_INTVEC_t $SFR_word_DM9;
__anonymous135__trpee__DM9 : _struct(DM9,1,1) __anonymous135__trpee_ {
                     BATPORFLAG $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     BATRGLEN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     BATRGLRST $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                 }
       SFR_BATSYS0_t_DM9 : _union(DM9,1,1) SFR_BATSYS0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous135__trpee__DM9 @0;
                 }
__anonymous137__trpee__DM9 : _struct(DM9,1,1) __anonymous137__trpee_ {
                     BATRST $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                 }
       SFR_BATSYS1_t_DM9 : _union(DM9,1,1) SFR_BATSYS1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous137__trpee__DM9 @0;
                 }
__anonymous139__trpee__DM9 : _struct(DM9,2,1) __anonymous139__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous140__trpee__DM9 : _struct(DM9,2,2) __anonymous140__trpee_ {
                     WDTOF $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     P12C $bitfield_t_DM9 @0 _bf 3 @1 $__sint_DM9;
                     P13C $bitfield_t_DM9 @0 _bf 3 @5 $__sint_DM9;
                     P14C $bitfield_t_DM9 @0 _bf 3 @9 $__sint_DM9;
                     P15C $bitfield_t_DM9 @0 _bf 3 @13 $__sint_DM9;
                 }
      SFR_PRESWUP0_t_DM9 : _union(DM9,2,2) SFR_PRESWUP0_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous139__trpee__DM9 @0;
                     bits $__anonymous140__trpee__DM9 @0;
                 }
__anonymous142__trpee__DM9 : _struct(DM9,2,1) __anonymous142__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous143__trpee__DM9 : _struct(DM9,2,2) __anonymous143__trpee_ {
                     P16C $bitfield_t_DM9 @0 _bf 3 @1 $__sint_DM9;
                     P17C $bitfield_t_DM9 @0 _bf 3 @5 $__sint_DM9;
                     P20C $bitfield_t_DM9 @0 _bf 3 @9 $__sint_DM9;
                     P21C $bitfield_t_DM9 @0 _bf 3 @13 $__sint_DM9;
                 }
      SFR_PRESWUP1_t_DM9 : _union(DM9,2,2) SFR_PRESWUP1_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous142__trpee__DM9 @0;
                     bits $__anonymous143__trpee__DM9 @0;
                 }
      SFR_PRESWUP2_t_DM9 : _typedef(DM9,2,2) SFR_PRESWUP2_t $SFR_word_DM9;
__anonymous145__trpee__DM9 : _struct(DM9,1,1) __anonymous145__trpee_ {
                     P10WRES $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11WRES $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12WRES $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13WRES $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14WRES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15WRES $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16WRES $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17WRES $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_P1WRES_t_DM9 : _union(DM9,1,1) SFR_P1WRES_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous145__trpee__DM9 @0;
                 }
__anonymous147__trpee__DM9 : _struct(DM9,1,1) __anonymous147__trpee_ {
                     P20WRES $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21WRES $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P21MRES $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
        SFR_P2WRES_t_DM9 : _union(DM9,1,1) SFR_P2WRES_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous147__trpee__DM9 @0;
                 }
       SFR_USRBATx_t_DM9 : _typedef(DM9,1,1) SFR_USRBATx_t $SFR_byte_DM9;
__anonymous149__trpee__DM9 : _struct(DM9,2,1) __anonymous149__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous150__trpee__DM9 : _struct(DM9,2,2) __anonymous150__trpee_ {
                     LF_CAP_CH1 $bitfield_t_DM9 @0 _bf 4 @0 $__sint_DM9;
                     LF_CAP_CH2 $bitfield_t_DM9 @0 _bf 4 @4 $__sint_DM9;
                     LF_CAP_CH3 $bitfield_t_DM9 @0 _bf 4 @8 $__sint_DM9;
                     LF_CAP_CH1_DIS $bitfield_t_DM9 @0 _bf 1 @12 $__sint_DM9;
                     LF_CAP_CH2_DIS $bitfield_t_DM9 @0 _bf 1 @13 $__sint_DM9;
                     LF_CAP_CH3_DIS $bitfield_t_DM9 @0 _bf 1 @14 $__sint_DM9;
                 }
    SFR_LFTUNEVBAT_t_DM9 : _union(DM9,2,2) SFR_LFTUNEVBAT_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous149__trpee__DM9 @0;
                     bits $__anonymous150__trpee__DM9 @0;
                 }
__anonymous152__trpee__DM9 : _struct(DM9,2,1) __anonymous152__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous153__trpee__DM9 : _struct(DM9,2,2) __anonymous153__trpee_ {
                     LF_CAP_CH1 $bitfield_t_DM9 @0 _bf 4 @0 $__sint_DM9;
                     LF_CAP_CH2 $bitfield_t_DM9 @0 _bf 4 @4 $__sint_DM9;
                     LF_CAP_CH3 $bitfield_t_DM9 @0 _bf 4 @8 $__sint_DM9;
                     LF_CAP_CH1_DIS $bitfield_t_DM9 @0 _bf 1 @12 $__sint_DM9;
                     LF_CAP_CH2_DIS $bitfield_t_DM9 @0 _bf 1 @13 $__sint_DM9;
                     LF_CAP_CH3_DIS $bitfield_t_DM9 @0 _bf 1 @14 $__sint_DM9;
                 }
     SFR_LFTUNEVDD_t_DM9 : _union(DM9,2,2) SFR_LFTUNEVDD_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous152__trpee__DM9 @0;
                     bits $__anonymous153__trpee__DM9 @0;
                 }
__anonymous155__trpee__DM9 : _struct(DM9,1,1) __anonymous155__trpee_ {
                     STDIS $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     SHCH1 $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     SHCH2 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     SHCH3 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                 }
       SFR_LFSHCON_t_DM9 : _union(DM9,1,1) SFR_LFSHCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous155__trpee__DM9 @0;
                 }
__anonymous157__trpee__DM9 : _struct(DM9,1,1) __anonymous157__trpee_ {
                     PWRFLD $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     PWRBAT $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     PRESW_LF $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     VBATBRNIND $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PRESW_MODE $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     VDDARGLEN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     VDDARST $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     VDDRST $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_PCON0_t_DM9 : _union(DM9,1,1) SFR_PCON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous157__trpee__DM9 @0;
                 }
__anonymous159__trpee__DM9 : _struct(DM9,1,1) __anonymous159__trpee_ {
                     PWUPIND $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     PMODE $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     LFFLD $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     VBATBRNFLAG $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PWRMANLFSTATE $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     VBATMONEN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     VDDABRNFLAG $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     VDDBRNFLAG $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_PCON1_t_DM9 : _union(DM9,1,1) SFR_PCON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous159__trpee__DM9 @0;
                 }
__anonymous161__trpee__DM9 : _struct(DM9,1,1) __anonymous161__trpee_ {
                     LOCKP $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     R2MSDET $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     VBATBRNEXT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     VBATBRNREG $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     VBATBRNINDEN $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     VDDABRNREG $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     VDDBRNREG $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_PCON2_t_DM9 : _union(DM9,1,1) SFR_PCON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous161__trpee__DM9 @0;
                 }
__anonymous163__trpee__DM9 : _struct(DM9,1,1) __anonymous163__trpee_ {
                     DUPLEX $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     MODE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     CLKPHA $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     CLKPOL $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     LSBF $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
      SFR_SPI0CON0_t_DM9 : _union(DM9,1,1) SFR_SPI0CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous163__trpee__DM9 @0;
                 }
__anonymous165__trpee__DM9 : _struct(DM9,1,1) __anonymous165__trpee_ {
                     EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     STOP $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     INTSS $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     CLOCK $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                 }
      SFR_SPI0CON1_t_DM9 : _union(DM9,1,1) SFR_SPI0CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous165__trpee__DM9 @0;
                 }
       SFR_SPI0DAT_t_DM9 : _typedef(DM9,1,1) SFR_SPI0DAT_t $SFR_byte_DM9;
__anonymous167__trpee__DM9 : _struct(DM9,1,1) __anonymous167__trpee_ {
                     BIT $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                     RXBFOVF $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     RXBF $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     BUSY $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_SPI0STAT_t_DM9 : _union(DM9,1,1) SFR_SPI0STAT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous167__trpee__DM9 @0;
                 }
__anonymous169__trpee__DM9 : _struct(DM9,1,1) __anonymous169__trpee_ {
                     DUPLEX $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     MODE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     CLKPHA $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     CLKPOL $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     LSBF $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
      SFR_SPI1CON0_t_DM9 : _union(DM9,1,1) SFR_SPI1CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous169__trpee__DM9 @0;
                 }
__anonymous171__trpee__DM9 : _struct(DM9,1,1) __anonymous171__trpee_ {
                     EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     STOP $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     INTSS $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     CLOCK $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                 }
      SFR_SPI1CON1_t_DM9 : _union(DM9,1,1) SFR_SPI1CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous171__trpee__DM9 @0;
                 }
       SFR_SPI1DAT_t_DM9 : _typedef(DM9,1,1) SFR_SPI1DAT_t $SFR_byte_DM9;
__anonymous173__trpee__DM9 : _struct(DM9,1,1) __anonymous173__trpee_ {
                     BIT $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                     RXBFOVF $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     RXBF $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     BUSY $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_SPI1STAT_t_DM9 : _union(DM9,1,1) SFR_SPI1STAT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous173__trpee__DM9 @0;
                 }
__anonymous175__trpee__DM9 : _struct(DM9,2,1) __anonymous175__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous176__trpee__DM9 : _struct(DM9,2,2) __anonymous176__trpee_ {
                     P10AF $bitfield_t_DM9 @0 _bf 2 @0 $__sint_DM9;
                     P11AF $bitfield_t_DM9 @0 _bf 2 @2 $__sint_DM9;
                     P12AF $bitfield_t_DM9 @0 _bf 2 @4 $__sint_DM9;
                     P13AF $bitfield_t_DM9 @0 _bf 2 @6 $__sint_DM9;
                     P14AF $bitfield_t_DM9 @0 _bf 2 @8 $__sint_DM9;
                     P15AF $bitfield_t_DM9 @0 _bf 2 @10 $__sint_DM9;
                     P16AF $bitfield_t_DM9 @0 _bf 2 @12 $__sint_DM9;
                     P17AF $bitfield_t_DM9 @0 _bf 2 @14 $__sint_DM9;
                 }
        SFR_P1ALTF_t_DM9 : _union(DM9,2,2) SFR_P1ALTF_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous175__trpee__DM9 @0;
                     bits $__anonymous176__trpee__DM9 @0;
                 }
__anonymous178__trpee__DM9 : _struct(DM9,2,1) __anonymous178__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous179__trpee__DM9 : _struct(DM9,2,2) __anonymous179__trpee_ {
                     P20AF $bitfield_t_DM9 @0 _bf 2 @0 $__sint_DM9;
                     P21AF $bitfield_t_DM9 @0 _bf 2 @2 $__sint_DM9;
                     P22AF $bitfield_t_DM9 @0 _bf 2 @4 $__sint_DM9;
                     P23AF $bitfield_t_DM9 @0 _bf 2 @6 $__sint_DM9;
                     P24AF $bitfield_t_DM9 @0 _bf 2 @8 $__sint_DM9;
                 }
        SFR_P2ALTF_t_DM9 : _union(DM9,2,2) SFR_P2ALTF_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous178__trpee__DM9 @0;
                     bits $__anonymous179__trpee__DM9 @0;
                 }
        SFR_BITCNT_t_DM9 : _typedef(DM9,2,2) SFR_BITCNT_t $SFR_word_DM9;
       SFR_BITSWAP_t_DM9 : _typedef(DM9,1,1) SFR_BITSWAP_t $SFR_byte_DM9;
      SFR_PREPOLL0_t_DM9 : _typedef(DM9,1,1) SFR_PREPOLL0_t $SFR_byte_DM9;
      SFR_PREPOLL1_t_DM9 : _typedef(DM9,1,1) SFR_PREPOLL1_t $SFR_byte_DM9;
      SFR_PRECON12_t_DM9 : _typedef(DM9,1,1) SFR_PRECON12_t $SFR_byte_DM9;
__anonymous181__trpee__DM9 : _struct(DM9,1,1) __anonymous181__trpee_ {
                     MSI_TLIM $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     MSI_TCLR $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
       SFR_MSICON0_t_DM9 : _union(DM9,1,1) SFR_MSICON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous181__trpee__DM9 @0;
                 }
__anonymous183__trpee__DM9 : _struct(DM9,1,1) __anonymous183__trpee_ {
                     MSI_OVF_INT_EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     MSI_MOT_INT_EN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     MSI_OVF_WUP_EN $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     MSI_MOT_WUP_EN $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     MSI_LFA_PU_EN $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     MSI_LFA_PD_EN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     MSI_MODE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     MSI_EN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_MSICON1_t_DM9 : _union(DM9,1,1) SFR_MSICON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous183__trpee__DM9 @0;
                 }
__anonymous185__trpee__DM9 : _struct(DM9,1,1) __anonymous185__trpee_ {
                     MSI_TREG $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                 }
      SFR_MSISTAT0_t_DM9 : _union(DM9,1,1) SFR_MSISTAT0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous185__trpee__DM9 @0;
                 }
__anonymous187__trpee__DM9 : _struct(DM9,1,1) __anonymous187__trpee_ {
                     MSI_OVF_INT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     MSI_MOT_INT $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     MSI_OVF $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     MSI_LFA_PD $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
      SFR_MSISTAT1_t_DM9 : _union(DM9,1,1) SFR_MSISTAT1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous187__trpee__DM9 @0;
                 }
__anonymous189__trpee__DM9 : _struct(DM9,1,1) __anonymous189__trpee_ {
                     IF_XORDY $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IF_VCOCAL $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IF_PLLLOCK $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IF_PLLUNLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IF_PARDY $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IF_PAILIM $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IF_TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IF_TXFIN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_INTFLAG3_t_DM9 : _union(DM9,1,1) SFR_INTFLAG3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous189__trpee__DM9 @0;
                 }
__anonymous191__trpee__DM9 : _struct(DM9,1,1) __anonymous191__trpee_ {
                     IE_XORDY $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IE_VCOCAL $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IE_PLLLOCK $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IE_PLLUNLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IE_PARDY $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IE_PAILIM $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IE_TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IE_TXFIN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_INTEN3_t_DM9 : _union(DM9,1,1) SFR_INTEN3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous191__trpee__DM9 @0;
                 }
__anonymous193__trpee__DM9 : _struct(DM9,1,1) __anonymous193__trpee_ {
                     IS_XORDY $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IS_VCOCAL $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IS_PLLLOCK $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IS_PLLUNLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IS_PARDY $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IS_PAILIM $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IS_TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IS_TXFIN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTSET3_t_DM9 : _union(DM9,1,1) SFR_INTSET3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous193__trpee__DM9 @0;
                 }
__anonymous195__trpee__DM9 : _struct(DM9,1,1) __anonymous195__trpee_ {
                     IC_XORDY $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IC_VCOCAL $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IC_PLLLOCK $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IC_PLLUNLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IC_PARDY $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IC_PAILIM $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IC_TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IC_TXFIN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTCLR3_t_DM9 : _union(DM9,1,1) SFR_INTCLR3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous195__trpee__DM9 @0;
                 }
__anonymous197__trpee__DM9 : _struct(DM9,1,1) __anonymous197__trpee_ {
                     VDDXOEN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     VDDPLLEN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     VDDHSEN $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     XOEN $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PLLEN $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     PAEN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     XO_READY $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     XO_READY_EN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_TXPCON_t_DM9 : _union(DM9,1,1) SFR_TXPCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous197__trpee__DM9 @0;
                 }
__anonymous199__trpee__DM9 : _struct(DM9,1,1) __anonymous199__trpee_ {
                     XODIV2CLKDIS $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                 }
     SFR_CLKRSTCON_t_DM9 : _union(DM9,1,1) SFR_CLKRSTCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous199__trpee__DM9 @0;
                 }
__anonymous201__trpee__DM9 : _struct(DM9,1,1) __anonymous201__trpee_ {
                     CALRUN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     CALRESET $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     CAL_IDAC_FILT_EN $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     CAL_IDAC_CTRL $bitfield_t_DM9 @0 _bf 5 @3 $__schar_DM9;
                 }
     SFR_VCOCALCON_t_DM9 : _union(DM9,1,1) SFR_VCOCALCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous201__trpee__DM9 @0;
                 }
__anonymous203__trpee__DM9 : _struct(DM9,1,1) __anonymous203__trpee_ {
                     FIXDIV_DIV2_EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     PLL_LOCK_DETECT_EN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     PLL_LOCK_DETECT_MODE $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     PLL_LOCK_DETECT_BLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PLL_LOCK_DETECT_TIME $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     PLL_UNLOCK_DETECTED $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     PLL_LOCK_DETECTED $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_PLLCON_t_DM9 : _union(DM9,1,1) SFR_PLLCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous203__trpee__DM9 @0;
                 }
         SFR_TXDAT_t_DM9 : _typedef(DM9,2,2) SFR_TXDAT_t $SFR_word_DM9;
         SFR_TXSPC_t_DM9 : _typedef(DM9,2,2) SFR_TXSPC_t $SFR_word_DM9;
__anonymous205__trpee__DM9 : _struct(DM9,2,1) __anonymous205__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous206__trpee__DM9 : _struct(DM9,2,2) __anonymous206__trpee_ {
                     TXSTOP $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     TXRESET $bitfield_t_DM9 @0 _bf 1 @1 $__sint_DM9;
                     TXPRBSPOLY $bitfield_t_DM9 @0 _bf 2 @2 $__sint_DM9;
                     TXPRBSINIT $bitfield_t_DM9 @0 _bf 1 @4 $__sint_DM9;
                     TXBUSY $bitfield_t_DM9 @0 _bf 1 @5 $__sint_DM9;
                     TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__sint_DM9;
                     TXBU $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     DATSRC $bitfield_t_DM9 @0 _bf 2 @8 $__sint_DM9;
                     DATLSBF $bitfield_t_DM9 @0 _bf 1 @10 $__sint_DM9;
                     DATINV $bitfield_t_DM9 @0 _bf 1 @11 $__sint_DM9;
                     DATENC $bitfield_t_DM9 @0 _bf 3 @12 $__sint_DM9;
                     DATLAST $bitfield_t_DM9 @0 _bf 1 @15 $__sint_DM9;
                 }
       SFR_ENCCON0_t_DM9 : _union(DM9,2,2) SFR_ENCCON0_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous205__trpee__DM9 @0;
                     bits $__anonymous206__trpee__DM9 @0;
                 }
__anonymous208__trpee__DM9 : _struct(DM9,2,1) __anonymous208__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous209__trpee__DM9 : _struct(DM9,2,2) __anonymous209__trpee_ {
                     BITCNT $bitfield_t_DM9 @0 _bf 4 @0 $__sint_DM9;
                     RPTCNT $bitfield_t_DM9 @0 _bf 7 @8 $__sint_DM9;
                     RPTFE $bitfield_t_DM9 @0 _bf 1 @15 $__sint_DM9;
                 }
       SFR_ENCCON1_t_DM9 : _union(DM9,2,2) SFR_ENCCON1_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous208__trpee__DM9 @0;
                     bits $__anonymous209__trpee__DM9 @0;
                 }
      SFR_FREQCON0_t_DM9 : _typedef(DM9,2,2) SFR_FREQCON0_t $SFR_word_DM9;
      SFR_FREQCON1_t_DM9 : _typedef(DM9,2,2) SFR_FREQCON1_t $SFR_word_DM9;
__anonymous211__trpee__DM9 : _struct(DM9,2,1) __anonymous211__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous212__trpee__DM9 : _struct(DM9,2,2) __anonymous212__trpee_ {
                     MAINSC $bitfield_t_DM9 @0 _bf 11 @0 $__sint_DM9;
                     PRESC $bitfield_t_DM9 @0 _bf 3 @11 $__sint_DM9;
                 }
        SFR_BRGCON_t_DM9 : _union(DM9,2,2) SFR_BRGCON_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous211__trpee__DM9 @0;
                     bits $__anonymous212__trpee__DM9 @0;
                 }
__anonymous214__trpee__DM9 : _struct(DM9,1,1) __anonymous214__trpee_ {
                     FDEVMANT $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     FDEVEXP $bitfield_t_DM9 @0 _bf 3 @5 $__schar_DM9;
                 }
        SFR_FSKCON_t_DM9 : _union(DM9,1,1) SFR_FSKCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous214__trpee__DM9 @0;
                 }
__anonymous216__trpee__DM9 : _struct(DM9,1,1) __anonymous216__trpee_ {
                     FRMPMANT $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     FRMPEXP $bitfield_t_DM9 @0 _bf 3 @5 $__schar_DM9;
                 }
        SFR_FSKRMP_t_DM9 : _union(DM9,1,1) SFR_FSKRMP_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous216__trpee__DM9 @0;
                 }
__anonymous218__trpee__DM9 : _struct(DM9,2,1) __anonymous218__trpee_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous219__trpee__DM9 : _struct(DM9,2,2) __anonymous219__trpee_ {
                     AMH $bitfield_t_DM9 @0 _bf 5 @0 $__sint_DM9;
                     RF_MUTE_EN $bitfield_t_DM9 @0 _bf 1 @6 $__sint_DM9;
                     ASK $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     AML $bitfield_t_DM9 @0 _bf 5 @8 $__sint_DM9;
                 }
        SFR_ASKCON_t_DM9 : _union(DM9,2,2) SFR_ASKCON_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous218__trpee__DM9 @0;
                     bits $__anonymous219__trpee__DM9 @0;
                 }
__anonymous221__trpee__DM9 : _struct(DM9,1,1) __anonymous221__trpee_ {
                     ARMPMANT $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     ARMPEXP $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
        SFR_ASKRMP_t_DM9 : _union(DM9,1,1) SFR_ASKRMP_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous221__trpee__DM9 @0;
                 }
__anonymous223__trpee__DM9 : _struct(DM9,1,1) __anonymous223__trpee_ {
                     PA_LPF $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     PA_10DBM_EN $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PA_CLK_OFF_TIME $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     PA_0DBM_EN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     PA_READY $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_PACON_t_DM9 : _union(DM9,1,1) SFR_PACON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous223__trpee__DM9 @0;
                 }
         SFR_PAPWR_t_DM9 : _typedef(DM9,1,1) SFR_PAPWR_t $SFR_byte_DM9;
        SFR_PATRIM_t_DM9 : _typedef(DM9,1,1) SFR_PATRIM_t $SFR_byte_DM9;
__anonymous225__trpee__DM9 : _struct(DM9,1,1) __anonymous225__trpee_ {
                     PA_IMAX $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     PA_ILIM_EN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     PA_ILIM $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PALIMIT_t_DM9 : _union(DM9,1,1) SFR_PALIMIT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous225__trpee__DM9 @0;
                 }
              bool_t_DM9 : _enum(DM9,1,1) bool_t $__uchar_DM9 {
                     FALSE = 0;
                     TRUE = 1;
                 }
              __uchar_DM : _basic(DM,1,1) __uchar;
              uint8_t_DM : _typedef(DM,1,1) uint8_t $__uchar_DM;
          __A6__uchar_DM : _array(DM,6,1) [6] $uint8_t_DM;
             uint32__ULP : _basic(ULP,1,1) uint32_;
             ulp32_t_ULP : _typedef(ULP,1,1) ulp32_t $uint32__ULP;
            _rng_m1_p1__ : _basic() _rng_m1_p1;
            _rng_m2_p2__ : _basic() _rng_m2_p2;
                 __dtp__ : _typedef() __dtp $__Pvoid__;
                 __stp__ : _typedef() __stp $__Pvoid__;
                 __ztp__ : _typedef() __ztp $__uint__;
               __sint_DM : _basic(DM,2,2) __sint;
             __P__sint__ : _pointer() $__Pvoid__ $__sint_DM;
              __cchar_DM : _basic(DM,1,1) __cchar;
            __P__cchar__ : _pointer() $__Pvoid__ $__cchar_DM;
                  void__ : _basic() void;
               __dtp___1 : _typedef() __dtp $__Pvoid__;
               __stp___1 : _typedef() __stp $__Pvoid__;
               __ztp___1 : _typedef() __ztp $__uint__;
             __PPMvoid__ : _basic() __PPMvoid;
             __PDMvoid__ : _basic() __PDMvoid;
               __dtp___2 : _typedef() __dtp $__Pvoid__;
               __stp___2 : _typedef() __stp $__Pvoid__;
               __ztp___2 : _typedef() __ztp $__uint__;
               __dtp___3 : _typedef() __dtp $__Pvoid__;
               __stp___3 : _typedef() __stp $__Pvoid__;
               __ztp___3 : _typedef() __ztp $__uint__;
           __PDM12void__ : _basic() __PDM12void;
               __dtp___4 : _typedef() __dtp $__Pvoid__;
               __stp___4 : _typedef() __stp $__Pvoid__;
               __ztp___4 : _typedef() __ztp $__uint__;
               __dtp___5 : _typedef() __dtp $__Pvoid__;
               __stp___5 : _typedef() __stp $__Pvoid__;
               __ztp___5 : _typedef() __ztp $__uint__;
            __PDM9void__ : _basic() __PDM9void;
               __dtp___6 : _typedef() __dtp $__Pvoid__;
               __stp___6 : _typedef() __stp $__Pvoid__;
               __ztp___6 : _typedef() __ztp $__uint__;
               __dtp___7 : _typedef() __dtp $__Pvoid__;
               __stp___7 : _typedef() __stp $__Pvoid__;
               __ztp___7 : _typedef() __ztp $__uint__;
            __PULPvoid__ : _basic() __PULPvoid;
               __dtp___8 : _typedef() __dtp $__PDMvoid__;
               __stp___8 : _typedef() __stp $__Pvoid__;
               __ztp___8 : _typedef() __ztp $__uint__;
               __dtp___9 : _typedef() __dtp $__Pvoid__;
               __stp___9 : _typedef() __stp $__Pvoid__;
               __ztp___9 : _typedef() __ztp $__uint__;
              __dtp___10 : _typedef() __dtp $__PDMvoid__;
              __stp___10 : _typedef() __stp $__Pvoid__;
              __ztp___10 : _typedef() __ztp $__uint__;
              __dtp___11 : _typedef() __dtp $__Pvoid__;
              __stp___11 : _typedef() __stp $__Pvoid__;
              __ztp___11 : _typedef() __ztp $__uint__;
              __dtp___12 : _typedef() __dtp $__PDMvoid__;
              __stp___12 : _typedef() __stp $__Pvoid__;
              __ztp___12 : _typedef() __ztp $__uint__;
              __dtp___13 : _typedef() __dtp $__Pvoid__;
              __stp___13 : _typedef() __stp $__Pvoid__;
              __ztp___13 : _typedef() __ztp $__uint__;
              __dtp___14 : _typedef() __dtp $__PDMvoid__;
              __stp___14 : _typedef() __stp $__Pvoid__;
              __ztp___14 : _typedef() __ztp $__uint__;
              __dtp___15 : _typedef() __dtp $__Pvoid__;
              __stp___15 : _typedef() __stp $__Pvoid__;
              __ztp___15 : _typedef() __ztp $__uint__;
              __dtp___16 : _typedef() __dtp $__PDM12void__;
              __stp___16 : _typedef() __stp $__Pvoid__;
              __ztp___16 : _typedef() __ztp $__uint__;
              __dtp___17 : _typedef() __dtp $__Pvoid__;
              __stp___17 : _typedef() __stp $__Pvoid__;
              __ztp___17 : _typedef() __ztp $__uint__;
              __dtp___18 : _typedef() __dtp $__PDM12void__;
              __stp___18 : _typedef() __stp $__Pvoid__;
              __ztp___18 : _typedef() __ztp $__uint__;
              __dtp___19 : _typedef() __dtp $__Pvoid__;
              __stp___19 : _typedef() __stp $__Pvoid__;
              __ztp___19 : _typedef() __ztp $__uint__;
              __dtp___20 : _typedef() __dtp $__PDM12void__;
              __stp___20 : _typedef() __stp $__Pvoid__;
              __ztp___20 : _typedef() __ztp $__uint__;
              __dtp___21 : _typedef() __dtp $__Pvoid__;
              __stp___21 : _typedef() __stp $__Pvoid__;
              __ztp___21 : _typedef() __ztp $__uint__;
              __dtp___22 : _typedef() __dtp $__PDM12void__;
              __stp___22 : _typedef() __stp $__Pvoid__;
              __ztp___22 : _typedef() __ztp $__uint__;
              __dtp___23 : _typedef() __dtp $__Pvoid__;
              __stp___23 : _typedef() __stp $__Pvoid__;
              __ztp___23 : _typedef() __ztp $__uint__;
              __dtp___24 : _typedef() __dtp $__PDM9void__;
              __stp___24 : _typedef() __stp $__Pvoid__;
              __ztp___24 : _typedef() __ztp $__uint__;
              __dtp___25 : _typedef() __dtp $__Pvoid__;
              __stp___25 : _typedef() __stp $__Pvoid__;
              __ztp___25 : _typedef() __ztp $__uint__;
              __dtp___26 : _typedef() __dtp $__PDM9void__;
              __stp___26 : _typedef() __stp $__Pvoid__;
              __ztp___26 : _typedef() __ztp $__uint__;
              __dtp___27 : _typedef() __dtp $__Pvoid__;
              __stp___27 : _typedef() __stp $__Pvoid__;
              __ztp___27 : _typedef() __ztp $__uint__;
              __dtp___28 : _typedef() __dtp $__PDM9void__;
              __stp___28 : _typedef() __stp $__Pvoid__;
              __ztp___28 : _typedef() __ztp $__uint__;
              __dtp___29 : _typedef() __dtp $__Pvoid__;
              __stp___29 : _typedef() __stp $__Pvoid__;
              __ztp___29 : _typedef() __ztp $__uint__;
              __dtp___30 : _typedef() __dtp $__PDM9void__;
              __stp___30 : _typedef() __stp $__Pvoid__;
              __ztp___30 : _typedef() __ztp $__uint__;
              __dtp___31 : _typedef() __dtp $__Pvoid__;
              __stp___31 : _typedef() __stp $__Pvoid__;
              __ztp___31 : _typedef() __ztp $__uint__;
