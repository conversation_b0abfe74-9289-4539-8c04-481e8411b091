/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------

  $Id: phcaiKEyLLGenFunc_IIU.h 24707 2020-01-23 11:35:51Z dep10330 $
  $Revision: 24707 $

*/

/**
 * @file
 * Support for Immobilizer Interface Unit (IIU)
 */


#ifndef PHCAIKEYLLGENFUNC_IIU_H
#define PHCAIKEYLLGENFUNC_IIU_H

#include "types.h"
#include "phcaiKEyLLGenFunc_Platform.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup IIU  Support for Immobilizer Interface Unit (IIU)
 * Hardware abstractions for the Immobilizer Interface Unit (IIU) in TOKEN platform.
 * May be used to implement LF Passive (immobilizer mode) data reception and transmission.
 * Applicable in USER state of HT-PRO2, HT-3 and HT-AES,
 * together with Modular Transponder Emulation, or in fully a user defined
 * transponder emulation.
 * @{
 */



/**
 * Defines whether CRC shall be appended to the sent data or not.
 */
typedef enum
{
  /** Append CRC.        */
  CRC,
  /** Do not append CRC. */
  NO_CRC
} phcaiKEyLLGenFunc_Iiu_Crc_t;


/**
 * The equalizer types
 */
typedef enum
{
  /** The standard equalizer (11111 bin)   */
  STANDARD_EQ = 0xF8,
  /** The modified equalizer (1111110 bin) */
  MODIFIED_EQ = 0xFC
} phcaiKEyLLGenFunc_Iiu_EQ_t;


/**
 * Defines one of the supported coding types.
 */
typedef enum
{
  /** Encoding disabled. */
  IIU_ENC_OFF,
  /** Plain encoding. */
  IIU_ENC_PLAIN,
  /** Manchester encoding. */
  IIU_ENC_MANCHESTER,
  /** CDP encoding. */
  IIU_ENC_CDP
} phcaiKEyLLGenFunc_Iiu_Encoding_t;

/**
 * Defines whether standard or strong modulation are enabled.
 */
typedef enum
{
  /** Strong modulation. */
  STRONG_MOD   = 0x20,
  /** Standard modulation. */
  STANDARD_MOD = 0x00
} phcaiKEyLLGenFunc_Modulation_t;

/**
 * States of the IIU.
 */
typedef enum
{
  /** 000 IDLE                            */
  IIUSTATE_IDLE      = 0x00,
  /** 001 RXWAIT                          */
  IIUSTATE_RXWAIT    = 0x01,
  /** 010 RXDATA                          */
  IIUSTATE_RXDATA    = 0x02,
  /** 011 TXWAIT                          */
  IIUSTATE_TXWAIT    = 0x03,
  /** 100 TXDATA                          */
  IIUSTATE_TXDATA    = 0x04,
  /** 101 SHIFT                           */
  IIUSTATE_SHIFT     = 0x05,
  /** 110 SHIFTCONT                       */
  IIUSTATE_SHIFTCONT = 0x06,
  /** 111 reserved for future use         */
  IIUSTATE_RFU7      = 0x07
} IIUSTATE_t;


/** The standard equalizer EQ  (11111 bin)   */
#define  STANDARD_EQ_LEN  5U
/** The modified equalizer EQM (1111110 bin) */
#define  MODIFIED_EQ_LEN  7U


/**
 * This function sets all needed SFRs to their initial value. This function has
 * to be called at transponder startup and also when leaving the Authorized state.
 */
void phcaiKEyLLGenFunc_Iiu_Reset( void );


/**
 * This function initializes the global variables used within the IIU module.
 * Does not include phcaiKEyLLGenFunc_Iiu_Reset().
 */
void phcaiKEyLLGenFunc_Iiu_Init( void );


/**
 * Select one of the two equalizer modes (STANDARD_EQ, MODIFIED_EQ ).
 * @param[in] e_EqType The equalizer mode.
 */
void phcaiKEyLLGenFunc_Iiu_SetEqualizer( phcaiKEyLLGenFunc_Iiu_EQ_t e_EqType );

/**
 * This function allows to select the encoding type.
 *
 * @param[in] e_encoding encoding: IIU_ENC_PLAIN (NRZ), IIU_ENC_MANCHESTER,
 * IIU_ENC_CDP, IIU_ENC_OFF
 */
void phcaiKEyLLGenFunc_Iiu_SetModMode( const phcaiKEyLLGenFunc_Iiu_Encoding_t e_encoding );


/**
 * Set the modulation depth.
 * One of STANDARD_MOD or STRONG_MOD.
 *
 * @param[in] e_mod the modulation setting.
 * @see phcaiKEyLLGenFunc_Modulation_t
 */
void phcaiKEyLLGenFunc_Iiu_SetModulation( phcaiKEyLLGenFunc_Modulation_t e_mod );


/**
 * This function sets up and starts timer0.
 * The timer uses the LF-field Clock (125kHz) with no Prescaler.
 * It is set up in single shot mode.
 *
 * @param[in] u16_cycles The number of clock cycles the timer shall run.
 */
void phcaiKEyLLGenFunc_LFClk_Timer0_Start( const uint16_t u16_cycles );


/**
 * Receive data via the IIU until a stop condition is detected.
 * Resets CRCDAT register and calculates CRC8 which is required
 * for data transmit in HITAG-PRO.
 * CPU clock setting (CLKCON0) is not changed.
 *
 * @param[out] pu8_data  Points to received data in RAM. Buffer must be large enough
 *             to accomodate the expected data frame.
 * @param[out] pu8_num_rcvd_bits  Number of received bits.
 * @param[in]  u8_buffer_size  Size of data buffer in BYTES.
 * @return the error indicator, ERROR in case of decode error, or
 * received data is more than buffer size.
 */
error_t phcaiKEyLLGenFunc_Iiu_ReceiveData(
  uint8_t* const   pu8_data,
  const    uint8_t u8_buffer_size,
  uint8_t* const   pu8_num_rcvd_bits );


/**
 * Transmit a given number of bytes plus optional CRC via the IIU.
 * The EQ/EQM isautomatically transmitted at the beginning.
 * CPU clock setting (CLKCON0) is not changed.
 *
 * @param[in] pu8_data Data to be transmitted
 * @param[in] u8_num_bytes number of bytes to be tranmitted
 * @param[in] e_WithCRC indicate if a CRC byte shall be appended (TRUE).
 */
void phcaiKEyLLGenFunc_Iiu_SendData_CRC(
  const uint8_t* const pu8_data,
  const uint8_t  u8_num_bytes,
  const bool_t   e_WithCRC );


/*@}*/
/*@}*/

#endif
/* eof */
