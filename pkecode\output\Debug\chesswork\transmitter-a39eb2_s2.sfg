
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-a39eb2 mrk3

[
 -309 : __adr_Temp_Buf typ=int16_ bnd=m adro=22
  -55 : __adr_Temp_Buf typ=int16_ bnd=m adro=22
    0 : void_Rf_TXFream___P__uchar___uchar typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : len typ=int8_ val=4t0 bnd=a sz=1 algn=1 stl=DM tref=uint8_t_DM
   21 : Txbuf typ=int8_ val=6t0 bnd=a sz=2 algn=2 stl=DM tref=__P__uchar_DM
   22 : Temp_Buf typ=int8_ val=10t0 bnd=a sz=4 algn=1 stl=DM tref=__A4__uchar_DM
   23 : i typ=int8_ val=8t0 bnd=a sz=1 algn=1 stl=DM tref=uint8_t_DM
   24 : mancode1 typ=int8_ bnd=i sz=1 algn=1 stl=DM tref=__A1__uchar_DM
   25 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   26 : j typ=int8_ val=9t0 bnd=a sz=1 algn=1 stl=DM tref=uint8_t_DM
   27 : encode typ=int8_ bnd=i sz=8 algn=1 stl=DM tref=__A8__uchar_DM
   28 : mancode0 typ=int8_ bnd=i sz=1 algn=1 stl=DM tref=__A1__uchar_DM
   29 : __extPM_void typ=uint16_ bnd=b stl=PM
   30 : __extDM_void typ=int8_ bnd=b stl=DM
   31 : __extULP_void typ=uint32_ bnd=b stl=ULP
   34 : __ptr_mancode1 typ=int16_ val=0a bnd=m adro=24
   36 : __ptr_encode typ=int16_ val=0a bnd=m adro=27
   38 : __ptr_mancode0 typ=int16_ val=0a bnd=m adro=28
   39 : __arg_Txbuf typ=int16_ bnd=p tref=__P__uchar__
   40 : __arg_len typ=int8_ bnd=p tref=uint8_t__
   45 : __ct_0t0 typ=int16_ val=4t0 bnd=m
   49 : __ct_2t0 typ=int16_ val=6t0 bnd=m
   53 : __ct_6t0 typ=int16_ val=10t0 bnd=m
   55 : __adr_Temp_Buf typ=int16_ bnd=m adro=22
   57 : __ct_4t0 typ=int16_ val=8t0 bnd=m
   61 : __ct_5t0 typ=int16_ val=9t0 bnd=m
   63 : __adr_j typ=int16_ bnd=m adro=26
   89 : __ct_25 typ=uint16_1_32768_ val=25f bnd=m
   91 : void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort typ=int16_ val=0r bnd=m
  100 : __ct_3 typ=uint8_ val=3f bnd=m
  102 : __ct_1 typ=uint16_1_32768_ val=1f bnd=m
  104 : void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar typ=int16_ val=0r bnd=m
  142 : __tmp typ=int16_ bnd=m
  143 : __tmp typ=int16_ bnd=m
  144 : __fch_Temp_Buf typ=int8_ bnd=m
  147 : __tmp typ=int16_ bnd=m
  155 : __ct_3 typ=uint8_ val=3f bnd=m
  157 : __ct_1 typ=uint16_1_32768_ val=1f bnd=m
  161 : __ct_3 typ=uint8_ val=3f bnd=m
  163 : __ct_1 typ=uint16_1_32768_ val=1f bnd=m
  205 : __tmp typ=int16_ bnd=m
  206 : __tmp typ=int16_ bnd=m
  207 : __fchtmp typ=int8_ bnd=m
  210 : __tmp typ=int16_ bnd=m
  218 : __ct_3 typ=uint8_ val=3f bnd=m
  220 : __ct_1 typ=uint16_1_32768_ val=1f bnd=m
  224 : __ct_3 typ=uint8_ val=3f bnd=m
  226 : __ct_1 typ=uint16_1_32768_ val=1f bnd=m
  249 : __fch_len typ=int8_ bnd=m
  252 : __ct_3 typ=uint8_ val=3f bnd=m
  254 : __ct_1 typ=uint16_1_32768_ val=1f bnd=m
  258 : __ct_10s0 typ=int16_ val=14s0 bnd=m
  294 : __ct_10s0 typ=int16_ val=14s0 bnd=m
  302 : __ct_7t0 typ=int16_ val=11t0 bnd=m
  304 : __ct_8t0 typ=int16_ val=12t0 bnd=m
  306 : __ct_9t0 typ=int16_ val=13t0 bnd=m
  308 : __ct_6t0 typ=int16_ val=10t0 bnd=m
  310 : __ct_7t0 typ=int16_ val=11t0 bnd=m
  317 : __apl_c typ=uint1_ bnd=m tref=uint1___
  319 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  322 : __apl_r typ=int16_ bnd=m tref=__sint__
  329 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  345 : __either typ=bool bnd=m
  346 : __trgt typ=rel8_ val=-9j bnd=m
  347 : __trgt typ=rel8_ val=8j bnd=m
  348 : __trgt typ=rel8_ val=7j bnd=m
  349 : __trgt typ=rel8_ val=-27j bnd=m
  350 : __trgt typ=rel8_ val=-38j bnd=m
  351 : __trgt typ=rel8_ val=8j bnd=m
  352 : __trgt typ=rel8_ val=7j bnd=m
  353 : __trgt typ=rel8_ val=-27j bnd=m
  354 : __trgt typ=rel8_ val=-38j bnd=m
  355 : __trgt typ=rel8_ val=38j bnd=m
  356 : __seff typ=any bnd=m
  357 : __seff typ=any bnd=m
  358 : __seff typ=any bnd=m
  359 : __seff typ=any bnd=m
  360 : __seff typ=any bnd=m
  361 : __seff typ=any bnd=m
  362 : __seff typ=any bnd=m
  363 : __seff typ=any bnd=m
  364 : __seff typ=any bnd=m
  365 : __seff typ=any bnd=m
  366 : __seff typ=any bnd=m
  367 : __seff typ=any bnd=m
  368 : __seff typ=any bnd=m
  369 : __seff typ=any bnd=m
  370 : __seff typ=any bnd=m
  371 : __seff typ=any bnd=m
  372 : __seff typ=any bnd=m
  373 : __seff typ=any bnd=m
  374 : __seff typ=any bnd=m
  375 : __seff typ=any bnd=m
  376 : __seff typ=any bnd=m
  377 : __seff typ=any bnd=m
  378 : __seff typ=any bnd=m
  379 : __seff typ=any bnd=m
  380 : __seff typ=any bnd=m
  384 : __seff typ=any bnd=m
  385 : __seff typ=any bnd=m
  386 : __seff typ=any bnd=m
  387 : __seff typ=any bnd=m
  394 : __seff typ=any bnd=m
  395 : __seff typ=any bnd=m
  396 : __seff typ=any bnd=m
  398 : __side_effect typ=any bnd=m
  399 : __stack_offs_ typ=any val=2o0 bnd=m
]
Fvoid_Rf_TXFream___P__uchar___uchar {
    #8 off=0 nxt=9
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (len.19 var=20) source ()  <30>;
    (Txbuf.20 var=21) source ()  <31>;
    (Temp_Buf.21 var=22) source ()  <32>;
    (i.22 var=23) source ()  <33>;
    (mancode1.23 var=24) source ()  <34>;
    (__extDM_int8_.24 var=25) source ()  <35>;
    (j.25 var=26) source ()  <36>;
    (encode.26 var=27) source ()  <37>;
    (mancode0.27 var=28) source ()  <38>;
    (__extPM_void.28 var=29) source ()  <39>;
    (__extDM_void.29 var=30) source ()  <40>;
    (__extULP_void.30 var=31) source ()  <41>;
    (__arg_Txbuf.38 var=39 stl=R46 off=0) inp ()  <49>;
    (__arg_len.41 var=40 stl=RbL off=0) inp ()  <52>;
    (__ptr_mancode1.1861 var=34) const_inp ()  <2059>;
    (__ptr_mancode0.1863 var=38) const_inp ()  <2061>;
    (__ct_0t0.1864 var=45) const_inp ()  <2062>;
    (__ct_2t0.1865 var=49) const_inp ()  <2063>;
    (__ct_6t0.1866 var=53) const_inp ()  <2064>;
    (__ct_4t0.1867 var=57) const_inp ()  <2065>;
    (__ct_5t0.1868 var=61) const_inp ()  <2066>;
    (__ct_10s0.1872 var=294) const_inp ()  <2070>;
    (__ct_6t0.1876 var=308) const_inp ()  <2074>;
    <360> {
      (__sp.49 var=19 __seff.2014 var=394 stl=c_flag_w __seff.2015 var=395 stl=nz_flag_w __seff.2016 var=396 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_10s0.1872 __sp.18 __sp.18)  <2225>;
      (__seff.2509 var=395 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2015)  <2886>;
      (__seff.2510 var=394 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2014)  <2887>;
      (__seff.2518 var=396 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2016)  <2893>;
    } stp=0;
    <361> {
      (len.81 var=20) _pl_rd_res_reg_const_store_2_B2 (__arg_len.2517 __ct_0t0.1864 len.19 __sp.49)  <2226>;
      (__arg_len.2517 var=40 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__arg_len.41)  <2892>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <92>;
    } #9 off=3 nxt=10
    #10 off=3 nxt=11
    <359> {
      (Txbuf.83 var=21) _pl_rd_res_reg_const_store_1_B2 (__arg_Txbuf.2516 __ct_2t0.1865 Txbuf.20 __sp.49)  <2224>;
      (__arg_Txbuf.2516 var=39 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__arg_Txbuf.38)  <2891>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <94>;
    } #11 off=4 nxt=12
    #12 off=4 nxt=13
    <357> {
      (Temp_Buf.90 var=22) store_const__pl_rd_res_reg_const_1_B2 (__ct_6t0.1866 Temp_Buf.21 __sp.49)  <2222>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <101>;
    } #13 off=6 nxt=14
    #14 off=6 nxt=15
    (__ct_7t0.1873 var=302) const_inp ()  <2071>;
    <356> {
      (Temp_Buf.97 var=22) store_const__pl_rd_res_reg_const_4_B2 (__ct_7t0.1873 Temp_Buf.90 __sp.49)  <2221>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <108>;
    } #15 off=8 nxt=16
    #16 off=8 nxt=17
    (__ct_8t0.1874 var=304) const_inp ()  <2072>;
    <355> {
      (Temp_Buf.104 var=22) store_const__pl_rd_res_reg_const_4_B2 (__ct_8t0.1874 Temp_Buf.97 __sp.49)  <2220>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <115>;
    } #17 off=10 nxt=18
    #18 off=10 nxt=20
    (__ct_9t0.1875 var=306) const_inp ()  <2073>;
    <354> {
      (Temp_Buf.111 var=22) store_const__pl_rd_res_reg_const_4_B2 (__ct_9t0.1875 Temp_Buf.104 __sp.49)  <2219>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <123>;
    } #20 off=12 nxt=21
    #21 off=12 nxt=22
    (void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort.1869 var=91) const_inp ()  <2067>;
    <353> {
      () call_const_1_B1 (void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort.1869)  <2218>;
    } stp=4;
    <699> {
      (__adr_Temp_Buf.2525 var=-309 stl=a_w2 __side_effect.2526 var=398 stl=c_flag_w __side_effect.2528 var=398 stl=nz_flag_w __side_effect.2530 var=398 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_6t0.1876 __sp.49)  <2701>;
      (__adr_Temp_Buf.2524 var=-309 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_Temp_Buf.2525)  <2899>;
      (__side_effect.2527 var=398 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.2526)  <2900>;
      (__side_effect.2529 var=398 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2528)  <2901>;
      (__side_effect.2531 var=398 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2530)  <2902>;
    } stp=0;
    <703> {
      (__ct_25.2533 var=89 stl=__CTa_w0_uint16__cstP16_E1) const_4_B1 ()  <2709>;
      (__ct_25.2532 var=89 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B0 (__ct_25.2533)  <2903>;
    } stp=2;
    call {
        (Temp_Buf.118 var=22 __extDM.119 var=17 __extDM_int8_.120 var=25 __extDM_void.121 var=30 __extPM.122 var=16 __extPM_void.123 var=29 __extULP.124 var=18 __extULP_void.125 var=31 __vola.126 var=13) Fvoid_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort (__adr_Temp_Buf.2524 __ct_25.2532 Temp_Buf.111 __extDM.16 __extDM_int8_.24 __extDM_void.29 __extPM.15 __extPM_void.28 __extULP.17 __extULP_void.30 __vola.12)  <131>;
    } #22 off=18 nxt=26
    #26 off=18 nxt=33
    (void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar.1870 var=104) const_inp ()  <2068>;
    (__trgt.1878 var=346) const_inp ()  <2076>;
    <347> {
      (i.130 var=23) store_const__pl_rd_res_reg_const_1_B3 (__ct_4t0.1867 i.22 __sp.49)  <2212>;
    } stp=0;
    <706> {
      (__ptr_mancode1.2539 var=34 stl=__CTa_w0_uint16__cstP16_E1) const_3_B1 (__ptr_mancode1.1861)  <2715>;
      (__ptr_mancode1.2538 var=34 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ptr_mancode1.2539)  <2906>;
    } stp=1;
    do {
        {
            (__vola.171 var=13) entry (__vola.270 __vola.126)  <178>;
            (__extPM.174 var=16) entry (__extPM.276 __extPM.122)  <181>;
            (__extDM.175 var=17) entry (__extDM.278 __extDM.119)  <182>;
            (__extULP.176 var=18) entry (__extULP.280 __extULP.124)  <183>;
            (Temp_Buf.180 var=22) entry (Temp_Buf.288 Temp_Buf.118)  <187>;
            (i.181 var=23) entry (i.290 i.130)  <188>;
            (__extDM_int8_.183 var=25) entry (__extDM_int8_.294 __extDM_int8_.120)  <190>;
            (__extPM_void.187 var=29) entry (__extPM_void.302 __extPM_void.123)  <194>;
            (__extDM_void.188 var=30) entry (__extDM_void.304 __extDM_void.121)  <195>;
            (__extULP_void.189 var=31) entry (__extULP_void.306 __extULP_void.125)  <196>;
            (__ptr_mancode1.2326 var=34 stl=R46 off=0) entry (__ptr_mancode1.2327 __ptr_mancode1.2538)  <2485>;
        } #31
        {
            #33 off=21 nxt=34
            <346> {
              () call_const_1_B1 (void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar.1870)  <2211>;
            } stp=2;
            <603> {
              (__ct_3.2323 var=100 stl=a_b0) const_1_B2 ()  <2482>;
              (__ct_3.2322 var=100 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_3.2323)  <2766>;
            } stp=0;
            <604> {
              (__ct_1.2325 var=102 stl=a_w0) const_2_B2 ()  <2484>;
              (__ct_1.2324 var=102 stl=RwL off=1) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_1.2325)  <2767>;
            } stp=1;
            call {
                (Temp_Buf.214 var=22 __extDM.215 var=17 __extDM_int8_.216 var=25 __extDM_void.217 var=30 __extPM.218 var=16 __extPM_void.219 var=29 __extULP.220 var=18 __extULP_void.221 var=31 __vola.222 var=13) Fvoid_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar (__ct_3.2322 __ct_1.2324 __ptr_mancode1.2326 Temp_Buf.180 __extDM.175 __extDM_int8_.183 __extDM_void.188 __extPM.174 __extPM_void.187 __extULP.176 __extULP_void.189 __vola.171)  <222>;
            } #34 off=25 nxt=38
            #38 off=25 nxt=40
            <345> {
              (i.230 var=23 __seff.1992 var=385 stl=c_flag_w __seff.1993 var=386 stl=nz_flag_w __seff.1994 var=387 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_4t0.1867 i.181 i.181 __sp.49)  <2210>;
              (__seff.2315 var=386 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1993)  <2762>;
              (__seff.2316 var=385 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1992)  <2763>;
              (__seff.2320 var=387 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1994)  <2764>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <233>;
            } #40 off=26 nxt=275
            #275 off=26 nxt=46 tgt=33
            <343> {
              (__apl_c.1760 var=317 stl=c_flag_w __apl_nz.1762 var=319 stl=nz_flag_w __seff.1990 var=384 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_2_B1 (__ct_4t0.1867 i.230 __sp.49)  <2208>;
              (__apl_nz.2312 var=319 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1762)  <2759>;
              (__apl_c.2314 var=317 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.1760)  <2761>;
              (__seff.2321 var=384 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1990)  <2765>;
            } stp=0;
            <344> {
              () cc_b__jump_const_1_B1 (__apl_c.2313 __apl_nz.2311 __trgt.1878)  <2209>;
              (__apl_nz.2311 var=319 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2312)  <2758>;
              (__apl_c.2313 var=317 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.2314)  <2760>;
            } stp=4;
            <607> {
              (__ptr_mancode1.2330 var=34 stl=__CTa_w0_uint16__cstP16_E1) const_3_B1 (__ptr_mancode1.1861)  <2488>;
              (__ptr_mancode1.2329 var=34 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ptr_mancode1.2330)  <2768>;
            } stp=2;
        } #32
        {
            () while_expr (__either.1845)  <273>;
            (__vola.270 var=13 __vola.271 var=13) exit (__vola.222)  <274>;
            (__extPM.276 var=16 __extPM.277 var=16) exit (__extPM.218)  <277>;
            (__extDM.278 var=17 __extDM.279 var=17) exit (__extDM.215)  <278>;
            (__extULP.280 var=18 __extULP.281 var=18) exit (__extULP.220)  <279>;
            (Temp_Buf.288 var=22 Temp_Buf.289 var=22) exit (Temp_Buf.214)  <283>;
            (i.290 var=23 i.291 var=23) exit (i.230)  <284>;
            (__extDM_int8_.294 var=25 __extDM_int8_.295 var=25) exit (__extDM_int8_.216)  <286>;
            (__extPM_void.302 var=29 __extPM_void.303 var=29) exit (__extPM_void.219)  <290>;
            (__extDM_void.304 var=30 __extDM_void.305 var=30) exit (__extDM_void.217)  <291>;
            (__extULP_void.306 var=31 __extULP_void.307 var=31) exit (__extULP_void.221)  <292>;
            (__either.1845 var=345) undefined ()  <2034>;
            (__ptr_mancode1.2327 var=34 stl=R46 off=0 __ptr_mancode1.2328 var=34 stl=R46 off=0) exit (__ptr_mancode1.2329)  <2486>;
        } #42
    } #30 rng=[1,2147483647]
    #46 off=31 nxt=47
    <342> {
      (Temp_Buf.412 var=22) store_const__pl_rd_res_reg_const_3_B2 (__ct_6t0.1876 Temp_Buf.289 __sp.49)  <2207>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <435>;
    } #47 off=33 nxt=48
    #48 off=33 nxt=49
    (__ct_7t0.1877 var=310) const_inp ()  <2075>;
    <341> {
      (Temp_Buf.419 var=22) store_const__pl_rd_res_reg_const_2_B2 (__ct_7t0.1877 Temp_Buf.412 __sp.49)  <2206>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <442>;
    } #49 off=35 nxt=50
    #50 off=35 nxt=51
    <340> {
      (i.423 var=23) store_const__pl_rd_res_reg_const_1_B3 (__ct_4t0.1867 i.291 __sp.49)  <2205>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <446>;
    } #51 off=36 nxt=52
    #52 off=36 nxt=59
    (__ptr_encode.1862 var=36) const_inp ()  <2060>;
    (__trgt.1879 var=347) const_inp ()  <2077>;
    (__trgt.1880 var=348) const_inp ()  <2078>;
    (__trgt.1881 var=349) const_inp ()  <2079>;
    (__trgt.1882 var=350) const_inp ()  <2080>;
    <707> {
      (__adr_Temp_Buf.2541 var=-55 stl=a_w2 __side_effect.2542 var=398 stl=c_flag_w __side_effect.2544 var=398 stl=nz_flag_w __side_effect.2546 var=398 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_6t0.1866 __sp.49)  <2717>;
      (__adr_Temp_Buf.2540 var=-55 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_Temp_Buf.2541)  <2907>;
      (__side_effect.2543 var=398 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.2542)  <2908>;
      (__side_effect.2545 var=398 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2544)  <2909>;
      (__side_effect.2547 var=398 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2546)  <2910>;
    } stp=0;
    do {
        {
            (__vola.464 var=13) entry (__vola.899 __vola.271)  <488>;
            (__extPM.467 var=16) entry (__extPM.905 __extPM.277)  <491>;
            (__extDM.468 var=17) entry (__extDM.907 __extDM.279)  <492>;
            (__extULP.469 var=18) entry (__extULP.909 __extULP.281)  <493>;
            (Temp_Buf.473 var=22) entry (Temp_Buf.917 Temp_Buf.419)  <497>;
            (i.474 var=23) entry (i.919 i.423)  <498>;
            (__extDM_int8_.476 var=25) entry (__extDM_int8_.923 __extDM_int8_.295)  <500>;
            (j.477 var=26) entry (j.925 j.25)  <501>;
            (__extPM_void.480 var=29) entry (__extPM_void.931 __extPM_void.303)  <504>;
            (__extDM_void.481 var=30) entry (__extDM_void.933 __extDM_void.305)  <505>;
            (__extULP_void.482 var=31) entry (__extULP_void.935 __extULP_void.307)  <506>;
            (__adr_Temp_Buf.2406 var=55 stl=R46 off=0) entry (__adr_Temp_Buf.2407 __adr_Temp_Buf.2540)  <2572>;
        } #57
        {
            #59 off=38 nxt=60
            <333> {
              (j.501 var=26) store_const__pl_rd_res_reg_const_1_B2 (__ct_5t0.1868 j.477 __sp.49)  <2198>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <525>;
            } #60 off=40 nxt=279
            #279 off=40 nxt=294
            <330> {
              (__apl_r.1766 var=322 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B2 (__ct_4t0.1867 i.474 __sp.49)  <2195>;
              (__apl_r.2398 var=322 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.1766)  <2812>;
            } stp=0;
            <331> {
              (__tmp.1768 var=142 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.2397)  <2196>;
              (__apl_r.2397 var=322 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.2398)  <2811>;
              (__tmp.2400 var=142 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.1768)  <2814>;
            } stp=1;
            <332> {
              (__tmp.578 var=143 stl=a_w2 __seff.1972 var=378 stl=c_flag_w __seff.1973 var=379 stl=nz_flag_w __seff.1974 var=380 stl=o_flag_w) _pl_1_B1 (__adr_Temp_Buf.2405 __tmp.2399)  <2197>;
              (__seff.2388 var=379 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1973)  <2802>;
              (__seff.2389 var=378 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1972)  <2803>;
              (__tmp.2396 var=143 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__tmp.578)  <2810>;
              (__tmp.2399 var=142 stl=a_w1) a_w0_a_w1_1_dr_move_Rw_1_int16__B2 (__tmp.2400)  <2813>;
              (__seff.2401 var=380 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1974)  <2815>;
              (__adr_Temp_Buf.2405 var=55 stl=a_w0) a_w0_a_w1_1_dr_move_Rw_1_int16__B1 (__adr_Temp_Buf.2406)  <2822>;
            } stp=2;
            <639> {
              (__tmp.2575 var=143 stl=__spill_DMw off=0) stack_store_indirect_bndl_B2 (__tmp.2402 __sp.49)  <2816>;
              (__tmp.2402 var=143 stl=DMw_w) to___spill_DMw_DMw_w_1_dr_move_Rw_1_int16__B1 (__tmp.2396)  <2819>;
            } stp=3;
            do {
                {
                    (__vola.542 var=13) entry (__vola.716 __vola.464)  <567>;
                    (__extPM.545 var=16) entry (__extPM.722 __extPM.467)  <570>;
                    (__extDM.546 var=17) entry (__extDM.724 __extDM.468)  <571>;
                    (__extULP.547 var=18) entry (__extULP.726 __extULP.469)  <572>;
                    (Temp_Buf.551 var=22) entry (Temp_Buf.734 Temp_Buf.473)  <576>;
                    (__extDM_int8_.554 var=25) entry (__extDM_int8_.740 __extDM_int8_.476)  <579>;
                    (j.555 var=26) entry (j.742 j.501)  <580>;
                    (__extPM_void.558 var=29) entry (__extPM_void.748 __extPM_void.480)  <583>;
                    (__extDM_void.559 var=30) entry (__extDM_void.750 __extDM_void.481)  <584>;
                    (__extULP_void.560 var=31) entry (__extULP_void.752 __extULP_void.482)  <585>;
                    (__tmp.2343 var=143 stl=R46 off=0) entry (__tmp.2344 __tmp.2396)  <2505>;
                } #66
                {
                    #294 off=44 nxt=76 tgt=72
                    <325> {
                      (__apl_r.1771 var=322 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B1 (__ct_5t0.1868 j.555 __sp.49)  <2190>;
                      (__apl_r.2341 var=322 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.1771)  <2778>;
                    } stp=0;
                    <326> {
                      (__tmp.1773 var=147 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.2340)  <2191>;
                      (__apl_r.2340 var=322 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.2341)  <2777>;
                      (__tmp.2349 var=147 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.1773)  <2786>;
                    } stp=2;
                    <327> {
                      (__fch_Temp_Buf.579 var=144 stl=DM_r) load_2_B1 (__tmp.2342 Temp_Buf.551)  <2192>;
                      (__tmp.2342 var=143 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__tmp.2343)  <2779>;
                      (__fch_Temp_Buf.2351 var=144 stl=RbH off=1) Rb_1_dr_move_DM_r_1_int8__B1 (__fch_Temp_Buf.579)  <2788>;
                    } stp=3;
                    <328> {
                      (__apl_nz.1778 var=329 stl=nz_flag_w) _pl_const_load__ad_cmp_const_1_B2 (__tmp.2347 __fch_Temp_Buf.2350 __ptr_encode.1862 encode.26)  <2193>;
                      (__apl_nz.2333 var=329 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1778)  <2770>;
                      (__tmp.2347 var=147 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__tmp.2348)  <2784>;
                      (__fch_Temp_Buf.2350 var=144 stl=a_b1) a_b0_a_b1_1_dr_move_Rb_1_int8__B3 (__fch_Temp_Buf.2351)  <2787>;
                    } stp=5;
                    <329> {
                      () cc_ne__jump_const_1_B1 (__apl_nz.2332 __trgt.1879)  <2194>;
                      (__apl_nz.2332 var=329 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2333)  <2769>;
                    } stp=7;
                    <616> {
                      (__tmp.2348 var=147 stl=R46 off=0) Rw_1_dr_move_Rw_1_int16__B0 (__tmp.2349)  <2785>;
                    } stp=4;
                    if {
                        {
                            () if_expr (__either.1847)  <649>;
                            (__either.1847 var=345) undefined ()  <2037>;
                        } #70
                        {
                            #72 off=59 nxt=73
                            <324> {
                              () call_const_1_B1 (void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar.1870)  <2189>;
                            } stp=4;
                            <629> {
                              (__ct_3.2383 var=155 stl=a_b0) const_1_B2 ()  <2542>;
                              (__ct_3.2382 var=155 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_3.2383)  <2799>;
                            } stp=0;
                            <630> {
                              (__ct_1.2385 var=157 stl=a_w0) const_2_B2 ()  <2544>;
                              (__ct_1.2384 var=157 stl=RwL off=1) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_1.2385)  <2800>;
                            } stp=1;
                            <631> {
                              (__ptr_mancode1.2387 var=34 stl=__CTa_w0_uint16__cstP16_E1) const_3_B1 (__ptr_mancode1.1861)  <2546>;
                              (__ptr_mancode1.2386 var=34 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ptr_mancode1.2387)  <2801>;
                            } stp=2;
                            call {
                                (Temp_Buf.633 var=22 __extDM.634 var=17 __extDM_int8_.635 var=25 __extDM_void.636 var=30 __extPM.637 var=16 __extPM_void.638 var=29 __extULP.639 var=18 __extULP_void.640 var=31 __vola.641 var=13) Fvoid_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar (__ct_3.2382 __ct_1.2384 __ptr_mancode1.2386 Temp_Buf.551 __extDM.546 __extDM_int8_.554 __extDM_void.559 __extPM.545 __extPM_void.558 __extULP.547 __extULP_void.560 __vola.542)  <660>;
                            } #73 off=65 nxt=361
                            #361 off=65 nxt=82
                        } #71
                        {
                            #76 off=52 nxt=77
                            <323> {
                              () call_const_1_B1 (void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar.1870)  <2188>;
                            } stp=4;
                            <619> {
                              (__ct_3.2361 var=161 stl=a_b0) const_1_B2 ()  <2521>;
                              (__ct_3.2360 var=161 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_3.2361)  <2791>;
                            } stp=0;
                            <620> {
                              (__ct_1.2363 var=163 stl=a_w0) const_2_B2 ()  <2523>;
                              (__ct_1.2362 var=163 stl=RwL off=1) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_1.2363)  <2792>;
                            } stp=1;
                            <621> {
                              (__ptr_mancode0.2365 var=38 stl=__CTa_w0_uint16__cstP16_E1) const_3_B1 (__ptr_mancode0.1863)  <2525>;
                              (__ptr_mancode0.2364 var=38 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ptr_mancode0.2365)  <2793>;
                            } stp=2;
                            call {
                                (Temp_Buf.651 var=22 __extDM.652 var=17 __extDM_int8_.653 var=25 __extDM_void.654 var=30 __extPM.655 var=16 __extPM_void.656 var=29 __extULP.657 var=18 __extULP_void.658 var=31 __vola.659 var=13) Fvoid_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar (__ct_3.2360 __ct_1.2362 __ptr_mancode0.2364 Temp_Buf.551 __extDM.546 __extDM_int8_.554 __extDM_void.559 __extPM.545 __extPM_void.558 __extULP.547 __extULP_void.560 __vola.542)  <672>;
                            } #77 off=58 nxt=359
                            #359 off=58 tgt=82
                            <322> {
                              () jump_const_1_B1 (__trgt.1880)  <2187>;
                            } stp=0;
                        } #75
                        {
                            (__vola.660 var=13) merge (__vola.641 __vola.659)  <674>;
                            (__extPM.661 var=16) merge (__extPM.637 __extPM.655)  <675>;
                            (__extDM.662 var=17) merge (__extDM.634 __extDM.652)  <676>;
                            (__extULP.663 var=18) merge (__extULP.639 __extULP.657)  <677>;
                            (Temp_Buf.664 var=22) merge (Temp_Buf.633 Temp_Buf.651)  <678>;
                            (__extDM_int8_.665 var=25) merge (__extDM_int8_.635 __extDM_int8_.653)  <679>;
                            (__extPM_void.666 var=29) merge (__extPM_void.638 __extPM_void.656)  <680>;
                            (__extDM_void.667 var=30) merge (__extDM_void.636 __extDM_void.654)  <681>;
                            (__extULP_void.668 var=31) merge (__extULP_void.640 __extULP_void.658)  <682>;
                        } #79
                    } #69
                    #82 off=65 nxt=84
                    <321> {
                      (j.676 var=26 __seff.1962 var=375 stl=c_flag_w __seff.1963 var=376 stl=nz_flag_w __seff.1964 var=377 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B2 (__ct_5t0.1868 j.555 j.555 __sp.49)  <2186>;
                      (__seff.2338 var=376 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1963)  <2775>;
                      (__seff.2339 var=375 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1962)  <2776>;
                      (__seff.2358 var=377 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1964)  <2789>;
                    } stp=0;
                    call {
                        () chess_separator_scheduler ()  <692>;
                    } #84 off=67 nxt=299
                    #299 off=67 nxt=92 tgt=294
                    <319> {
                      (__apl_c.1782 var=317 stl=c_flag_w __apl_nz.1784 var=319 stl=nz_flag_w __seff.1960 var=374 stl=o_flag_w) load_cmp_const_1_B2 (__adr_j.2366 j.676)  <2184>;
                      (__apl_nz.2335 var=319 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1784)  <2772>;
                      (__apl_c.2337 var=317 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.1782)  <2774>;
                      (__seff.2359 var=374 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1960)  <2790>;
                      (__adr_j.2366 var=63 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_j.2367)  <2794>;
                    } stp=2;
                    <320> {
                      () cc_b__jump_const_1_B1 (__apl_c.2336 __apl_nz.2334 __trgt.1881)  <2185>;
                      (__apl_nz.2334 var=319 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2335)  <2771>;
                      (__apl_c.2336 var=317 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.2337)  <2773>;
                    } stp=4;
                    <622> {
                      (__adr_j.2368 var=63 stl=a_w2 __side_effect.2369 var=398 stl=c_flag_w __side_effect.2371 var=398 stl=nz_flag_w __side_effect.2373 var=398 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_5t0.1868 __sp.49)  <2528>;
                      (__adr_j.2367 var=63 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_j.2368)  <2795>;
                      (__side_effect.2370 var=398 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.2369)  <2796>;
                      (__side_effect.2372 var=398 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2371)  <2797>;
                      (__side_effect.2374 var=398 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2373)  <2798>;
                    } stp=0;
                    <615> {
                      (__tmp.2572 var=143 stl=DMw_r) stack_load_indirect_bndl_B2 (__tmp.2575 __sp.49)  <2780>;
                      (__tmp.2346 var=143 stl=R46 off=0) from___spill_DMw_Rw_1_dr_move_DMw_r_1_int16__B0 (__tmp.2572)  <2783>;
                    } stp=3;
                } #67
                {
                    () while_expr (__either.1850)  <732>;
                    (__vola.716 var=13 __vola.717 var=13) exit (__vola.660)  <733>;
                    (__extPM.722 var=16 __extPM.723 var=16) exit (__extPM.661)  <736>;
                    (__extDM.724 var=17 __extDM.725 var=17) exit (__extDM.662)  <737>;
                    (__extULP.726 var=18 __extULP.727 var=18) exit (__extULP.663)  <738>;
                    (Temp_Buf.734 var=22 Temp_Buf.735 var=22) exit (Temp_Buf.664)  <742>;
                    (__extDM_int8_.740 var=25 __extDM_int8_.741 var=25) exit (__extDM_int8_.665)  <745>;
                    (j.742 var=26 j.743 var=26) exit (j.676)  <746>;
                    (__extPM_void.748 var=29 __extPM_void.749 var=29) exit (__extPM_void.666)  <749>;
                    (__extDM_void.750 var=30 __extDM_void.751 var=30) exit (__extDM_void.667)  <750>;
                    (__extULP_void.752 var=31 __extULP_void.753 var=31) exit (__extULP_void.668)  <751>;
                    (__either.1850 var=345) undefined ()  <2042>;
                    (__tmp.2344 var=143 stl=R46 off=0 __tmp.2345 var=143 stl=R46 off=0) exit (__tmp.2346)  <2506>;
                } #86
            } #65 rng=[1,2147483647]
            #92 off=72 nxt=94
            <318> {
              (i.859 var=23 __seff.1955 var=371 stl=c_flag_w __seff.1956 var=372 stl=nz_flag_w __seff.1957 var=373 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_4t0.1867 i.474 i.474 __sp.49)  <2183>;
              (__seff.2394 var=372 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1956)  <2808>;
              (__seff.2395 var=371 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1955)  <2809>;
              (__seff.2403 var=373 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1957)  <2820>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <897>;
            } #94 off=73 nxt=304
            #304 off=73 nxt=100 tgt=59
            <316> {
              (__apl_c.1788 var=317 stl=c_flag_w __apl_nz.1790 var=319 stl=nz_flag_w __seff.1953 var=370 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B3 (__ct_4t0.1867 i.859 __sp.49)  <2181>;
              (__apl_nz.2391 var=319 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1790)  <2805>;
              (__apl_c.2393 var=317 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.1788)  <2807>;
              (__seff.2404 var=370 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1953)  <2821>;
            } stp=2;
            <317> {
              () cc_b__jump_const_1_B1 (__apl_c.2392 __apl_nz.2390 __trgt.1882)  <2182>;
              (__apl_nz.2390 var=319 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2391)  <2804>;
              (__apl_c.2392 var=317 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.2393)  <2806>;
            } stp=3;
            <642> {
              (__adr_Temp_Buf.2410 var=55 stl=a_w2 __side_effect.2411 var=398 stl=c_flag_w __side_effect.2413 var=398 stl=nz_flag_w __side_effect.2415 var=398 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_6t0.1866 __sp.49)  <2575>;
              (__adr_Temp_Buf.2409 var=55 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_Temp_Buf.2410)  <2823>;
              (__side_effect.2412 var=398 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.2411)  <2824>;
              (__side_effect.2414 var=398 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2413)  <2825>;
              (__side_effect.2416 var=398 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2415)  <2826>;
            } stp=0;
        } #58
        {
            () while_expr (__either.1852)  <937>;
            (__vola.899 var=13 __vola.900 var=13) exit (__vola.717)  <938>;
            (__extPM.905 var=16 __extPM.906 var=16) exit (__extPM.723)  <941>;
            (__extDM.907 var=17 __extDM.908 var=17) exit (__extDM.725)  <942>;
            (__extULP.909 var=18 __extULP.910 var=18) exit (__extULP.727)  <943>;
            (Temp_Buf.917 var=22 Temp_Buf.918 var=22) exit (Temp_Buf.735)  <947>;
            (i.919 var=23 i.920 var=23) exit (i.859)  <948>;
            (__extDM_int8_.923 var=25 __extDM_int8_.924 var=25) exit (__extDM_int8_.741)  <950>;
            (j.925 var=26 j.926 var=26) exit (j.743)  <951>;
            (__extPM_void.931 var=29 __extPM_void.932 var=29) exit (__extPM_void.749)  <954>;
            (__extDM_void.933 var=30 __extDM_void.934 var=30) exit (__extDM_void.751)  <955>;
            (__extULP_void.935 var=31 __extULP_void.936 var=31) exit (__extULP_void.753)  <956>;
            (__either.1852 var=345) undefined ()  <2045>;
            (__adr_Temp_Buf.2407 var=55 stl=R46 off=0 __adr_Temp_Buf.2408 var=55 stl=R46 off=0) exit (__adr_Temp_Buf.2409)  <2573>;
        } #96
    } #56 rng=[1,2147483647]
    #100 off=77 nxt=101
    <315> {
      (i.1038 var=23) store_const__pl_rd_res_reg_const_1_B3 (__ct_4t0.1867 i.920 __sp.49)  <2180>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <1096>;
    } #101 off=78 nxt=148
    #148 off=78 nxt=-3 tgt=5
    () sink (__vola.900)  <1623>;
    () sink (__extPM.906)  <1626>;
    () sink (__extDM.908)  <1627>;
    () sink (__extULP.910)  <1628>;
    () sink (__sp.49)  <1629>;
    () sink (len.81)  <1630>;
    () sink (Txbuf.83)  <1631>;
    () sink (Temp_Buf.918)  <1632>;
    () sink (i.1038)  <1633>;
    () sink (__extDM_int8_.924)  <1635>;
    () sink (j.926)  <1636>;
    () sink (__extPM_void.932)  <1639>;
    () sink (__extDM_void.934)  <1640>;
    () sink (__extULP_void.936)  <1641>;
    () sync_sink (__vola.900) sid=137  <1642>;
    () sync_sink (__extPM.906) sid=140  <1645>;
    () sync_sink (__extDM.908) sid=141  <1646>;
    () sync_sink (__extULP.910) sid=142  <1647>;
    () sync_sink (Temp_Buf.918) sid=146  <1651>;
    () sync_sink (i.1038) sid=147  <1652>;
    () sync_sink (__extDM_int8_.924) sid=149  <1654>;
    () sync_sink (j.926) sid=150  <1655>;
    () sync_sink (__extPM_void.932) sid=153  <1658>;
    () sync_sink (__extDM_void.934) sid=154  <1659>;
    () sync_sink (__extULP_void.936) sid=155  <1660>;
    (__vola.1833 var=13) never ()  <2021>;
    (__extPM.1834 var=16) never ()  <2022>;
    (__extDM.1835 var=17) never ()  <2023>;
    (__extULP.1836 var=18) never ()  <2024>;
    (Temp_Buf.1837 var=22) never ()  <2025>;
    (i.1838 var=23) never ()  <2026>;
    (__extDM_int8_.1839 var=25) never ()  <2027>;
    (j.1840 var=26) never ()  <2028>;
    (__extPM_void.1841 var=29) never ()  <2029>;
    (__extDM_void.1842 var=30) never ()  <2030>;
    (__extULP_void.1843 var=31) never ()  <2031>;
    (__trgt.1883 var=351) const_inp ()  <2081>;
    (__trgt.1884 var=352) const_inp ()  <2082>;
    (__trgt.1885 var=353) const_inp ()  <2083>;
    (__trgt.1886 var=354) const_inp ()  <2084>;
    (__trgt.1887 var=355) const_inp ()  <2085>;
    <309> {
      (__fch_len.1512 var=249 stl=DM_r) load__pl_rd_res_reg_const_1_B2 (__ct_0t0.1864 len.81 __sp.49)  <2174>;
      (__fch_len.2513 var=249 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_len.1512)  <2890>;
    } stp=0;
    <314> {
      () jump_const_1_B1 (__trgt.1887)  <2179>;
    } stp=2;
    () sync_sink (__fch_len.2513) sid=165  <2683>;
    <698> {
      (__fch_len.2587 var=249 stl=__spill_DM off=0) stack_store_indirect_bndl_B1 (__fch_len.2523 __sp.49)  <2895>;
      (__fch_len.2523 var=249 stl=DM_w) to___spill_DM_DM_w_1_dr_move_Rb_1_int8__B0 (__fch_len.2513)  <2898>;
    } stp=1;
    do {
        {
            (__vola.1079 var=13) entry (__vola.1515 __vola.1833)  <1138>;
            (__extPM.1082 var=16) entry (__extPM.1521 __extPM.1834)  <1141>;
            (__extDM.1083 var=17) entry (__extDM.1523 __extDM.1835)  <1142>;
            (__extULP.1084 var=18) entry (__extULP.1525 __extULP.1836)  <1143>;
            (Temp_Buf.1088 var=22) entry (Temp_Buf.1533 Temp_Buf.1837)  <1147>;
            (i.1089 var=23) entry (i.1535 i.1838)  <1148>;
            (__extDM_int8_.1091 var=25) entry (__extDM_int8_.1539 __extDM_int8_.1839)  <1150>;
            (j.1092 var=26) entry (j.1541 j.1840)  <1151>;
            (__extPM_void.1095 var=29) entry (__extPM_void.1547 __extPM_void.1841)  <1154>;
            (__extDM_void.1096 var=30) entry (__extDM_void.1549 __extDM_void.1842)  <1155>;
            (__extULP_void.1097 var=31) entry (__extULP_void.1551 __extULP_void.1843)  <1156>;
        } #107
        {
            #109 off=81 nxt=110
            <308> {
              (j.1116 var=26) store_const__pl_rd_res_reg_const_1_B2 (__ct_5t0.1868 j.1092 __sp.49)  <2173>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <1175>;
            } #110 off=83 nxt=321
            #321 off=83 nxt=336
            <305> {
              (__apl_r.1800 var=322 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B2 (__ct_4t0.1867 i.1089 __sp.49)  <2170>;
              (__apl_r.2492 var=322 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.1800)  <2870>;
            } stp=0;
            <306> {
              (__tmp.1802 var=205 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.2491)  <2171>;
              (__apl_r.2491 var=322 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.2492)  <2869>;
              (__tmp.2495 var=205 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.1802)  <2873>;
            } stp=1;
            <307> {
              (__tmp.1194 var=206 stl=a_w2 __seff.1941 var=367 stl=c_flag_w __seff.1942 var=368 stl=nz_flag_w __seff.1943 var=369 stl=o_flag_w) _pl_load__pl_rd_res_reg_const_1_B2 (__tmp.2493 __ct_2t0.1865 Txbuf.83 __sp.49)  <2172>;
              (__seff.2482 var=368 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1942)  <2860>;
              (__seff.2483 var=367 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1941)  <2861>;
              (__tmp.2490 var=206 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__tmp.1194)  <2868>;
              (__tmp.2493 var=205 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B1 (__tmp.2494)  <2871>;
              (__seff.2496 var=369 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1943)  <2874>;
            } stp=3;
            <683> {
              (__tmp.2494 var=205 stl=R46 off=0) Rw_1_dr_move_Rw_1_int16__B0 (__tmp.2495)  <2872>;
            } stp=2;
            <684> {
              (__tmp.2581 var=206 stl=__spill_DMw off=2) stack_store_bndl_B4 (__tmp.2497 __sp.49 __stack_offs_.2591)  <2875>;
              (__tmp.2497 var=206 stl=DMw_w) to___spill_DMw_DMw_w_1_dr_move_Rw_1_int16__B1 (__tmp.2490)  <2878>;
              (__stack_offs_.2591 var=399) const_inp ()  <2913>;
            } stp=4;
            do {
                {
                    (__vola.1157 var=13) entry (__vola.1332 __vola.1079)  <1217>;
                    (__extPM.1160 var=16) entry (__extPM.1338 __extPM.1082)  <1220>;
                    (__extDM.1161 var=17) entry (__extDM.1340 __extDM.1083)  <1221>;
                    (__extULP.1162 var=18) entry (__extULP.1342 __extULP.1084)  <1222>;
                    (Temp_Buf.1166 var=22) entry (Temp_Buf.1350 Temp_Buf.1088)  <1226>;
                    (__extDM_int8_.1169 var=25) entry (__extDM_int8_.1356 __extDM_int8_.1091)  <1229>;
                    (j.1170 var=26) entry (j.1358 j.1116)  <1230>;
                    (__extPM_void.1173 var=29) entry (__extPM_void.1364 __extPM_void.1095)  <1233>;
                    (__extDM_void.1174 var=30) entry (__extDM_void.1366 __extDM_void.1096)  <1234>;
                    (__extULP_void.1175 var=31) entry (__extULP_void.1368 __extULP_void.1097)  <1235>;
                    (__tmp.2430 var=206 stl=R46 off=0) entry (__tmp.2431 __tmp.2490)  <2599>;
                } #116
                {
                    #336 off=88 nxt=126 tgt=122
                    <300> {
                      (__apl_r.1805 var=322 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B1 (__ct_5t0.1868 j.1170 __sp.49)  <2165>;
                      (__apl_r.2428 var=322 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.1805)  <2836>;
                    } stp=0;
                    <301> {
                      (__tmp.1807 var=210 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.2427)  <2166>;
                      (__apl_r.2427 var=322 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.2428)  <2835>;
                      (__tmp.2436 var=210 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.1807)  <2844>;
                    } stp=2;
                    <302> {
                      (__fchtmp.1195 var=207 stl=DM_r) load_1_B1 (__tmp.2429 __extDM.1161 __extDM_int8_.1169 __extDM_void.1174 encode.26 mancode0.27 mancode1.23)  <2167>;
                      (__tmp.2429 var=206 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__tmp.2430)  <2837>;
                      (__fchtmp.2438 var=207 stl=RbH off=1) Rb_1_dr_move_DM_r_1_int8__B1 (__fchtmp.1195)  <2846>;
                    } stp=3;
                    <303> {
                      (__apl_nz.1812 var=329 stl=nz_flag_w) _pl_const_load__ad_cmp_const_1_B2 (__tmp.2434 __fchtmp.2437 __ptr_encode.1862 encode.26)  <2168>;
                      (__apl_nz.2420 var=329 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1812)  <2828>;
                      (__tmp.2434 var=210 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__tmp.2435)  <2842>;
                      (__fchtmp.2437 var=207 stl=a_b1) a_b0_a_b1_1_dr_move_Rb_1_int8__B3 (__fchtmp.2438)  <2845>;
                    } stp=5;
                    <304> {
                      () cc_ne__jump_const_1_B1 (__apl_nz.2419 __trgt.1883)  <2169>;
                      (__apl_nz.2419 var=329 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2420)  <2827>;
                    } stp=7;
                    <655> {
                      (__tmp.2435 var=210 stl=R46 off=0) Rw_1_dr_move_Rw_1_int16__B0 (__tmp.2436)  <2843>;
                    } stp=4;
                    if {
                        {
                            () if_expr (__either.1854)  <1300>;
                            (__either.1854 var=345) undefined ()  <2048>;
                        } #120
                        {
                            #122 off=103 nxt=123
                            <299> {
                              () call_const_1_B1 (void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar.1870)  <2164>;
                            } stp=4;
                            <673> {
                              (__ct_3.2477 var=218 stl=a_b0) const_1_B2 ()  <2641>;
                              (__ct_3.2476 var=218 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_3.2477)  <2857>;
                            } stp=0;
                            <674> {
                              (__ct_1.2479 var=220 stl=a_w0) const_2_B2 ()  <2643>;
                              (__ct_1.2478 var=220 stl=RwL off=1) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_1.2479)  <2858>;
                            } stp=1;
                            <675> {
                              (__ptr_mancode0.2481 var=38 stl=__CTa_w0_uint16__cstP16_E1) const_3_B1 (__ptr_mancode0.1863)  <2645>;
                              (__ptr_mancode0.2480 var=38 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ptr_mancode0.2481)  <2859>;
                            } stp=2;
                            call {
                                (Temp_Buf.1249 var=22 __extDM.1250 var=17 __extDM_int8_.1251 var=25 __extDM_void.1252 var=30 __extPM.1253 var=16 __extPM_void.1254 var=29 __extULP.1255 var=18 __extULP_void.1256 var=31 __vola.1257 var=13) Fvoid_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar (__ct_3.2476 __ct_1.2478 __ptr_mancode0.2480 Temp_Buf.1166 __extDM.1161 __extDM_int8_.1169 __extDM_void.1174 __extPM.1160 __extPM_void.1173 __extULP.1162 __extULP_void.1175 __vola.1157)  <1311>;
                            } #123 off=109 nxt=362
                            #362 off=109 nxt=132
                        } #121
                        {
                            #126 off=96 nxt=127
                            <298> {
                              () call_const_1_B1 (void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar.1870)  <2163>;
                            } stp=4;
                            <660> {
                              (__ct_3.2451 var=224 stl=a_b0) const_1_B2 ()  <2617>;
                              (__ct_3.2450 var=224 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_3.2451)  <2849>;
                            } stp=0;
                            <661> {
                              (__ct_1.2453 var=226 stl=a_w0) const_2_B2 ()  <2619>;
                              (__ct_1.2452 var=226 stl=RwL off=1) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_1.2453)  <2850>;
                            } stp=1;
                            <662> {
                              (__ptr_mancode1.2455 var=34 stl=__CTa_w0_uint16__cstP16_E1) const_3_B1 (__ptr_mancode1.1861)  <2621>;
                              (__ptr_mancode1.2454 var=34 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ptr_mancode1.2455)  <2851>;
                            } stp=2;
                            call {
                                (Temp_Buf.1267 var=22 __extDM.1268 var=17 __extDM_int8_.1269 var=25 __extDM_void.1270 var=30 __extPM.1271 var=16 __extPM_void.1272 var=29 __extULP.1273 var=18 __extULP_void.1274 var=31 __vola.1275 var=13) Fvoid_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar (__ct_3.2450 __ct_1.2452 __ptr_mancode1.2454 Temp_Buf.1166 __extDM.1161 __extDM_int8_.1169 __extDM_void.1174 __extPM.1160 __extPM_void.1173 __extULP.1162 __extULP_void.1175 __vola.1157)  <1323>;
                            } #127 off=102 nxt=360
                            #360 off=102 tgt=132
                            <297> {
                              () jump_const_1_B1 (__trgt.1884)  <2162>;
                            } stp=0;
                        } #125
                        {
                            (__vola.1276 var=13) merge (__vola.1257 __vola.1275)  <1325>;
                            (__extPM.1277 var=16) merge (__extPM.1253 __extPM.1271)  <1326>;
                            (__extDM.1278 var=17) merge (__extDM.1250 __extDM.1268)  <1327>;
                            (__extULP.1279 var=18) merge (__extULP.1255 __extULP.1273)  <1328>;
                            (Temp_Buf.1280 var=22) merge (Temp_Buf.1249 Temp_Buf.1267)  <1329>;
                            (__extDM_int8_.1281 var=25) merge (__extDM_int8_.1251 __extDM_int8_.1269)  <1330>;
                            (__extPM_void.1282 var=29) merge (__extPM_void.1254 __extPM_void.1272)  <1331>;
                            (__extDM_void.1283 var=30) merge (__extDM_void.1252 __extDM_void.1270)  <1332>;
                            (__extULP_void.1284 var=31) merge (__extULP_void.1256 __extULP_void.1274)  <1333>;
                        } #129
                    } #119
                    #132 off=109 nxt=134
                    <296> {
                      (j.1292 var=26 __seff.1931 var=364 stl=c_flag_w __seff.1932 var=365 stl=nz_flag_w __seff.1933 var=366 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B2 (__ct_5t0.1868 j.1170 j.1170 __sp.49)  <2161>;
                      (__seff.2425 var=365 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1932)  <2833>;
                      (__seff.2426 var=364 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1931)  <2834>;
                      (__seff.2448 var=366 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1933)  <2847>;
                    } stp=0;
                    call {
                        () chess_separator_scheduler ()  <1343>;
                    } #134 off=111 nxt=341
                    #341 off=111 nxt=142 tgt=336
                    <294> {
                      (__apl_c.1816 var=317 stl=c_flag_w __apl_nz.1818 var=319 stl=nz_flag_w __seff.1929 var=363 stl=o_flag_w) load_cmp_const_1_B2 (__adr_j.2456 j.1292)  <2159>;
                      (__apl_nz.2422 var=319 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1818)  <2830>;
                      (__apl_c.2424 var=317 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.1816)  <2832>;
                      (__seff.2449 var=363 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1929)  <2848>;
                      (__adr_j.2456 var=63 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_j.2457)  <2852>;
                    } stp=2;
                    <295> {
                      () cc_b__jump_const_1_B1 (__apl_c.2423 __apl_nz.2421 __trgt.1885)  <2160>;
                      (__apl_nz.2421 var=319 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2422)  <2829>;
                      (__apl_c.2423 var=317 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.2424)  <2831>;
                    } stp=4;
                    <663> {
                      (__adr_j.2458 var=63 stl=a_w2 __side_effect.2459 var=398 stl=c_flag_w __side_effect.2461 var=398 stl=nz_flag_w __side_effect.2463 var=398 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_5t0.1868 __sp.49)  <2624>;
                      (__adr_j.2457 var=63 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_j.2458)  <2853>;
                      (__side_effect.2460 var=398 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.2459)  <2854>;
                      (__side_effect.2462 var=398 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2461)  <2855>;
                      (__side_effect.2464 var=398 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2463)  <2856>;
                    } stp=0;
                    <654> {
                      (__tmp.2578 var=206 stl=DMw_r) stack_load_bndl_B4 (__tmp.2581 __sp.49 __stack_offs_.2590)  <2838>;
                      (__tmp.2433 var=206 stl=R46 off=0) from___spill_DMw_Rw_1_dr_move_DMw_r_1_int16__B0 (__tmp.2578)  <2841>;
                      (__stack_offs_.2590 var=399) const_inp ()  <2912>;
                    } stp=3;
                } #117
                {
                    () while_expr (__either.1857)  <1383>;
                    (__vola.1332 var=13 __vola.1333 var=13) exit (__vola.1276)  <1384>;
                    (__extPM.1338 var=16 __extPM.1339 var=16) exit (__extPM.1277)  <1387>;
                    (__extDM.1340 var=17 __extDM.1341 var=17) exit (__extDM.1278)  <1388>;
                    (__extULP.1342 var=18 __extULP.1343 var=18) exit (__extULP.1279)  <1389>;
                    (Temp_Buf.1350 var=22 Temp_Buf.1351 var=22) exit (Temp_Buf.1280)  <1393>;
                    (__extDM_int8_.1356 var=25 __extDM_int8_.1357 var=25) exit (__extDM_int8_.1281)  <1396>;
                    (j.1358 var=26 j.1359 var=26) exit (j.1292)  <1397>;
                    (__extPM_void.1364 var=29 __extPM_void.1365 var=29) exit (__extPM_void.1282)  <1400>;
                    (__extDM_void.1366 var=30 __extDM_void.1367 var=30) exit (__extDM_void.1283)  <1401>;
                    (__extULP_void.1368 var=31 __extULP_void.1369 var=31) exit (__extULP_void.1284)  <1402>;
                    (__either.1857 var=345) undefined ()  <2053>;
                    (__tmp.2431 var=206 stl=R46 off=0 __tmp.2432 var=206 stl=R46 off=0) exit (__tmp.2433)  <2600>;
                } #136
            } #115 rng=[1,2147483647]
            #142 off=116 nxt=5
            <293> {
              (i.1475 var=23 __seff.1924 var=360 stl=c_flag_w __seff.1925 var=361 stl=nz_flag_w __seff.1926 var=362 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_4t0.1867 i.1089 i.1089 __sp.49)  <2158>;
              (__seff.2484 var=361 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1925)  <2862>;
              (__seff.2485 var=360 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1924)  <2863>;
              (__seff.2502 var=362 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1926)  <2879>;
            } stp=0;
            <689> {
              (__fch_len.2584 var=249 stl=DM_r) stack_load_indirect_bndl_B1 (__fch_len.2587 __sp.49)  <2881>;
              (__fch_len.2505 var=249 stl=RbL off=0) from___spill_DM_Rb_1_dr_move_DM_r_1_int8__B1 (__fch_len.2584)  <2884>;
            } stp=1;
            sync {
                (__vola.1476 var=13) sync_link (__vola.1333) sid=137  <1549>;
                (__extPM.1479 var=16) sync_link (__extPM.1339) sid=140  <1552>;
                (__extDM.1480 var=17) sync_link (__extDM.1341) sid=141  <1553>;
                (__extULP.1481 var=18) sync_link (__extULP.1343) sid=142  <1554>;
                (Temp_Buf.1485 var=22) sync_link (Temp_Buf.1351) sid=146  <1558>;
                (i.1486 var=23) sync_link (i.1475) sid=147  <1559>;
                (__extDM_int8_.1488 var=25) sync_link (__extDM_int8_.1357) sid=149  <1561>;
                (j.1489 var=26) sync_link (j.1359) sid=150  <1562>;
                (__extPM_void.1492 var=29) sync_link (__extPM_void.1365) sid=153  <1565>;
                (__extDM_void.1493 var=30) sync_link (__extDM_void.1367) sid=154  <1566>;
                (__extULP_void.1494 var=31) sync_link (__extULP_void.1369) sid=155  <1567>;
                (__fch_len.2504 var=249 stl=RbL off=0) sync_link (__fch_len.2505) sid=165  <2674>;
            } #5 off=118 nxt=346
            #346 off=118 nxt=150 tgt=109
            <291> {
              (__apl_c.1822 var=317 stl=c_flag_w __apl_nz.1824 var=319 stl=nz_flag_w __seff.1922 var=359 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_1_B2 (__fch_len.2503 __ct_4t0.1867 i.1486 __sp.49)  <2156>;
              (__apl_nz.2487 var=319 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1824)  <2865>;
              (__apl_c.2489 var=317 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.1822)  <2867>;
              (__fch_len.2503 var=249 stl=a_b1) a_b1_1_dr_move_Rb_1_int8__B0 (__fch_len.2504)  <2880>;
              (__seff.2506 var=359 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1922)  <2885>;
            } stp=0;
            <292> {
              () cc_b__jump_const_1_B1 (__apl_c.2488 __apl_nz.2486 __trgt.1886)  <2157>;
              (__apl_nz.2486 var=319 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2487)  <2864>;
              (__apl_c.2488 var=317 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.2489)  <2866>;
            } stp=1;
        } #108
        {
            () while_expr (__either.1859)  <1588>;
            (__vola.1515 var=13 __vola.1516 var=13) exit (__vola.1476)  <1589>;
            (__extPM.1521 var=16 __extPM.1522 var=16) exit (__extPM.1479)  <1592>;
            (__extDM.1523 var=17 __extDM.1524 var=17) exit (__extDM.1480)  <1593>;
            (__extULP.1525 var=18 __extULP.1526 var=18) exit (__extULP.1481)  <1594>;
            (Temp_Buf.1533 var=22 Temp_Buf.1534 var=22) exit (Temp_Buf.1485)  <1598>;
            (i.1535 var=23 i.1536 var=23) exit (i.1486)  <1599>;
            (__extDM_int8_.1539 var=25 __extDM_int8_.1540 var=25) exit (__extDM_int8_.1488)  <1601>;
            (j.1541 var=26 j.1542 var=26) exit (j.1489)  <1602>;
            (__extPM_void.1547 var=29 __extPM_void.1548 var=29) exit (__extPM_void.1492)  <1605>;
            (__extDM_void.1549 var=30 __extDM_void.1550 var=30) exit (__extDM_void.1493)  <1606>;
            (__extULP_void.1551 var=31 __extULP_void.1552 var=31) exit (__extULP_void.1494)  <1607>;
            (__either.1859 var=345) undefined ()  <2056>;
        } #146
    } #106
    #150 off=120 nxt=151
    <290> {
      () call_const_1_B1 (void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar.1870)  <2155>;
    } stp=4;
    <704> {
      (__ct_3.2535 var=252 stl=a_b0) const_1_B2 ()  <2711>;
      (__ct_3.2534 var=252 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_3.2535)  <2904>;
    } stp=0;
    <705> {
      (__ct_1.2537 var=254 stl=a_w0) const_2_B2 ()  <2713>;
      (__ct_1.2536 var=254 stl=RwL off=1) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_1.2537)  <2905>;
    } stp=1;
    <711> {
      (__ptr_mancode1.2549 var=34 stl=__CTa_w0_uint16__cstP16_E1) const_3_B1 (__ptr_mancode1.1861)  <2725>;
      (__ptr_mancode1.2548 var=34 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ptr_mancode1.2549)  <2911>;
    } stp=2;
    call {
        (Temp_Buf.1660 var=22 __extDM.1661 var=17 __extDM_int8_.1662 var=25 __extDM_void.1663 var=30 __extPM.1664 var=16 __extPM_void.1665 var=29 __extULP.1666 var=18 __extULP_void.1667 var=31 __vola.1668 var=13) Fvoid_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar (__ct_3.2534 __ct_1.2536 __ptr_mancode1.2548 Temp_Buf.1534 __extDM.1524 __extDM_int8_.1540 __extDM_void.1550 __extPM.1522 __extPM_void.1548 __extULP.1526 __extULP_void.1552 __vola.1516)  <1754>;
    } #151 off=126 nxt=154
    #154 off=126 nxt=-2
    () sink (__vola.1668)  <1762>;
    () sink (__extPM.1664)  <1765>;
    () sink (__extDM.1661)  <1766>;
    () sink (__extULP.1666)  <1767>;
    () sink (__sp.1674)  <1768>;
    () sink (len.81)  <1769>;
    () sink (Txbuf.83)  <1770>;
    () sink (Temp_Buf.1660)  <1771>;
    () sink (i.1536)  <1772>;
    () sink (__extDM_int8_.1662)  <1774>;
    () sink (j.1542)  <1775>;
    () sink (__extPM_void.1665)  <1778>;
    () sink (__extDM_void.1663)  <1779>;
    () sink (__extULP_void.1667)  <1780>;
    (__ct_10s0.1871 var=258) const_inp ()  <2069>;
    <286> {
      (__sp.1674 var=19 __seff.1915 var=356 stl=c_flag_w __seff.1916 var=357 stl=nz_flag_w __seff.1917 var=358 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_10s0.1871 __sp.49 __sp.49)  <2151>;
      (__seff.2511 var=357 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1916)  <2888>;
      (__seff.2512 var=356 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1915)  <2889>;
      (__seff.2522 var=358 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1917)  <2894>;
    } stp=0;
    <287> {
      () ret_1_B1 ()  <2152>;
    } stp=2;
    327 -> 616 del=0;
    319 -> 615 del=0;
    302 -> 655 del=0;
    294 -> 654 del=0;
    642 -> 316 del=1;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,271:0,0);
5 : (0,306:9,87);
8 : (0,271:33,0);
9 : (0,271:33,0);
10 : (0,271:17,0);
11 : (0,271:17,0);
12 : (0,274:40,5);
13 : (0,274:40,5);
14 : (0,274:40,6);
15 : (0,274:40,6);
16 : (0,274:40,7);
17 : (0,274:40,7);
18 : (0,274:40,8);
20 : (0,274:40,8);
21 : (0,276:61,9);
22 : (0,276:16,9);
26 : (0,277:16,11);
30 : (0,277:16,12);
32 : (0,277:16,12);
33 : (0,280:65,12);
34 : (0,280:10,12);
38 : (0,277:32,0);
40 : (0,277:16,16);
46 : (0,283:24,25);
47 : (0,283:24,25);
48 : (0,284:24,26);
49 : (0,284:24,26);
50 : (0,286:23,27);
51 : (0,286:23,27);
52 : (0,286:16,27);
56 : (0,286:16,28);
58 : (0,286:16,28);
59 : (0,288:11,29);
60 : (0,288:11,29);
65 : (0,288:4,30);
67 : (0,288:4,30);
69 : (0,290:4,30);
71 : (0,292:6,31);
72 : (0,294:61,31);
73 : (0,294:6,31);
75 : (0,298:6,34);
76 : (0,300:60,34);
77 : (0,300:5,34);
82 : (0,288:24,0);
84 : (0,288:4,41);
92 : (0,286:36,0);
94 : (0,286:16,52);
100 : (0,306:16,61);
101 : (0,306:16,61);
106 : (0,306:9,62);
108 : (0,306:9,62);
109 : (0,308:11,63);
110 : (0,308:11,63);
115 : (0,308:4,64);
117 : (0,308:4,64);
119 : (0,310:4,64);
121 : (0,312:6,65);
122 : (0,314:61,65);
123 : (0,314:6,65);
125 : (0,318:6,68);
126 : (0,320:63,68);
127 : (0,320:8,68);
132 : (0,308:24,0);
134 : (0,308:4,75);
142 : (0,306:31,0);
148 : (0,306:9,91);
150 : (0,326:55,94);
151 : (0,326:0,94);
154 : (0,327:0,95);
275 : (0,277:27,19);
279 : (0,290:17,30);
294 : (0,290:4,30);
299 : (0,288:18,44);
304 : (0,286:30,55);
321 : (0,310:14,64);
336 : (0,310:4,64);
341 : (0,308:18,78);
346 : (0,306:23,89);
----------
92 : (0,271:33,0);
94 : (0,271:17,0);
101 : (0,274:40,5);
108 : (0,274:40,6);
115 : (0,274:40,7);
123 : (0,274:40,8);
131 : (0,276:16,9);
178 : (0,277:16,12);
181 : (0,277:16,12);
182 : (0,277:16,12);
183 : (0,277:16,12);
187 : (0,277:16,12);
188 : (0,277:16,12);
190 : (0,277:16,12);
194 : (0,277:16,12);
195 : (0,277:16,12);
196 : (0,277:16,12);
222 : (0,280:10,12);
233 : (0,277:16,16);
273 : (0,277:16,19);
274 : (0,277:16,19);
277 : (0,277:16,19);
278 : (0,277:16,19);
279 : (0,277:16,19);
283 : (0,277:16,19);
284 : (0,277:16,19);
286 : (0,277:16,19);
290 : (0,277:16,19);
291 : (0,277:16,19);
292 : (0,277:16,19);
435 : (0,283:24,25);
442 : (0,284:24,26);
446 : (0,286:23,27);
488 : (0,286:16,28);
491 : (0,286:16,28);
492 : (0,286:16,28);
493 : (0,286:16,28);
497 : (0,286:16,28);
498 : (0,286:16,28);
500 : (0,286:16,28);
501 : (0,286:16,28);
504 : (0,286:16,28);
505 : (0,286:16,28);
506 : (0,286:16,28);
525 : (0,288:11,29);
567 : (0,288:4,30);
570 : (0,288:4,30);
571 : (0,288:4,30);
572 : (0,288:4,30);
576 : (0,288:4,30);
579 : (0,288:4,30);
580 : (0,288:4,30);
583 : (0,288:4,30);
584 : (0,288:4,30);
585 : (0,288:4,30);
649 : (0,290:4,30);
660 : (0,294:6,31);
672 : (0,300:5,34);
674 : (0,290:4,37);
675 : (0,290:4,37);
676 : (0,290:4,37);
677 : (0,290:4,37);
678 : (0,290:4,37);
679 : (0,290:4,37);
680 : (0,290:4,37);
681 : (0,290:4,37);
682 : (0,290:4,37);
692 : (0,288:4,41);
732 : (0,288:4,44);
733 : (0,288:4,44);
736 : (0,288:4,44);
737 : (0,288:4,44);
738 : (0,288:4,44);
742 : (0,288:4,44);
745 : (0,288:4,44);
746 : (0,288:4,44);
749 : (0,288:4,44);
750 : (0,288:4,44);
751 : (0,288:4,44);
897 : (0,286:16,52);
937 : (0,286:16,55);
938 : (0,286:16,55);
941 : (0,286:16,55);
942 : (0,286:16,55);
943 : (0,286:16,55);
947 : (0,286:16,55);
948 : (0,286:16,55);
950 : (0,286:16,55);
951 : (0,286:16,55);
954 : (0,286:16,55);
955 : (0,286:16,55);
956 : (0,286:16,55);
1096 : (0,306:16,61);
1138 : (0,306:9,62);
1141 : (0,306:9,62);
1142 : (0,306:9,62);
1143 : (0,306:9,62);
1147 : (0,306:9,62);
1148 : (0,306:9,62);
1150 : (0,306:9,62);
1151 : (0,306:9,62);
1154 : (0,306:9,62);
1155 : (0,306:9,62);
1156 : (0,306:9,62);
1175 : (0,308:11,63);
1217 : (0,308:4,64);
1220 : (0,308:4,64);
1221 : (0,308:4,64);
1222 : (0,308:4,64);
1226 : (0,308:4,64);
1229 : (0,308:4,64);
1230 : (0,308:4,64);
1233 : (0,308:4,64);
1234 : (0,308:4,64);
1235 : (0,308:4,64);
1300 : (0,310:4,64);
1311 : (0,314:6,65);
1323 : (0,320:8,68);
1325 : (0,310:4,71);
1326 : (0,310:4,71);
1327 : (0,310:4,71);
1328 : (0,310:4,71);
1329 : (0,310:4,71);
1330 : (0,310:4,71);
1331 : (0,310:4,71);
1332 : (0,310:4,71);
1333 : (0,310:4,71);
1343 : (0,308:4,75);
1383 : (0,308:4,78);
1384 : (0,308:4,78);
1387 : (0,308:4,78);
1388 : (0,308:4,78);
1389 : (0,308:4,78);
1393 : (0,308:4,78);
1396 : (0,308:4,78);
1397 : (0,308:4,78);
1400 : (0,308:4,78);
1401 : (0,308:4,78);
1402 : (0,308:4,78);
1588 : (0,306:9,89);
1589 : (0,306:9,89);
1592 : (0,306:9,89);
1593 : (0,306:9,89);
1594 : (0,306:9,89);
1598 : (0,306:9,89);
1599 : (0,306:9,89);
1601 : (0,306:9,89);
1602 : (0,306:9,89);
1605 : (0,306:9,89);
1606 : (0,306:9,89);
1607 : (0,306:9,89);
1754 : (0,326:0,94);
2151 : (0,327:0,0) (0,327:0,95);
2152 : (0,327:0,95);
2155 : (0,326:0,94);
2156 : (0,306:21,89) (0,273:24,0) (0,306:23,89);
2157 : (0,306:23,89) (0,306:9,89);
2158 : (0,306:31,84) (0,306:30,0) (0,273:24,0);
2159 : (0,308:16,78) (0,308:18,78);
2160 : (0,308:18,78) (0,308:4,78);
2161 : (0,308:24,73) (0,308:23,0) (0,273:26,0);
2163 : (0,320:8,68);
2164 : (0,314:6,65);
2165 : (0,310:27,64) (0,273:26,0) (0,310:26,64) (0,310:26,0);
2166 : (0,310:26,64);
2167 : (0,310:14,64);
2168 : (0,310:26,64) (0,310:18,64) (0,310:4,64);
2169 : (0,310:4,64);
2170 : (0,310:15,64) (0,273:24,0) (0,310:14,64) (0,310:14,0);
2171 : (0,310:14,64);
2172 : (0,310:14,64) (0,310:9,0) (0,271:27,0);
2173 : (0,308:9,62) (0,273:26,0);
2174 : (0,306:25,89) (0,271:41,0);
2180 : (0,306:14,60) (0,273:24,0);
2181 : (0,286:28,55) (0,273:24,0) (0,286:30,55);
2182 : (0,286:30,55) (0,286:16,55);
2183 : (0,286:36,50) (0,286:35,0) (0,273:24,0);
2184 : (0,288:16,44) (0,288:18,44);
2185 : (0,288:18,44) (0,288:4,44);
2186 : (0,288:24,39) (0,288:23,0) (0,273:26,0);
2188 : (0,300:5,34);
2189 : (0,294:6,31);
2190 : (0,290:30,30) (0,273:26,0) (0,290:29,30) (0,290:29,0);
2191 : (0,290:29,30);
2192 : (0,290:17,30);
2193 : (0,290:29,30) (0,290:21,30) (0,290:4,30);
2194 : (0,290:4,30);
2195 : (0,290:18,30) (0,273:24,0) (0,290:17,30) (0,290:17,0);
2196 : (0,290:17,30);
2197 : (0,290:17,30);
2198 : (0,288:9,28) (0,273:26,0);
2205 : (0,286:21,26) (0,273:24,0);
2206 : (0,284:21,25) (0,284:21,0) (0,274:24,0);
2207 : (0,283:21,24) (0,274:24,0);
2208 : (0,277:26,19) (0,273:24,0) (0,277:27,19);
2209 : (0,277:27,19) (0,277:16,19);
2210 : (0,277:32,14) (0,277:31,0) (0,273:24,0);
2211 : (0,280:10,12);
2212 : (0,273:24,0) (0,277:20,10);
2218 : (0,276:16,9);
2219 : (0,274:40,7) (0,274:40,0) (0,274:24,0);
2220 : (0,274:40,6) (0,274:40,0) (0,274:24,0);
2221 : (0,274:40,5) (0,274:40,0) (0,274:24,0);
2222 : (0,274:24,0) (0,274:40,4);
2224 : (0,271:27,0) (0,271:17,0);
2225 : (0,271:5,0);
2226 : (0,271:41,0) (0,271:33,0);
2482 : (0,280:42,0);
2484 : (0,280:60,0);
2488 : (0,280:65,0);
2521 : (0,300:37,0);
2523 : (0,300:55,0);
2525 : (0,300:60,0);
2528 : (0,273:26,0);
2542 : (0,294:38,0);
2544 : (0,294:56,0);
2546 : (0,294:61,0);
2575 : (0,274:24,0);
2617 : (0,320:40,0);
2619 : (0,320:58,0);
2621 : (0,320:63,0);
2624 : (0,273:26,0);
2641 : (0,314:38,0);
2643 : (0,314:56,0);
2645 : (0,314:61,0);
2701 : (0,274:24,0);
2709 : (0,276:61,0);
2711 : (0,326:32,0);
2713 : (0,326:50,0);
2715 : (0,280:65,0);
2717 : (0,274:24,0);
2725 : (0,326:55,0);
2780 : (0,290:17,0);
2785 : (0,290:29,0) (0,290:21,0) (0,290:4,0);
2838 : (0,310:14,0);
2843 : (0,310:26,0) (0,310:18,0) (0,310:4,0);
2872 : (0,310:14,0);
2881 : (0,306:21,0) (0,306:23,0);

