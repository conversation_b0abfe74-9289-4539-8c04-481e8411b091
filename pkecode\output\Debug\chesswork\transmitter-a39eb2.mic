
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-a39eb2 mrk3


// m8;   next: m9 (next offset: 3)
000000  2 0  "0001011111111111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (14,R7,R7); 
000001  0 0  "1111111111110010"   // /
000002  1 0  "0110101101000011"   // (DM[4]) = _pl_rd_res_reg_const_store_2_B2 (RbL[0],4,DM[4],R7); 

// m9 chess_separator_scheduler;   next: m10 (next offset: 3)

// m10;   next: m11 (next offset: 4)
000003  1 0  "0110111101100010"   // (DM[6]) = _pl_rd_res_reg_const_store_1_B2 (R46[0],6,DM[6],R7); 

// m11 chess_separator_scheduler;   next: m12 (next offset: 4)

// m12;   next: m13 (next offset: 6)
000004  2 0  "0110101010000011"   // (DM[10]) = store_const__pl_rd_res_reg_const_1_B2 (10,DM[10],R7); 
000005  0 0  "0000000000001010"   // /

// m13 chess_separator_scheduler;   next: m14 (next offset: 6)

// m14;   next: m15 (next offset: 8)
000006  2 0  "0110101010000011"   // (DM[10]) = store_const__pl_rd_res_reg_const_4_B2 (11,DM[10],R7); 
000007  0 0  "0000000000001011"   // /

// m15 chess_separator_scheduler;   next: m16 (next offset: 8)

// m16;   next: m17 (next offset: 10)
000008  2 0  "0110101010000011"   // (DM[10]) = store_const__pl_rd_res_reg_const_4_B2 (12,DM[10],R7); 
000009  0 0  "0000000000001100"   // /

// m17 chess_separator_scheduler;   next: m18 (next offset: 10)

// m18;   next: m20 (next offset: 12)
000010  2 0  "0110101010000011"   // (DM[10]) = store_const__pl_rd_res_reg_const_4_B2 (13,DM[10],R7); 
000011  0 0  "0000000000001101"   // /

// m20 chess_separator_scheduler;   next: m21 (next offset: 12)

// m21;   next: m22 (next offset: 18)
000012  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (10,R7); 
000013  0 0  "0000000000001010"   // /
000014  2 0  "0110110000000000"   // (RwL[0]) = const_4_B1 (); 
000015  0 0  "0000000000011001"   // /
000016  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000017  0 0  "0000000000000000"   // /

// m22 subroutine call;   next: m26 (next offset: 18)

// m26;   next: m33 (next offset: 21)
000018  1 0  "0110101110000111"   // (DM[8]) = store_const__pl_rd_res_reg_const_1_B3 (8,DM[8],R7); 
000019  2 0  "0110110000000100"   // (R46[0]) = const_3_B1 (0); 
000020  0 0  "0000000000000000"   // /

// m33;   next: m34 (next offset: 25)
000021  1 0  "0110100010011000" .loop_nesting 1    // (RbL[0]) = const_1_B2 (); 
000022  1 0  "0110110010001001"   // (RwL[1]) = const_2_B2 (); 
000023  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000024  0 0  "0000000000000000"   // /

// m34 subroutine call;   next: m38 (next offset: 25)

// m38;   next: m40 (next offset: 26)
000025  1 0  "0001001110001111"   // (DM[8],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B3 (8,DM[8],DM[8],R7); 

// m40 chess_separator_scheduler;   next: m275 (next offset: 26)

// m275;   next: m46, jump target: m33 (next offset: 31)
000026  2 0  "0100000000110011"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_2_B1 (8,DM[8],R7); 
000027  0 0  "0000100000011000"   // /
000028  2 0  "0110110000000100"   // (R46[0]) = const_3_B1 (0); 
000029  0 0  "0000000000000000"   // /
000030  1 0  "0101100011110111"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-9); 

// m46;   next: m47 (next offset: 33)
000031  2 0  "0110101010001011" .loop_nesting 0    // (DM[10]) = store_const__pl_rd_res_reg_const_3_B2 (10,DM[10],R7); 
000032  0 0  "0000000000001010"   // /

// m47 chess_separator_scheduler;   next: m48 (next offset: 33)

// m48;   next: m49 (next offset: 35)
000033  2 0  "0110101010001011"   // (DM[10]) = store_const__pl_rd_res_reg_const_2_B2 (11,DM[10],R7); 
000034  0 0  "0000000000001011"   // /

// m49 chess_separator_scheduler;   next: m50 (next offset: 35)

// m50;   next: m51 (next offset: 36)
000035  1 0  "0110101110000111"   // (DM[8]) = store_const__pl_rd_res_reg_const_1_B3 (8,DM[8],R7); 

// m51 chess_separator_scheduler;   next: m52 (next offset: 36)

// m52;   next: m59 (next offset: 38)
000036  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (10,R7); 
000037  0 0  "0000000000001010"   // /

// m59;   next: m60 (next offset: 40)
000038  2 0  "0110101010000011" .loop_nesting 1    // (DM[9]) = store_const__pl_rd_res_reg_const_1_B2 (9,DM[9],R7); 
000039  0 0  "0000000000001001"   // /

// m60 chess_separator_scheduler;   next: m279 (next offset: 40)

// m279;   next: m294 (next offset: 44)
000040  1 0  "0110101100000111"   // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B2 (8,DM[8],R7); 
000041  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000042  1 0  "0001010001000100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_1_B1 (R46[0],RwL[0]); 
000043  1 0  "0110110101100011"   // (__spill_DMw[0]) = stack_store_indirect_bndl_B2 (R46[0],R7); 

// m294;   next: m76, jump target: m72 (next offset: 52)
000044  2 0  "0110101000000011" .loop_nesting 2    // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B1 (9,DM[9],R7); 
000045  0 0  "0000000000001001"   // /
000046  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000047  1 0  "0110100100101000"   // (RbH[1]) = load_2_B1 (R46[0],DM[10]); 
000048  1 0  "0110110001000100"   // R46[0] = RwL[0]; 
000049  2 0  "0110001001101000"   // (nz_flag) = _pl_const_load__ad_cmp_const_1_B2 (R46[0],RbH[1],0,DM); 
000050  0 0  "0000000000000000"   // /
000051  1 0  "0101000100001000"   // () = cc_ne__jump_const_1_B1 (nz_flag,8); 

// m76;   next: m77 (next offset: 58)
000052  1 0  "0110100010011000"   // (RbL[0]) = const_1_B2 (); 
000053  1 0  "0110110010001001"   // (RwL[1]) = const_2_B2 (); 
000054  2 0  "0110110000000100"   // (R46[0]) = const_3_B1 (0); 
000055  0 0  "0000000000000000"   // /
000056  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000057  0 0  "0000000000000000"   // /

// m77 subroutine call;   next: m359 (next offset: 58)

// m359, jump target: m82 (next offset: 59)
000058  1 0  "0101101000000111"   // () = jump_const_1_B1 (7); 

// m72;   next: m73 (next offset: 65)
000059  1 0  "0110100010011000"   // (RbL[0]) = const_1_B2 (); 
000060  1 0  "0110110010001001"   // (RwL[1]) = const_2_B2 (); 
000061  2 0  "0110110000000100"   // (R46[0]) = const_3_B1 (0); 
000062  0 0  "0000000000000000"   // /
000063  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000064  0 0  "0000000000000000"   // /

// m73 subroutine call;   next: m361 (next offset: 65)

// m361;   next: m82 (next offset: 65)

// m82;   next: m84 (next offset: 67)
000065  2 0  "0001001010001011"   // (DM[9],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B2 (9,DM[9],DM[9],R7); 
000066  0 0  "0000000000001001"   // /

// m84 chess_separator_scheduler;   next: m299 (next offset: 67)

// m299;   next: m92, jump target: m294 (next offset: 72)
000067  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (9,R7); 
000068  0 0  "0000000000001001"   // /
000069  1 0  "0100000111000000"   // (c_flag,nz_flag,o_flag) = load_cmp_const_1_B2 (R46[0],DM[9]); 
000070  1 0  "0110110100100011"   // (R46[0]) = stack_load_indirect_bndl_B2 (__spill_DMw[0],R7); 
000071  1 0  "0101100011100101"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-27); 

// m92;   next: m94 (next offset: 73)
000072  1 0  "0001001110001111" .loop_nesting 1    // (DM[8],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B3 (8,DM[8],DM[8],R7); 

// m94 chess_separator_scheduler;   next: m304 (next offset: 73)

// m304;   next: m100, jump target: m59 (next offset: 77)
000073  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (10,R7); 
000074  0 0  "0000000000001010"   // /
000075  1 0  "0100001110010111"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B3 (8,DM[8],R7); 
000076  1 0  "0101100011011010"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-38); 

// m100;   next: m101 (next offset: 78)
000077  1 0  "0110101110000111" .loop_nesting 0    // (DM[8]) = store_const__pl_rd_res_reg_const_1_B3 (8,DM[8],R7); 

// m101 chess_separator_scheduler;   next: m148 (next offset: 78)

// m148, jump target: m5 (next offset: 81)
000078  1 0  "0110101100000011"   // (RbL[0]) = load__pl_rd_res_reg_const_1_B2 (4,DM[4],R7); 
000079  1 0  "0110100101000011"   // (__spill_DM[0]) = stack_store_indirect_bndl_B1 (RbL[0],R7); 
000080  1 0  "0101101000100110"   // () = jump_const_1_B1 (38); 

// m109;   next: m110 (next offset: 83)
000081  2 0  "0110101010000011" .loop_nesting 1    // (DM[9]) = store_const__pl_rd_res_reg_const_1_B2 (9,DM[9],R7); 
000082  0 0  "0000000000001001"   // /

// m110 chess_separator_scheduler;   next: m321 (next offset: 83)

// m321;   next: m336 (next offset: 88)
000083  1 0  "0110101100000111"   // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B2 (8,DM[8],R7); 
000084  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000085  1 0  "0110110001000100"   // R46[0] = RwL[0]; 
000086  1 0  "0001011100100010"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_load__pl_rd_res_reg_const_1_B2 (R46[0],6,DM[6],R7); 
000087  1 0  "0110111101100000"   // (__spill_DMw[2]) = stack_store_bndl_B4 (R46[0],R7,2); 

// m336;   next: m126, jump target: m122 (next offset: 96)
000088  2 0  "0110101000000011" .loop_nesting 2    // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B1 (9,DM[9],R7); 
000089  0 0  "0000000000001001"   // /
000090  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000091  1 0  "0110100100101000"   // (RbH[1]) = load_1_B1 (R46[0],DM,DM,DM,DM,DM,DM); 
000092  1 0  "0110110001000100"   // R46[0] = RwL[0]; 
000093  2 0  "0110001001101000"   // (nz_flag) = _pl_const_load__ad_cmp_const_1_B2 (R46[0],RbH[1],0,DM); 
000094  0 0  "0000000000000000"   // /
000095  1 0  "0101000100001000"   // () = cc_ne__jump_const_1_B1 (nz_flag,8); 

// m126;   next: m127 (next offset: 102)
000096  1 0  "0110100010011000"   // (RbL[0]) = const_1_B2 (); 
000097  1 0  "0110110010001001"   // (RwL[1]) = const_2_B2 (); 
000098  2 0  "0110110000000100"   // (R46[0]) = const_3_B1 (0); 
000099  0 0  "0000000000000000"   // /
000100  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000101  0 0  "0000000000000000"   // /

// m127 subroutine call;   next: m360 (next offset: 102)

// m360, jump target: m132 (next offset: 103)
000102  1 0  "0101101000000111"   // () = jump_const_1_B1 (7); 

// m122;   next: m123 (next offset: 109)
000103  1 0  "0110100010011000"   // (RbL[0]) = const_1_B2 (); 
000104  1 0  "0110110010001001"   // (RwL[1]) = const_2_B2 (); 
000105  2 0  "0110110000000100"   // (R46[0]) = const_3_B1 (0); 
000106  0 0  "0000000000000000"   // /
000107  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000108  0 0  "0000000000000000"   // /

// m123 subroutine call;   next: m362 (next offset: 109)

// m362;   next: m132 (next offset: 109)

// m132;   next: m134 (next offset: 111)
000109  2 0  "0001001010001011"   // (DM[9],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B2 (9,DM[9],DM[9],R7); 
000110  0 0  "0000000000001001"   // /

// m134 chess_separator_scheduler;   next: m341 (next offset: 111)

// m341;   next: m142, jump target: m336 (next offset: 116)
000111  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (9,R7); 
000112  0 0  "0000000000001001"   // /
000113  1 0  "0100000111000000"   // (c_flag,nz_flag,o_flag) = load_cmp_const_1_B2 (R46[0],DM[9]); 
000114  1 0  "0110111100100000"   // (R46[0]) = stack_load_bndl_B4 (__spill_DMw[2],R7,2); 
000115  1 0  "0101100011100101"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-27); 

// m142;   next: m5 (next offset: 118)
000116  1 0  "0001001110001111" .loop_nesting 1    // (DM[8],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B3 (8,DM[8],DM[8],R7); 
000117  1 0  "0110100100000011"   // (RbL[0]) = stack_load_indirect_bndl_B1 (__spill_DM[0],R7); 

// m5;   next: m346 (next offset: 118)

// m346;   next: m150, jump target: m109 (next offset: 120)
000118  1 0  "0100001101000111"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_1_B2 (RbL[0],8,DM[8],R7); 
000119  1 0  "0101100011011010"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-38); 

// m150;   next: m151 (next offset: 126)
000120  1 0  "0110100010011000" .loop_nesting 0    // (RbL[0]) = const_1_B2 (); 
000121  1 0  "0110110010001001"   // (RwL[1]) = const_2_B2 (); 
000122  2 0  "0110110000000100"   // (R46[0]) = const_3_B1 (0); 
000123  0 0  "0000000000000000"   // /
000124  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000125  0 0  "0000000000000000"   // /

// m151 subroutine call;   next: m154 (next offset: 126)

// m154 (next offset: /)
000126  2 0  "0001011111111111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (14,R7,R7); 
000127  0 0  "0000000000001110"   // /
000128  1 0  "0001101111000100"   // () = ret_1_B1 (); 

