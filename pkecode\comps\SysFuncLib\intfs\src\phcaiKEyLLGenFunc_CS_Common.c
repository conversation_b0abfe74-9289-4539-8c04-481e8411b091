/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_Common.c 12934 2018-04-04 09:11:45Z dep10330 $
  $Revision: 12934 $
*/

/**
 * @file
 * Implementation of the stubs to call common KEyLink Lite General Functions
 * placed in ROM.
 */

#include "phcaiKEyLLGenFunc_CS_common.h"
#include "phcaiKEyLLGenFunc_CS_utils.h"
#include "phcaiKEyLLGenFunc_SCI.h"

/*
  Change Log
  2017-10-18 (MMr):
  - phcaiKEyLLGenFunc_CS_SetAdc_LIntSwCon: moved from _ADC to _Common module and renamed to
    phcaiKEyLLGenFunc_CS_LIntSwCon.
  2018-04-04 (MMr):
  - phcaiKEyLLGenFunc_CS_EROM_enable_PF2: added (for SRX types)

 */

void phcaiKEyLLGenFunc_CS_MDI_Print(const string_t* const text)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_MDI_PRINT;
  phcaiKEyLLGenFunc_Func_Params.params.mdi_prnt.input_string = text;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_MDI_PrintLn(const string_t* const text)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_MDI_PRINTLN;
  phcaiKEyLLGenFunc_Func_Params.params.mdi_prntln.input_string = text;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_GetVersion(phcaiMRK3Versions_Info_t* const version)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_GET_VERSION;
  phcaiKEyLLGenFunc_Func_Params.params.get_version.version = version;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_SetClkCon(const uint8_t clkcon)
{
  call_syscall_8(1, clkcon);
}

#if defined(PHFL_CONFIG_WORKAROUND_FOR_VBATREG_READ) && (PHFL_CONFIG_WORKAROUND_FOR_VBATREG_READ == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_SetClkCon_safe(const uint8_t clkcon)
{
  uint8_t new_clkcon0;
  if ((clkcon & 0x3FU) == 0x20U)
  {
    new_clkcon0 = clkcon | 0x1U;
  } else {
    new_clkcon0 = clkcon;
  }
  call_syscall_8(1, new_clkcon0);
}
#else
void phcaiKEyLLGenFunc_CS_SetClkCon_safe(const uint8_t clkcon)
{
  phcaiKEyLLGenFunc_CS_SetClkCon(clkcon);
}
#endif

#if defined(PHFL_CONFIG_HAVE_CLKCON4_CPUAUXDIVSEL) && (PHFL_CONFIG_HAVE_CLKCON4_CPUAUXDIVSEL == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_SetAuxDivClk(const uint8_t value)
{
  call_syscall_8((uint8_t)6, value);
}
#endif

void phcaiKEyLLGenFunc_CS_SI_Init(uint32_t si_field[8], uint8_t page)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_SI_INIT;
  phcaiKEyLLGenFunc_Func_Params.params.si_init.si_field = si_field;
  phcaiKEyLLGenFunc_Func_Params.params.si_init.page = page;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_SI_Get(uint32_t si_field[8], uint8_t* const si)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_SI_GET;
  phcaiKEyLLGenFunc_Func_Params.params.si_get.si_field = si_field;
  phcaiKEyLLGenFunc_Func_Params.params.si_get.si = si;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_SI_Inc(uint32_t si_field[8], uint8_t page)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_SI_INC;
  phcaiKEyLLGenFunc_Func_Params.params.si_inc.si_field = si_field;
  phcaiKEyLLGenFunc_Func_Params.params.si_inc.page = page;
  call_syscall(5);
}

SI_RESULT_t phcaiKEyLLGenFunc_CS_SI_Init_Ext(uint32_t si_field[8], uint16_t page, uint8_t maxnumrepair )
{
  phcaiKEyLLGenFunc_Func_Params.function_code              = KEYLL_FUNC_CODE_SI_INIT_EXT;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.si_field     = si_field;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.page         = page;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.maxnumrepair = maxnumrepair;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.si_ext.result;
}

#if (defined(PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS) && (PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS == CONFIG_YES))
SI_RESULT_t phcaiKEyLLGenFunc_CS_SI_Init_Ext_NoWait(uint32_t si_field[8], uint16_t page, uint8_t maxnumrepair )
{
  phcaiKEyLLGenFunc_Func_Params.function_code              = KEYLL_FUNC_CODE_SI_INIT_EXT_NOWAIT;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.si_field     = si_field;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.page         = page;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.maxnumrepair = maxnumrepair;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.si_ext.result;
}
#endif

SI_RESULT_t phcaiKEyLLGenFunc_CS_SI_Inc_Ext( uint32_t si_field[8], uint16_t page )
{
  phcaiKEyLLGenFunc_Func_Params.function_code              = KEYLL_FUNC_CODE_SI_INC_EXT;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.si_field     = si_field;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.page         = page;
  // note: si_ext.maxnumrepair not used
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.si_ext.result;
}

#if (defined(PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS) && (PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS == CONFIG_YES))
void phcaiKEyLLGenFunc_CS_SI_Inc_Ext_NoWait( uint32_t si_field[8], uint16_t page )
{
  phcaiKEyLLGenFunc_Func_Params.function_code              = KEYLL_FUNC_CODE_SI_INC_EXT_NOWAIT;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.si_field     = si_field;
  phcaiKEyLLGenFunc_Func_Params.params.si_ext.page         = page;
  // note: si_ext.maxnumrepair not used
  call_syscall(5);
}
#endif

void phcaiKEyLLGenFunc_CS_MdiClkSel(uint8_t val)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_SET_MDICLKSEL;
  phcaiKEyLLGenFunc_Func_Params.params.set_mdiclksel.val = val;
  call_syscall(5);
}

#if (defined(CONFIG_HAVE_LFCLOCK_DOUBLER) && (CONFIG_HAVE_LFCLOCK_DOUBLER == CONFIG_YES))
void phcaiKEyLLGenFunc_CS_SetLfclkx2Dis(bool_t set)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_SET_LFCLKX2DIS;
  phcaiKEyLLGenFunc_Func_Params.params.set_lfclkx2dis.set = set;
  call_syscall(5);
}
#else
void phcaiKEyLLGenFunc_CS_SetLfclkx2Dis(bool_t set)
{
}
#endif

#if defined(PHFL_CONFIG_HAVE_READ_EROM) && (PHFL_CONFIG_HAVE_READ_EROM == CONFIG_YES)
eeprom_write_error_t phcaiKEyLLGenFunc_CS_EROM_read(
    uint16_t* const ram_buffer, uint16_t const erom_start_word_addr, const uint16_t number_of_bytes) {
    phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_EROM_READ;
    phcaiKEyLLGenFunc_Func_Params.params.erom_rw.ram_buffer_start = ram_buffer;
    phcaiKEyLLGenFunc_Func_Params.params.erom_rw.erom_physical_start = erom_start_word_addr;
    phcaiKEyLLGenFunc_Func_Params.params.erom_rw.len = number_of_bytes;
    call_syscall(5);
    return phcaiKEyLLGenFunc_Func_Params.params.erom_rw.result;
}
#endif

#if defined(PHFL_CONFIG_HAVE_WRITE_EROM) && (PHFL_CONFIG_HAVE_WRITE_EROM == CONFIG_YES)
eeprom_write_error_t phcaiKEyLLGenFunc_CS_EROM_write(
  uint16_t* const ram_buffer, uint16_t const erom_start_word_addr, const uint16_t number_of_bytes) {
    phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_EROM_WRITE;
    phcaiKEyLLGenFunc_Func_Params.params.erom_rw.ram_buffer_start = ram_buffer;
    phcaiKEyLLGenFunc_Func_Params.params.erom_rw.erom_physical_start = erom_start_word_addr;
    phcaiKEyLLGenFunc_Func_Params.params.erom_rw.len = number_of_bytes;
    call_syscall(5);
    return phcaiKEyLLGenFunc_Func_Params.params.erom_rw.result;
}
#endif

#if (defined(PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL) && \
  (PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL == CONFIG_YES))
void phcaiKEyLLGenFunc_CS_EROM_enable_PF2(const bool_t enabled)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_EROM_ENABLE_PF2;
  phcaiKEyLLGenFunc_Func_Params.params.erom_enable.value = enabled;
  call_syscall(5);
}
#endif

void phcaiKEyLLGenFunc_CS_LIntSwCon(const bool_t set)
{
  call_syscall_8((uint8_t)5, ((set == TRUE) ? (uint8_t)1 : (uint8_t)0));
}

