
// File generated by noodle version P-2019.09#78e58cd307#210222, Sat Mar  2 20:51:11 2024
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/transmitter.c mrk3

chess_source_level_debug

             __schar_DM9 : _basic(DM9,1,1) __schar;
                int8_DM9 : _typedef(DM9,1,1) int8 $__schar_DM9;
                  bool__ : _basic() bool;
               __cchar__ : _basic() __cchar;
               __schar__ : _basic() __schar;
               __uchar__ : _basic() __uchar;
              __sshort__ : _basic() __sshort;
              __ushort__ : _basic() __ushort;
                __sint__ : _basic() __sint;
                __uint__ : _basic() __uint;
           __slonglong__ : _basic() __slonglong;
           __ulonglong__ : _basic() __ulonglong;
               __fhalf__ : _basic() __fhalf;
                int16___ : _basic() int16_;
                uint1___ : _basic() uint1_;
                uint2___ : _basic() uint2_;
                 int8___ : _basic() int8_;
               __Pvoid__ : _basic() __Pvoid;
               __slong__ : _basic() __slong;
               __ulong__ : _basic() __ulong;
               float24__ : _basic() float24;
               float32__ : _typedef() float32 $__ulong__;
              __ffloat__ : _basic() __ffloat;
             __fdouble__ : _basic() __fdouble;
         __flongdouble__ : _basic() __flongdouble;
             __uchar_DM9 : _basic(DM9,1,1) __uchar;
             uint8_t_DM9 : _typedef(DM9,1,1) uint8_t $__uchar_DM9;
phcaiKEyLLGenFunc_SCI_CallerProxy_function_code_DM9 : _enum(DM9,1,1) phcaiKEyLLGenFunc_SCI_CallerProxy_function_code $__uchar_DM9 {
                     KEYLL_FUNC_CODE_AES_LOAD_KEY = 0;
                     KEYLL_FUNC_CODE_AES_LOAD_DATA = 1;
                     KEYLL_FUNC_CODE_AES_INIT = 2;
                     KEYLL_FUNC_CODE_AES_ENCRYPT = 3;
                     KEYLL_FUNC_CODE_SI_INIT = 4;
                     KEYLL_FUNC_CODE_SI_GET = 5;
                     KEYLL_FUNC_CODE_SI_INC = 6;
                     KEYLL_FUNC_CODE_MDI_PRINT = 7;
                     KEYLL_FUNC_CODE_MDI_PRINTLN = 8;
                     KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE = 9;
                     KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE_LE = 10;
                     KEYLL_FUNC_CODE_ULPEE_READ_PAGE = 11;
                     KEYLL_FUNC_CODE_ULPEE_READ_PAGE_LE = 12;
                     KEYLL_FUNC_CODE_ULPEE_READ_BYTES = 13;
                     KEYLL_FUNC_CODE_SET_CLKCON = 14;
                     KEYLL_FUNC_CODE_SET_VBATRGL = 15;
                     KEYLL_FUNC_CODE_RESET_VBAT = 16;
                     KEYLL_FUNC_CODE_SET_BATPOR_FLAG = 17;
                     KEYLL_FUNC_CODE_IMMO_INIT = 18;
                     KEYLL_FUNC_CODE_IMMO_RESET = 19;
                     KEYLL_FUNC_CODE_IMMO_RECEIVE_CMD = 20;
                     KEYLL_FUNC_CODE_IMMO_EXECUTE_CMD = 21;
                     KEYLL_FUNC_CODE_EEPROM_EN = 22;
                     KEYLL_FUNC_CODE_EEPROM_DIS = 23;
                     KEYLL_FUNC_CODE_EEPROM_PROG = 24;
                     KEYLL_FUNC_CODE_GET_VERSION = 25;
                     SMART2_FUNC_CODE_XO_START_UP = 26;
                     SMART2_FUNC_CODE_PLL_START_UP = 27;
                     KEYLL_FUNC_CODE_ULPEE_SET_PROGEN = 28;
                     KEYLL_FUNC_CODE_SET_LFCLKX2DIS = 29;
                     KEYLL_FUNC_CODE_SET_MDICLKSEL = 30;
                     SMART2_FUNC_CODE_SET_RFGATE = 31;
                     SMART2_FUNC_CODE_SET_PAINGATE = 32;
                     SMART2_FUNC_CODE_SET_CP_ICP = 33;
                     SMART2_FUNC_CODE_SET_PTRIM_XO = 34;
                     SMART2_FUNC_CODE_SET_PTRIM_PLL = 35;
                     SMART2_FUNC_CODE_SET_PTRIM_HS = 36;
                     KEYLL_FUNC_CODE_SI_INIT_EXT = 37;
                     KEYLL_FUNC_CODE_SI_INC_EXT = 38;
                     KEYLL_FUNC_CODE_EROM_READ = 39;
                     KEYLL_FUNC_CODE_EROM_WRITE = 40;
                     SMART2_FUNC_CODE_SET_PA_BITFIELD = 41;
                     SMART2_FUNC_CODE_GET_PA_BITFIELD = 42;
                     UHF_FUNC_CODE_LOAD_CP_ICP_TRIM = 43;
                     KEYLL_FUNC_CODE_AES_DISABLE = 44;
                     ADC_ENABLE_EXT_INPUT = 45;
                     ADC_ALLOW_POWERONOFF = 46;
                     KEYLL_FUNC_CODE_WRITE_ULPEE_3D4 = 47;
                     KEYLL_FUNC_CODE_CLEAR_VDDABRNTRIM = 48;
                     KEYLL_FUNC_CODE_IIU_RECEIVE = 49;
                     KEYLL_FUNC_CODE_IIU_SEND = 50;
                     KEYLL_FUNC_CODE_IIU_TIMX_SFR_SET = 51;
                     KEYLL_FUNC_CODE_SI_INIT_EXT_NOWAIT = 52;
                     KEYLL_FUNC_CODE_SI_INC_EXT_NOWAIT = 53;
                     KEYLL_FUNC_CODE_UHF_FORCE_XO_READY = 54;
                     KEYLL_FUNC_CODE_UHF_FORCE_PLL_LOCK_DETECTED = 55;
                     KEYLL_FUNC_CODE_IIU_CAT_INIT = 56;
                     KEYLL_FUNC_CODE_IIU_CAT_SEND = 57;
                     KEYLL_FUNC_CODE_IIU_CAT_SEND_MUTED = 58;
                     KEYLL_FUNC_CODE_EROM_ENABLE_PF2 = 59;
                 }
phcaiKEyLLGenFunc_SCI_CallerProxy_function_code_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_CallerProxy_function_code_t $phcaiKEyLLGenFunc_SCI_CallerProxy_function_code_DM9;
              __uchar_DM : _basic(DM,1,1) __uchar;
              uint8_t_DM : _typedef(DM,1,1) uint8_t $__uchar_DM;
             __Pvoid_DM9 : _basic(DM9,2,2) __Pvoid;
          __P__uchar_DM9 : _pointer(DM9,2,2) $__Pvoid_DM9 $uint8_t_DM;
phcaiKEyLLGenFunc_SCI_AES_Load_Key_DM9 : _struct(DM9,2,2) phcaiKEyLLGenFunc_SCI_AES_Load_Key {
                     key $__P__uchar_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_AES_Load_Key_t_DM9 : _typedef(DM9,2,2) phcaiKEyLLGenFunc_SCI_AES_Load_Key_t $phcaiKEyLLGenFunc_SCI_AES_Load_Key_DM9;
phcaiKEyLLGenFunc_SCI_AES_Load_Data_DM9 : _struct(DM9,4,2) phcaiKEyLLGenFunc_SCI_AES_Load_Data {
                     data $__P__uchar_DM9 @0;
                     len $uint8_t_DM9 @2;
                 }
phcaiKEyLLGenFunc_SCI_AES_Load_Data_t_DM9 : _typedef(DM9,4,2) phcaiKEyLLGenFunc_SCI_AES_Load_Data_t $phcaiKEyLLGenFunc_SCI_AES_Load_Data_DM9;
phcaiKEyLLGenFunc_SCI_AES_Encrypt_DM9 : _struct(DM9,2,2) phcaiKEyLLGenFunc_SCI_AES_Encrypt {
                     output_vector $__P__uchar_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_AES_Encrypt_t_DM9 : _typedef(DM9,2,2) phcaiKEyLLGenFunc_SCI_AES_Encrypt_t $phcaiKEyLLGenFunc_SCI_AES_Encrypt_DM9;
              __ulong_DM : _basic(DM,4,2) __ulong;
             uint32_t_DM : _typedef(DM,4,2) uint32_t $__ulong_DM;
          __P__ulong_DM9 : _pointer(DM9,2,2) $__Pvoid_DM9 $uint32_t_DM;
phcaiKEyLLGenFunc_SCI_SI_Get_DM9 : _struct(DM9,4,2) phcaiKEyLLGenFunc_SCI_SI_Get {
                     si_field $__P__ulong_DM9 @0;
                     si $__P__uchar_DM9 @2;
                 }
phcaiKEyLLGenFunc_SCI_SI_Get_t_DM9 : _typedef(DM9,4,2) phcaiKEyLLGenFunc_SCI_SI_Get_t $phcaiKEyLLGenFunc_SCI_SI_Get_DM9;
phcaiKEyLLGenFunc_SCI_SI_Inc_DM9 : _struct(DM9,4,2) phcaiKEyLLGenFunc_SCI_SI_Inc {
                     si_field $__P__ulong_DM9 @0;
                     page $uint8_t_DM9 @2;
                 }
phcaiKEyLLGenFunc_SCI_SI_Inc_t_DM9 : _typedef(DM9,4,2) phcaiKEyLLGenFunc_SCI_SI_Inc_t $phcaiKEyLLGenFunc_SCI_SI_Inc_DM9;
phcaiKEyLLGenFunc_SCI_SI_Init_DM9 : _struct(DM9,4,2) phcaiKEyLLGenFunc_SCI_SI_Init {
                     si_field $__P__ulong_DM9 @0;
                     page $uint8_t_DM9 @2;
                 }
phcaiKEyLLGenFunc_SCI_SI_Init_t_DM9 : _typedef(DM9,4,2) phcaiKEyLLGenFunc_SCI_SI_Init_t $phcaiKEyLLGenFunc_SCI_SI_Init_DM9;
              __cchar_DM : _basic(DM,1,1) __cchar;
             string_t_DM : _typedef(DM,1,1) string_t $__cchar_DM;
          __P__cchar_DM9 : _pointer(DM9,2,2) $__Pvoid_DM9 $string_t_DM;
phcaiKEyLLGenFunc_SCI_MDI_Prnt_DM9 : _struct(DM9,2,2) phcaiKEyLLGenFunc_SCI_MDI_Prnt {
                     input_string $__P__cchar_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_MDI_Prnt_t_DM9 : _typedef(DM9,2,2) phcaiKEyLLGenFunc_SCI_MDI_Prnt_t $phcaiKEyLLGenFunc_SCI_MDI_Prnt_DM9;
phcaiKEyLLGenFunc_SCI_MDI_PrntLn_DM9 : _struct(DM9,2,2) phcaiKEyLLGenFunc_SCI_MDI_PrntLn {
                     input_string $__P__cchar_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_MDI_PrntLn_t_DM9 : _typedef(DM9,2,2) phcaiKEyLLGenFunc_SCI_MDI_PrntLn_t $phcaiKEyLLGenFunc_SCI_MDI_PrntLn_DM9;
phcaiKEyLLGenFunc_SCI_Immo_Type_DM9 : _enum(DM9,1,1) phcaiKEyLLGenFunc_SCI_Immo_Type $__uchar_DM9 {
                     IMMO_HT2 = 0;
                     IMMO_HT2E = 1;
                     IMMO_HT3 = 2;
                     IMMO_HTAES = 3;
                     IMMO_HTPRO = 4;
                     IMMO_UNDEF = 5;
                 }
phcaiKEyLLGenFunc_SCI_Immo_Type_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_Immo_Type_t $phcaiKEyLLGenFunc_SCI_Immo_Type_DM9;
             error_t_DM9 : _enum(DM9,1,1) error_t $__uchar_DM9 {
                     SUCCESS = 0;
                     ERROR = 1;
                 }
phcaiKEyLLGenFunc_SCI_Immo_Init_DM9 : _struct(DM9,2,1) phcaiKEyLLGenFunc_SCI_Immo_Init {
                     immo_type $phcaiKEyLLGenFunc_SCI_Immo_Type_t_DM9 @0;
                     return_val $error_t_DM9 @1;
                 }
phcaiKEyLLGenFunc_SCI_Immo_Init_t_DM9 : _typedef(DM9,2,1) phcaiKEyLLGenFunc_SCI_Immo_Init_t $phcaiKEyLLGenFunc_SCI_Immo_Init_DM9;
phcaiKEyLLGenFunc_SCI_Immo_Reset_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_Immo_Reset {
                     return_val $error_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_Immo_Reset_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_Immo_Reset_t $phcaiKEyLLGenFunc_SCI_Immo_Reset_DM9;
phcaiKEyLLGenFunc_SCI_Immo_Rcv_DM9 : _struct(DM9,6,2) phcaiKEyLLGenFunc_SCI_Immo_Rcv {
                     rcv_buff $__P__uchar_DM9 @0;
                     len $__P__uchar_DM9 @2;
                     return_val $error_t_DM9 @4;
                 }
phcaiKEyLLGenFunc_SCI_Immo_Rcv_t_DM9 : _typedef(DM9,6,2) phcaiKEyLLGenFunc_SCI_Immo_Rcv_t $phcaiKEyLLGenFunc_SCI_Immo_Rcv_DM9;
phcaiKEyLLGenFunc_SCI_Immo_Exec_DM9 : _struct(DM9,4,2) phcaiKEyLLGenFunc_SCI_Immo_Exec {
                     rcv_data $__P__uchar_DM9 @0;
                     len $uint8_t_DM9 @2;
                 }
phcaiKEyLLGenFunc_SCI_Immo_Exec_t_DM9 : _typedef(DM9,4,2) phcaiKEyLLGenFunc_SCI_Immo_Exec_t $phcaiKEyLLGenFunc_SCI_Immo_Exec_DM9;
phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode_DM9 : _enum(DM9,1,1) phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode $__uchar_DM9 {
                     EE_RAM_AUT = 0;
                     EE_RAM_STR = 16;
                 }
phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode_t $phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode_DM9;
phcaiKEyLLGenFunc_SCI_EEPROM_Enable_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_EEPROM_Enable {
                     eeprom_read_access_mode $phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_EEPROM_Enable_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_EEPROM_Enable_t $phcaiKEyLLGenFunc_SCI_EEPROM_Enable_DM9;
            __ushort_DM9 : _basic(DM9,2,2) __ushort;
            uint16_t_DM9 : _typedef(DM9,2,2) uint16_t $__ushort_DM9;
              bool_t_DM9 : _enum(DM9,1,1) bool_t $__uchar_DM9 {
                     FALSE = 0;
                     TRUE = 1;
                 }
  eeprom_write_error_DM9 : _enum(DM9,1,1) eeprom_write_error $__uchar_DM9 {
                     EE_WR_OK = 0;
                     EE_WR_BRN_ERR = 1;
                     EE_WR_HV_ERR = 2;
                     EE_WR_ADDR_ERR = 4;
                     EE_WR_LEN_ZERO = 8;
                     EE_WR_INVALID_START_ADDR = 24;
                     EE_WR_INVALID_END_ADDR = 40;
                     EE_WR_UNALIGNED_ERR = 56;
                     EE_WR_FORBIDDEN_ERR = 64;
                     EE_WR_INVALID_LEN_ERR = 72;
                 }
eeprom_write_error_t_DM9 : _typedef(DM9,1,1) eeprom_write_error_t $eeprom_write_error_DM9;
phcaiKEyLLGenFunc_SCI_EEPROM_Prog_DM9 : _struct(DM9,8,2) phcaiKEyLLGenFunc_SCI_EEPROM_Prog {
                     addr $__P__uchar_DM9 @0;
                     data $__P__uchar_DM9 @2;
                     len $uint16_t_DM9 @4;
                     wait $bool_t_DM9 @6;
                     return_val $eeprom_write_error_t_DM9 @7;
                 }
phcaiKEyLLGenFunc_SCI_EEPROM_Prog_t_DM9 : _typedef(DM9,8,2) phcaiKEyLLGenFunc_SCI_EEPROM_Prog_t $phcaiKEyLLGenFunc_SCI_EEPROM_Prog_DM9;
phcaiKEyLLGenFunc_SCI_ULPEE_WritePage_DM9 : _struct(DM9,6,2) phcaiKEyLLGenFunc_SCI_ULPEE_WritePage {
                     data $__P__uchar_DM9 @0;
                     addr $uint16_t_DM9 @2;
                     wait $bool_t_DM9 @4;
                     return_val $error_t_DM9 @5;
                 }
phcaiKEyLLGenFunc_SCI_ULPEE_WritePage_t_DM9 : _typedef(DM9,6,2) phcaiKEyLLGenFunc_SCI_ULPEE_WritePage_t $phcaiKEyLLGenFunc_SCI_ULPEE_WritePage_DM9;
phcaiKEyLLGenFunc_SCI_ULPEE_ReadPage_DM9 : _struct(DM9,4,2) phcaiKEyLLGenFunc_SCI_ULPEE_ReadPage {
                     data $__P__uchar_DM9 @0;
                     addr $uint16_t_DM9 @2;
                 }
phcaiKEyLLGenFunc_SCI_ULPEE_ReadPage_t_DM9 : _typedef(DM9,4,2) phcaiKEyLLGenFunc_SCI_ULPEE_ReadPage_t $phcaiKEyLLGenFunc_SCI_ULPEE_ReadPage_DM9;
phcaiKEyLLGenFunc_SCI_ULPEE_ReadBytes_DM9 : _struct(DM9,6,2) phcaiKEyLLGenFunc_SCI_ULPEE_ReadBytes {
                     data $__P__uchar_DM9 @0;
                     byte_addr $uint16_t_DM9 @2;
                     num_bytes $uint16_t_DM9 @4;
                 }
phcaiKEyLLGenFunc_SCI_ULPEE_ReadBytes_t_DM9 : _typedef(DM9,6,2) phcaiKEyLLGenFunc_SCI_ULPEE_ReadBytes_t $phcaiKEyLLGenFunc_SCI_ULPEE_ReadBytes_DM9;
             __ushort_DM : _basic(DM,2,2) __ushort;
             uint16_t_DM : _typedef(DM,2,2) uint16_t $__ushort_DM;
               __uint_DM : _basic(DM,2,2) __uint;
phcaiMRK3Versions_Id_t_DM : _enum(DM,2,2) phcaiMRK3Versions_Id_t $__uint_DM {
                     hardware_version = 0;
                     product_version = 1;
                     mrk3_cpu_id = 2;
                     system_rom_version = 4096;
                     boot_loader_version = 4097;
                     mdi_version = 4098;
                     hal_version = 4099;
                     gen_func_lib_version = 4100;
                     version_version = 4101;
                     hitag_cmn_version = 4102;
                     hitag2_version = 4103;
                     hitag2_ext_version = 4104;
                     hitag_pro_version = 4105;
                     hitag3_version = 4106;
                     iso14443_3_version = 4107;
                     hitag_aes_version = 4108;
                     custom_mod_version = 4112;
                     custom_immo_version = 4115;
                     ide_0 = 8192;
                     ide_1 = 8193;
                     ide_2 = 8194;
                     ide_3 = 8195;
                     unused_version_id = 61455;
                 }
phcaiMRK3Versions_Item_t_DM : _struct(DM,4,2) phcaiMRK3Versions_Item_t {
                     id $phcaiMRK3Versions_Id_t_DM @0;
                     major $uint8_t_DM @2;
                     minor $uint8_t_DM @3;
                 }
__A18phcaiMRK3Versions_Item_t_DM : _array(DM,72,2) [18] $phcaiMRK3Versions_Item_t_DM;
phcaiMRK3Versions_Info_t_DM : _struct(DM,74,2) phcaiMRK3Versions_Info_t {
                     nrItems $uint16_t_DM @0;
                     items $__A18phcaiMRK3Versions_Item_t_DM @2;
                 }
__PphcaiMRK3Versions_Info_t_DM9 : _pointer(DM9,2,2) $__Pvoid_DM9 $phcaiMRK3Versions_Info_t_DM;
phcaiKEyLLGenFunc_SCI_GetVersion_DM9 : _struct(DM9,2,2) phcaiKEyLLGenFunc_SCI_GetVersion {
                     version $__PphcaiMRK3Versions_Info_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_GetVersion_t_DM9 : _typedef(DM9,2,2) phcaiKEyLLGenFunc_SCI_GetVersion_t $phcaiKEyLLGenFunc_SCI_GetVersion_DM9;
phcaiKEyLLGenFunc_SCI_SetLfclkx2Dis_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetLfclkx2Dis {
                     set $bool_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_SetLfclkx2Dis_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetLfclkx2Dis_t $phcaiKEyLLGenFunc_SCI_SetLfclkx2Dis_DM9;
phcaiKEyLLGenFunc_SCI_set_MdiClkSel_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_set_MdiClkSel {
                     val $uint8_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_set_MdiClkSel_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_set_MdiClkSel_t $phcaiKEyLLGenFunc_SCI_set_MdiClkSel_DM9;
phcaiKEyLLGenFunc_SCI_XOStartUp_DM9 : _struct(DM9,3,1) phcaiKEyLLGenFunc_SCI_XOStartUp {
                     validate $bool_t_DM9 @0;
                     force_xo_ready $bool_t_DM9 @1;
                     return_val $error_t_DM9 @2;
                 }
phcaiKEyLLGenFunc_SCI_XOStartUp_t_DM9 : _typedef(DM9,3,1) phcaiKEyLLGenFunc_SCI_XOStartUp_t $phcaiKEyLLGenFunc_SCI_XOStartUp_DM9;
phcaiKEyLLGenFunc_SCI_PllStartUp_DM9 : _struct(DM9,4,1) phcaiKEyLLGenFunc_SCI_PllStartUp {
                     calibrate $bool_t_DM9 @0;
                     force_pll_lock_ready $bool_t_DM9 @1;
                     cal_idac_ctrl $uint8_t_DM9 @2;
                     return_val $error_t_DM9 @3;
                 }
phcaiKEyLLGenFunc_SCI_PllStartUp_t_DM9 : _typedef(DM9,4,1) phcaiKEyLLGenFunc_SCI_PllStartUp_t $phcaiKEyLLGenFunc_SCI_PllStartUp_DM9;
phcaiKEyLLGenFunc_SCI_SetRFGate_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetRFGate {
                     rfgate_off $bool_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_SetRFGate_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetRFGate_t $phcaiKEyLLGenFunc_SCI_SetRFGate_DM9;
phcaiKEyLLGenFunc_SCI_SetPAINGate_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetPAINGate {
                     paingate_off $bool_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_SetPAINGate_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetPAINGate_t $phcaiKEyLLGenFunc_SCI_SetPAINGate_DM9;
phcaiKEyLLGenFunc_SCI_SetCPICP_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetCPICP {
                     value $uint8_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_SetCPICP_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetCPICP_t $phcaiKEyLLGenFunc_SCI_SetCPICP_DM9;
phcaiKEyLLGenFunc_SCI_SetPTRIMXO_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetPTRIMXO {
                     value $uint8_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_SetPTRIMXO_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetPTRIMXO_t $phcaiKEyLLGenFunc_SCI_SetPTRIMXO_DM9;
phcaiKEyLLGenFunc_SCI_SetPTRIMPLL_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetPTRIMPLL {
                     value $uint8_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_SetPTRIMPLL_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetPTRIMPLL_t $phcaiKEyLLGenFunc_SCI_SetPTRIMPLL_DM9;
phcaiKEyLLGenFunc_SCI_SetPTRIMHS_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetPTRIMHS {
                     value $uint8_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_SetPTRIMHS_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_SetPTRIMHS_t $phcaiKEyLLGenFunc_SCI_SetPTRIMHS_DM9;
           SI_RESULT_DM9 : _enum(DM9,1,1) SI_RESULT $__uchar_DM9 {
                     SI_SUCCESS = 0;
                     SI_NOREPAIR = 1;
                     SI_PRGFAIL = 2;
                     SI_OVERFLOW = 3;
                     SI_INVPARAM = 4;
                 }
         SI_RESULT_t_DM9 : _typedef(DM9,1,1) SI_RESULT_t $SI_RESULT_DM9;
phcaiKEyLLGenFunc_SCI_SI_Ext_DM9 : _struct(DM9,6,2) phcaiKEyLLGenFunc_SCI_SI_Ext {
                     si_field $__P__ulong_DM9 @0;
                     page $uint16_t_DM9 @2;
                     maxnumrepair $uint8_t_DM9 @4;
                     result $SI_RESULT_t_DM9 @5;
                 }
phcaiKEyLLGenFunc_SCI_SI_Ext_t_DM9 : _typedef(DM9,6,2) phcaiKEyLLGenFunc_SCI_SI_Ext_t $phcaiKEyLLGenFunc_SCI_SI_Ext_DM9;
         __P__ushort_DM9 : _pointer(DM9,2,2) $__Pvoid_DM9 $uint16_t_DM;
phcaiKEyLLGenFunc_SCI_erom_rw_DM9 : _struct(DM9,8,2) phcaiKEyLLGenFunc_SCI_erom_rw {
                     ram_buffer_start $__P__ushort_DM9 @0;
                     erom_physical_start $uint16_t_DM9 @2;
                     len $uint16_t_DM9 @4;
                     result $eeprom_write_error_t_DM9 @6;
                 }
phcaiKEyLLGenFunc_SCI_erom_rw_t_DM9 : _typedef(DM9,8,2) phcaiKEyLLGenFunc_SCI_erom_rw_t $phcaiKEyLLGenFunc_SCI_erom_rw_DM9;
phcaiKEyLLGenFunc_SCI_EnableDCBUS_ext_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_EnableDCBUS_ext {
                     enable $bool_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_EnableDCBUS_ext_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_EnableDCBUS_ext_t $phcaiKEyLLGenFunc_SCI_EnableDCBUS_ext_DM9;
phcaiKEyLLGenFunc_SCI_Enable_ADC_POWERON_ext_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_Enable_ADC_POWERON_ext {
                     enable $bool_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_Enable_ADC_POWERON_ext_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_Enable_ADC_POWERON_ext_t $phcaiKEyLLGenFunc_SCI_Enable_ADC_POWERON_ext_DM9;
phcaiKEyLLGenFunc_SCI_clear_VDDABRNTRIM_DM9 : _struct(DM9,1,1) phcaiKEyLLGenFunc_SCI_clear_VDDABRNTRIM {
                     clear $bool_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_clear_VDDABRNTRIM_t_DM9 : _typedef(DM9,1,1) phcaiKEyLLGenFunc_SCI_clear_VDDABRNTRIM_t $phcaiKEyLLGenFunc_SCI_clear_VDDABRNTRIM_DM9;
phcaiKEyLLGenFunc_SCI_IIU_receive_DM9 : _struct(DM9,6,2) phcaiKEyLLGenFunc_SCI_IIU_receive {
                     data $__P__uchar_DM9 @0;
                     number_of_rcv_bits $uint8_t_DM9 @2;
                     buffer_size $uint8_t_DM9 @3;
                     retval $error_t_DM9 @4;
                 }
phcaiKEyLLGenFunc_SCI_IIU_receive_t_DM9 : _typedef(DM9,6,2) phcaiKEyLLGenFunc_SCI_IIU_receive_t $phcaiKEyLLGenFunc_SCI_IIU_receive_DM9;
phcaiKEyLLGenFunc_SCI_IIU_send_DM9 : _struct(DM9,4,2) phcaiKEyLLGenFunc_SCI_IIU_send {
                     data $__P__uchar_DM9 @0;
                     number_of_bytes $uint8_t_DM9 @2;
                     retval $error_t_DM9 @3;
                 }
phcaiKEyLLGenFunc_SCI_IIU_send_t_DM9 : _typedef(DM9,4,2) phcaiKEyLLGenFunc_SCI_IIU_send_t $phcaiKEyLLGenFunc_SCI_IIU_send_DM9;
phcaiKEyLLGenFunc_SCI_Parameter_List_DM9 : _union(DM9,8,2) phcaiKEyLLGenFunc_SCI_Parameter_List {
                     aes_load_key $phcaiKEyLLGenFunc_SCI_AES_Load_Key_t_DM9 @0;
                     aes_load_data $phcaiKEyLLGenFunc_SCI_AES_Load_Data_t_DM9 @0;
                     aes_encrypt $phcaiKEyLLGenFunc_SCI_AES_Encrypt_t_DM9 @0;
                     si_get $phcaiKEyLLGenFunc_SCI_SI_Get_t_DM9 @0;
                     si_inc $phcaiKEyLLGenFunc_SCI_SI_Inc_t_DM9 @0;
                     si_init $phcaiKEyLLGenFunc_SCI_SI_Init_t_DM9 @0;
                     mdi_prnt $phcaiKEyLLGenFunc_SCI_MDI_Prnt_t_DM9 @0;
                     mdi_prntln $phcaiKEyLLGenFunc_SCI_MDI_PrntLn_t_DM9 @0;
                     immo_init $phcaiKEyLLGenFunc_SCI_Immo_Init_t_DM9 @0;
                     immo_reset $phcaiKEyLLGenFunc_SCI_Immo_Reset_t_DM9 @0;
                     immo_rcv $phcaiKEyLLGenFunc_SCI_Immo_Rcv_t_DM9 @0;
                     immo_exec $phcaiKEyLLGenFunc_SCI_Immo_Exec_t_DM9 @0;
                     eeprom_en $phcaiKEyLLGenFunc_SCI_EEPROM_Enable_t_DM9 @0;
                     eeprom_prog $phcaiKEyLLGenFunc_SCI_EEPROM_Prog_t_DM9 @0;
                     ulpee_write_pg $phcaiKEyLLGenFunc_SCI_ULPEE_WritePage_t_DM9 @0;
                     ulpee_read_pg $phcaiKEyLLGenFunc_SCI_ULPEE_ReadPage_t_DM9 @0;
                     ulpee_read_bytes $phcaiKEyLLGenFunc_SCI_ULPEE_ReadBytes_t_DM9 @0;
                     get_version $phcaiKEyLLGenFunc_SCI_GetVersion_t_DM9 @0;
                     set_lfclkx2dis $phcaiKEyLLGenFunc_SCI_SetLfclkx2Dis_t_DM9 @0;
                     set_mdiclksel $phcaiKEyLLGenFunc_SCI_set_MdiClkSel_t_DM9 @0;
                     xo_startup $phcaiKEyLLGenFunc_SCI_XOStartUp_t_DM9 @0;
                     pll_startup $phcaiKEyLLGenFunc_SCI_PllStartUp_t_DM9 @0;
                     set_rfgate $phcaiKEyLLGenFunc_SCI_SetRFGate_t_DM9 @0;
                     set_paingate $phcaiKEyLLGenFunc_SCI_SetPAINGate_t_DM9 @0;
                     set_cpicp $phcaiKEyLLGenFunc_SCI_SetCPICP_t_DM9 @0;
                     set_ptrimxo $phcaiKEyLLGenFunc_SCI_SetPTRIMXO_t_DM9 @0;
                     set_ptrimpll $phcaiKEyLLGenFunc_SCI_SetPTRIMPLL_t_DM9 @0;
                     set_ptrimhs $phcaiKEyLLGenFunc_SCI_SetPTRIMHS_t_DM9 @0;
                     si_ext $phcaiKEyLLGenFunc_SCI_SI_Ext_t_DM9 @0;
                     erom_rw $phcaiKEyLLGenFunc_SCI_erom_rw_t_DM9 @0;
                     enable_dcbus $phcaiKEyLLGenFunc_SCI_EnableDCBUS_ext_t_DM9 @0;
                     enable_poweron $phcaiKEyLLGenFunc_SCI_Enable_ADC_POWERON_ext_t_DM9 @0;
                     clear_vddabrntrim $phcaiKEyLLGenFunc_SCI_clear_VDDABRNTRIM_t_DM9 @0;
                     iiu_receive $phcaiKEyLLGenFunc_SCI_IIU_receive_t_DM9 @0;
                     iiu_send $phcaiKEyLLGenFunc_SCI_IIU_send_t_DM9 @0;
                 }
phcaiKEyLLGenFunc_SCI_Parameter_List_t_DM9 : _typedef(DM9,8,2) phcaiKEyLLGenFunc_SCI_Parameter_List_t $phcaiKEyLLGenFunc_SCI_Parameter_List_DM9;
phcaiKEyLLGenFunc_SCI_Func_Params_DM9 : _struct(DM9,10,2) phcaiKEyLLGenFunc_SCI_Func_Params {
                     function_code $phcaiKEyLLGenFunc_SCI_CallerProxy_function_code_t_DM9 @0;
                     params $phcaiKEyLLGenFunc_SCI_Parameter_List_t_DM9 @2;
                 }
phcaiKEyLLGenFunc_SCI_Func_Params_t_DM9 : _typedef(DM9,10,2) phcaiKEyLLGenFunc_SCI_Func_Params_t $phcaiKEyLLGenFunc_SCI_Func_Params_DM9;
__anonymous9__transmitter__DM9 : _struct(DM9,2,1) __anonymous9__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
            SFR_word_DM9 : _union(DM9,2,2) SFR_word {
                     byte $__anonymous9__transmitter__DM9 @0;
                     val $uint16_t_DM9 @0;
                 }
          SFR_CXPC_t_DM9 : _typedef(DM9,2,2) SFR_CXPC_t $SFR_word_DM9;
              __uint_DM9 : _basic(DM9,2,2) __uint;
          bitfield_t_DM9 : _typedef(DM9,2,2) bitfield_t $__uint_DM9;
__anonymous11__transmitter__DM9 : _struct(DM9,1,1) __anonymous11__transmitter_ {
                     XINSTR $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     XSTACK $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     XPMEM $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     INTLEV $bitfield_t_DM9 @0 _bf 4 @3 $__schar_DM9;
                     SYSMODE $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
          SFR_CXSW_t_DM9 : _union(DM9,1,1) SFR_CXSW_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous11__transmitter__DM9 @0;
                 }
__anonymous13__transmitter__DM9 : _struct(DM9,1,1) __anonymous13__transmitter_ {
                     P10S $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11S $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12S $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13S $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14S $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15S $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16S $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17S $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_P1INS_t_DM9 : _union(DM9,1,1) SFR_P1INS_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous13__transmitter__DM9 @0;
                 }
__anonymous15__transmitter__DM9 : _struct(DM9,1,1) __anonymous15__transmitter_ {
                     P10O $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11O $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12O $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13O $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14O $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15O $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16O $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17O $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_P1OUT_t_DM9 : _union(DM9,1,1) SFR_P1OUT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous15__transmitter__DM9 @0;
                 }
__anonymous17__transmitter__DM9 : _struct(DM9,1,1) __anonymous17__transmitter_ {
                     P10D $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11D $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12D $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13D $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14D $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15D $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16D $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17D $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_P1DIR_t_DM9 : _union(DM9,1,1) SFR_P1DIR_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous17__transmitter__DM9 @0;
                 }
__anonymous19__transmitter__DM9 : _struct(DM9,1,1) __anonymous19__transmitter_ {
                     P20S $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21S $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P22S $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P23S $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P24S $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                 }
         SFR_P2INS_t_DM9 : _union(DM9,1,1) SFR_P2INS_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous19__transmitter__DM9 @0;
                 }
__anonymous21__transmitter__DM9 : _struct(DM9,1,1) __anonymous21__transmitter_ {
                     P20O $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21O $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P22O $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P23O $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P24O $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                 }
         SFR_P2OUT_t_DM9 : _union(DM9,1,1) SFR_P2OUT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous21__transmitter__DM9 @0;
                 }
__anonymous23__transmitter__DM9 : _struct(DM9,1,1) __anonymous23__transmitter_ {
                     P20D $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21D $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P22D $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P23D $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P24D $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                 }
         SFR_P2DIR_t_DM9 : _union(DM9,1,1) SFR_P2DIR_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous23__transmitter__DM9 @0;
                 }
__anonymous25__transmitter__DM9 : _struct(DM9,1,1) __anonymous25__transmitter_ {
                     P10INTDIS $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11INTDIS $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12INTDIS $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13INTDIS $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14INTDIS $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15INTDIS $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16INTDIS $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17INTDIS $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_P1INTDIS_t_DM9 : _union(DM9,1,1) SFR_P1INTDIS_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous25__transmitter__DM9 @0;
                 }
__anonymous27__transmitter__DM9 : _struct(DM9,1,1) __anonymous27__transmitter_ {
                     P20INTDIS $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21INTDIS $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P22INTDIS $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P23INTDIS $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P24INTDIS $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                 }
      SFR_P2INTDIS_t_DM9 : _union(DM9,1,1) SFR_P2INTDIS_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous27__transmitter__DM9 @0;
                 }
__anonymous29__transmitter__DM9 : _struct(DM9,1,1) __anonymous29__transmitter_ {
                     IIU_OBCSEN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                 }
       SFR_IIUCON2_t_DM9 : _union(DM9,1,1) SFR_IIUCON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous29__transmitter__DM9 @0;
                 }
__anonymous31__transmitter__DM9 : _struct(DM9,1,1) __anonymous31__transmitter_ {
                     IIU_CLKSEL $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     IIU_DPSEL $bitfield_t_DM9 @0 _bf 3 @2 $__schar_DM9;
                     IIU_MODSEL $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                     IIU_LSBF $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_IIUCON0_t_DM9 : _union(DM9,1,1) SFR_IIUCON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous31__transmitter__DM9 @0;
                 }
__anonymous33__transmitter__DM9 : _struct(DM9,1,1) __anonymous33__transmitter_ {
                     IIU_BCNTZERO $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IIU_FINISHED $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IIU_TXWAIT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IIU_ENCSEL $bitfield_t_DM9 @0 _bf 2 @3 $__schar_DM9;
                     IIU_RST $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IIU_LFDEMEN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IIU_LFDATA $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_IIUSTAT_t_DM9 : _union(DM9,1,1) SFR_IIUSTAT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous33__transmitter__DM9 @0;
                 }
__anonymous35__transmitter__DM9 : _struct(DM9,1,1) __anonymous35__transmitter_ {
                     IIU_BITCNT $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     IIU_PRIO $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_IIUCON1_t_DM9 : _union(DM9,1,1) SFR_IIUCON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous35__transmitter__DM9 @0;
                 }
            SFR_byte_DM9 : _union(DM9,1,1) SFR_byte {
                     val $uint8_t_DM9 @0;
                 }
        SFR_IIUDAT_t_DM9 : _typedef(DM9,1,1) SFR_IIUDAT_t $SFR_byte_DM9;
__anonymous37__transmitter__DM9 : _struct(DM9,1,1) __anonymous37__transmitter_ {
                     IIU_STATE $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                 }
      SFR_IIUSTATE_t_DM9 : _union(DM9,1,1) SFR_IIUSTATE_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous37__transmitter__DM9 @0;
                 }
__anonymous39__transmitter__DM9 : _struct(DM9,1,1) __anonymous39__transmitter_ {
                     HTMODE $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                     HTOSEL $bitfield_t_DM9 @0 _bf 2 @3 $__schar_DM9;
                     HTEN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
         SFR_HTCON_t_DM9 : _union(DM9,1,1) SFR_HTCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous39__transmitter__DM9 @0;
                 }
        SFR_AESDAT_t_DM9 : _typedef(DM9,2,2) SFR_AESDAT_t $SFR_word_DM9;
__anonymous41__transmitter__DM9 : _struct(DM9,1,1) __anonymous41__transmitter_ {
                     AESBC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     AESACC $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     AESRST $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     AESRUN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_AESCON_t_DM9 : _union(DM9,1,1) SFR_AESCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous41__transmitter__DM9 @0;
                 }
        SFR_CRCDAT_t_DM9 : _typedef(DM9,1,1) SFR_CRCDAT_t $SFR_byte_DM9;
       SFR_CRC8DIN_t_DM9 : _typedef(DM9,1,1) SFR_CRC8DIN_t $SFR_byte_DM9;
__anonymous43__transmitter__DM9 : _struct(DM9,1,1) __anonymous43__transmitter_ {
                     WDCLR $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     WDTRIG $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     WDMODE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     WDTIM $bitfield_t_DM9 @0 _bf 4 @4 $__schar_DM9;
                 }
         SFR_WDCON_t_DM9 : _union(DM9,1,1) SFR_WDCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous43__transmitter__DM9 @0;
                 }
__anonymous45__transmitter__DM9 : _struct(DM9,1,1) __anonymous45__transmitter_ {
                     CPUCLKCYC $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     CPUCLKSEL $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                 }
       SFR_CLKCON0_t_DM9 : _union(DM9,1,1) SFR_CLKCON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous45__transmitter__DM9 @0;
                 }
__anonymous47__transmitter__DM9 : _struct(DM9,1,1) __anonymous47__transmitter_ {
                     RER_COMP $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     MDICLKSEL $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     LFCLKX2DIS $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
       SFR_CLKCON1_t_DM9 : _union(DM9,1,1) SFR_CLKCON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous47__transmitter__DM9 @0;
                 }
__anonymous49__transmitter__DM9 : _struct(DM9,1,1) __anonymous49__transmitter_ {
                     MRCOSC_EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     AUXRCOSC_EN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     AUXRCOSC_OUTDIS $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     AESCLKSEL $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                     ADCCLKSEL $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_CLKCON2_t_DM9 : _union(DM9,1,1) SFR_CLKCON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous49__transmitter__DM9 @0;
                 }
__anonymous51__transmitter__DM9 : _struct(DM9,1,1) __anonymous51__transmitter_ {
                     TMUX0C $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     TMUX1C $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                 }
       SFR_CLKCON3_t_DM9 : _union(DM9,1,1) SFR_CLKCON3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous51__transmitter__DM9 @0;
                 }
__anonymous53__transmitter__DM9 : _struct(DM9,1,1) __anonymous53__transmitter_ {
                     CPUAUXDIVSEL $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     CGAESDIS $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                 }
       SFR_CLKCON4_t_DM9 : _union(DM9,1,1) SFR_CLKCON4_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous53__transmitter__DM9 @0;
                 }
        SFR_PREDAT_t_DM9 : _typedef(DM9,1,1) SFR_PREDAT_t $SFR_byte_DM9;
__anonymous55__transmitter__DM9 : _struct(DM9,1,1) __anonymous55__transmitter_ {
                     IT_MODE $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     IT_SEL $bitfield_t_DM9 @0 _bf 3 @2 $__schar_DM9;
                     RTC_SEL $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                     ITRST $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_RTCCON_t_DM9 : _union(DM9,1,1) SFR_RTCCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous55__transmitter__DM9 @0;
                 }
__anonymous57__transmitter__DM9 : _struct(DM9,1,1) __anonymous57__transmitter_ {
                     LPRC_EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     BDRATE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     PDLFACT $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     PRERST $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     DIGFILRST $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON2_t_DM9 : _union(DM9,1,1) SFR_PRECON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous57__transmitter__DM9 @0;
                 }
__anonymous59__transmitter__DM9 : _struct(DM9,1,1) __anonymous59__transmitter_ {
                     QFACT1 $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     PD1_6DB $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     DISCH1ACT $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON3_t_DM9 : _union(DM9,1,1) SFR_PRECON3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous59__transmitter__DM9 @0;
                 }
__anonymous61__transmitter__DM9 : _struct(DM9,1,1) __anonymous61__transmitter_ {
                     QFACT2 $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     PD2_6DB $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     DISCH2ACT $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON4_t_DM9 : _union(DM9,1,1) SFR_PRECON4_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous61__transmitter__DM9 @0;
                 }
__anonymous63__transmitter__DM9 : _struct(DM9,1,1) __anonymous63__transmitter_ {
                     QFACT3 $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     PD3_6DB $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     DISCH3ACT $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON5_t_DM9 : _union(DM9,1,1) SFR_PRECON5_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous63__transmitter__DM9 @0;
                 }
__anonymous65__transmitter__DM9 : _struct(DM9,1,1) __anonymous65__transmitter_ {
                     LPRC_CAL $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     CVTYPE $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     ERRTOLEN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
       SFR_PRECON6_t_DM9 : _union(DM9,1,1) SFR_PRECON6_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous65__transmitter__DM9 @0;
                 }
__anonymous67__transmitter__DM9 : _struct(DM9,1,1) __anonymous67__transmitter_ {
                     WUP1_LEN $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     WUPSEL $bitfield_t_DM9 @0 _bf 3 @5 $__schar_DM9;
                 }
       SFR_PRECON7_t_DM9 : _union(DM9,1,1) SFR_PRECON7_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous67__transmitter__DM9 @0;
                 }
__anonymous69__transmitter__DM9 : _struct(DM9,1,1) __anonymous69__transmitter_ {
                     WUP2_LEN $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     PDIREFSTUP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     NEWBYTEOVFHOLD $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PRECON8_t_DM9 : _union(DM9,1,1) SFR_PRECON8_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous69__transmitter__DM9 @0;
                 }
__anonymous71__transmitter__DM9 : _struct(DM9,1,1) __anonymous71__transmitter_ {
                     WUP3_LEN $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     FIFOEN $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     DIGFILMODE $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                 }
       SFR_PRECON9_t_DM9 : _union(DM9,1,1) SFR_PRECON9_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous71__transmitter__DM9 @0;
                 }
         SFR_PREPD_t_DM9 : _typedef(DM9,1,1) SFR_PREPD_t $SFR_byte_DM9;
    SFR_USRBATRGLx_t_DM9 : _typedef(DM9,1,1) SFR_USRBATRGLx_t $SFR_byte_DM9;
__anonymous73__transmitter__DM9 : _struct(DM9,2,1) __anonymous73__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
              __sint_DM9 : _basic(DM9,2,2) __sint;
__anonymous74__transmitter__DM9 : _struct(DM9,2,2) __anonymous74__transmitter_ {
                     WUP1M $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     WUP2M $bitfield_t_DM9 @0 _bf 1 @1 $__sint_DM9;
                     WUP3M $bitfield_t_DM9 @0 _bf 1 @2 $__sint_DM9;
                     NEWBYTE $bitfield_t_DM9 @0 _bf 1 @3 $__sint_DM9;
                     BITFAIL $bitfield_t_DM9 @0 _bf 1 @4 $__sint_DM9;
                     RTC_WUP $bitfield_t_DM9 @0 _bf 1 @5 $__sint_DM9;
                     IT_WUP $bitfield_t_DM9 @0 _bf 1 @6 $__sint_DM9;
                     ERRTOL $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     WUP1MH $bitfield_t_DM9 @0 _bf 1 @8 $__sint_DM9;
                     WUP2MH $bitfield_t_DM9 @0 _bf 1 @9 $__sint_DM9;
                     WUP3MH $bitfield_t_DM9 @0 _bf 1 @10 $__sint_DM9;
                     NEWBYTE_OVF $bitfield_t_DM9 @0 _bf 1 @11 $__sint_DM9;
                     NEXTFIFODATOK $bitfield_t_DM9 @0 _bf 1 @12 $__sint_DM9;
                     RTC_WUP_OVF $bitfield_t_DM9 @0 _bf 1 @13 $__sint_DM9;
                     MODE $bitfield_t_DM9 @0 _bf 2 @14 $__sint_DM9;
                 }
       SFR_PRESTAT_t_DM9 : _union(DM9,2,2) SFR_PRESTAT_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous73__transmitter__DM9 @0;
                     bits $__anonymous74__transmitter__DM9 @0;
                 }
        SFR_WUP1W0_t_DM9 : _typedef(DM9,2,2) SFR_WUP1W0_t $SFR_word_DM9;
        SFR_WUP1W1_t_DM9 : _typedef(DM9,2,2) SFR_WUP1W1_t $SFR_word_DM9;
        SFR_WUP2W0_t_DM9 : _typedef(DM9,2,2) SFR_WUP2W0_t $SFR_word_DM9;
        SFR_WUP2W1_t_DM9 : _typedef(DM9,2,2) SFR_WUP2W1_t $SFR_word_DM9;
        SFR_WUP3W0_t_DM9 : _typedef(DM9,2,2) SFR_WUP3W0_t $SFR_word_DM9;
          SFR_PRET_t_DM9 : _typedef(DM9,2,2) SFR_PRET_t $SFR_word_DM9;
         SFR_PRE3T_t_DM9 : _typedef(DM9,2,2) SFR_PRE3T_t $SFR_word_DM9;
__anonymous76__transmitter__DM9 : _struct(DM9,2,1) __anonymous76__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous77__transmitter__DM9 : _struct(DM9,2,2) __anonymous77__transmitter_ {
                     FSEC $bitfield_t_DM9 @0 _bf 4 @0 $__sint_DM9;
                     SEC $bitfield_t_DM9 @0 _bf 6 @4 $__sint_DM9;
                     MIN $bitfield_t_DM9 @0 _bf 6 @10 $__sint_DM9;
                 }
        SFR_RTCDAT_t_DM9 : _union(DM9,2,2) SFR_RTCDAT_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous76__transmitter__DM9 @0;
                     bits $__anonymous77__transmitter__DM9 @0;
                 }
__anonymous79__transmitter__DM9 : _struct(DM9,1,1) __anonymous79__transmitter_ {
                     AGCLOWBWEN $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     QLIM $bitfield_t_DM9 @0 _bf 3 @5 $__schar_DM9;
                 }
      SFR_PRECON10_t_DM9 : _union(DM9,1,1) SFR_PRECON10_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous79__transmitter__DM9 @0;
                 }
__anonymous81__transmitter__DM9 : _struct(DM9,1,1) __anonymous81__transmitter_ {
                     RINMODE $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     GAINCH1 $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     GAINCH2 $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     GAINCH3 $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
      SFR_PRECON11_t_DM9 : _union(DM9,1,1) SFR_PRECON11_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous81__transmitter__DM9 @0;
                 }
__anonymous83__transmitter__DM9 : _struct(DM9,2,1) __anonymous83__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous84__transmitter__DM9 : _struct(DM9,2,2) __anonymous84__transmitter_ {
                     ADCDATA $bitfield_t_DM9 @0 _bf 10 @0 $__sint_DM9;
                     CONV_OV $bitfield_t_DM9 @0 _bf 1 @14 $__sint_DM9;
                 }
        SFR_ADCDAT_t_DM9 : _union(DM9,2,2) SFR_ADCDAT_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous83__transmitter__DM9 @0;
                     bits $__anonymous84__transmitter__DM9 @0;
                 }
__anonymous86__transmitter__DM9 : _struct(DM9,2,1) __anonymous86__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous87__transmitter__DM9 : _struct(DM9,2,2) __anonymous87__transmitter_ {
                     CONVSTART $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     CONVRESET $bitfield_t_DM9 @0 _bf 1 @1 $__sint_DM9;
                     BG1V2EN $bitfield_t_DM9 @0 _bf 1 @4 $__sint_DM9;
                     BG1V2BUFEN $bitfield_t_DM9 @0 _bf 1 @5 $__sint_DM9;
                     BATMEASEN $bitfield_t_DM9 @0 _bf 1 @6 $__sint_DM9;
                     TSENSEN $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     SAMTIM $bitfield_t_DM9 @0 _bf 2 @8 $__sint_DM9;
                     REFSEL $bitfield_t_DM9 @0 _bf 3 @10 $__sint_DM9;
                     INSEL $bitfield_t_DM9 @0 _bf 3 @13 $__sint_DM9;
                 }
        SFR_ADCCON_t_DM9 : _union(DM9,2,2) SFR_ADCCON_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous86__transmitter__DM9 @0;
                     bits $__anonymous87__transmitter__DM9 @0;
                 }
__anonymous89__transmitter__DM9 : _struct(DM9,2,1) __anonymous89__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous90__transmitter__DM9 : _struct(DM9,2,2) __anonymous90__transmitter_ {
                     RSSI_RST $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     RSSI_OVF $bitfield_t_DM9 @0 _bf 3 @1 $__sint_DM9;
                     RSSI_HOLD $bitfield_t_DM9 @0 _bf 1 @4 $__sint_DM9;
                     RSSI_CHANSEL $bitfield_t_DM9 @0 _bf 2 @5 $__sint_DM9;
                     RSSI_PON $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     RSSI_RANGEEXTDIS $bitfield_t_DM9 @0 _bf 1 @8 $__sint_DM9;
                     RSSI_RANGE $bitfield_t_DM9 @0 _bf 3 @9 $__sint_DM9;
                 }
       SFR_RSSICON_t_DM9 : _union(DM9,2,2) SFR_RSSICON_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous89__transmitter__DM9 @0;
                     bits $__anonymous90__transmitter__DM9 @0;
                 }
       SFR_ULPADDR_t_DM9 : _typedef(DM9,2,2) SFR_ULPADDR_t $SFR_word_DM9;
        SFR_ULPSEL_t_DM9 : _typedef(DM9,2,2) SFR_ULPSEL_t $SFR_word_DM9;
__anonymous92__transmitter__DM9 : _struct(DM9,1,1) __anonymous92__transmitter_ {
                     ULPBITAMOUNT $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                     ULPWR_RD $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     ULPPON $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     ULPRST $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     ULPPROGERR $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     ULPRUN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_ULPCON0_t_DM9 : _union(DM9,1,1) SFR_ULPCON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous92__transmitter__DM9 @0;
                 }
        SFR_ULPDAT_t_DM9 : _typedef(DM9,1,1) SFR_ULPDAT_t $SFR_byte_DM9;
__anonymous94__transmitter__DM9 : _struct(DM9,1,1) __anonymous94__transmitter_ {
                     BUSYPROG $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_ULPCON1_t_DM9 : _union(DM9,1,1) SFR_ULPCON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous94__transmitter__DM9 @0;
                 }
__anonymous96__transmitter__DM9 : _struct(DM9,1,1) __anonymous96__transmitter_ {
                     T0RUN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     T0RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     T0SGL $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     T0OUT $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
        SFR_T0CON0_t_DM9 : _union(DM9,1,1) SFR_T0CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous96__transmitter__DM9 @0;
                 }
__anonymous98__transmitter__DM9 : _struct(DM9,1,1) __anonymous98__transmitter_ {
                     T0PRESC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     T0CLKSEL $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                 }
        SFR_T0CON1_t_DM9 : _union(DM9,1,1) SFR_T0CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous98__transmitter__DM9 @0;
                 }
         SFR_T0REG_t_DM9 : _typedef(DM9,2,2) SFR_T0REG_t $SFR_word_DM9;
         SFR_T0RLD_t_DM9 : _typedef(DM9,2,2) SFR_T0RLD_t $SFR_word_DM9;
__anonymous100__transmitter__DM9 : _struct(DM9,1,1) __anonymous100__transmitter_ {
                     T1RUN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     T1RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     T1MODE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     T1RSTCMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     T1RSTCAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     T1OUT $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
        SFR_T1CON0_t_DM9 : _union(DM9,1,1) SFR_T1CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous100__transmitter__DM9 @0;
                 }
__anonymous102__transmitter__DM9 : _struct(DM9,1,1) __anonymous102__transmitter_ {
                     T1PRESC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     T1CLKSEL $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                 }
        SFR_T1CON1_t_DM9 : _union(DM9,1,1) SFR_T1CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous102__transmitter__DM9 @0;
                 }
__anonymous104__transmitter__DM9 : _struct(DM9,1,1) __anonymous104__transmitter_ {
                     T1CAPSRC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     T1CAPMODE $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                     T1MANCAP $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_T1CON2_t_DM9 : _union(DM9,1,1) SFR_T1CON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous104__transmitter__DM9 @0;
                 }
         SFR_T1REG_t_DM9 : _typedef(DM9,2,2) SFR_T1REG_t $SFR_word_DM9;
         SFR_T1CAP_t_DM9 : _typedef(DM9,2,2) SFR_T1CAP_t $SFR_word_DM9;
         SFR_T1CMP_t_DM9 : _typedef(DM9,2,2) SFR_T1CMP_t $SFR_word_DM9;
__anonymous106__transmitter__DM9 : _struct(DM9,1,1) __anonymous106__transmitter_ {
                     T2RUN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     T2RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     T2SGL $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     T2OUT $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
        SFR_T2CON0_t_DM9 : _union(DM9,1,1) SFR_T2CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous106__transmitter__DM9 @0;
                 }
__anonymous108__transmitter__DM9 : _struct(DM9,1,1) __anonymous108__transmitter_ {
                     T2PRESC $bitfield_t_DM9 @0 _bf 4 @0 $__schar_DM9;
                     T2CLKSEL $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                 }
        SFR_T2CON1_t_DM9 : _union(DM9,1,1) SFR_T2CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous108__transmitter__DM9 @0;
                 }
         SFR_T2REG_t_DM9 : _typedef(DM9,2,2) SFR_T2REG_t $SFR_word_DM9;
         SFR_T2RLD_t_DM9 : _typedef(DM9,2,2) SFR_T2RLD_t $SFR_word_DM9;
        SFR_RNGDAT_t_DM9 : _typedef(DM9,2,2) SFR_RNGDAT_t $SFR_word_DM9;
__anonymous110__transmitter__DM9 : _struct(DM9,1,1) __anonymous110__transmitter_ {
                     RNGRST $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     RNGEN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     RNGTRIMOSC $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     RNGCONFIG $bitfield_t_DM9 @0 _bf 2 @3 $__schar_DM9;
                     RNGBITSHIFT $bitfield_t_DM9 @0 _bf 2 @5 $__schar_DM9;
                     RNGRUN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_RNGCON_t_DM9 : _union(DM9,1,1) SFR_RNGCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous110__transmitter__DM9 @0;
                 }
__anonymous112__transmitter__DM9 : _struct(DM9,1,1) __anonymous112__transmitter_ {
                     SYSINTPRIO $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     GLOBSYSINTEN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     LINTSWCON $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_INTCON_t_DM9 : _union(DM9,1,1) SFR_INTCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous112__transmitter__DM9 @0;
                 }
__anonymous114__transmitter__DM9 : _struct(DM9,1,1) __anonymous114__transmitter_ {
                     IF_LF $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IF_CX $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IF_PORT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IF_T0 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IF_T1CMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IF_T1CAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IF_ALTPORT $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IF_ST0 $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_INTFLAG0_t_DM9 : _union(DM9,1,1) SFR_INTFLAG0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous114__transmitter__DM9 @0;
                 }
__anonymous116__transmitter__DM9 : _struct(DM9,1,1) __anonymous116__transmitter_ {
                     IF_IIU $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IF_ULP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IF_ADC $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IF_AES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IF_RNG $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
      SFR_INTFLAG1_t_DM9 : _union(DM9,1,1) SFR_INTFLAG1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous116__transmitter__DM9 @0;
                 }
__anonymous118__transmitter__DM9 : _struct(DM9,1,1) __anonymous118__transmitter_ {
                     IF_IT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IF_PP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IF_SP0 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IF_SP1 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IF_T2 $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IF_MSI $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IF_VBATBRN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_INTFLAG2_t_DM9 : _union(DM9,1,1) SFR_INTFLAG2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous118__transmitter__DM9 @0;
                 }
__anonymous120__transmitter__DM9 : _struct(DM9,1,1) __anonymous120__transmitter_ {
                     IE_LFNMI $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IE_CXNMI $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IE_PORT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IE_T0 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IE_T1CMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IE_T1CAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IE_ALTPORT $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
        SFR_INTEN0_t_DM9 : _union(DM9,1,1) SFR_INTEN0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous120__transmitter__DM9 @0;
                 }
__anonymous122__transmitter__DM9 : _struct(DM9,1,1) __anonymous122__transmitter_ {
                     IE_IIU $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IE_ULP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IE_ADC $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IE_AES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IE_RNG $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
        SFR_INTEN1_t_DM9 : _union(DM9,1,1) SFR_INTEN1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous122__transmitter__DM9 @0;
                 }
__anonymous124__transmitter__DM9 : _struct(DM9,1,1) __anonymous124__transmitter_ {
                     IE_IT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IE_PP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IE_SP0 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IE_SP1 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IE_T2 $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IE_MSI $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IE_VBATBRN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_INTEN2_t_DM9 : _union(DM9,1,1) SFR_INTEN2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous124__transmitter__DM9 @0;
                 }
     SFR_SYSINTEN0_t_DM9 : _typedef(DM9,1,1) SFR_SYSINTEN0_t $SFR_byte_DM9;
     SFR_SYSINTEN1_t_DM9 : _typedef(DM9,1,1) SFR_SYSINTEN1_t $SFR_byte_DM9;
__anonymous126__transmitter__DM9 : _struct(DM9,1,1) __anonymous126__transmitter_ {
                     IS_LF $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IS_CX $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IS_PORT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IS_T0 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IS_T1CMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IS_T1CAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IS_ALTPORT $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IS_ST0 $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTSET0_t_DM9 : _union(DM9,1,1) SFR_INTSET0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous126__transmitter__DM9 @0;
                 }
__anonymous128__transmitter__DM9 : _struct(DM9,1,1) __anonymous128__transmitter_ {
                     IS_IIU $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IS_ULP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IS_ADC $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IS_AES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IS_RNG $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
       SFR_INTSET1_t_DM9 : _union(DM9,1,1) SFR_INTSET1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous128__transmitter__DM9 @0;
                 }
__anonymous130__transmitter__DM9 : _struct(DM9,1,1) __anonymous130__transmitter_ {
                     IS_IT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IS_PP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IS_SP0 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IS_SP1 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IS_T2 $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IS_MSI $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IS_VBATBRN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTSET2_t_DM9 : _union(DM9,1,1) SFR_INTSET2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous130__transmitter__DM9 @0;
                 }
__anonymous132__transmitter__DM9 : _struct(DM9,1,1) __anonymous132__transmitter_ {
                     IC_LF $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IC_CX $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IC_PORT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IC_T0 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IC_T1CMP $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IC_T1CAP $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IC_ALTPORT $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IC_ST0 $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTCLR0_t_DM9 : _union(DM9,1,1) SFR_INTCLR0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous132__transmitter__DM9 @0;
                 }
__anonymous134__transmitter__DM9 : _struct(DM9,1,1) __anonymous134__transmitter_ {
                     IC_IIU $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IC_ULP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IC_ADC $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IC_AES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IC_RNG $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
       SFR_INTCLR1_t_DM9 : _union(DM9,1,1) SFR_INTCLR1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous134__transmitter__DM9 @0;
                 }
__anonymous136__transmitter__DM9 : _struct(DM9,1,1) __anonymous136__transmitter_ {
                     IC_IT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IC_PP $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IC_SP0 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IC_SP1 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IC_T2 $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IC_MSI $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IC_VBATBRN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTCLR2_t_DM9 : _union(DM9,1,1) SFR_INTCLR2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous136__transmitter__DM9 @0;
                 }
        SFR_INTVEC_t_DM9 : _typedef(DM9,2,2) SFR_INTVEC_t $SFR_word_DM9;
__anonymous138__transmitter__DM9 : _struct(DM9,1,1) __anonymous138__transmitter_ {
                     BATPORFLAG $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     BATRGLEN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     BATRGLRST $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                 }
       SFR_BATSYS0_t_DM9 : _union(DM9,1,1) SFR_BATSYS0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous138__transmitter__DM9 @0;
                 }
__anonymous140__transmitter__DM9 : _struct(DM9,1,1) __anonymous140__transmitter_ {
                     BATRST $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                 }
       SFR_BATSYS1_t_DM9 : _union(DM9,1,1) SFR_BATSYS1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous140__transmitter__DM9 @0;
                 }
__anonymous142__transmitter__DM9 : _struct(DM9,2,1) __anonymous142__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous143__transmitter__DM9 : _struct(DM9,2,2) __anonymous143__transmitter_ {
                     WDTOF $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     P12C $bitfield_t_DM9 @0 _bf 3 @1 $__sint_DM9;
                     P13C $bitfield_t_DM9 @0 _bf 3 @5 $__sint_DM9;
                     P14C $bitfield_t_DM9 @0 _bf 3 @9 $__sint_DM9;
                     P15C $bitfield_t_DM9 @0 _bf 3 @13 $__sint_DM9;
                 }
      SFR_PRESWUP0_t_DM9 : _union(DM9,2,2) SFR_PRESWUP0_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous142__transmitter__DM9 @0;
                     bits $__anonymous143__transmitter__DM9 @0;
                 }
__anonymous145__transmitter__DM9 : _struct(DM9,2,1) __anonymous145__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous146__transmitter__DM9 : _struct(DM9,2,2) __anonymous146__transmitter_ {
                     P16C $bitfield_t_DM9 @0 _bf 3 @1 $__sint_DM9;
                     P17C $bitfield_t_DM9 @0 _bf 3 @5 $__sint_DM9;
                     P20C $bitfield_t_DM9 @0 _bf 3 @9 $__sint_DM9;
                     P21C $bitfield_t_DM9 @0 _bf 3 @13 $__sint_DM9;
                 }
      SFR_PRESWUP1_t_DM9 : _union(DM9,2,2) SFR_PRESWUP1_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous145__transmitter__DM9 @0;
                     bits $__anonymous146__transmitter__DM9 @0;
                 }
      SFR_PRESWUP2_t_DM9 : _typedef(DM9,2,2) SFR_PRESWUP2_t $SFR_word_DM9;
__anonymous148__transmitter__DM9 : _struct(DM9,1,1) __anonymous148__transmitter_ {
                     P10WRES $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P11WRES $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P12WRES $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     P13WRES $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     P14WRES $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     P15WRES $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     P16WRES $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     P17WRES $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_P1WRES_t_DM9 : _union(DM9,1,1) SFR_P1WRES_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous148__transmitter__DM9 @0;
                 }
__anonymous150__transmitter__DM9 : _struct(DM9,1,1) __anonymous150__transmitter_ {
                     P20WRES $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     P21WRES $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     P21MRES $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
        SFR_P2WRES_t_DM9 : _union(DM9,1,1) SFR_P2WRES_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous150__transmitter__DM9 @0;
                 }
       SFR_USRBATx_t_DM9 : _typedef(DM9,1,1) SFR_USRBATx_t $SFR_byte_DM9;
__anonymous152__transmitter__DM9 : _struct(DM9,2,1) __anonymous152__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous153__transmitter__DM9 : _struct(DM9,2,2) __anonymous153__transmitter_ {
                     LF_CAP_CH1 $bitfield_t_DM9 @0 _bf 4 @0 $__sint_DM9;
                     LF_CAP_CH2 $bitfield_t_DM9 @0 _bf 4 @4 $__sint_DM9;
                     LF_CAP_CH3 $bitfield_t_DM9 @0 _bf 4 @8 $__sint_DM9;
                     LF_CAP_CH1_DIS $bitfield_t_DM9 @0 _bf 1 @12 $__sint_DM9;
                     LF_CAP_CH2_DIS $bitfield_t_DM9 @0 _bf 1 @13 $__sint_DM9;
                     LF_CAP_CH3_DIS $bitfield_t_DM9 @0 _bf 1 @14 $__sint_DM9;
                 }
    SFR_LFTUNEVBAT_t_DM9 : _union(DM9,2,2) SFR_LFTUNEVBAT_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous152__transmitter__DM9 @0;
                     bits $__anonymous153__transmitter__DM9 @0;
                 }
__anonymous155__transmitter__DM9 : _struct(DM9,2,1) __anonymous155__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous156__transmitter__DM9 : _struct(DM9,2,2) __anonymous156__transmitter_ {
                     LF_CAP_CH1 $bitfield_t_DM9 @0 _bf 4 @0 $__sint_DM9;
                     LF_CAP_CH2 $bitfield_t_DM9 @0 _bf 4 @4 $__sint_DM9;
                     LF_CAP_CH3 $bitfield_t_DM9 @0 _bf 4 @8 $__sint_DM9;
                     LF_CAP_CH1_DIS $bitfield_t_DM9 @0 _bf 1 @12 $__sint_DM9;
                     LF_CAP_CH2_DIS $bitfield_t_DM9 @0 _bf 1 @13 $__sint_DM9;
                     LF_CAP_CH3_DIS $bitfield_t_DM9 @0 _bf 1 @14 $__sint_DM9;
                 }
     SFR_LFTUNEVDD_t_DM9 : _union(DM9,2,2) SFR_LFTUNEVDD_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous155__transmitter__DM9 @0;
                     bits $__anonymous156__transmitter__DM9 @0;
                 }
__anonymous158__transmitter__DM9 : _struct(DM9,1,1) __anonymous158__transmitter_ {
                     STDIS $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     SHCH1 $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     SHCH2 $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     SHCH3 $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                 }
       SFR_LFSHCON_t_DM9 : _union(DM9,1,1) SFR_LFSHCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous158__transmitter__DM9 @0;
                 }
__anonymous160__transmitter__DM9 : _struct(DM9,1,1) __anonymous160__transmitter_ {
                     PWRFLD $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     PWRBAT $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     PRESW_LF $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     VBATBRNIND $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PRESW_MODE $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     VDDARGLEN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     VDDARST $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     VDDRST $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_PCON0_t_DM9 : _union(DM9,1,1) SFR_PCON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous160__transmitter__DM9 @0;
                 }
__anonymous162__transmitter__DM9 : _struct(DM9,1,1) __anonymous162__transmitter_ {
                     PWUPIND $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     PMODE $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     LFFLD $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     VBATBRNFLAG $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PWRMANLFSTATE $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     VBATMONEN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     VDDABRNFLAG $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     VDDBRNFLAG $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_PCON1_t_DM9 : _union(DM9,1,1) SFR_PCON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous162__transmitter__DM9 @0;
                 }
__anonymous164__transmitter__DM9 : _struct(DM9,1,1) __anonymous164__transmitter_ {
                     LOCKP $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     R2MSDET $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     VBATBRNEXT $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     VBATBRNREG $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     VBATBRNINDEN $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     VDDABRNREG $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     VDDBRNREG $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_PCON2_t_DM9 : _union(DM9,1,1) SFR_PCON2_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous164__transmitter__DM9 @0;
                 }
__anonymous166__transmitter__DM9 : _struct(DM9,1,1) __anonymous166__transmitter_ {
                     DUPLEX $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     MODE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     CLKPHA $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     CLKPOL $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     LSBF $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
      SFR_SPI0CON0_t_DM9 : _union(DM9,1,1) SFR_SPI0CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous166__transmitter__DM9 @0;
                 }
__anonymous168__transmitter__DM9 : _struct(DM9,1,1) __anonymous168__transmitter_ {
                     EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     STOP $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     INTSS $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     CLOCK $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                 }
      SFR_SPI0CON1_t_DM9 : _union(DM9,1,1) SFR_SPI0CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous168__transmitter__DM9 @0;
                 }
       SFR_SPI0DAT_t_DM9 : _typedef(DM9,1,1) SFR_SPI0DAT_t $SFR_byte_DM9;
__anonymous170__transmitter__DM9 : _struct(DM9,1,1) __anonymous170__transmitter_ {
                     BIT $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                     RXBFOVF $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     RXBF $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     BUSY $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_SPI0STAT_t_DM9 : _union(DM9,1,1) SFR_SPI0STAT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous170__transmitter__DM9 @0;
                 }
__anonymous172__transmitter__DM9 : _struct(DM9,1,1) __anonymous172__transmitter_ {
                     DUPLEX $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     MODE $bitfield_t_DM9 @0 _bf 2 @2 $__schar_DM9;
                     CLKPHA $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     CLKPOL $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     LSBF $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
      SFR_SPI1CON0_t_DM9 : _union(DM9,1,1) SFR_SPI1CON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous172__transmitter__DM9 @0;
                 }
__anonymous174__transmitter__DM9 : _struct(DM9,1,1) __anonymous174__transmitter_ {
                     EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     RST $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     STOP $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     INTSS $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     CLOCK $bitfield_t_DM9 @0 _bf 3 @4 $__schar_DM9;
                 }
      SFR_SPI1CON1_t_DM9 : _union(DM9,1,1) SFR_SPI1CON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous174__transmitter__DM9 @0;
                 }
       SFR_SPI1DAT_t_DM9 : _typedef(DM9,1,1) SFR_SPI1DAT_t $SFR_byte_DM9;
__anonymous176__transmitter__DM9 : _struct(DM9,1,1) __anonymous176__transmitter_ {
                     BIT $bitfield_t_DM9 @0 _bf 3 @0 $__schar_DM9;
                     RXBFOVF $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     RXBF $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     BUSY $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_SPI1STAT_t_DM9 : _union(DM9,1,1) SFR_SPI1STAT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous176__transmitter__DM9 @0;
                 }
__anonymous178__transmitter__DM9 : _struct(DM9,2,1) __anonymous178__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous179__transmitter__DM9 : _struct(DM9,2,2) __anonymous179__transmitter_ {
                     P10AF $bitfield_t_DM9 @0 _bf 2 @0 $__sint_DM9;
                     P11AF $bitfield_t_DM9 @0 _bf 2 @2 $__sint_DM9;
                     P12AF $bitfield_t_DM9 @0 _bf 2 @4 $__sint_DM9;
                     P13AF $bitfield_t_DM9 @0 _bf 2 @6 $__sint_DM9;
                     P14AF $bitfield_t_DM9 @0 _bf 2 @8 $__sint_DM9;
                     P15AF $bitfield_t_DM9 @0 _bf 2 @10 $__sint_DM9;
                     P16AF $bitfield_t_DM9 @0 _bf 2 @12 $__sint_DM9;
                     P17AF $bitfield_t_DM9 @0 _bf 2 @14 $__sint_DM9;
                 }
        SFR_P1ALTF_t_DM9 : _union(DM9,2,2) SFR_P1ALTF_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous178__transmitter__DM9 @0;
                     bits $__anonymous179__transmitter__DM9 @0;
                 }
__anonymous181__transmitter__DM9 : _struct(DM9,2,1) __anonymous181__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous182__transmitter__DM9 : _struct(DM9,2,2) __anonymous182__transmitter_ {
                     P20AF $bitfield_t_DM9 @0 _bf 2 @0 $__sint_DM9;
                     P21AF $bitfield_t_DM9 @0 _bf 2 @2 $__sint_DM9;
                     P22AF $bitfield_t_DM9 @0 _bf 2 @4 $__sint_DM9;
                     P23AF $bitfield_t_DM9 @0 _bf 2 @6 $__sint_DM9;
                     P24AF $bitfield_t_DM9 @0 _bf 2 @8 $__sint_DM9;
                 }
        SFR_P2ALTF_t_DM9 : _union(DM9,2,2) SFR_P2ALTF_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous181__transmitter__DM9 @0;
                     bits $__anonymous182__transmitter__DM9 @0;
                 }
        SFR_BITCNT_t_DM9 : _typedef(DM9,2,2) SFR_BITCNT_t $SFR_word_DM9;
       SFR_BITSWAP_t_DM9 : _typedef(DM9,1,1) SFR_BITSWAP_t $SFR_byte_DM9;
      SFR_PREPOLL0_t_DM9 : _typedef(DM9,1,1) SFR_PREPOLL0_t $SFR_byte_DM9;
      SFR_PREPOLL1_t_DM9 : _typedef(DM9,1,1) SFR_PREPOLL1_t $SFR_byte_DM9;
      SFR_PRECON12_t_DM9 : _typedef(DM9,1,1) SFR_PRECON12_t $SFR_byte_DM9;
__anonymous184__transmitter__DM9 : _struct(DM9,1,1) __anonymous184__transmitter_ {
                     MSI_TLIM $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     MSI_TCLR $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                 }
       SFR_MSICON0_t_DM9 : _union(DM9,1,1) SFR_MSICON0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous184__transmitter__DM9 @0;
                 }
__anonymous186__transmitter__DM9 : _struct(DM9,1,1) __anonymous186__transmitter_ {
                     MSI_OVF_INT_EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     MSI_MOT_INT_EN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     MSI_OVF_WUP_EN $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     MSI_MOT_WUP_EN $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     MSI_LFA_PU_EN $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     MSI_LFA_PD_EN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     MSI_MODE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     MSI_EN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_MSICON1_t_DM9 : _union(DM9,1,1) SFR_MSICON1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous186__transmitter__DM9 @0;
                 }
__anonymous188__transmitter__DM9 : _struct(DM9,1,1) __anonymous188__transmitter_ {
                     MSI_TREG $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                 }
      SFR_MSISTAT0_t_DM9 : _union(DM9,1,1) SFR_MSISTAT0_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous188__transmitter__DM9 @0;
                 }
__anonymous190__transmitter__DM9 : _struct(DM9,1,1) __anonymous190__transmitter_ {
                     MSI_OVF_INT $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     MSI_MOT_INT $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     MSI_OVF $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     MSI_LFA_PD $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                 }
      SFR_MSISTAT1_t_DM9 : _union(DM9,1,1) SFR_MSISTAT1_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous190__transmitter__DM9 @0;
                 }
__anonymous192__transmitter__DM9 : _struct(DM9,1,1) __anonymous192__transmitter_ {
                     IF_XORDY $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IF_VCOCAL $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IF_PLLLOCK $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IF_PLLUNLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IF_PARDY $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IF_PAILIM $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IF_TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IF_TXFIN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
      SFR_INTFLAG3_t_DM9 : _union(DM9,1,1) SFR_INTFLAG3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous192__transmitter__DM9 @0;
                 }
__anonymous194__transmitter__DM9 : _struct(DM9,1,1) __anonymous194__transmitter_ {
                     IE_XORDY $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IE_VCOCAL $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IE_PLLLOCK $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IE_PLLUNLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IE_PARDY $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IE_PAILIM $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IE_TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IE_TXFIN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_INTEN3_t_DM9 : _union(DM9,1,1) SFR_INTEN3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous194__transmitter__DM9 @0;
                 }
__anonymous196__transmitter__DM9 : _struct(DM9,1,1) __anonymous196__transmitter_ {
                     IS_XORDY $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IS_VCOCAL $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IS_PLLLOCK $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IS_PLLUNLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IS_PARDY $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IS_PAILIM $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IS_TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IS_TXFIN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTSET3_t_DM9 : _union(DM9,1,1) SFR_INTSET3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous196__transmitter__DM9 @0;
                 }
__anonymous198__transmitter__DM9 : _struct(DM9,1,1) __anonymous198__transmitter_ {
                     IC_XORDY $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     IC_VCOCAL $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     IC_PLLLOCK $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     IC_PLLUNLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     IC_PARDY $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     IC_PAILIM $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     IC_TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     IC_TXFIN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_INTCLR3_t_DM9 : _union(DM9,1,1) SFR_INTCLR3_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous198__transmitter__DM9 @0;
                 }
__anonymous200__transmitter__DM9 : _struct(DM9,1,1) __anonymous200__transmitter_ {
                     VDDXOEN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     VDDPLLEN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     VDDHSEN $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     XOEN $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PLLEN $bitfield_t_DM9 @0 _bf 1 @4 $__schar_DM9;
                     PAEN $bitfield_t_DM9 @0 _bf 1 @5 $__schar_DM9;
                     XO_READY $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     XO_READY_EN $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_TXPCON_t_DM9 : _union(DM9,1,1) SFR_TXPCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous200__transmitter__DM9 @0;
                 }
__anonymous202__transmitter__DM9 : _struct(DM9,1,1) __anonymous202__transmitter_ {
                     XODIV2CLKDIS $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                 }
     SFR_CLKRSTCON_t_DM9 : _union(DM9,1,1) SFR_CLKRSTCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous202__transmitter__DM9 @0;
                 }
__anonymous204__transmitter__DM9 : _struct(DM9,1,1) __anonymous204__transmitter_ {
                     CALRUN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     CALRESET $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     CAL_IDAC_FILT_EN $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     CAL_IDAC_CTRL $bitfield_t_DM9 @0 _bf 5 @3 $__schar_DM9;
                 }
     SFR_VCOCALCON_t_DM9 : _union(DM9,1,1) SFR_VCOCALCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous204__transmitter__DM9 @0;
                 }
__anonymous206__transmitter__DM9 : _struct(DM9,1,1) __anonymous206__transmitter_ {
                     FIXDIV_DIV2_EN $bitfield_t_DM9 @0 _bf 1 @0 $__schar_DM9;
                     PLL_LOCK_DETECT_EN $bitfield_t_DM9 @0 _bf 1 @1 $__schar_DM9;
                     PLL_LOCK_DETECT_MODE $bitfield_t_DM9 @0 _bf 1 @2 $__schar_DM9;
                     PLL_LOCK_DETECT_BLOCK $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PLL_LOCK_DETECT_TIME $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     PLL_UNLOCK_DETECTED $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     PLL_LOCK_DETECTED $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
        SFR_PLLCON_t_DM9 : _union(DM9,1,1) SFR_PLLCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous206__transmitter__DM9 @0;
                 }
         SFR_TXDAT_t_DM9 : _typedef(DM9,2,2) SFR_TXDAT_t $SFR_word_DM9;
         SFR_TXSPC_t_DM9 : _typedef(DM9,2,2) SFR_TXSPC_t $SFR_word_DM9;
__anonymous208__transmitter__DM9 : _struct(DM9,2,1) __anonymous208__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous209__transmitter__DM9 : _struct(DM9,2,2) __anonymous209__transmitter_ {
                     TXSTOP $bitfield_t_DM9 @0 _bf 1 @0 $__sint_DM9;
                     TXRESET $bitfield_t_DM9 @0 _bf 1 @1 $__sint_DM9;
                     TXPRBSPOLY $bitfield_t_DM9 @0 _bf 2 @2 $__sint_DM9;
                     TXPRBSINIT $bitfield_t_DM9 @0 _bf 1 @4 $__sint_DM9;
                     TXBUSY $bitfield_t_DM9 @0 _bf 1 @5 $__sint_DM9;
                     TXBE $bitfield_t_DM9 @0 _bf 1 @6 $__sint_DM9;
                     TXBU $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     DATSRC $bitfield_t_DM9 @0 _bf 2 @8 $__sint_DM9;
                     DATLSBF $bitfield_t_DM9 @0 _bf 1 @10 $__sint_DM9;
                     DATINV $bitfield_t_DM9 @0 _bf 1 @11 $__sint_DM9;
                     DATENC $bitfield_t_DM9 @0 _bf 3 @12 $__sint_DM9;
                     DATLAST $bitfield_t_DM9 @0 _bf 1 @15 $__sint_DM9;
                 }
       SFR_ENCCON0_t_DM9 : _union(DM9,2,2) SFR_ENCCON0_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous208__transmitter__DM9 @0;
                     bits $__anonymous209__transmitter__DM9 @0;
                 }
__anonymous211__transmitter__DM9 : _struct(DM9,2,1) __anonymous211__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous212__transmitter__DM9 : _struct(DM9,2,2) __anonymous212__transmitter_ {
                     BITCNT $bitfield_t_DM9 @0 _bf 4 @0 $__sint_DM9;
                     RPTCNT $bitfield_t_DM9 @0 _bf 7 @8 $__sint_DM9;
                     RPTFE $bitfield_t_DM9 @0 _bf 1 @15 $__sint_DM9;
                 }
       SFR_ENCCON1_t_DM9 : _union(DM9,2,2) SFR_ENCCON1_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous211__transmitter__DM9 @0;
                     bits $__anonymous212__transmitter__DM9 @0;
                 }
      SFR_FREQCON0_t_DM9 : _typedef(DM9,2,2) SFR_FREQCON0_t $SFR_word_DM9;
      SFR_FREQCON1_t_DM9 : _typedef(DM9,2,2) SFR_FREQCON1_t $SFR_word_DM9;
__anonymous214__transmitter__DM9 : _struct(DM9,2,1) __anonymous214__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous215__transmitter__DM9 : _struct(DM9,2,2) __anonymous215__transmitter_ {
                     MAINSC $bitfield_t_DM9 @0 _bf 11 @0 $__sint_DM9;
                     PRESC $bitfield_t_DM9 @0 _bf 3 @11 $__sint_DM9;
                 }
        SFR_BRGCON_t_DM9 : _union(DM9,2,2) SFR_BRGCON_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous214__transmitter__DM9 @0;
                     bits $__anonymous215__transmitter__DM9 @0;
                 }
__anonymous217__transmitter__DM9 : _struct(DM9,1,1) __anonymous217__transmitter_ {
                     FDEVMANT $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     FDEVEXP $bitfield_t_DM9 @0 _bf 3 @5 $__schar_DM9;
                 }
        SFR_FSKCON_t_DM9 : _union(DM9,1,1) SFR_FSKCON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous217__transmitter__DM9 @0;
                 }
__anonymous219__transmitter__DM9 : _struct(DM9,1,1) __anonymous219__transmitter_ {
                     FRMPMANT $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     FRMPEXP $bitfield_t_DM9 @0 _bf 3 @5 $__schar_DM9;
                 }
        SFR_FSKRMP_t_DM9 : _union(DM9,1,1) SFR_FSKRMP_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous219__transmitter__DM9 @0;
                 }
__anonymous221__transmitter__DM9 : _struct(DM9,2,1) __anonymous221__transmitter_ {
                     lo $uint8_t_DM9 @0;
                     hi $uint8_t_DM9 @1;
                 }
__anonymous222__transmitter__DM9 : _struct(DM9,2,2) __anonymous222__transmitter_ {
                     AMH $bitfield_t_DM9 @0 _bf 5 @0 $__sint_DM9;
                     RF_MUTE_EN $bitfield_t_DM9 @0 _bf 1 @6 $__sint_DM9;
                     ASK $bitfield_t_DM9 @0 _bf 1 @7 $__sint_DM9;
                     AML $bitfield_t_DM9 @0 _bf 5 @8 $__sint_DM9;
                 }
        SFR_ASKCON_t_DM9 : _union(DM9,2,2) SFR_ASKCON_t {
                     val $uint16_t_DM9 @0;
                     byte $__anonymous221__transmitter__DM9 @0;
                     bits $__anonymous222__transmitter__DM9 @0;
                 }
__anonymous224__transmitter__DM9 : _struct(DM9,1,1) __anonymous224__transmitter_ {
                     ARMPMANT $bitfield_t_DM9 @0 _bf 6 @0 $__schar_DM9;
                     ARMPEXP $bitfield_t_DM9 @0 _bf 2 @6 $__schar_DM9;
                 }
        SFR_ASKRMP_t_DM9 : _union(DM9,1,1) SFR_ASKRMP_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous224__transmitter__DM9 @0;
                 }
__anonymous226__transmitter__DM9 : _struct(DM9,1,1) __anonymous226__transmitter_ {
                     PA_LPF $bitfield_t_DM9 @0 _bf 2 @0 $__schar_DM9;
                     PA_10DBM_EN $bitfield_t_DM9 @0 _bf 1 @3 $__schar_DM9;
                     PA_CLK_OFF_TIME $bitfield_t_DM9 @0 _bf 2 @4 $__schar_DM9;
                     PA_0DBM_EN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     PA_READY $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
         SFR_PACON_t_DM9 : _union(DM9,1,1) SFR_PACON_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous226__transmitter__DM9 @0;
                 }
         SFR_PAPWR_t_DM9 : _typedef(DM9,1,1) SFR_PAPWR_t $SFR_byte_DM9;
        SFR_PATRIM_t_DM9 : _typedef(DM9,1,1) SFR_PATRIM_t $SFR_byte_DM9;
__anonymous228__transmitter__DM9 : _struct(DM9,1,1) __anonymous228__transmitter_ {
                     PA_IMAX $bitfield_t_DM9 @0 _bf 5 @0 $__schar_DM9;
                     PA_ILIM_EN $bitfield_t_DM9 @0 _bf 1 @6 $__schar_DM9;
                     PA_ILIM $bitfield_t_DM9 @0 _bf 1 @7 $__schar_DM9;
                 }
       SFR_PALIMIT_t_DM9 : _union(DM9,1,1) SFR_PALIMIT_t {
                     val $uint8_t_DM9 @0;
                     bits $__anonymous228__transmitter__DM9 @0;
                 }
phcaiKEyLLGenFunc_NbrssiError_t_DM9 : _enum(DM9,1,1) phcaiKEyLLGenFunc_NbrssiError_t $__uchar_DM9 {
                     NBRSSI_OK = 0;
                     NBRSSI_ERROR_SDADC_DIG_OVERLOAD = 1;
                     NBRSSI_ERROR_SDADC_ANA_OVERLOAD = 2;
                     NBRSSI_ERROR_INBAND_DISTURBER = 4;
                     NBRSSI_ERROR_PHASE_SPEED_HIGH = 8;
                     NBRSSI_ERROR_STRONG_DISTURBER = 16;
                 }
          __A6__uchar_DM : _array(DM,6,1) [6] $uint8_t_DM;
          __A1__uchar_DM : _array(DM,1,1) [1] $uint8_t_DM;
          __A8__uchar_DM : _array(DM,8,1) [8] $uint8_t_DM;
          __A4__uchar_DM : _array(DM,4,1) [4] $uint8_t_DM;
             uint32__ULP : _basic(ULP,1,1) uint32_;
             ulp32_t_ULP : _typedef(ULP,1,1) ulp32_t $uint32__ULP;
            _rng_m1_p1__ : _basic() _rng_m1_p1;
            _rng_m2_p2__ : _basic() _rng_m2_p2;
                 __dtp__ : _typedef() __dtp $__Pvoid__;
                 __stp__ : _typedef() __stp $__Pvoid__;
                 __ztp__ : _typedef() __ztp $__uint__;
               __sint_DM : _basic(DM,2,2) __sint;
             __P__sint__ : _pointer() $__Pvoid__ $__sint_DM;
            __P__cchar__ : _pointer() $__Pvoid__ $__cchar_DM;
                  void__ : _basic() void;
               __dtp___1 : _typedef() __dtp $__Pvoid__;
               __stp___1 : _typedef() __stp $__Pvoid__;
               __ztp___1 : _typedef() __ztp $__uint__;
             __PPMvoid__ : _basic() __PPMvoid;
             __PDMvoid__ : _basic() __PDMvoid;
               __dtp___2 : _typedef() __dtp $__Pvoid__;
               __stp___2 : _typedef() __stp $__Pvoid__;
               __ztp___2 : _typedef() __ztp $__uint__;
               __dtp___3 : _typedef() __dtp $__Pvoid__;
               __stp___3 : _typedef() __stp $__Pvoid__;
               __ztp___3 : _typedef() __ztp $__uint__;
           __PDM12void__ : _basic() __PDM12void;
               __dtp___4 : _typedef() __dtp $__Pvoid__;
               __stp___4 : _typedef() __stp $__Pvoid__;
               __ztp___4 : _typedef() __ztp $__uint__;
               __dtp___5 : _typedef() __dtp $__Pvoid__;
               __stp___5 : _typedef() __stp $__Pvoid__;
               __ztp___5 : _typedef() __ztp $__uint__;
            __PDM9void__ : _basic() __PDM9void;
               __dtp___6 : _typedef() __dtp $__Pvoid__;
               __stp___6 : _typedef() __stp $__Pvoid__;
               __ztp___6 : _typedef() __ztp $__uint__;
               __dtp___7 : _typedef() __dtp $__Pvoid__;
               __stp___7 : _typedef() __stp $__Pvoid__;
               __ztp___7 : _typedef() __ztp $__uint__;
            __PULPvoid__ : _basic() __PULPvoid;
               __dtp___8 : _typedef() __dtp $__PDMvoid__;
               __stp___8 : _typedef() __stp $__Pvoid__;
               __ztp___8 : _typedef() __ztp $__uint__;
               __dtp___9 : _typedef() __dtp $__Pvoid__;
               __stp___9 : _typedef() __stp $__Pvoid__;
               __ztp___9 : _typedef() __ztp $__uint__;
              __dtp___10 : _typedef() __dtp $__PDMvoid__;
              __stp___10 : _typedef() __stp $__Pvoid__;
              __ztp___10 : _typedef() __ztp $__uint__;
              __dtp___11 : _typedef() __dtp $__Pvoid__;
              __stp___11 : _typedef() __stp $__Pvoid__;
              __ztp___11 : _typedef() __ztp $__uint__;
              __dtp___12 : _typedef() __dtp $__PDMvoid__;
              __stp___12 : _typedef() __stp $__Pvoid__;
              __ztp___12 : _typedef() __ztp $__uint__;
              __dtp___13 : _typedef() __dtp $__Pvoid__;
              __stp___13 : _typedef() __stp $__Pvoid__;
              __ztp___13 : _typedef() __ztp $__uint__;
              __dtp___14 : _typedef() __dtp $__PDMvoid__;
              __stp___14 : _typedef() __stp $__Pvoid__;
              __ztp___14 : _typedef() __ztp $__uint__;
              __dtp___15 : _typedef() __dtp $__Pvoid__;
              __stp___15 : _typedef() __stp $__Pvoid__;
              __ztp___15 : _typedef() __ztp $__uint__;
              __dtp___16 : _typedef() __dtp $__PDM12void__;
              __stp___16 : _typedef() __stp $__Pvoid__;
              __ztp___16 : _typedef() __ztp $__uint__;
              __dtp___17 : _typedef() __dtp $__Pvoid__;
              __stp___17 : _typedef() __stp $__Pvoid__;
              __ztp___17 : _typedef() __ztp $__uint__;
              __dtp___18 : _typedef() __dtp $__PDM12void__;
              __stp___18 : _typedef() __stp $__Pvoid__;
              __ztp___18 : _typedef() __ztp $__uint__;
              __dtp___19 : _typedef() __dtp $__Pvoid__;
              __stp___19 : _typedef() __stp $__Pvoid__;
              __ztp___19 : _typedef() __ztp $__uint__;
              __dtp___20 : _typedef() __dtp $__PDM12void__;
              __stp___20 : _typedef() __stp $__Pvoid__;
              __ztp___20 : _typedef() __ztp $__uint__;
              __dtp___21 : _typedef() __dtp $__Pvoid__;
              __stp___21 : _typedef() __stp $__Pvoid__;
              __ztp___21 : _typedef() __ztp $__uint__;
              __dtp___22 : _typedef() __dtp $__PDM12void__;
              __stp___22 : _typedef() __stp $__Pvoid__;
              __ztp___22 : _typedef() __ztp $__uint__;
              __dtp___23 : _typedef() __dtp $__Pvoid__;
              __stp___23 : _typedef() __stp $__Pvoid__;
              __ztp___23 : _typedef() __ztp $__uint__;
              __dtp___24 : _typedef() __dtp $__PDM9void__;
              __stp___24 : _typedef() __stp $__Pvoid__;
              __ztp___24 : _typedef() __ztp $__uint__;
              __dtp___25 : _typedef() __dtp $__Pvoid__;
              __stp___25 : _typedef() __stp $__Pvoid__;
              __ztp___25 : _typedef() __ztp $__uint__;
              __dtp___26 : _typedef() __dtp $__PDM9void__;
              __stp___26 : _typedef() __stp $__Pvoid__;
              __ztp___26 : _typedef() __ztp $__uint__;
              __dtp___27 : _typedef() __dtp $__Pvoid__;
              __stp___27 : _typedef() __stp $__Pvoid__;
              __ztp___27 : _typedef() __ztp $__uint__;
              __dtp___28 : _typedef() __dtp $__PDM9void__;
              __stp___28 : _typedef() __stp $__Pvoid__;
              __ztp___28 : _typedef() __ztp $__uint__;
              __dtp___29 : _typedef() __dtp $__Pvoid__;
              __stp___29 : _typedef() __stp $__Pvoid__;
              __ztp___29 : _typedef() __ztp $__uint__;
              __dtp___30 : _typedef() __dtp $__PDM9void__;
              __stp___30 : _typedef() __stp $__Pvoid__;
              __ztp___30 : _typedef() __ztp $__uint__;
              __dtp___31 : _typedef() __dtp $__Pvoid__;
              __stp___31 : _typedef() __stp $__Pvoid__;
              __ztp___31 : _typedef() __ztp $__uint__;
              uint16_t__ : _typedef() uint16_t $__ushort__;
      TxRegSettings_t_DM : _struct(DM,20,2) TxRegSettings_t {
                     Cfg_FREQCON0 $uint16_t_DM @0;
                     Cfg_FREQCON1 $uint16_t_DM @2;
                     Cfg_PLLCON $uint8_t_DM @4;
                     Cfg_ASKCON $uint16_t_DM @6;
                     Cfg_FSKCON $uint8_t_DM @8;
                     Cfg_BRGCON $uint16_t_DM @10;
                     Cfg_ASKRMP $uint8_t_DM @12;
                     Cfg_FSKRMP $uint8_t_DM @13;
                     Cfg_PACON $uint8_t_DM @14;
                     Cfg_PAPWR $uint8_t_DM @15;
                     Cfg_PATRIM $uint8_t_DM @16;
                     Cfg_PALIMIT $uint8_t_DM @17;
                     Cfg_ENCCON0 $uint16_t_DM @18;
                 }
    __PTxRegSettings_t__ : _pointer() $__Pvoid__ $TxRegSettings_t_DM;
              __Pvoid_DM : _basic(DM,2,2) __Pvoid;
   __PTxRegSettings_t_DM : _pointer(DM,2,2) $__Pvoid_DM $TxRegSettings_t_DM;
               error_t__ : _enum() error_t $__uchar__ {
                     SUCCESS = 0;
                     ERROR = 1;
                 }
                bool_t__ : _enum() bool_t $__uchar__ {
                     FALSE = 0;
                     TRUE = 1;
                 }
               bool_t_DM : _enum(DM,1,1) bool_t $__uchar_DM {
                     FALSE = 0;
                     TRUE = 1;
                 }
         __PDM9__schar__ : _pointer() $__Pvoid__ $__schar_DM9;
              error_t_DM : _enum(DM,1,1) error_t $__uchar_DM {
                     SUCCESS = 0;
                     ERROR = 1;
                 }
             DataEnc_t__ : _enum() DataEnc_t $__uchar__ {
                     DataEnc_NRZ_single = 0;
                     DataEnc_Man_single = 1;
                     DataEnc_NRZ_double = 2;
                     DataEnc_Man_double = 3;
                 }
            __P__uchar__ : _pointer() $__Pvoid__ $uint8_t_DM;
           __P__uchar_DM : _pointer(DM,2,2) $__Pvoid_DM $uint8_t_DM;
            DataEnc_t_DM : _enum(DM,1,1) DataEnc_t $__uchar_DM {
                     DataEnc_NRZ_single = 0;
                     DataEnc_Man_single = 1;
                     DataEnc_NRZ_double = 2;
                     DataEnc_Man_double = 3;
                 }
          __PDM9__sint__ : _pointer() $__Pvoid__ $__sint_DM9;
               uint8_t__ : _typedef() uint8_t $__uchar__;
