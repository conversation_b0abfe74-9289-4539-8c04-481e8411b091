<project name="Project" processor="mrk3">
    <file type="c" name="phcaiKEyLLGenFunc_CS_ADC.c" path="../../../../intfs/src"/>
    <file type="c" name="phcaiKEyLLGenFunc_CS_AES.c" path="../../../../intfs/src"/>
    <file type="c" name="phcaiKEyLLGenFunc_CS_Common.c" path="../../../../intfs/src"/>
    <file type="c" name="phcaiKEyLLGenFunc_CS_IIU.c" path="../../../../intfs/src"/>
    <file type="c" name="phcaiKEyLLGenFunc_CS_Immo.c" path="../../../../intfs/src"/>
    <file type="c" name="phcaiKEyLLGenFunc_CS_UHF.c" path="../../../../intfs/src"/>
    <file type="c" name="phcaiKEyLLGenFunc_CS_ULPEE.c" path="../../../../intfs/src"/>
    <file type="c" name="phcaiKEyLLGenFunc_CS_Utils.c" path="../../../../intfs/src"/>
    <file type="c" name="phcaiKEyLLGenFunc_CS_Vbat.c" path="../../../../intfs/src"/>
    <option id="cpp.define" value="NCF29A1 ROMCODE_VERSION=6 MRK3_HAS_MUL MRK3_HAS_DIV" inherit="1"/>
    <option id="cpp.include" value="../../../../../../types ../../../../intfs/inc ../../../../../../external" inherit="1"/>
    <option id="ear.mur" value="on"/>
    <option id="ear.smur" value="on"/>
    <option id="project.name" value="libSystem.a"/>
    <option id="project.nsrc" value="on" cfg="Release"/>
    <option id="project.nvar" value="on" cfg="Release"/>
    <option id="project.postbuild" value="{..\..\copy_archive.bat} ncf29A1 RC007 &lt;CONFIG&gt;" inherit="1"/>
    <option id="project.type" value="arch"/>
</project>
