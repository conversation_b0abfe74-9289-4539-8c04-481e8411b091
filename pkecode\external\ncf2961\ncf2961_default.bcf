// Linker configuration file for NCF2961
// Using configuration option "_shared" if available.
// EROM size in KBytes specified by optional macro EROMSIZEKB, default =16.
// $Revision: 5767 $
//
//

_exclusive DM9  0x0000..0x017F // Reserved for SFR

// DM9 ( RAM 0x0180..0x01FF) will be used preferably for global scalars
// and data explicitly assigned to DM9 (more efficient addressing modes exist to access this data).
// Next line specifies that all symbols should be sorted on size before mapping.
_symbol_sort mem_size ascending

// Default stack size: 256 bytes.
// Note: in RC001 (no longer supported), the boot routine sets user stack pointer to 0800.
_stack     DM 0x0780..0x087F // User mode stack

_reserved  DM 0x0880..0x097F // empty area

// TEST: locally define EROMSIZEKB to avoid recompile of all project files when changed
// in project settings.
//#undef EROMSIZEKB
//#define EROMSIZEKB 8

// TEST: locally define NO_SHARED to avoid recompile of all project files when changed
//#define NO_SHARED

#if !defined( EROMSIZEKB )
// default is 16 KBytes EROM
#warning No EROM size specified, assuming 16 KBytes (EROMSIZEKB=16).
#define EROMSIZEKB 16
#endif

// IMPORTANT - explanations on conventional versus new "_shared" EROM memory allocation.
// From release 13R1.4 onwards, the "_shared" feature is implemented in Bridge (linker).
// It removes the need to partition the EROM in a part for code and another for rodata.
// If available, we recommend to use "_shared" instead of the fixed partitioning.
// Set macro NO_SHARED to avoid _shared and use the conventional linking mode.
//
// HOWEVER, in release 13R1.4, "_shared" requires to add the linker option +P
// in the project file (means: place code first, then rodata; see MRK3 User Guide).
// This option has become the default in later releases.
// For this reason, _shared is only used in this configuration file for release version
// K-2015.06 and later.
// Do not use option +P with versions before 13R1.4 to avoid error messages.
// Notes:
// In version 13R1.4,    __tct_release__ equals 1301, __tct_patch__ equals 04.
// In version K-2015.06, __tct_release__ equals 1502.
// In version L-2016.03, __tct_release__ equals 1601.

#if ( ( (__tct_release__ * 100) + __tct_patch__ ) == 130104 )
#warning Note: Although this linker version (release 13R1.4) already supports the \
  _shared feature, it is NOT used now, since it requires the linker option "+P". \
  This is not a default option before release 2015.06 and causes errors if missing \
  or if used with tool suite versions before 13R1.4.
#endif


#if ( ( __tct_release__ >= 1502 ) && !defined(NO_SHARED) )
// Note: if you wish to use _shared with 13R1.4 already, add "+P" linker option and
// enable following line instead of the above:
//if ( ( ( (__tct_release__ * 100) + __tct_patch__ ) >= 130104 ) && !defined(NO_SHARED) )

#if ( EROMSIZEKB == 16 )
#warning Note: _shared EROM, selected EROM size is 16 kBytes
_mem_size PM 0x2000
_mem_size DM 0x4000
_shared DM 0x0980..0x3FFF   PM 0x04C0..0x1FFF
_rodata DM 0x0980..0x3FFF

#elif ( EROMSIZEKB == 8 )
#warning Note: _shared EROM, selected EROM size is 8 kBytes
_mem_size PM 0x1000
_mem_size DM 0x2000
_shared DM 0x0980..0x1FFF   PM 0x04C0..0x0FFF
_rodata DM 0x0980..0x1FFF

#else
#error Unsupported EROM size specified. Set macro EROMSIZEKB = one of 16, 8.
#endif

#if ( ( (__tct_release__ * 100) + __tct_patch__ ) == 130104 )
#warning Make sure to set linker option "+P".
#endif


#else

// Conventional link mode, NOT using "_shared", fixed partitioning of EROM for code and rodata.

#if ( EROMSIZEKB == 16 )
#warning Note: Conventional link mode, selected EROM size is 16 kBytes (partitioned in 15 kB for code / 1 kB for rodata)

// EROM memory (word addressing): 0x0000..0x3FFF (16 kBytes), use 1 kByte for rodata
_mem_size  PM 0x1E00         // EROM area 0000h .. 1DFFh used for program code, remaining 1E00h..0x1FFFh for _rodata

// The range DM:0x0980..0x3FFF (byte addressing) is available for read-only data.
// However, it is shared with PM:0x04C0..0x1FFF. To avoid conflicts between
// code in PM and read only data in DM, we have to divide this region
// into a part usable for code, and a part usable for constant data.
// Here the split is chosen arbitrarily. It should be adapted if the
// linker does not find enough memory for either program code or
// constant data.
_reserved  DM 0x0980..0x3BFF // Reserved for code in PM:0x04C0..0x1EFF
_rodata    DM 0x3C00..0x3FFF // 1024 bytes read-only data in EROM (word adr.0x1E00..0x1FFF) mapped to DM
_reserved  DM 0x4000..0xFFFF // empty area

#elif ( EROMSIZEKB == 8 )
#warning Note: Conventional link mode, selected EROM size is 8 kBytes (partitioned in 7 kB for code / 1 kB for rodata)

// EROM memory (word addressing): 0x0000..0x1FFF (8 kBytes), use 1 kByte for rodata
_mem_size  PM 0x0E00         // EROM area 0000h .. 0DFFh used for program code, remaining 0E00h..0x0FFFh for _rodata

// The range DM:0x0980..0x1FFF (byte addressing) is available for read-only data.
// However, it is shared with PM:0x04C0..0x0FFF. To avoid conflicts between
// code in PM and read only data in DM, we have to divide this region
// into a part usable for code, and a part usable for constant data.
// Here the split is chosen arbitrarily. It should be adapted if the
// linker does not find enough memory for either program code or
// constant data.
_reserved  DM 0x0980..0x1BFF // Reserved for code in PM:0x04C0..0x0DFF
_rodata    DM 0x1C00..0x1FFF // 1024 bytes read-only data in EROM (word adr.0x0E00..0x0FFF) mapped to DM
_reserved  DM 0x2000..0xFFFF // empty area

#else

#error Unsupported EROM size specified. Set macro EROMSIZEKB = one of 16, 8.

#endif

#endif

_rwinit_mem    DM                  // place .rwinit segment in DM .rodata (with option +i)
_no_init_range DM  0x0000..0x017F  // exclude SFRs  from .rwinit
//_no_init_range DM  0x0880..0xFFFF  // exclude rodata (EROM area) from .rwinit and from loading by debugger
//_no_init_range ULP 0x0000..0x03FF  // exclude ULPEE from .rwinit and from loading by debugger

#if ( __tct_release__ >= 1601 )
#warning Note: 2016 (or newer) release; excluding ULP EEPROM and PM from _rwinit segment.
_rwinit_exclude ULP
_rwinit_exclude PM
#endif

// Defaults: symbol table and startup function main().
_symbol _vector_table 0
_entry_point _vector_table
// Note: not required, only nice to have main function on top of disassembly.
_symbol _main _after _vector_table

// These two variables are only used exclusively hence they are mapped to the same User RAM address.
_overlay hitagpro_user_param              0x180
_overlay phcaiKEyLLGenFunc_Func_Params    0x180

// eof
