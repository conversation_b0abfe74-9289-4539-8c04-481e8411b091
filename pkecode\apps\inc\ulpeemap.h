/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: ulpeemap.h 23740 2019-12-02 10:17:10Z dep10330 $
  $Revision: 23740 $
*/


/**
*  @file
*  Definitions of all ULP EEPROM page numbers used by demo applications.
*
*/

#ifndef ULPEEMAP_H
#define ULPEEMAP_H


/*
  Change Log

  2019-09-06 (MMr):
  - moved RKE pages to the end, to allow space for WFS6 pages.
 */

/*-----------------------------------------------------------------------------------------------*/

// Immobilizer Secret Key (ISK) Pages
#define EE_ISK0_PAGE                          0x001u
#define EE_ISK1_PAGE                          0x002u
#define EE_ISK2_PAGE                          0x003u
#define EE_ISK3_PAGE                          0x004u      /* used for AES only */

#define EEPAGE_TSI0                           0x008u      /* used by HT-PRO only */

#define EEPAGE_IMMORESVD                      0x010u      /* used by WFS6 only */

/*-----------------------------------------------------------------------------------------------*/

// Generic RKE

// Block 2 (pages 10h..17h) is used for the RKE configuration settings (formerly 0x010u)
#define EEPAGE_BASE_RKE_CONFIG                0x0D0u

// Block 3 (pages 18h..1Fh) : UHF config settings for RKE (8 pages) (formerly 0x018u)
#define EEPAGE_BASE_RKE_TX_CONFIG             0x0D8u

// Block 4 (pages 20h..27h) is used for the Remote Sequence Incr. Counter (formerly 0x020u)
#define EEPAGE_BASE_RSI                       0x0E0u

/*-----------------------------------------------------------------------------------------------*/

// Pages for configuration values of VBAT(RGE) registers (see VbatRegSettings_t)
#define EEPAGE_BASE_VBATREG_CONFIG            0x030

// Pages for PKE software settings/options (3 pages)
#define EEPAGE_BASE_PKESWOPTIONS              0x050

// Pages 0x53 - 0x57 reserved for test software

// Base page for RSSI software settings/options (8 pages)
#define EEPAGE_BASE_RSSISWOPTIONS             0x058

// Base page of UHF config settings for PKE (8 pages)
#define EEPAGE_BASE_PKE_TX_CONFIG             0x060

/*-----------------------------------------------------------------------------------------------*/

// Page to count occurances of Core Exceptions and store last status
// Bytes 3..0: CXPC.HI CXPC.LO CXSW count
#define EEPAGE_CX_DETECT                      0x068u

/*-----------------------------------------------------------------------------------------------*/

// Pages 0x78 .. 0x7F are reserved for HT-2E/-3/-AES transponder memory configuration
#define EEPAGE_MEMCFG0                        0x078u

/*-----------------------------------------------------------------------------------------------*/

// HT-2 secret keys for PKE (two pages)
#define EEPAGE_BASE_HT2_CRYPTO_DATA           0x080

// HT-3 secret keys for PKE (3 pages)
#define EEPAGE_BASE_HT3_CRYPTO_DATA           0x082

// AES secret keys for PKE (4 pages)
#define EEPAGE_BASE_AES_CRYPTO_DATA           0x085

/*-----------------------------------------------------------------------------------------------*/

// Pages in which calibration data for temperature sensor is stored
#define EEPAGE_TEMPSENS_CALIB_A               0x08E
#define EEPAGE_TEMPSENS_CALIB_B               0x08F

/*-----------------------------------------------------------------------------------------------*/

// Definition of pages for RSSI calibration data storage in ULPEE
#define EEPAGE_BASE_RSSI_CAL                  0x090u

#define EEPAGE_RSSI_CAL_CH1_R54               (EEPAGE_BASE_RSSI_CAL +  0u)
#define EEPAGE_RSSI_CAL_CH1_R36               (EEPAGE_BASE_RSSI_CAL +  1u)
#define EEPAGE_RSSI_CAL_CH1_R18               (EEPAGE_BASE_RSSI_CAL +  2u)
#define EEPAGE_RSSI_CAL_CH1_R0                (EEPAGE_BASE_RSSI_CAL +  3u)
#define EEPAGE_RSSI_CAL_CH1_Rm18              (EEPAGE_BASE_RSSI_CAL +  4u)

#define EEPAGE_RSSI_CAL_CH2_R54               (EEPAGE_BASE_RSSI_CAL +  5u)
#define EEPAGE_RSSI_CAL_CH2_R36               (EEPAGE_BASE_RSSI_CAL +  6u)
#define EEPAGE_RSSI_CAL_CH2_R18               (EEPAGE_BASE_RSSI_CAL +  7u)
#define EEPAGE_RSSI_CAL_CH2_R0                (EEPAGE_BASE_RSSI_CAL +  8u)
#define EEPAGE_RSSI_CAL_CH2_Rm18              (EEPAGE_BASE_RSSI_CAL +  9u)

#define EEPAGE_RSSI_CAL_CH3_R54               (EEPAGE_BASE_RSSI_CAL + 10u)
#define EEPAGE_RSSI_CAL_CH3_R36               (EEPAGE_BASE_RSSI_CAL + 11u)
#define EEPAGE_RSSI_CAL_CH3_R18               (EEPAGE_BASE_RSSI_CAL + 12u)
#define EEPAGE_RSSI_CAL_CH3_R0                (EEPAGE_BASE_RSSI_CAL + 13u)
#define EEPAGE_RSSI_CAL_CH3_Rm18              (EEPAGE_BASE_RSSI_CAL + 14u)

#define EEPAGE_RSSI_CAL_SLOPEIDEAL_R54        (EEPAGE_BASE_RSSI_CAL + 15u)
#define EEPAGE_RSSI_CAL_SLOPEIDEAL_R36        (EEPAGE_BASE_RSSI_CAL + 16u)
#define EEPAGE_RSSI_CAL_SLOPEIDEAL_R18        (EEPAGE_BASE_RSSI_CAL + 17u)
#define EEPAGE_RSSI_CAL_SLOPEIDEAL_R0         (EEPAGE_BASE_RSSI_CAL + 18u)
#define EEPAGE_RSSI_CAL_SLOPEIDEAL_Rm18       (EEPAGE_BASE_RSSI_CAL + 19u)


// 1-Point calibration: one page (four bytes for each channel)
#define EEPAGE_BASE_RSSI_1PCAL                0x0A8u

#define EEPAGE_RSSI_1PCAL_CH1 (EEPAGE_BASE_RSSI_1PCAL + 0u)
#define EEPAGE_RSSI_1PCAL_CH2 (EEPAGE_BASE_RSSI_1PCAL + 1u)
#define EEPAGE_RSSI_1PCAL_CH3 (EEPAGE_BASE_RSSI_1PCAL + 2u)

#define EEPAGE_BASE_RSSI_COMP_URE             0x0ABu

#define EEPAGE_BASE_RSSI_COMP_URE_CH1 (EEPAGE_BASE_RSSI_COMP_URE + 0u)
#define EEPAGE_BASE_RSSI_COMP_URE_CH2 (EEPAGE_BASE_RSSI_COMP_URE + 1u)
#define EEPAGE_BASE_RSSI_COMP_URE_CH3 (EEPAGE_BASE_RSSI_COMP_URE + 2u)

#define EEPAGE_RSSI_COMP_GM18DB               0x0AEu

/*-----------------------------------------------------------------------------------------------*/
// Configuration register settings for FXLS8962AF (NEWSTEIN) MEMS sensor

// Settings bank 0 for motion detection
#define EEPAGE_BASE_SENSOR_MDET_CONFIG        0x0B0u
// Settings bank 1 for data logger mode
#define EEPAGE_BASE_SENSOR_DLOG_CONFIG        0x0BCu
// Settings for on-chip transmitter
#define EEPAGE_BASE_DLOG_TX_CONFIG            0x0C8u

/*-----------------------------------------------------------------------------------------------*/

// Pages reserved for HT-PRO2 transponder distance memory and configuration bytes

#define EEPAGE_HTPRODISTMEM                   0x1E8u
#define EEPAGE_HTPROMEMCFG                    0x1F8u

/*-----------------------------------------------------------------------------------------------*/

// Configuration module (ULP15) pages:
// DCFG A (3D0h): LF field detection configuration (LFFLDDIS, CXDIS)
#define EEPAGE_FLD_CONFIG                     0x3D0u

// DCFG B (3D1h): transponder enable and select
#define EEPAGE_TRANSPONDER_CONFIG             0x3D1u

// DCFG C (3D2h): RFU

// DCFG D (3D3h): In TOKEN, control WriteEROM syscall enable (TOKEN-PLUS/-SRX: ENWER in DCFG A bits 31..30)
#define EEPAGE_ENABLE_WRITE_EROM              0x3D3u
// DCFG D (3D3h): In TOKEN-SRX, control EROM bank 2 (page flash) enable
#define EEPAGE_PF_ENABLE                      0x3D3u

// DCFG E (3D4h): bits 15..0 are the value written to LFTUNEVDD register at immo boot
#define EEPAGE_LFTUNE_IMMO                    0x3D4u

// page containing trim value PA_POWER_MAX in bits 14..8 (see DS; page 999)
#define EEPAGE_PA_POWER_MAX                   0x3E7u

// Page Address of IDE
#define IDE_PAGE                              0x3FFu

/*-----------------------------------------------------------------------------------------------*/

#endif

/* eof */
