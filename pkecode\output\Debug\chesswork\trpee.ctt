
// File generated by noodle version P-2019.09#78e58cd307#210222, Fri Nov  3 15:38:56 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/trpee.c mrk3

#const float_tininess_after_rounding	enum __anonymous0__trpee_ 0 (0x0)
#const float_tininess_before_rounding	enum __anonymous0__trpee_ 1 (0x1)
#const float_round_nearest_even	enum __anonymous1__trpee_ 0 (0x0)
#const float_round_to_zero	enum __anonymous1__trpee_ 1 (0x1)
#const float_round_up	enum __anonymous1__trpee_ 2 (0x2)
#const float_round_down	enum __anonymous1__trpee_ 3 (0x3)
#const SUCCESS	enum __anonymous2__trpee_ 0 (0x0)
#const ERROR	enum __anonymous2__trpee_ 1 (0x1)
#const FALSE	enum __anonymous3__trpee_ 0 (0x0)
#const TRUE	enum __anonymous3__trpee_ 1 (0x1)
