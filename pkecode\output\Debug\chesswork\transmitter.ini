
// File generated by noodle version P-2019.09#78e58cd307#210222, Sat Mar  2 20:51:11 2024
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/transmitter.c mrk3

mancode1/176 : #80 
mancode0/177 : #40 
encode/178 : #80 #40 #20 #10 #08 #04 #02 #01 
RF_FreqHCfg/179 : #7d #f2 #a4 #80 
RF_FreqLCfg/180 : #7d #99 #99 #80 
EE_RKE_TX_CONFIG_PAGE0/181 : #7da87080 
EE_RKE_TX_CONFIG_PAGE1/182 : #005f7201 
EE_RKE_TX_CONFIG_PAGE2/183 : #00001b66 
EE_RKE_TX_CONFIG_PAGE3/184 : #4f6c6b2a 
EE_RKE_TX_CONFIG_PAGE4/185 : #00003000 
EE_RKE1_TX_CONFIG_PAGE0/186 : #7da87080 
EE_RKE1_TX_CONFIG_PAGE1/187 : #005f7201 
EE_RKE1_TX_CONFIG_PAGE2/188 : #00001b66 
EE_RKE1_TX_CONFIG_PAGE3/189 : #4f6c6b2a 
EE_RKE1_TX_CONFIG_PAGE4/190 : #00003000 
EE_PKE_TX_CONFIG_PAGE0/191 : #7da87080 
EE_PKE_TX_CONFIG_PAGE1/192 : #005f7201 
EE_PKE_TX_CONFIG_PAGE2/193 : #00001b66 
EE_PKE_TX_CONFIG_PAGE3/194 : #4f6c5f2a 
EE_PKE_TX_CONFIG_PAGE4/195 : #00003000 
EE_PKE1_TX_CONFIG_PAGE0/196 : #7da87080 
EE_PKE1_TX_CONFIG_PAGE1/197 : #005f7201 
EE_PKE1_TX_CONFIG_PAGE2/198 : #00001b66 
EE_PKE1_TX_CONFIG_PAGE3/199 : #4f6c5f2a 
EE_PKE1_TX_CONFIG_PAGE4/200 : #00003000 
