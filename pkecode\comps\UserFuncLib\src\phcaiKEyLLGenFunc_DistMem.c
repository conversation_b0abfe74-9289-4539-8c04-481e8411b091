/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_DistMem.c 11676 2018-01-02 13:17:01Z dep17622 $
  $Revision: 11676 $

*/

/**
 * @file
 * Implementation of User Library Functions for HT-PRO2 Distance Memory access.
 */

/*
 * Change Log:
 *
 * 2015-09-03 (MMr):
 * - Improved MISRA-C compliance:
 *   incorrect local function declarations
 *
 * */

#include "phcaiKEyLLGenFunc.h"
#include "phcaiKEyLLGenFunc_CS.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @addtogroup HTPro2DistMem
 * @{
 */

/*-----------------------------------------------------------------------------------------------*/

/**
  * Check if the distance feature is enabled.
  * @param[in] cfg the distance feature configuration, as reported by phcaiKEyLLGenFunc_DistMem_init
  * @return cfg->is_enabled
  * @see phcaiKEyLLGenFunc_DistMem_init
  * @note Must run phcaiKEyLLGenFunc_DistMem_init before calling this function.
  */
static inline bool_t DistMem_check_if_feature_enabled(phcaiKEyLLGenFunc_DistMem_cfg_t *cfg);


/**
  * Compares two unsigned 24bit integers.
  *
  * @param[in] a the first "integer" (3 bytes array) to compare
  * @param[in] b the second "integer" (3 bytes array) to compare
  * @return TRUE if a >= b FALSE otherwise
  *
  * @note The "integer" parameters are not treated as a native integer type, but as arrays of
  * unsigned chars (uint8_t) and the function will start comparing from the MSB (index 0) to the LSB (index 2).
  */
static bool_t DistMem_cmp_ge_24bit(const uint8_t* const a, const uint8_t* const b);

/**
  * For a rejected data (data is less than the cfg->max_dist),
  * treat the data as a reject value, and decide wether the data needs to be stored on the reject entry or not.
  *
  * @param[in] cfg the dist configuration as generated by phcaiKEyLLGenFunc_DistMem_init
  * @param[in] data the data to be rejected
  *
  * @note Must run phcaiKEyLLGenFunc_DistMem_init before calling this function.
  * @see phcaiKEyLLGenFunc_DistMem_write
  */
static phcaiKEyLLGenFunc_DistMem_WriteResult_t
  DistMem_reject_write( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg, phcaiKEyLLGenFunc_DistMem_entry_t *data );

/**
  * Reset/clear the configuration:
  * set max_dist[i]=0, min_rej_dist[i]=0xFF, max_dist_entry_idx = 0.
  *
  * @param[in] cfg the dist configuration as generated by phcaiKEyLLGenFunc_DistMem_init
  */
static void DistMem_reset_cfg(phcaiKEyLLGenFunc_DistMem_cfg_t *cfg);

/*-----------------------------------------------------------------------------------------------*/

static inline bool_t DistMem_check_if_feature_enabled(phcaiKEyLLGenFunc_DistMem_cfg_t *cfg)
{
  return cfg->is_enabled;
}

/*-----------------------------------------------------------------------------------------------*/

static void DistMem_reset_cfg( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg )
{
  uint8_t idx;

  /* clear all entries in struct */
  for (idx=0U; idx<3U; idx++)
  {
    cfg->max_dist[idx] = 0U;
    cfg->min_rej_dist[idx] = 0xFFU;
  }

  /* point to item 0 as the max_dist_entry */
  cfg->max_dist_entry_idx = 0U;
}

/*-----------------------------------------------------------------------------------------------*/

static bool_t DistMem_cmp_ge_24bit( const uint8_t* const a, const uint8_t* const b )
{
  uint8_t i;
  bool_t result = TRUE;
  for (i = 0U; i < 3U; i++) {
    if (a[i] < b[i]) {
      result = FALSE;
    }

    if (a[i] != b[i]) {
      break;
    }
  }
  return result;
}

/*-----------------------------------------------------------------------------------------------*/

static phcaiKEyLLGenFunc_DistMem_WriteResult_t
  DistMem_reject_write( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg, phcaiKEyLLGenFunc_DistMem_entry_t *data )
{
  uint16_t dist_addr;
  uint8_t maxdist_page[4];
  uint8_t idx;
  phcaiKEyLLGenFunc_DistMem_WriteResult_t e_rescode;
  error_t e_eewrite;

  /* is the new value less than the minimum rejected distance? */
  if (DistMem_cmp_ge_24bit(data->distance, cfg->min_rej_dist) == FALSE)
  {
    /* yes, hence update the minimum rejected distance. */
    dist_addr = ((NUMBER_OF_HITAGPRO_BLOCKS - NUMBER_OF_HITAGPRO_DIST_MEM_BLOCKS)
                * (PHCAI_HITAGPRO_PAGES_PER_BLOCK)) + 14U;
    maxdist_page[0] = cfg->max_dist[0];
    maxdist_page[1] = cfg->max_dist[1];
    maxdist_page[2] = cfg->max_dist[2];
    maxdist_page[3] = 0U; /* the last byte in the maxdist is RFU so we write 0 in this implementation */

    /* write the distance to the rejected distance entry */
    e_eewrite = phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(data->distance, dist_addr);
    if (e_eewrite == SUCCESS)
    {
      /* write maxdist to the rejected distance entry */
      e_eewrite = phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(maxdist_page, dist_addr + 1U);
    }

    /* if hw i/o was successful update the memory structures. */
    if (e_eewrite == SUCCESS)
    {
      for (idx = 0U; idx<3U; idx++)
      {
        /* update the min_rej_distance upon a successful write */
        cfg->min_rej_dist[idx] = data->distance[idx];
      }
      e_rescode = DISTWR_REJECTED_MIN;
    }
    else
    {
      /* ULPEE write failed. */
      e_rescode = DISTWR_WRITE_FAIL_REJ;
    }
  }
  else
  {
    /* indicate that the minimum rejected distance was not updated. */
    e_rescode = DISTWR_REJECTED;
  }

  return e_rescode;
}

/*-----------------------------------------------------------------------------------------------*/

error_t phcaiKEyLLGenFunc_DistMem_init( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg )
{
  error_t ret_val = ERROR;
  uint8_t uma_cfg;
  uint8_t idx, idy, idz;
  uint8_t dist_data[PHCAI_HITAGPRO_BYTES_PER_PAGE];
  uint16_t dist_addr;

  // power on ULPEE to prepare for reading / writing
  //ULPSEL.val = 0xFFFFU;
  //ULPCON0.val |= 0x10U;
  phcaiKEyLLGenFunc_ULPEE_ActivateAllModules();
  phcaiKEyLLGenFunc_timer0_delay_us( t_ULP_PON_us );

  DistMem_reset_cfg(cfg);

  /* the feature is enabled if UMA configuration bit 1 is set */
  phcaiKEyLLGenFunc_CS_ULPEE_ReadBytes( &uma_cfg, (((HTPRO_CFG_PAGE + 4U) * PHCAI_HITAGPRO_BYTES_PER_PAGE) + 1U), 1U );

  /* Check if feature is enabled or not */
  if ( (uma_cfg & 0x02U) != 0U )
  {
    /* distance feature is enabled. */
    cfg->is_enabled = TRUE;
    cfg->dist_mem_blocks = NUMBER_OF_HITAGPRO_DIST_MEM_BLOCKS;

    dist_addr = (NUMBER_OF_HITAGPRO_BLOCKS - NUMBER_OF_HITAGPRO_DIST_MEM_BLOCKS)
                 * (PHCAI_HITAGPRO_PAGES_PER_BLOCK);

    /* figure out what the max_dist is out of all entries. */
    for (idx = 0U; idx < NUMBER_OF_HITAGPRO_DIST_ENTRIES; idx++)
    {
      phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(dist_data, (uint16_t)(((uint16_t)idx * 2U) + dist_addr));
      for (idy = 0U; idy < 3U; idy++) {
        if (dist_data[idy] > cfg->max_dist[idy]) {
          cfg->max_dist_entry_idx = idx;
          for (idz = 0U; idz < 3U; idz++) {
            cfg->max_dist[idz] = dist_data[idz];
          }
        }
        if (dist_data[idy] != cfg->max_dist[idy]) {
          break;
        }
      }
    }

    /* Read the rejected distance entry. */
    phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(dist_data, dist_addr + 14U);
    for (idx = 0U; idx < 3U; idx++) {
      cfg->min_rej_dist[idx] = dist_data[idx];
    }

    /* all good */
    ret_val = SUCCESS;
  }
  else
  {
    /* flag is 0 means that the distance feature is disabled. */
    cfg->is_enabled = FALSE;
    cfg->dist_mem_blocks = 0U;
  }

  return ret_val;
}

/*-----------------------------------------------------------------------------------------------*/

error_t phcaiKEyLLGenFunc_DistMem_reset( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg )
{
  /* Note: dont add static keyword since syscall does not accept source address outside user RAM. */
  const uint8_t zero_data[4] = { 0x00U, 0x00U, 0x00U, 0x00U };
  const uint8_t ones_data[4] = { 0xFFU, 0xFFU, 0xFFU, 0x00U };
  uint16_t idx;
  uint16_t dest_addr;
  error_t ret_val = ERROR;

  if ( DistMem_check_if_feature_enabled(cfg) == TRUE )
  {
    /* clears the RAM contents only */
    DistMem_reset_cfg(cfg);

    /* clear ULPEE entries */
    for ( idx = 0U; idx < 7U; idx++ )
    {
      /* calculate the destination address */
      dest_addr = (uint16_t)((NUMBER_OF_HITAGPRO_BLOCKS - NUMBER_OF_HITAGPRO_DIST_MEM_BLOCKS)
                  * (PHCAI_HITAGPRO_PAGES_PER_BLOCK)) + ( (uint16_t)idx * 2U);
      ret_val = phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(zero_data, dest_addr);
      if (ret_val == SUCCESS)
      {
        ret_val = phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(zero_data, dest_addr + 1U);
      }
      /* if we have a failure at any point then abort the execution */
      if (ret_val != SUCCESS)
      {
        break;
      }
    }
    /* set min.rejected to 0xFFFFFF00U and valid stored to 0 */
    if (ret_val == SUCCESS)
    {
      dest_addr = (uint16_t)((NUMBER_OF_HITAGPRO_BLOCKS - NUMBER_OF_HITAGPRO_DIST_MEM_BLOCKS)
                  * (PHCAI_HITAGPRO_PAGES_PER_BLOCK)) + ( 7U * 2U);
      ret_val = phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(ones_data, dest_addr);
      if (ret_val == SUCCESS) {
        ret_val = phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(zero_data, dest_addr+1U);
      }
    }
  }
  return ret_val;
}

/*-----------------------------------------------------------------------------------------------*/

error_t phcaiKEyLLGenFunc_DistMem_read_entry( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg,
                                              uint8_t entry_id,
                                              phcaiKEyLLGenFunc_DistMem_entry_t *ret_data )
{
  error_t ret_val = ERROR;
  uint16_t dist_addr;

  if ( DistMem_check_if_feature_enabled(cfg) == TRUE )
  {
    /* validate the input */
    if (entry_id < 8U)
    {
      /* Calculate the distance to be written */
      dist_addr = (uint16_t)((NUMBER_OF_HITAGPRO_BLOCKS - NUMBER_OF_HITAGPRO_DIST_MEM_BLOCKS)
                             * PHCAI_HITAGPRO_PAGES_PER_BLOCK)
                             + ((uint16_t)entry_id * NUMBER_OF_HITAGPRO_PAGES_PER_DIST_ENTRY);
      phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(ret_data->distance, dist_addr);
      phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(ret_data->date, dist_addr + 1U);
      ret_val = SUCCESS;
    }
  }
  return ret_val;
}

error_t phcaiKEyLLGenFunc_DistMem_read_latest( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg,
                                               phcaiKEyLLGenFunc_DistMem_entry_t *ret_data )
{
  /* the last entry is pointed by cfg->max_dist_entry_idx so reuse the phcaiKEyLLGenFunc_DistMem_read_entry */
  return phcaiKEyLLGenFunc_DistMem_read_entry( cfg, cfg->max_dist_entry_idx, ret_data );
}

/*-----------------------------------------------------------------------------------------------*/

phcaiKEyLLGenFunc_DistMem_WriteResult_t phcaiKEyLLGenFunc_DistMem_write( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg,
                                                                         phcaiKEyLLGenFunc_DistMem_entry_t *data )
{
  phcaiKEyLLGenFunc_DistMem_WriteResult_t e_rescode = DISTWR_DISABLED;
  error_t e_eewrite;
  uint8_t idx;
  uint8_t tmp_max_dist;
  uint16_t dist_addr;

  if ( DistMem_check_if_feature_enabled(cfg) == TRUE )
  {
    /* if the distance is lower than the max_dist   */
    if ( DistMem_cmp_ge_24bit(data->distance, cfg->max_dist) == TRUE )
    {
      /* use a temporary variable to hold the max_dist index, in order to update the next entry */
      tmp_max_dist = cfg->max_dist_entry_idx;
      tmp_max_dist++;

      /* if we are in the rejected entry (or above) restart from 0 */
      if ( tmp_max_dist >= NUMBER_OF_HITAGPRO_DIST_ENTRIES )
      {
        tmp_max_dist = 0U;
      }

      /* calculate the distance address */
      dist_addr = (uint16_t)((NUMBER_OF_HITAGPRO_BLOCKS - NUMBER_OF_HITAGPRO_DIST_MEM_BLOCKS)
                   * (PHCAI_HITAGPRO_PAGES_PER_BLOCK)) + ( (uint16_t)tmp_max_dist * 2U);

      /* perform the distance write */
      e_eewrite = phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(data->distance, dist_addr);
      if ( e_eewrite == SUCCESS )
      {
        /* perform the date write */
        e_eewrite = phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(data->date, dist_addr + 1U);
      }
      /* if everything is succesful we update the internal RAM structures */
      if ( e_eewrite == SUCCESS )
      {
        e_rescode = DISTWR_OK;
        cfg->max_dist_entry_idx = tmp_max_dist;
        for (idx = 0U; idx < 3U; idx++)
        {
          cfg->max_dist[idx] = data->distance[idx];
        }
      }
      else
      {
        e_rescode = DISTWR_WRITE_FAIL;
      }
    }
    else
    {
      /* in this case the value needs to be rejected! */
      e_rescode = DistMem_reject_write( cfg, data );
    }
  }

  return e_rescode;
}

/*-----------------------------------------------------------------------------------------------*/

/*@}*/
/*@}*/

/* eof */
