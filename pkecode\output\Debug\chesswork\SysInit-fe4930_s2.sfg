
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:29 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i SysInit-fe4930 mrk3

[
    0 : void_GPIO_Init typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : P1OUT typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P1OUT_t_DM9
   24 : P1DIR typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P1DIR_t_DM9
   26 : P2OUT typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P2OUT_t_DM9
   28 : P2DIR typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P2DIR_t_DM9
   30 : P1ALTF typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_P1ALTF_t_DM9
   32 : P2ALTF typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_P2ALTF_t_DM9
   36 : __ptr_P1OUT typ=int16_ val=0a bnd=m adro=20
   38 : __ptr_P1DIR typ=int16_ val=0a bnd=m adro=24
   40 : __ptr_P2OUT typ=int16_ val=0a bnd=m adro=26
   42 : __ptr_P2DIR typ=int16_ val=0a bnd=m adro=28
   44 : __ptr_P1ALTF typ=int16_ val=0a bnd=m adro=30
   46 : __ptr_P2ALTF typ=int16_ val=0a bnd=m adro=32
  113 : __seff typ=any bnd=m
  114 : __seff typ=any bnd=m
  115 : __seff typ=any bnd=m
  119 : __seff typ=any bnd=m
]
Fvoid_GPIO_Init {
    #5 off=0 nxt=8
    (__vola.12 var=13) source ()  <23>;
    (__sp.18 var=19) source ()  <29>;
    (P1OUT.19 var=20) source ()  <30>;
    (P1DIR.23 var=24) source ()  <34>;
    (P2OUT.25 var=26) source ()  <36>;
    (P2DIR.27 var=28) source ()  <38>;
    (P1ALTF.29 var=30) source ()  <40>;
    (P2ALTF.31 var=32) source ()  <42>;
    (__ptr_P1OUT.171 var=36) const_inp ()  <206>;
    <38> {
      (P1OUT.68 var=20 __vola.69 var=13 __seff.205 var=119 stl=nz_flag_w) load_const_store_1_B1 (__ptr_P1OUT.171 P1OUT.19 __vola.12)  <228>;
      (__seff.209 var=119 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.205)  <252>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <82>;
    } #8 off=2 nxt=9
    #9 off=2 nxt=12
    (__ptr_P1DIR.172 var=38) const_inp ()  <207>;
    <36> {
      (P1DIR.82 var=24 __vola.83 var=13 __seff.198 var=115 stl=nz_flag_w) load_const__or_const_store_1_B1 (__ptr_P1DIR.172 P1DIR.23 __vola.69)  <226>;
      (__seff.210 var=115 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.198)  <253>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <94>;
    } #12 off=4 nxt=13
    #13 off=4 nxt=16
    (__ptr_P2OUT.173 var=40) const_inp ()  <208>;
    <35> {
      (P2OUT.96 var=26 __vola.97 var=13 __seff.195 var=114 stl=nz_flag_w) load_const_store_1_B1 (__ptr_P2OUT.173 P2OUT.25 __vola.83)  <225>;
      (__seff.211 var=114 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.195)  <254>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <106>;
    } #16 off=6 nxt=17
    #17 off=6 nxt=18
    (__ptr_P2DIR.174 var=42) const_inp ()  <209>;
    <34> {
      (P2DIR.110 var=28 __vola.111 var=13 __seff.192 var=113 stl=nz_flag_w) load_const_store_1_B1 (__ptr_P2DIR.174 P2DIR.27 __vola.97)  <224>;
      (__seff.212 var=113 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.192)  <255>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <114>;
    } #18 off=8 nxt=19
    #19 off=8 nxt=20
    (__ptr_P1ALTF.175 var=44) const_inp ()  <210>;
    <33> {
      (P1ALTF.118 var=30 __vola.119 var=13) store_const_const_1_B1 (__ptr_P1ALTF.175 P1ALTF.29 __vola.111)  <223>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <121>;
    } #20 off=10 nxt=21
    #21 off=10 nxt=22
    (__ptr_P2ALTF.176 var=46) const_inp ()  <211>;
    <32> {
      (P2ALTF.126 var=32 __vola.127 var=13) store_const_const_1_B1 (__ptr_P2ALTF.176 P2ALTF.31 __vola.119)  <222>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <128>;
    } #22 off=12 nxt=24
    #24 off=12 nxt=-2
    () sink (__vola.127)  <135>;
    () sink (__sp.18)  <141>;
    () sink (P1OUT.68)  <142>;
    () sink (P1DIR.82)  <146>;
    () sink (P2OUT.96)  <148>;
    () sink (P2DIR.110)  <150>;
    () sink (P1ALTF.118)  <152>;
    () sink (P2ALTF.126)  <154>;
    <31> {
      () ret_1_B1 ()  <221>;
    } stp=0;
} #0
0 : 'apps/src/SysInit.c';
----------
0 : (0,66:0,0);
5 : (0,68:13,3);
8 : (0,69:7,4);
9 : (0,69:13,5);
12 : (0,73:7,6);
13 : (0,73:13,7);
16 : (0,74:7,8);
17 : (0,74:13,9);
18 : (0,74:13,9);
19 : (0,75:13,10);
20 : (0,75:13,10);
21 : (0,76:13,11);
22 : (0,76:13,11);
24 : (0,77:0,11);
----------
82 : (0,69:7,4);
94 : (0,73:7,6);
106 : (0,74:7,8);
114 : (0,74:13,9);
121 : (0,75:13,10);
128 : (0,76:13,11);
221 : (0,77:0,11);
222 : (0,76:8,10);
223 : (0,75:8,9);
224 : (0,74:7,8);
225 : (0,73:7,6);
226 : (0,69:7,4) (0,69:13,4);
228 : (0,68:7,2);

