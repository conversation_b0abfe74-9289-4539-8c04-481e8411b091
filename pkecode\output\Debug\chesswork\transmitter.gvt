
// File generated by noodle version P-2019.09#78e58cd307#210222, Sat Mar  2 20:51:11 2024
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/transmitter.c mrk3

[
    7 : P1INS typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P1INS_t_DM9
   29 : CLKCON2 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_CLKCON2_t_DM9
   41 : PRECON9 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON9_t_DM9
   51 : PRESTAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESTAT_t_DM9
  102 : BATSYS0 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_BATSYS0_t_DM9
  120 : PCON0 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PCON0_t_DM9
  146 : TXPCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_TXPCON_t_DM9
  150 : TXDAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_TXDAT_t_DM9
  152 : ENCCON0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ENCCON0_t_DM9
  153 : ENCCON1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ENCCON1_t_DM9
  154 : FREQCON0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_FREQCON0_t_DM9
  155 : FREQCON1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_FREQCON1_t_DM9
  156 : BRGCON typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_BRGCON_t_DM9
  157 : FSKCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_FSKCON_t_DM9
  158 : FSKRMP typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_FSKRMP_t_DM9
  159 : ASKCON typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ASKCON_t_DM9
  160 : ASKRMP typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_ASKRMP_t_DM9
  161 : PACON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PACON_t_DM9
  162 : PAPWR typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PAPWR_t_DM9
  163 : PATRIM typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PATRIM_t_DM9
  164 : PALIMIT typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PALIMIT_t_DM9
  176 : mancode1 typ=int8_ bnd=F sz=1 algn=1 stl=DM tref=__A1__uchar_DM
  177 : mancode0 typ=int8_ bnd=F sz=1 algn=1 stl=DM tref=__A1__uchar_DM
  178 : encode typ=int8_ bnd=F sz=8 algn=1 stl=DM tref=__A8__uchar_DM
  179 : RF_FreqHCfg typ=int8_ bnd=F sz=4 algn=1 stl=DM tref=__A4__uchar_DM
  180 : RF_FreqLCfg typ=int8_ bnd=F sz=4 algn=1 stl=DM tref=__A4__uchar_DM
  181 : EE_RKE_TX_CONFIG_PAGE0 typ=uint32_ val=64f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  182 : EE_RKE_TX_CONFIG_PAGE1 typ=uint32_ val=65f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  183 : EE_RKE_TX_CONFIG_PAGE2 typ=uint32_ val=66f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  184 : EE_RKE_TX_CONFIG_PAGE3 typ=uint32_ val=67f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  185 : EE_RKE_TX_CONFIG_PAGE4 typ=uint32_ val=68f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  186 : EE_RKE1_TX_CONFIG_PAGE0 typ=uint32_ val=80f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  187 : EE_RKE1_TX_CONFIG_PAGE1 typ=uint32_ val=81f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  188 : EE_RKE1_TX_CONFIG_PAGE2 typ=uint32_ val=82f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  189 : EE_RKE1_TX_CONFIG_PAGE3 typ=uint32_ val=83f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  190 : EE_RKE1_TX_CONFIG_PAGE4 typ=uint32_ val=84f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  191 : EE_PKE_TX_CONFIG_PAGE0 typ=uint32_ val=72f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  192 : EE_PKE_TX_CONFIG_PAGE1 typ=uint32_ val=73f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  193 : EE_PKE_TX_CONFIG_PAGE2 typ=uint32_ val=74f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  194 : EE_PKE_TX_CONFIG_PAGE3 typ=uint32_ val=75f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  195 : EE_PKE_TX_CONFIG_PAGE4 typ=uint32_ val=76f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  196 : EE_PKE1_TX_CONFIG_PAGE0 typ=uint32_ val=88f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  197 : EE_PKE1_TX_CONFIG_PAGE1 typ=uint32_ val=89f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  198 : EE_PKE1_TX_CONFIG_PAGE2 typ=uint32_ val=90f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  199 : EE_PKE1_TX_CONFIG_PAGE3 typ=uint32_ val=91f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  200 : EE_PKE1_TX_CONFIG_PAGE4 typ=uint32_ val=92f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
]
__transmitter_sttc {
} #0
----------
----------

