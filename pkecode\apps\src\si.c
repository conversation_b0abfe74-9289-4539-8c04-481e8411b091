#ifndef _SI_C
#define _SI_C
#endif

#include "si.h"
#include "timer.h"
#include "phcaiKEyLLGenFunc_CS.h"
/**********public variable here***********************************************************/
  uint32_t SI_Counts[8] = {0x00};
  uint8_t  SI_CntTx[4] ;
/**********static variable here***********************************************************/

/**********static funtion here******************************************/

void SI_GlobalVariableInit(void)
{
    uint8_t i = 0;

    for(i = 0; i < 8; i++)
    {
        SI_Counts[i] = 0x0;
    }
    for(i = 0; i < 4; i++)
    {
        SI_CntTx[i] = 0x0;
    }
}
/*********************************************************************/
/*----------------------------------------------------------------------------
;| Name:
;|   SI_Init
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
uint8_t SI_Init(void )
{
    uint8_t ret;
    ret = phcaiKEyLLGenFunc_CS_SI_Init_Ext( SI_Counts,SI_PAGE0,SI_PagSyncNum );
    return ret;
}
/*----------------------------------------------------------------------------
;| Name:
;|   SI_Inc
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
uint8_t SI_Inc( void )
{
    uint8_t ret; 
    ret = phcaiKEyLLGenFunc_CS_SI_Inc_Ext( SI_Counts,SI_PAGE0 );
    return ret;
}
/*----------------------------------------------------------------------------
;| Name:
;|   SI_Get
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
void SI_Get( uint8_t *si_value )
{
    phcaiKEyLLGenFunc_CS_SI_Get( SI_Counts,si_value );
}
/*----------------------------------------------------------------------------
;| Name:
;|   SI_ReSync
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
void SI_ReSync( void )
{
     uint8_t SICMP_Buf[4] = {0}; 
     uint8_t SILAST_Buf[4] = {0};
     uint8_t SITemp_Buf[4] = {0};
     uint32_t temp = 0; 
    uint8_t i = 0;
    uint32_t SITempCunt = 0;
    
    phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( SICMP_Buf,SICMP_PG );
    cycle_delay_ms( GAPDLY );
    phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( SILAST_Buf,SILAST_PG );
    cycle_delay_ms( GAPDLY ); 
    if( (SICMP_Buf[0] != SILAST_Buf[0])
        ||(SICMP_Buf[1] != SILAST_Buf[1])
        ||(SICMP_Buf[2] != SILAST_Buf[2])
        ||(SICMP_Buf[3] != SILAST_Buf[3])
      )
    { 
        phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(SICMP_Buf,SILAST_PG);
		cycle_delay_ms( GAPDLY); 
        SITempCunt =    (uint32_t)(((uint32_t)SICMP_Buf[0])<<(24U))
                       |(uint32_t)(((uint32_t)SICMP_Buf[1])<<(16U))
                       |(uint32_t)(((uint32_t)SICMP_Buf[2])<<(8U))
                       |(uint32_t)((uint32_t)SICMP_Buf[3]);
        temp = SITempCunt;
        
        if( (temp%8) == 0 )
        {
            temp = SITempCunt/8;
            SITemp_Buf[0]= (uint8_t)((temp&0xFF000000)>>(24U));
            SITemp_Buf[1]= (uint8_t)((temp&0x00FF0000)>>(16U)); 
            SITemp_Buf[2]= (uint8_t)((temp&0x0000FF00)>>(8U)); 
            SITemp_Buf[3]= (uint8_t)(temp&0x000000FF); 
            for(i=0;i<8;i++)
            {
                phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(SITemp_Buf,SIFIRST_PG+i); 
                cycle_delay_ms( GAPDLY );
            }
        }
        else if( (temp%8) != 0 )
        {
            temp = (SITempCunt/8)+1;
            SITemp_Buf[0]= (uint8_t)((temp&0xFF000000)>>(24U));
            SITemp_Buf[1]= (uint8_t)((temp&0x00FF0000)>>(16U)); 
            SITemp_Buf[2]= (uint8_t)((temp&0x0000FF00)>>(8U)); 
            SITemp_Buf[3]= (uint8_t)((temp&0x000000FF));
            for(i=0;i<8;i++)
            {
                phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(SITemp_Buf,SIFIRST_PG+i); 
                cycle_delay_ms( GAPDLY );
            } 
        }
        else
        {}
        phcaiKEyLLGenFunc_CS_SI_Init_Ext( SI_Counts,SI_PAGE0,SI_PagSyncNum );
    }
}
/*----------------------------------------------------------------------------
;| Name:
;|   SIError_ReSync
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
void SIError_ReSync( void )
{
        uint8_t SICMP_Buf[4] = {0}; 
        uint8_t SITemp_Buf[4] = {0};
        uint32_t temp = 0; 
        uint8_t i = 0;
        uint32_t SITempCunt = 0;
//-----read 7th page-----------------------------------------------------------------------    
        phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( SICMP_Buf,SICMP_PG );
        timer_delay_ms( GAPDLY );
//-----write 24th page-----------------------------------------------------------------------   
        phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(SICMP_Buf,SILAST_PG);
        cycle_delay_ms( GAPDLY); 
        
        SITempCunt =    (uint32_t)(((uint32_t)SICMP_Buf[0])<<(24U))
                       |(uint32_t)(((uint32_t)SICMP_Buf[1])<<(16U))
                       |(uint32_t)(((uint32_t)SICMP_Buf[2])<<(8U))
                       |(uint32_t)((uint32_t)SICMP_Buf[3]);
        temp = SITempCunt;
//-----write 8 page-----------------------------------------------------------------------   
        if( (temp%8) == 0 )
        {
            temp = SITempCunt/8;
            SITemp_Buf[0]= (uint8_t)((temp&0xFF000000)>>(24U));
            SITemp_Buf[1]= (uint8_t)((temp&0x00FF0000)>>(16U)); 
            SITemp_Buf[2]= (uint8_t)((temp&0x0000FF00)>>(8U)); 
            SITemp_Buf[3]= (uint8_t)(temp&0x000000FF); 
            for(i=0;i<8;i++)
            {
                phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(SITemp_Buf,SIFIRST_PG+i); 
                cycle_delay_ms( GAPDLY);
            }
        }
        else if( (temp%8) != 0 )
        {
            temp = (SITempCunt/8)+1;
            SITemp_Buf[0]= (uint8_t)((temp&0xFF000000)>>(24U));
            SITemp_Buf[1]= (uint8_t)((temp&0x00FF0000)>>(16U)); 
            SITemp_Buf[2]= (uint8_t)((temp&0x0000FF00)>>(8U)); 
            SITemp_Buf[3]= (uint8_t)((temp&0x000000FF));
            for(i=0;i<8;i++)
            {
                phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(SITemp_Buf,SIFIRST_PG+i); 
                timer_delay_ms( GAPDLY);
            } 
        }
        else
        {}
        phcaiKEyLLGenFunc_CS_SI_Init_Ext( SI_Counts,SI_PAGE0,SI_PagSyncNum );
}



















