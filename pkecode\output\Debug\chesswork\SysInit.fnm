
// File generated by noodle version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:28 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/SysInit.c mrk3

// toolrelease _19R3;

"D:/pke/01_code/00_230519/AP210054_SMARTKEY_CODE/apps/src/SysInit.c"
"D:\pke\01_code\00_230519\AP210054_SMARTKEY_CODE"

"SysInit-f419b5.sfg"
  : void_CPUClock_Init
  : "CPUClock_Init" global 43 Ofile
  (
    void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar
  )

"SysInit-fe4930.sfg"
  : void_GPIO_Init
  : "GPIO_Init" global 66 Ofile
  (
  )

"SysInit-55eb59.sfg"
  : __schar_Vbat_Check___ushort
  : "Vbat_Check" global 93 Ofile
  (
    error_t_phcaiKEyLLGenFunc_ADC_check_VbatMin___ushort___P__ushort
  )

"SysInit-c6cb69.sfg"
  : void_WUP_Init
  : "WUP_Init" global 114 Ofile
  (
    __ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort
    void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort
    void_timer_delay_ms___ushort
    error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort
  )

"SysInit-db3ca8.sfg"
  : void_hw_refresh_VBAT_VBATREG_registers
  : "hw_refresh_VBAT_VBATREG_registers" global 226 Ofile
  (
    void_Power_Off
    void_phcaiKEyLLGenFunc_CS_SetBatPORFlag_bool_t
    __uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort
    __ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort
    void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort
    void_timer_delay_ms___ushort
    error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort
  )

"SysInit-2768ff.sfg"
  : void_BatReset
  : "BatReset" global 389 Ofile
  (
    void_phcaiKEyLLGenFunc_CS_ResetVBat
  )

"SysInit-4cebda.sfg"
  : void_Power_Off
  : "Power_Off" global 408 Ofile
  (
    void_phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules
    void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar
    void_phcaiKEyLLGenFunc_timer0_delay_us___ushort
  )

"SysInit-85b6da.sfg"
  : void_read_IDE_from_ULPEE
  : "read_IDE_from_ULPEE" global 445 Ofile
  (
    void_phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait___ushort
    void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort
    void_restore_IDE_in_RAM
  )

"SysInit-78117e.sfg"
  : void_restore_IDE_in_RAM
  : "restore_IDE_in_RAM" global 486 Ofile
  (
  )

"SysInit-281542.sfg"
  : __uchar_hw_get_button_code
  : "hw_get_button_code" global 500 Ofile
  (
    void_phcaiKEyLLGenFunc_Util_Debounce___P__uchar___ushort___uchar___uchar
  )

""
  : void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar
  : "phcaiKEyLLGenFunc_CS_SetClkCon" global 1 Ofile
  (
  )

""
  : error_t_phcaiKEyLLGenFunc_ADC_check_VbatMin___ushort___P__ushort
  : "phcaiKEyLLGenFunc_ADC_check_VbatMin" global 1 Ofile
  (
  )

""
  : __ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort
  : "phcaiKEyLLGenFunc_ULPEE_ReadOneWord" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort
  : "phcaiKEyLLGenFunc_CS_ULPEE_ReadPage" global 1 Ofile
  (
  )

""
  : void_timer_delay_ms___ushort
  : "timer_delay_ms" global 1 Ofile
  (
  )

""
  : error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort
  : "phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_CS_SetBatPORFlag_bool_t
  : "phcaiKEyLLGenFunc_CS_SetBatPORFlag" global 1 Ofile
  (
  )

""
  : __uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort
  : "phcaiKEyLLGenFunc_ULPEE_ReadOneByte" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_CS_ResetVBat
  : "phcaiKEyLLGenFunc_CS_ResetVBat" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules
  : "phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_timer0_delay_us___ushort
  : "phcaiKEyLLGenFunc_timer0_delay_us" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait___ushort
  : "phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_Util_Debounce___P__uchar___ushort___uchar___uchar
  : "phcaiKEyLLGenFunc_Util_Debounce" global 1 Ofile
  (
  )

