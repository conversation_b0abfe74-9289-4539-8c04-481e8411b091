/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of HiRain Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   HiRain Technologies.
************************************************************************************************
*   File Name       : pke.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
* Revision History��
*
*  Version      Date      Initials         CR#            Descriptions
*  -------   ----------   --------       ------------   ------------------
*  1.0       2017/05/02   jian.tang        N/A            Original
************************************************************************************************
* END_FILE_HDR*/
#ifndef _PKE_C
#define _PKE_C
#endif 
//--------Include Files--------------------------------------------------------------------------------------
#include "phcaiKEyLLGenFunc_CS.h"
#include "phcaiKEyLLGenFunc.h"
#include "pke.h"
#include "transmitter.h"
#include "SysInit.h"
#include "lf_receive.h"
#include "rke.h"
#include "crypto.h"
#include "timer.h"
#include "interrupt.h"
#include "buttons.h"

//--------public variable here--------------------------------------------------------------------------------------
uint8_t LF_PollingActive = INACTIVE;
extern uint8_t RKEIsrPKE_Flag; 
//--------public function here--------------------------------------------------------------------------------------
void WUP1M_Event(void);
void WUP2M_Event(void);
void WUP3M_Event(void);
//--------static variable here--------------------------------------------------------------------------------------
static  uint8_t LF_Cmd = INACTIVE;
static  uint8_t LF_DataTemp;
static  uint8_t LF_ReadRsl = INACTIVE;
static  uint8_t LF_CrcRsl = INACTIVE;
static  uint8_t LF_VfyRsl = INACTIVE;
static  uint8_t LF_PgNum = INACTIVE;
static  uint8_t LF_WrtE2Rsl = INACTIVE;
static  uint8_t LF_Ctrl = INACTIVE; 

static const uint8_t Polling_Pream[2] = {0x00, 0x00};
static const uint8_t PKE_Pream[2]   = {0x00, 0x00};


static  uint8_t PKE_TXFream[35] = { 0x00 };
static  uint8_t LF_BlockTemp[32] = { 0x00 };

static  TxRegSettings_t PkeTxCfg;
static  TxRegSettings_t PkeTxCfg1;

static  uint16_t OffSet = INACTIVE;
static  uint16_t RssiCH1Val = INACTIVE;
static  uint16_t RssiCH2Val = INACTIVE;
static  uint16_t RssiCH3Val = INACTIVE;
static  uint32_t RSSIVal1 = INACTIVE;
static  uint32_t RSSIVal2 = INACTIVE;
static  uint32_t RSSIVal3 = INACTIVE;
static  uint32_t RSSISum = INACTIVE;
static  uint8_t RangeCH1;
static  uint8_t RangeCH2;
static  uint8_t RangeCH3;

static  uint8_t  AntNum = INACTIVE;

//--------static function here--------------------------------------------------------------------------------------
static void PKE_Authent( void );
static void PKE_Calibrate( void );
static void PKE_Rssi_All( void );
static void PKE_Rssi_Single( void );
static void PKE_Poll_Mute( void );
static void PKE_ReadVbat( void );
static void PKE_Read_Eeprom( void );
static void PKE_Write_Eeprom( void );
static void PKE_Authent_Rssi( void );
static void PKE_Flash_Led( void );

static void Polling_Cmd( void );
static void Polling_DataTrans(  uint8_t * Txbuf,uint8_t len );
static void Pke_Poll_IDERSSIAnt1( void );
static void Pke_Poll_IDE( void );
static void Pke_Poll_Enable( void );
static void Pke_Poll_Rssi( void );
static void Pke_Poll_Vbat( void );
static void pke_command_dispatch_default( void );
static void PKE_Cmd( void );

static void EnableE2Mod( uint16_t address );
static uint8_t Get_TXCrc( uint8_t * databuf,uint8_t len,uint8_t excrc );
static uint8_t Get_AntNum( uint16_t pagaddress );
static uint8_t Get_KeyNum( uint16_t pagaddress );
static void Rest_2msDetevc(void);
static void PKE_DataTrans( uint8_t * Txbuf,uint8_t len );
static void PKE_DataTrans_SigF( uint8_t * Txbuf,uint8_t len );
static void RssiInit(uint16_t *pucAdcOffset);
static uint8_t CalculateChRssi(phcaiKEyLLGenFunc_RssiChannel_t Chan, uint16_t * RssiVal,uint16_t ADCOffset );
static uint8_t StopRssi(void);

static uint32_t ConvertRssi(uint16_t u16_ADCOffset, uint16_t u16_ADC, uint8_t e_range);

extern uint8_t VbatLowPowerFlag;

void Pke_GlobalVariableInit(void)
{
  uint8_t i = 0;

  for(i = 0; i < 35; i++)
  {
    PKE_TXFream[i] = 0;
  }
  
  for(i = 0; i < 32; i++)
  {
    LF_BlockTemp[i] = 0;
  }
  LF_PollingActive = INACTIVE;
  LF_Cmd = INACTIVE;
  LF_DataTemp = INACTIVE;;
  LF_ReadRsl = INACTIVE;
  LF_CrcRsl = INACTIVE;
  LF_VfyRsl = INACTIVE;
  LF_PgNum = INACTIVE;
  LF_WrtE2Rsl = INACTIVE;
  LF_Ctrl = INACTIVE; 
  OffSet = INACTIVE;
  RssiCH1Val = INACTIVE;
  RssiCH2Val = INACTIVE;
  RssiCH3Val = INACTIVE;
  RSSIVal1 = INACTIVE;
  RSSIVal2 = INACTIVE;
  RSSIVal3 = INACTIVE;
  RSSISum = INACTIVE;
  RangeCH1 = INACTIVE;
  RangeCH2 = INACTIVE;
  RangeCH3 = INACTIVE;
  AntNum = INACTIVE;
}
//-------------------------------------------------------------------------------------------------
void WUP1M_Event(void)
{
	phcaiKEyLLGenFunc_ULPEE_ActivateAllModules();
	PKE_Cmd();
}

//-------------------------------------------------------------------------------------------------
void WUP2M_Event(void)
{
        phcaiKEyLLGenFunc_ULPEE_ActivateAllModules();
	PKE_Cmd();
	
}

//-------------------------------------------------------------------------------------------------
void WUP3M_Event(void)
{
	phcaiKEyLLGenFunc_ULPEE_ActivateAllModules();
	Polling_Cmd();
}

/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   Polling_Cmd
;| 
;| Description:
;|   Welcome lamp functuon 
;| 
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;| Return:
;|   none
-----------------------------------------------------------------------------------------------------------------*/
static void Polling_Cmd( void )
{
     uint8_t i,CRC;
     uint8_t keynum;   
       
        PKE_TXFream[0]  = RF_MODEPOLLINGSLEEP<<4;
        PKE_TXFream[0] |= ReseveData&0xF;                 
        for( i = 0; i < 4; i++ )
        {     
	   PKE_TXFream[i+1] = KEY_ID[ i ];
	} 	  
//--------------get reserve data-----------------------------------------------------------------------------------   
        for( i = 0; i < 6; i++ )
        {     
	   PKE_TXFream[i+5] = INACTIVE;  
	}
//--------------CRC--------------------------------------------------------------------------------------------          
	CRC = Get_TXCrc( PKE_TXFream,POLLINGWGLEN-1,CRCRSTVAL );
        PKE_TXFream[11] = CRC;
//--------------trans data----------------------------------------------------------------------------------------- 
       LED_ON();
       keynum = Get_KeyNum(ANT_Adr);
       timer_delay_ms( (keynum)*RF_CHANEL_GAP );
       Polling_DataTrans( PKE_TXFream,POLLINGWGLEN );
}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   PKE_Cmd
;| 
;| Description:
;|   PKE functuon choice
;| 
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;| Return:
;|   none
-----------------------------------------------------------------------------------------------------------------*/
static void PKE_Cmd( void )
{
   uint8_t i,CRC; 
   
  lf_read_init();
    
  LF_ReadRsl =  lf_read_block( LF_BlockTemp,1 );
  LF_Cmd = LF_BlockTemp[0];
  
  if( LF_ReadRsl == LF_OK )
  {
    switch( LF_Cmd )
    {
        case 0 :
        { 
            PKE_Authent();
            break;
        }
        case 1 : 
        {
           PKE_Rssi_All();
           break; 
        }
        case 2 :
        {
           PKE_Rssi_Single();
            break;
        }
        case 3 :
        {
            PKE_Poll_Mute();
            break;
        }
        case 4 :
        {
            PKE_ReadVbat();
            break;
        }
        case 5 :
        {
            PKE_Read_Eeprom();
            break;
        }
        case 6 :
        {
            PKE_Write_Eeprom();
            break;
        }
        case 7 :
        {
            PKE_Authent_Rssi();
            break;
        }
         case 8 :
        {
           Pke_Poll_IDERSSIAnt1(); 
            break;
        }
        case 9 :
        {
           PKE_Calibrate(); 
            break;
        }
        case 0x0a :
        {
            PKE_Flash_Led();
            break;
        }
        case 0x40:
        {
            Pke_Poll_IDE();   
            break;
        }
        case 0x41:
        {
            Pke_Poll_Enable();
            break;
        }
        case 0x42:
        {
            Pke_Poll_Rssi();
            break;
        }
          case 0x43:
        {
            //Pke_Poll_IDERSSIAnt1();not using
            break;
        }
        case 0x44:
        {
            Pke_Poll_Vbat();
            break;
        }
//------get invalid cmd---------------------------------        
        default:
        {
            pke_command_dispatch_default();
            break;
        }    
    }
  }
//------receive wrong data------------------------------ 
  else
  {
      pke_command_dispatch_default();
  }
}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   pke_command_dispatch_default
;| 
;| Description:
;|   Get Invalid Cmd.
;|   
;| Format:
;|   
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;| Result:
;|   none
-----------------------------------------------------------------------------------------------------------------*/
static void pke_command_dispatch_default( void )
{
   //LED_ON();
   //timer0_delay_ms( 100U );
}
/*-----------------------------------------------------------------------------
;| Name:
;|   PKE_Authent
;| 
;| Description:
;|   PKE authentication. Receives challenge + signature via LF interface,
;|   verifies correctness and transmits response via UHF interface (P17).
;| 
;| Format:
;|   LF: WUPB = IDE (4bytes) + Command (1byte) + random no (4bytes) + signature (2bytes)+ crc (1byte)
;|   RF: Response (6bytes) + crc (1byte)
-----------------------------------------------------------------------------
;| Parameters:
;|   none
;| 
;| Result:
;|   none
;| 
;| Broken Registers:
;|   all
-----------------------------------------------------------------------------*/
static void PKE_Authent( void )
{
    uint8_t CRC = 0;
    uint8_t i = 0;
    uint8_t index = 0;
    uint32_t RSSISumValidMaskH = 0x80000000;
    uint32_t RSSISumValidMaskL = 0x00800000;
    
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 7);
    WDRESET; 
     if( LF_ReadRsl == LF_OK )
    {
        LF_CrcRsl = lf_read_crc();
          
        if( LF_CrcRsl == LF_OK )
        {
          LF_VfyRsl = PKE_CYP( &LF_BlockTemp[1],AUTHMODE );
           if( TRUE == LF_VfyRsl )
           {   
                          
                for ( i = 0; i < 6; i++ ) 
	        {
	            PKE_TXFream[i] = LF_BlockTemp[i+1];
	        }	        
	       
	        timer_delay_ms( 1 );
	        Rest_2msDetevc();  
	        timer_delay_ms( 1 ); 
                Rest_2msDetevc();
                
	        WDRESET; 
                RssiInit( &OffSet ); 
                Rest_2msDetevc();
#ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif	       
//--------------get sum of squares------------------------------------------------------------------------------      
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
//--------------deal data --------------------------------------------------------------------------------------    
                if( RSSISum<=0xFFFF )
                {
                    if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[6] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[6] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[7] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[6] = (0x10-i);
                    PKE_TXFream[7] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[6] = (0x18-i);
                    PKE_TXFream[7] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
                 Rest_2msDetevc();
 //--------------second ANT Gap Confirm--6ms----------------------------------------------------------------------
                timer_delay_ms( 1 ); 
                Rest_2msDetevc(); 
                timer_delay_ms( 1 );
                Rest_2msDetevc();  
                timer_delay_ms( 1 );
                Rest_2msDetevc(); 
                timer_delay_ms( 1 );
                Rest_2msDetevc(); 
                timer_delay_ms( 1 );
                Rest_2msDetevc();    
                WDRESET;   
 //--------------get rssi Original data---------------------------------------------------------------------------  
 #ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------get sum of squares------------------------------------------------------------------------------      
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
//--------------deal data --------------------------------------------------------------------------------------    
                 if( RSSISum<=0xFFFF )
                {
                   if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[8] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[8] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[9] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[8] = (0x10-i);
                    PKE_TXFream[9] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[8] = (0x18-i);
                    PKE_TXFream[9] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
//--------------judge ant num--------------------------------------------------------------------------------------         
                    AntNum = Get_AntNum( ANT_Adr );
                if( AntNum == 0x02 )
                {
                    StopRssi();
 //--------------get bat status-----------------            
                    PKE_TXFream[10] = Vbat_Check( LowPower );
 //--------------CRC----------------------------           
                    CRC = Get_TXCrc( PKE_TXFream,AUTHRSSILEN2-1,CRCRSTVAL );
                    PKE_TXFream[11] = CRC;
            
	            Rest_2msDetevc();
	            WDRESET;
	   
//--------------trans data---------------------- 
                    LED_ON();
	            PKE_DataTrans( PKE_TXFream,AUTHRSSILEN2 ); 
                 }
                 else if( AntNum == 0x03 )
                 {
   //--------------third ANT Gap Confirm--6ms--------------------------------------------------------------------
                   timer_delay_ms( 1 ); 
                Rest_2msDetevc(); 
                                timer_delay_ms( 1 ); 
                Rest_2msDetevc(); 
                                timer_delay_ms( 1 ); 
                Rest_2msDetevc(); 
                                timer_delay_ms( 1 ); 
                Rest_2msDetevc(); 
                                timer_delay_ms( 1 ); 
                Rest_2msDetevc(); 
                                timer_delay_ms( 1 ); 
                Rest_2msDetevc(); 
                   // timer_delay_ms( GAPDLY+2 );    
                    WDRESET; 
 //--------------get rssi Original data----------------------------------------------------------------------- 
 #ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------get sum of squares------------------------------------------------------------------------------      
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
//--------------deal data --------------------------------------------------------------------------------------    
                 if( RSSISum<=0xFFFF )
                {
                    if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[10] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[10] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[11] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[10] = (0x10-i);
                    PKE_TXFream[11] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[10] = (0x18-i);
                    PKE_TXFream[11] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
//--------------end RSSI measrue-------------------------------------------------------------------------------=
                StopRssi();
 //--------------get bat status---------------------------------------------------------------------------------            
                PKE_TXFream[12] = Vbat_Check( LowPower );
 //--------------CRC--------------------------------------------------------------------------------------------           
                CRC = Get_TXCrc( PKE_TXFream,AUTHRSSILEN3-1,CRCRSTVAL );
                PKE_TXFream[13] = CRC;
            
	        Rest_2msDetevc();
	        WDRESET;
//--------------trans data-------------------------------------------------------------------------------------- 
                LED_ON();
	        PKE_DataTrans( PKE_TXFream,AUTHRSSILEN3 );
               }
               else
              {    
              }
        }
        else
        {
        }
        
    }
    else
    {
    }
    
  }  
}
/*-----------------------------------------------------------------------------
;| Name:
;|   PKE_Calibrate
;| 
;| Description:
;|   
;| 
;| Format:
;|   LF: WUPB = IDE (4bytes) + Command (1byte) + random no (4bytes) + signature (2bytes) + crc (1byte)+2ms gap + 4ms RSSI
;|   RF: Response (13bytes) + crc (1byte)
-----------------------------------------------------------------------------
;| Parameters:
;|   none
;| 
;| Result:
;|   none
;| 
;| Broken Registers:
;|   all
-----------------------------------------------------------------------------*/
static void PKE_Calibrate( void )
{
    uint8_t CRC = 0;
    uint8_t i = 0;
    uint8_t index = 0;
    uint32_t RSSISumValidMaskH = 0x80000000;
    uint32_t RSSISumValidMaskL = 0x00800000;
    
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 7);
    WDRESET; 
     if( LF_ReadRsl == LF_OK )
    {
        LF_CrcRsl = lf_read_crc();
          
        if( LF_CrcRsl == LF_OK )
        {
          LF_VfyRsl = PKE_CYP( &LF_BlockTemp[1],AUTHMODE );
           if(1)//( TRUE == LF_VfyRsl )
           {   
                          
                for ( i = 0; i < 6; i++ ) 
	        {
	            PKE_TXFream[i] = LF_BlockTemp[i+1];
	        }	        
                
	        WDRESET; 
                RssiInit( &OffSet ); 
                Rest_2msDetevc();
#ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif	       
//--------------get sum of squares------------------------------------------------------------------------------      
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
//--------------deal data --------------------------------------------------------------------------------------    
                if( RSSISum<=0xFFFF )
                {
                    if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[6] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[6] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[7] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[6] = (0x10-i);
                    PKE_TXFream[7] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[6] = (0x18-i);
                    PKE_TXFream[7] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
                
                 Rest_2msDetevc();
             
           PKE_TXFream[0] = (uint8_t)(RssiCH1Val&0x00FF);
           PKE_TXFream[1] = (uint8_t)((RssiCH1Val&0xFF00)>>(8U));
           PKE_TXFream[2] = RangeCH1;
           
           PKE_TXFream[3] = (uint8_t)(RssiCH2Val&0x00FF);
           PKE_TXFream[4] = (uint8_t)((RssiCH2Val&0xFF00)>>(8U));
           PKE_TXFream[5] = RangeCH2;
           
           PKE_TXFream[8] = (uint8_t)(RssiCH3Val&0x00FF);
           PKE_TXFream[9] = (uint8_t)((RssiCH3Val&0xFF00)>>(8U));;
           PKE_TXFream[10] = RangeCH3;
           PKE_TXFream[11] = 0x00;

//--------------end RSSI measrue-------------------------------------------------------------------------------=
                StopRssi();
 //--------------get bat status---------------------------------------------------------------------------------            
                PKE_TXFream[12] = Vbat_Check( LowPower );
 //--------------CRC--------------------------------------------------------------------------------------------           
                CRC = Get_TXCrc( PKE_TXFream,AUTHRSSILEN3-1,CRCRSTVAL );
                PKE_TXFream[13] = CRC;
            
	        Rest_2msDetevc();
	        WDRESET;
//--------------trans data-------------------------------------------------------------------------------------- 
                LED_ON();
	        PKE_DataTrans( PKE_TXFream,AUTHRSSILEN3 );
 
        }
        else
        {
        }
        
    }
    else
    {
    }
    
  }  
}
/*-----------------------------------------------------------------------------
;| Name:
;|   PKE_Rssi_All
;| 
;| Description:
;|   Receives parameters bytes via LF interface, performs RSSI measurements 
;|   on all three receiver channels and transmits via UHF a value proportional 
;|   to the field strength applied to the three coils.
;|   
;| Format:
;|   LF: WUPB = IDE (4bytes) + Command (1byte) + ADC Resolution (1byte) + CRC (1byte)
;|   RF: RSSI result in float (3bytes) + crc (1byte) 
-----------------------------------------------------------------------------
;| Parameters:
;|   crc = result of running CRC calculation (global variable)
;| 
;| Result:
;|   none
;| 
;| Broken Registers:
;|   all
-----------------------------------------------------------------------------*/
static void PKE_Rssi_All( void )
{
     uint8_t CRC;
      
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 1);
    LF_Ctrl = LF_BlockTemp[0];
    WDRESET;
    
   if( LF_ReadRsl == LF_OK )
  {
        LF_CrcRsl = lf_read_crc();
        
        Rest_2msDetevc();
        WDRESET;
        
        if( LF_CrcRsl == LF_OK )
        {
           RssiInit( &OffSet );
           Rest_2msDetevc();
 //--------------Original data-----------------------------------------------------------------------    
           RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
           Rest_2msDetevc();
	   RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	   Rest_2msDetevc();
	   RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	   Rest_2msDetevc();	   
	   
	   StopRssi();

         }
  }      
}
/*-----------------------------------------------------------------------------
;| Name:
;|   pke_rssi_single
;| 
;| Description:
;|   Receives parameter bytes via LF interface, performs RSSI measurements 
;|   on all three receiver channels and transmits via UHF 3 values (float format)
;|   proportional to the field strength at every single channel.
;|   
;| Format:
;|   LF: WUPB = IDE (4bytes) + Command (1byte) + Control:[MUXAR1 MUXAR0 ATTR AUTORNGEN LIMEN RES2 RES1 RES0] (1byte) + CRC (1byte)
;|   RF: RSSI results of every single channnel in float (3x3bytes) + crc (1byte)
;|    AUTORNGEN (Autorange enable) = 1: Autorange enabled (MUXAR1 MUXAR0 ATTR setting ignored)
;|    LIMEN (Limiter enable)=1: Enable Limiter range selection (only in autorange mode)
;|    MUXAR1 MUXAR0 ATTR: Manual range settings (see data sheet)
;|    RES2 RES1 RES0: ADC resolution setting (see data sheet)
-----------------------------------------------------------------------------
;| Parameters:
;|   crc = result of running CRC calculation (global variable)
;| 
;| Result:
;|   none
;| 
;| Broken Registers:
;|   all
-----------------------------------------------------------------------------*/
static void PKE_Rssi_Single( void )
{
    uint8_t CRC;
      
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 1);
    LF_Ctrl = LF_BlockTemp[0];
    WDRESET;
    
    if( LF_ReadRsl == LF_OK )
    {
        LF_CrcRsl = lf_read_crc();
        
        Rest_2msDetevc(); 
        WDRESET;
        
        if( LF_CrcRsl == LF_OK )
        {
           
           RssiInit( &OffSet );
           Rest_2msDetevc();
 //--------------Original data-----------------------------------------------------------------------  
  #ifdef debug
IOTestH();  
#endif  
           RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
           Rest_2msDetevc();
	   RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	   Rest_2msDetevc();
	   RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);

 #ifdef debug
IOTestL();  
#endif
	   Rest_2msDetevc();	   
	   StopRssi();   

           PKE_TXFream[0] = (uint8_t)(RssiCH1Val&0x00FF);
           PKE_TXFream[1] = (uint8_t)((RssiCH1Val&0xFF00)>>(8U));
           PKE_TXFream[2] = RangeCH1;
           
           PKE_TXFream[3] = (uint8_t)(RssiCH2Val&0x00FF);
           PKE_TXFream[4] = (uint8_t)((RssiCH2Val&0xFF00)>>(8U));
           PKE_TXFream[5] = RangeCH2;
           
           PKE_TXFream[6] = (uint8_t)(RssiCH3Val&0x00FF);
           PKE_TXFream[7] = (uint8_t)((RssiCH3Val&0xFF00)>>(8U));;
           PKE_TXFream[8] = RangeCH3;
           
           PKE_TXFream[9] = Vbat_Check( LowPower );
           
           CRC = Get_TXCrc( PKE_TXFream,RSSILEN-1,CRCRSTVAL );
           PKE_TXFream[10] = CRC;
           
         
           LED_ON(); 
	   PKE_DataTrans( PKE_TXFream,RSSILEN );
       }
        else
        {
            
        }
    }
    else 
    {
    }
      
}
/*-----------------------------------------------------------------------------
;| Name:
;|   pke_poll_mute
;| 
;| Description:
;|   Safely mutes the addressed tag for current anti collision polling sequence
;|   by changing WUPA from POLLING WUP (MWUP) to Activation WUP (AWUP).
;| 
;| Format:
;|   LF: WUPB = IDE (4bytes) + Command (1byte) + IDE (4bytes) + crc (1byte)
;|   RF: no response
-----------------------------------------------------------------------------
;| Parameters:
;|   none
;| 
;| Result:
;|   none
;| 
;| Broken Registers:
;|   all
-----------------------------------------------------------------------------*/
static void PKE_Poll_Mute( void )
{
      uint8_t CRC,i;
     uint16_t Vbat;
      
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 4);
    WDRESET;
    
   if( LF_ReadRsl == LF_OK )
  {
      LF_CrcRsl = lf_read_crc();     
      if( LF_CrcRsl == LF_OK )
      {   
            for(i=0;i<4;i++)
            {
	        LF_BlockTemp[ i ] -= KEY_ID[ i ];
            }
            if( (LF_BlockTemp[0]&LF_BlockTemp[1]&LF_BlockTemp[2]&LF_BlockTemp[3]) == 0 )
            {
                LED_ON();
                timer_delay_ms( 100 );
                LED_OFF();  
            }
      }
  }    
}
/*-----------------------------------------------------------------------------
;| Name:
;|   pke_read_vbat
;| 
;| Description:
;|   Performes battery voltage measurement and transmits the result via UHF.
;|   
;| Format:
;|   LF: WUPB = IDE (4bytes) + Command (1byte) + Load during VBAT measurement (1byte) + CRC (1byte)
;|   RF: VBAT result (0000..02FFh) (2byte) + crc (1byte) 
-----------------------------------------------------------------------------
;| Parameters:
;|   crc = result of running CRC calculation (global variable)
;| 
;| Result:
;|   none
;| 
;| Broken Registers:
;|   all
-----------------------------------------------------------------------------*/
static void PKE_ReadVbat( void )
{
     uint8_t CRC;
     uint16_t Vbat;
      
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 1);
    WDRESET;
    
    if( LF_ReadRsl == LF_OK )
    {
        LF_CrcRsl = lf_read_crc();
        
        if( LF_CrcRsl == LF_OK )
        {
           Vbat = phcaiKEyLLGenFunc_ADC_measure_VBAT();
           PKE_TXFream[0] = (uint8_t)(Vbat&0x00FF);
           PKE_TXFream[1] = (uint8_t)((Vbat&0xFF00)>>(8U));
        
           CRC = Get_TXCrc( PKE_TXFream,2,CRCRSTVAL );
           PKE_TXFream[2] = CRC;
          
         LED_ON();
	 PKE_DataTrans_SigF( PKE_TXFream,READBATLEN );  
        }
    }   
    
}
/*-----------------------------------------------------------------------------
;| Name:
;|   pke_read_eeprom
;| 
;| Description:
;|   Reads an EEPROM page and returns its content via UHF.
;|   
;| Format:
;|   LF: WUPB = IDE (4bytes) + Command (1byte) + EE page number (1byte) + CRC (1byte)
;|   RF: EEPROM page content (4bytes) + crc (1byte) 
-----------------------------------------------------------------------------
;| Parameters:
;|   crc = result of running CRC calculation (global variable)
;| 
;| Result:
;|   none
;| 
;| Broken Registers:
;|   all
-----------------------------------------------------------------------------*/
static void PKE_Read_Eeprom( void )
{
     uint8_t CRC;
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 1);
    LF_PgNum = LF_BlockTemp[0];
    WDRESET;
    
    if( LF_ReadRsl == LF_OK )
   {
        LF_CrcRsl = lf_read_crc();        
        if( (LF_CrcRsl == LF_OK) && (LF_PgNum >=PGRED_LIMIT) )
        {
           phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( PKE_TXFream,LF_PgNum ); 
           timer_delay_ms( GAPDLY + 1);          
           CRC = Get_TXCrc( PKE_TXFream,E2COUNTLEN-1,CRCRSTVAL );
           PKE_TXFream[4] = CRC;
           
	   PKE_DataTrans_SigF( PKE_TXFream,E2COUNTLEN ); 
        }
        else
        {
            
        }
  }   
}
/*-----------------------------------------------------------------------------
;| Name:
;|   pke_write_eeprom
;| 
;| Description:
;|   Writes an EEPROM page and returns its new content via UHF.
;|   
;| Format:
;|   LF: WUPB = IDE (4bytes) + Command (1byte) + EE page number (1byte) + EEPROM data (4bytes) + CRC (1byte)
;|   RF: updated EEPROM page content (4bytes) + crc (1byte) 
-----------------------------------------------------------------------------
;| Parameters:
;|   crc = result of running CRC calculation (global variable)
;| 
;| Result:
;|   none
;| 
;| Broken Registers:
;|   all
;-----------------------------------------------------------------------------*/
static void PKE_Write_Eeprom( void )
{
    uint8_t CRC;
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 5);
    LF_PgNum = LF_BlockTemp[0];
    WDRESET;
    
    if( LF_ReadRsl == LF_OK )
   {
//--------------CRC Check Out----------------------------------------------------------------------- 
      LF_CrcRsl = lf_read_crc();            
      if (LF_CrcRsl == LF_OK)  
      {
//--------------Write EEPROM-----------------------------------------------------------------------   
           BEGIN_CRITICAL_SECTION();      
           LF_WrtE2Rsl = phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage( &LF_BlockTemp[1], LF_PgNum); 
           END_CRITICAL_SECTION();
           if( LF_WrtE2Rsl == SUCCESS )
           {    
//--------------Read EEPROM and trans----------------------------------------------------------------------- 
                timer_delay_ms( GAPDLY + 3);//wait for ULPEE steady
                phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( PKE_TXFream,LF_PgNum );
                CRC = Get_TXCrc( PKE_TXFream,E2COUNTLEN-1,CRCRSTVAL );
                PKE_TXFream[4] = CRC;
            
	        PKE_DataTrans_SigF( PKE_TXFream,E2COUNTLEN );
           }
           else
           {
           }
       }
       else
       {
            
       }
  }  
}
/*-----------------------------------------------------------------------------
;| Name:
;|   pke_authent_and_rssi
;|
;| Descripition:
;|   Function impliments authentic and rssi single proceeding together.
;|
;| Format:
;|   LF: WUPB =IDE(4B)+Command(1B)+random(4B)+signature(2B)+CONTROL(1B)+crc(1B)
;|   RF: Response(6B) + RSSI results of every single channnel(3x3x2B) + crc(1B)
;|
;| Note:
;|   CONTROL = 0x15 by default.
;|   [MUXAR1 MUXAR0  ATTR AUTORNGEN  LIMEN  RES2  RES1  RES0]
;|   [     0      0     0      1         0     1     0     1]
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   crc = result of running CRC calculation (global variable)
;| 
;| Result:
;|   none 
;| 
;| Broken Registers:
;|   all
-----------------------------------------------------------------------------*/
static  void PKE_Authent_Rssi( void )
{
    uint8_t CRC = 0;
    uint8_t i = 0;
    uint8_t index = 0;
    uint32_t RSSISumValidMaskH = 0x80000000;
    uint32_t RSSISumValidMaskL = 0x00800000;
    
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 7);
    LF_Ctrl = LF_BlockTemp[0];
    WDRESET;
    
    if( LF_ReadRsl == LF_OK )
    {
        LF_CrcRsl = lf_read_crc();        
        if( LF_CrcRsl == LF_OK )
        {
//--------------crypto----------------------------------------------------------------------- 
            LF_VfyRsl = PKE_CYP( &LF_BlockTemp[1],AUTHMODE );
            if( LF_VfyRsl == TRUE ) 
            {
                for( i=0;i<6;i++)
                {
                   PKE_TXFream[i] = LF_BlockTemp[i+1];  
                } 
            }  
            if( LF_VfyRsl == TRUE ) 
            {
                RssiInit( &OffSet );
                //Rest_2msDetevc();
                //first ANT Gap Confirm---4ms---------------------------------------------------------------------------------- 
                //timer_delay_ms( GAPDLY+1  );    
                WDRESET;  
//--------------get rssi Original data-----------------------------------------------------------------------  
 #ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------get sum of squares------------------------------------------------------------------------------      
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
//--------------deal data --------------------------------------------------------------------------------------    
                if( RSSISum<=0xFFFF )
                {
                    if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[6] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[6] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[7] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                        if(RSSISum&RSSISumValidMaskL)
                        {
                            break;   
                        }
                        else
                        {
                            RSSISum = (RSSISum<<1);
                        }
                    }
                    PKE_TXFream[6] = (0x10-i);
                    PKE_TXFream[7] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[6] = (0x18-i);
                    PKE_TXFream[7] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
                //--------------second ANT Gap Confirm--5ms---------------------------------------------------------------------
                timer_delay_ms( 1 );    
                WDRESET;         
                //--------------get rssi Original data-----------------------------------------------------------------------  
 #ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------get sum of squares------------------------------------------------------------------------------      
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
//--------------deal data --------------------------------------------------------------------------------------    
                if( RSSISum<=0xFFFF )
                {
                    if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[8] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[8] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[9] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[8] = (0x10-i);
                    PKE_TXFream[9] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[8] = (0x18-i);
                    PKE_TXFream[9] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
 //--------------judge ant num----------------------------------------------------------         
                AntNum = Get_AntNum( ANT_Adr );
                if( AntNum == 0x02 )
                {
                    StopRssi();
 //--------------get bat status----------------------------------------------------------            
                    PKE_TXFream[10] = Vbat_Check( LowPower );
 //--------------CRC---------------------------------------------------------------------           
                    CRC = Get_TXCrc( PKE_TXFream,AUTHRSSILEN2-1,CRCRSTVAL );
                    PKE_TXFream[11] = CRC;
            
	            Rest_2msDetevc();
	            WDRESET;
	   
//--------------trans data----------------------------------------------------------------- 
                    LED_ON();
	            PKE_DataTrans( PKE_TXFream,AUTHRSSILEN2 ); 
                }
                else if( AntNum == 0x03 )
                {
                    //--------------third ANT Gap Confirm--5ms--------------------------------------------------------------------
                    timer_delay_ms(2);    
                    WDRESET;    
                    //--------------get rssi Original data----------------------------------------------------------------------- 
 #ifdef debug
IOTestH();  
#endif
                    RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                    Rest_2msDetevc();
                    RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
                    Rest_2msDetevc();
                    RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
                    Rest_2msDetevc();
#ifdef debug
IOTestL(); 
#endif
//--------------get sum of squares------------------------------------------------------------------------------      
                    RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
                    RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
                    RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
                    RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
//--------------deal data --------------------------------------------------------------------------------------    
                    if( RSSISum<=0xFFFF )
                    {
                        if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                          ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                          ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                          ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                          )
                        {
                           PKE_TXFream[10] = (uint8_t)((RSSISum+16)&0x000000FF);
                        }
                        else
                        {
                           PKE_TXFream[10] = (uint8_t)(RSSISum&0x000000FF);
                        }
                        PKE_TXFream[11] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                    }
                    else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                    {
                        for(i=0;i<8;i++)
                        {
                           if(RSSISum&RSSISumValidMaskL)
                           {
                             break;   
                           }
                           else
                           {
                            RSSISum = (RSSISum<<1);
                           }
                        }
                        PKE_TXFream[10] = (0x10-i);
                        PKE_TXFream[11] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                        
                    }
                    else if(RSSISum>0x00FFFFFF)
                    {
                        for(i=0;i<8;i++)
                        {
                           if(RSSISum&RSSISumValidMaskH)
                           {
                             break;   
                           }
                           else
                           {
                            RSSISum = (RSSISum<<1);
                           }
                        }
                        PKE_TXFream[10] = (0x18-i);
                        PKE_TXFream[11] = (uint8_t)((RSSISum&0xFF000000)>>24);
                    }
                    else
                    {
                        
                    }
//--------------end RSSI measrue-------------------------------------------------------------------------------=
                    StopRssi();
 //--------------get bat status---------------------------------------------------------------------------------          
                    PKE_TXFream[12] = Vbat_Check( LowPower );
 //--------------CRC--------------------------------------------------------------------------------------------          
                    CRC = Get_TXCrc( PKE_TXFream,AUTHRSSILEN3-1,CRCRSTVAL );
                    PKE_TXFream[13] = CRC;
            
                    Rest_2msDetevc();
                    WDRESET;
//--------------trans data-------------------------------------------------------------------------------------- 
                    LED_ON();
	            PKE_DataTrans( PKE_TXFream,AUTHRSSILEN3 );
	            LED_OFF();
                 } 
                 else
                 {
                     /*do nothing*/
                 }            
            } 
        }
    }
}
/*-----------------------------------------------------------------------------
;| Name:
;|   pke_flash_led
;| 
;| Description:
;|   Flash LED by 0.5Hz, last 3s.
;|   
;| Format:
;|   LF: WUPB = IDE (4bytes) + Command (1byte) + CRC (1byte)
;|   RF: None 
-----------------------------------------------------------------------------
;| Parameters:
;|   crc = result of running CRC calculation (global variable)
;| 
;| Result:
;|   none
;| 
;| Broken Registers:
;|   all
-----------------------------------------------------------------------------*/
static void PKE_Flash_Led( void )
{
    uint8_t i;
//--------------CRC Check Out-------------------------------------------------   
     LF_CrcRsl = lf_read_crc();        
     if( LF_CrcRsl == LF_OK )
     {
//--------------flash led by 0.5Hz,last 3s------------------------------------        
         LED_ON();
         timer_delay_ms( 100 );
         
         LED_OFF(); 
         timer_delay_ms( 100 ); 
         
         LED_ON(); 
         timer_delay_ms( 100 );
         
         LED_OFF(); 
     }
}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   Pke_Poll_IDERSSIAnt1
;| 
;| Description:
;|   The IDE (page 0) is transmitted in a randomly choosen RF time slot.
;|   
;| Format:
;|   LF: WUPA = POLLING-WUP (4bytes) + Command (1byte) + CRC (1byte)
;|   RF: IDE (4bytes) + crc (1byte) in random RF time slot
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;| 
;| Result:
;|   none

-----------------------------------------------------------------------------------------------------------------*/
static void Pke_Poll_IDERSSIAnt1( void )
{
    uint8_t CRC=0,i=0;
    uint8_t keynum=0;
    static uint8_t battemp = 0x00u; 
    uint32_t RSSISumValidMaskH = 0x80000000;
    uint32_t RSSISumValidMaskL = 0x00800000;
    
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 1);
    LF_Ctrl = LF_BlockTemp[0];
    WDRESET;
    
    if( LF_ReadRsl == LF_OK )
    {
        LF_CrcRsl = lf_read_crc();
        Rest_2msDetevc(); 
        if( LF_CrcRsl == LF_OK )
        {
          PortIntEnable();
//--------------Reset Trans Data----------------------------------------------------------------------------                   
          for( i = 0; i < POLLING_ACTIVELEN; i++ )
          {     
            PKE_TXFream[i] = INACTIVE;  
          } 
//--------------stor ID&MODE-------------------------------------------------------------------------------------  
          PKE_TXFream[0]  = RF_MODEPOLLINGACTIVE_L<<4;
          PKE_TXFream[0] |= VbatLowPowerFlag & 0x1;                 
          for( i = 0; i < 4; i++ )
          {     
            PKE_TXFream[i+1] = KEY_ID[ i ];
          } 	  
//--------------get rssi Original data----------------------------------------------------------------------------                   
                RssiInit( &OffSet );
                Rest_2msDetevc();
 //first ANT Gap Confirm---2ms---------------------------------------------------------------------------------- 
  cycle_delay_ms( 1 );
  Rest_2msDetevc();  
  cycle_delay_ms( 1 );
  Rest_2msDetevc();  
  WDRESET;  
 //--------------get rssi Original data-----------------------------------------------------------------------  
#ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------get sum of squares------------------------------------------------------------------------------      
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
//--------------deal data --------------------------------------------------------------------------------------    
               if( RSSISum<=0xFFFF )
                {
                  if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[5] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[5] = (uint8_t)(RSSISum&0x000000FF);
                    }
	                  PKE_TXFream[6] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[5] = (0x10-i);
                    PKE_TXFream[6] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[5] = (0x18-i);
                    PKE_TXFream[6] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
 //--------------second ANT Gap Confirm--5ms---------------------------------------------------------------------
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  WDRESET;   
 //--------------get rssi Original data-----------------------------------------------------------------------  
#ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------get sum of squares------------------------------------------------------------------------------      
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
//--------------deal data --------------------------------------------------------------------------------------    
              if( RSSISum<=0xFFFF )
                {
                    if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[7] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[7] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[8] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[7] = (0x10-i);
                    PKE_TXFream[8] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[7] = (0x18-i);
                    PKE_TXFream[8] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
 //--------------third ANT Gap Confirm--6ms--------------------------------------------------------------------
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  WDRESET;    
 //--------------get rssi Original data----------------------------------------------------------------------- 
#ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------deal data --------------------------------------------------------------------------------------    
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
             if( RSSISum<=0xFFFF )
                {
                   if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[9] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[9] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[10] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[9] = (0x10-i);
                    PKE_TXFream[10] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[9] = (0x18-i);
                    PKE_TXFream[10] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
//--------------fourth ANT Gap Confirm--5ms--------------------------------------------------------------------
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  WDRESET;    
 //--------------get rssi Original data----------------------------------------------------------------------- 
#ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------deal data --------------------------------------------------------------------------------------    
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
             if( RSSISum<=0xFFFF )
                {
                   if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[11] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[11] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[12] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[11] = (0x10-i);
                    PKE_TXFream[12] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[11] = (0x18-i);
                    PKE_TXFream[12] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
//--------------fifth ANT Gap Confirm--6ms--------------------------------------------------------------------
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc();
  cycle_delay_ms( 1 );
  Rest_2msDetevc();  
  WDRESET;    
 //--------------get rssi Original data----------------------------------------------------------------------- 
 #ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------deal data --------------------------------------------------------------------------------------    
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
             if( RSSISum<=0xFFFF )
                {
                   if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[13] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[13] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[14] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[13] = (0x10-i);
                    PKE_TXFream[14] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[13] = (0x18-i);
                    PKE_TXFream[14] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
                
//--------------six ANT Gap Confirm--6ms--------------------------------------------------------------------
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  WDRESET;    
 //--------------get rssi Original data----------------------------------------------------------------------- 
 #ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------deal data --------------------------------------------------------------------------------------    
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
             if( RSSISum<=0xFFFF )
                {
                   if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[15] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[15] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[16] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[15] = (0x10-i);
                    PKE_TXFream[16] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[15] = (0x18-i);
                    PKE_TXFream[16] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
//--------------seven ANT Gap Confirm--5ms--------------------------------------------------------------------
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  WDRESET;    
 //--------------get rssi Original data----------------------------------------------------------------------- 
 #ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------deal data --------------------------------------------------------------------------------------    
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
             if( RSSISum<=0xFFFF )
                {
                   if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[17] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[17] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[18] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[17] = (0x10-i);
                    PKE_TXFream[18] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[17] = (0x18-i);
                    PKE_TXFream[18] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
//--------------eight ANT Gap Confirm--5ms--------------------------------------------------------------------
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  cycle_delay_ms( 1 );
  Rest_2msDetevc(); 
  WDRESET;    
 //--------------get rssi Original data----------------------------------------------------------------------- 
 #ifdef debug
IOTestH();  
#endif
                RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
                Rest_2msDetevc();
	        RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	        Rest_2msDetevc();
	        RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	        Rest_2msDetevc();
#ifdef debug
IOTestL();  
#endif
//--------------deal data --------------------------------------------------------------------------------------    
	        RSSIVal1 = ConvertRssi( OffSet,RssiCH1Val,RangeCH1 );
	        RSSIVal2 = ConvertRssi( OffSet,RssiCH2Val,RangeCH2 );
	        RSSIVal3 = ConvertRssi( OffSet,RssiCH3Val,RangeCH3 );
	        RSSISum = (RSSIVal1*RSSIVal1) + (RSSIVal2*RSSIVal2) + (RSSIVal3*RSSIVal3); 
             if( RSSISum<=0xFFFF )
                {
                   if( ((uint8_t)(RSSISum&0x000000FF)== 0x09)||((uint8_t)(RSSISum&0x000000FF)== 0x0A)||((uint8_t)(RSSISum&0x000000FF)== 0x0B)||((uint8_t)(RSSISum&0x000000FF)== 0x0C)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x0D)||((uint8_t)(RSSISum&0x000000FF)== 0x0E)||((uint8_t)(RSSISum&0x000000FF)== 0x0F)||((uint8_t)(RSSISum&0x000000FF)== 0x10)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x11)||((uint8_t)(RSSISum&0x000000FF)== 0x12)||((uint8_t)(RSSISum&0x000000FF)== 0x13)||((uint8_t)(RSSISum&0x000000FF)== 0x14)
                      ||((uint8_t)(RSSISum&0x000000FF)== 0x15)||((uint8_t)(RSSISum&0x000000FF)== 0x16)||((uint8_t)(RSSISum&0x000000FF)== 0x17)||((uint8_t)(RSSISum&0x000000FF)== 0x18)
                      )
                    {
                       PKE_TXFream[19] = (uint8_t)((RSSISum+16)&0x000000FF);
                    }
                    else
                    {
                       PKE_TXFream[19] = (uint8_t)(RSSISum&0x000000FF);
                    }
	            PKE_TXFream[20] = (uint8_t)( (RSSISum>>8)&0x000000FF);
                }
                else if( (RSSISum>0x0000FFFF)&&(RSSISum<=0x00FFFFFF) )
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskL)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[19] = (0x10-i);
                    PKE_TXFream[20] = (uint8_t)((RSSISum&0x00FF0000)>>16);
                    
                }
                else if(RSSISum>0x00FFFFFF)
                {
                    for(i=0;i<8;i++)
                    {
                       if(RSSISum&RSSISumValidMaskH)
                       {
                         break;   
                       }
                       else
                       {
                        RSSISum = (RSSISum<<1);
                       }
                    }
                    PKE_TXFream[19] = (0x18-i);
                    PKE_TXFream[20] = (uint8_t)((RSSISum&0xFF000000)>>24);
                }
                else
                {
                    
                }
//--------------end RSSI measrue-------------------------------------------------------------------------------=
                StopRssi();
//--------------CRC--------------------------------------------------------------------------------------------          
                CRC = Get_TXCrc( PKE_TXFream,POLLING_ACTIVELEN-1,CRCRSTVAL );
                PKE_TXFream[21] = CRC;
            
	        Rest_2msDetevc();
	        WDRESET;                
//--------------trans data----------------------------------------------------------------------------------------- 
          BEGIN_CRITICAL_SECTION();
          
          LED_ON();    
          keynum = Get_KeyNum(ANT_Adr);
          cycle_delay_ms((keynum)*RF_CHANEL_GAP);//get the time channel and delay 
          
          if( RKEIsrPKE_Flag!=ACTIVE )
          {
              Polling_DataTrans( PKE_TXFream,POLLING_ACTIVELEN );  
          }
          
          END_CRITICAL_SECTION();
          PortIntDisable();
          
          if(RKEIsrPKE_Flag==ACTIVE)
          {
            KEY_Code_ISR_Temp = KEY_Code_ISR;
            RKEIsrPKE_Flag = INACTIVE;
            rke_isr();
          }
      }
   }
}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   Pke_Poll_IDE
;| 
;| Description:
;|   The IDE (page 0) is transmitted in a randomly choosen RF time slot.
;|   
;| Format:
;|   LF: WUPA = POLLING-WUP (4bytes) + Command (1byte) + CRC (1byte)
;|   RF: IDE (4bytes) + crc (1byte) in random RF time slot
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;| 
;| Result:
;|   none

-----------------------------------------------------------------------------------------------------------------*/
static void Pke_Poll_IDE( void )
{
    uint8_t CRC,Random,u16_delay,i;
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 2);
    WDRESET;
    
   if( LF_ReadRsl == LF_OK )
  {
      LF_CrcRsl = lf_read_crc();        
      if( LF_CrcRsl == LF_OK )
      {
          //phcaiKEyLLGenFunc_Util_RNG_Start( (rng_con_t) 0x3EU, LEAVE_ON );
          //Random = phcaiKEyLLGenFunc_RNG_Get();
//--------------add 1 to avoid delay time=0,mintime = 65ms ------------------------------------        
          u16_delay = ( Random  & 0x00C0U ) + 1U;
          timer_delay_ms( u16_delay );  
          
	  for ( i = 0; i < 4; i++ ) 
	  {
	    PKE_TXFream[ i ] = KEY_ID[ i ];
	  }
	  CRC = Get_TXCrc( &PKE_TXFream[4],POLLIDELEN-1,CRCRSTVAL );
          PKE_TXFream[4] = CRC;
          
          Rest_2msDetevc();
	  WDRESET;
	   
//--------------trans data----------------------------------------------------------------------------------------- 
          LED_ON();
          PKE_DataTrans_SigF( PKE_TXFream,POLLIDELEN );
          
      }
  }
}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   Pke_Poll_Enable
;| 
;| Description:
;|   The IDE (page 0) is transmitted in a randomly choosen RF time slot.
;|   
;| Format:
;|   LF: WUPA = ACTION-WUP (4bytes) + Command (1byte) + CRC (1byte)
;|   RF: NONE
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;| 
;| Result:
;|   none

-----------------------------------------------------------------------------------------------------------------*/
static void Pke_Poll_Enable( void )
{
    
   LF_CrcRsl = lf_read_crc();        
   if( LF_CrcRsl == LF_OK )
   {
      WDRESET;      
   }
}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   Pke_Poll_Rssi
;| 
;| Description:
;|   The IDE (page 0) is transmitted in a randomly choosen RF time slot.
;|   
;| Format:
;|   LF: WUPA = ACTIVATION-WUP (4bytes) + Command (1byte) + RSSI_CON(1byte) + CRC(1byte)
;|   RF: IDE(4bytes) + RSSI Value(9bytes) + CRC(1byte)
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;| 
;| Result:
;|   none

-----------------------------------------------------------------------------------------------------------------*/
static void Pke_Poll_Rssi( void )
{
    
    uint8_t CRC,i;
      
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 1);
    LF_Ctrl = LF_BlockTemp[0];
    WDRESET;
    
    if( LF_ReadRsl == LF_OK )
    {
        LF_CrcRsl = lf_read_crc();
        
        Rest_2msDetevc();
        WDRESET;
        
        if( LF_CrcRsl == LF_OK )
        {
           
           RssiInit( &OffSet );
           Rest_2msDetevc();
 //--------------Original data-----------------------------------------------------------------------    
           RangeCH1 = CalculateChRssi(CH1, &RssiCH1Val,OffSet);
           Rest_2msDetevc();
	   RangeCH2 = CalculateChRssi(CH2, &RssiCH2Val,OffSet);
	   Rest_2msDetevc();
	   RangeCH3 = CalculateChRssi(CH3, &RssiCH3Val,OffSet);
	   Rest_2msDetevc();
	   
	   StopRssi();
	   
	//   RssiCH1Val = ConvertRssi(OffSet, RssiCH1Val, RangeCH1);
	//   RssiCH2Val = ConvertRssi(OffSet, RssiCH2Val, RangeCH2);
	//   RssiCH3Val = ConvertRssi(OffSet, RssiCH3Val, RangeCH3);
 //--------------store data--------------------------------------------------------------------------   
           PKE_TXFream[4] = (uint8_t)(RssiCH1Val&0x00FF);
           PKE_TXFream[5] = (uint8_t)((RssiCH1Val&0xFF00)>>(8U));
           PKE_TXFream[6] = RangeCH1;
           
           PKE_TXFream[7] = (uint8_t)(RssiCH2Val&0x00FF);
           PKE_TXFream[8] = (uint8_t)((RssiCH2Val&0xFF00)>>(8U));
           PKE_TXFream[9] = RangeCH2;
           
           PKE_TXFream[10] = (uint8_t)(RssiCH3Val&0x00FF);
           PKE_TXFream[11] = (uint8_t)((RssiCH3Val&0xFF00)>>(8U));
           PKE_TXFream[12] = RangeCH3;
//--------------get IDE-----------------------------------------------------------------------           
             for ( i = 0; i < 4; i++ ) 
	  {
	    PKE_TXFream[ i ] = KEY_ID[ i ];
	  }
           
           CRC = Get_TXCrc( PKE_TXFream,POLLRSSILEN-1,CRCRSTVAL );
           PKE_TXFream[13] = CRC;
           
         
           LED_ON(); 
	   PKE_DataTrans( PKE_TXFream,POLLRSSILEN );  
 
        }
        else
        {
            
        }
  } 
}
/*-----------------------------------------------------------------------------
;| Name:
;|   pke_poll_vbat
;| 
;| Description:
;|   Performes battery voltage measurement and transmits the result via UHF.
;|   
;| Format:
;|   LF: WUPB = ACTIVATION-WUP (4bytes) + Command (1byte) + Load during VBAT measurement (1byte) + CRC (1byte)
;|   RF: VBAT result (00..0Fh) (1byte) + crc (1byte) 
-----------------------------------------------------------------------------
;| Parameters:
;|   crc = result of running CRC calculation (global variable)
;| 
;| Result:
;|   none
;| 
;+-----------------------------------------------------------------------------*/
static void Pke_Poll_Vbat( void )
{
      uint8_t CRC;
     uint16_t Vbat;
      
    LF_ReadRsl = lf_read_block( LF_BlockTemp, 1);
    WDRESET;
    
    if( LF_ReadRsl == LF_OK )
   {
       LF_CrcRsl = lf_read_crc();
        
       if( LF_CrcRsl == LF_OK )
       {
           Vbat = phcaiKEyLLGenFunc_ADC_measure_VBAT();
           PKE_TXFream[0] = (uint8_t)(Vbat&0x00FF);
           PKE_TXFream[1] = (uint8_t)((Vbat&0xFF00)>>(8U));
        
           CRC = Get_TXCrc( PKE_TXFream,2,CRCRSTVAL );
           PKE_TXFream[2] = CRC;
             
         LED_ON();
	 PKE_DataTrans_SigF( PKE_TXFream,READBATLEN );  
        }
  }   
}
/*----------------------------------------------------------------------------
;| Name:
;|   RssiInit
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   pucAdcOffset:RSSI offset
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
static void RssiInit(uint16_t *pucAdcOffset)
{
	// It is recommended to decide for one setting of RSSI_RANGEEXTDIS 
    // throughout the application (either ON or OFF).
        RSSICON.bits.RSSI_RANGEEXTDIS = RSSI_RANGEEXT_DIS;         // RSSI range extension DISabled (NOT high sensitivity)
	
	// step1:enable VDDA regulator
	if (PCON1.bits.VDDBRNFLAG == 0) 
	{
		PCON0.bits.VDDARGLEN = 1;
		phcaiKEyLLGenFunc_timer0_delay_us( t_VDDA_PON_us );
		PCON0.bits.VDDARST = 0;
		if (PCON1.bits.VDDABRNFLAG == 0) 
		{
			PCON2.bits.VDDABRNREG = 0;
		} 
		else 
		{
			
		 }
	} 
	else 
	{
		
	}
	// step2:start critical section 
	BEGIN_CRITICAL_SECTION();
//	phcaiKEyLLGenFunc_CS_SetBatPORFlag( TRUE ); // set BATPORFLAG 
	// step3:prepare for rssi measurement
	PRECON3.bits.DISCH1ACT = 1; // disable AGC of 3 channel 
	PRECON4.bits.DISCH2ACT = 1;
	PRECON5.bits.DISCH3ACT = 1;
	PRECON10.bits.QLIM = 0;     // disable QLIM
	//******active QFACT if necessary
	
	// step4:preparation before power on 
	phcaiKEyLLGenFunc_RSSI_ADCInit(FALSE,ADC_SAMPTIME_64US);// ??1us??1??????4?
	
	// step5:power-on rssi and adc blocks // ????????????
	
	// step6:perform offset measurement
	*pucAdcOffset = phcaiKEyLLGenFunc_RSSI_MeasOffset_FractBits(4,0);
	//*pucAdcOffset = phcaiKEyLLGenFunc_RSSI_MeasOffset(4);   // range = 0db
	
}

/*----------------------------------------------------------------------------
;| Name:
;|   CalculateChRssi
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   Chan:       RSSI channel
;|   RssiVal:    channel rssi data
;|   ADCOffset:  channel rssi offset
;| Return:
;|   ret:        RSSI range
;| 
-----------------------------------------------------------------------------*/
static uint8_t CalculateChRssi(phcaiKEyLLGenFunc_RssiChannel_t Chan, uint16_t * RssiVal,uint16_t ADCOffset )
{
	phcaiKEyLLGenFunc_RssiRange_t Range_CH;
       uint8_t ret = 0x10;
     /*if(Chan == 0x01)        LFSHCON.val = 0x0CU;
        else if(Chan == 0x02)       LFSHCON.val = 0x0AU;
        else if(Chan == 0x03)           LFSHCON.val = 0x06U;
       */
	// step7:measure RSSI
	Range_CH = phcaiKEyLLGenFunc_RSSI_SelectRange(Chan,RSSI_RANGE_0dB,TRUE);
	*RssiVal = phcaiKEyLLGenFunc_RSSI_MeasChannel_FractBits(4,0);
	//*RssiVal = phcaiKEyLLGenFunc_RSSI_MeasChannel(4);
	 if ( *RssiVal < ADCOffset ) 
        {
            *RssiVal = 0;
        } 
        else 
        {
            *RssiVal -= ADCOffset;
        }
	
	switch( Range_CH )
	{   
	    case RSSI_RANGE_54dB:
	    {
	        *RssiVal = (*RssiVal)/8;
	        ret = 0x08;
	        break;
	    }
	    
	     case RSSI_RANGE_36dB:
	    {
	        ret = 0x08;
	        break;
	    }
	     case RSSI_RANGE_18dB:
	    {
	        
	        ret = 0x04;
	        break;
	    }
	     case RSSI_RANGE_0dB:
	    {
	        ret = 0x00;
	        break;
	    }
	     case RSSI_RANGE_m18dB:
	    {
	         ret = 0x02;
	        break;
	    }
	    case RSSI_RANGE_LIM:
	    {
	        ret = 0x0E;
	        break;
	    }
	    default:
	    {
	        ret = 0x10;
	        break;
	    }
	}
        return ret;
}

/*----------------------------------------------------------------------------
;| Name:
;|   Get_TXCrc
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   databuf:       data
;|   len:           data len
;|   excrc:         last crc value
;| Return:
;|   ret:           CRC result
;| 
-----------------------------------------------------------------------------*/
static uint8_t Get_TXCrc( uint8_t * databuf,uint8_t len,uint8_t excrc )
{
    uint8_t ret;
    ret = phcaiKEyLLGenFunc_Util_CRC8_bytes( databuf,len,excrc);  
    return ret;
}
/*----------------------------------------------------------------------------
;| Name:
;|   Get_AntNum
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   address:       EEPROM address
;|   
;|   
;| Return:
;|    ret:          read result 
;| 
-----------------------------------------------------------------------------*/
static uint8_t Get_AntNum( uint16_t pagaddress )
{
    uint8_t ret;
    
    phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( ISKFream,pagaddress );
    ret = ISKFream[1];
    return ret;
}
/*----------------------------------------------------------------------------
;| Name:
;|   Get_KeyNum
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   address:       EEPROM address
;|   
;|   
;| Return:
;|    ret:          read result 
;| 
-----------------------------------------------------------------------------*/
static uint8_t Get_KeyNum( uint16_t pagaddress )
{
    uint8_t ret;
    
    phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( ISKFream,pagaddress );
    ret = ISKFream[0];
    return ret;
}
/*----------------------------------------------------------------------------
;| Name:
;|   EnableE2Mod
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   address:       EEPROM address
;|   
;|   
;| Return:
;|    none:          
;| 
-----------------------------------------------------------------------------*/
static void EnableE2Mod( uint16_t address )
{
    phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait( address );
}
/*----------------------------------------------------------------------------
;| Name:
;|   Rest_2msDetevc
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   none
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
static void Rest_2msDetevc(void)
{
    PCON2.bits.R2MSDET = 1U; 
}
/*----------------------------------------------------------------------------
;| Name:
;|   PKE_DataTrans
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   Txbuf:        data for trans
;|   len:          data length
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
static void PKE_DataTrans( uint8_t * Txbuf,uint8_t len )
{
     
    uint8_t i = 0;
    uint8_t j = 0;
    BEGIN_CRITICAL_SECTION(); 
    tx_read_configuration( EE_PKE_TX_CONFIG_BASEPAGE, &PkeTxCfg1 );
    tx_apply_configuration( &PkeTxCfg1 );
    
    if ( SUCCESS == tx_enable_Xtal_oscillator( TRUE ) )
    {
        if ( SUCCESS == tx_enable_PLL( TRUE ) )
        {
            if ( SUCCESS == tx_enable_PA( TRUE ) )
            {
                WDRESET;       	
            //**********************9.6KHz baud with 0.104ms a bit
                for (i = 0; i < 37; i++)
                {
                          tx_transmit_buffer_encoded_bytes( DataEnc_Man_double, 2, PKE_Pream );	   	   
                }
                cycle_delay_ms(5);
                Rf_TXFream( Txbuf,len );
                      
                WDRESET;   
            }
	          tx_enable_PA( FALSE );
    	  }
    }
    tx_shutdown();
    
    cycle_delay_ms(50);
    
    tx_read_configuration( EE_PKE1_TX_CONFIG_BASEPAGE, &PkeTxCfg1 );
    tx_apply_configuration( &PkeTxCfg1 );
    
    if ( SUCCESS == tx_enable_Xtal_oscillator( TRUE ) )
    {
        if ( SUCCESS == tx_enable_PLL( TRUE ) )
        {
            if ( SUCCESS == tx_enable_PA( TRUE ) )
            {
                WDRESET;       	
	     //**********************9.6KHz baud with 0.104ms a bit
                for (i = 0; i < 37; i++)
                {
                          tx_transmit_buffer_encoded_bytes( DataEnc_Man_double, 2, PKE_Pream );	   	   
                }
                cycle_delay_ms(5);
                Rf_TXFream( Txbuf,len );
                
                WDRESET;   
            }
	     tx_enable_PA( FALSE );
    	}
    }
    LED_OFF();
    tx_shutdown();
    END_CRITICAL_SECTION();
}
/*----------------------------------------------------------------------------
;| Name:
;|   PKE_DataTrans_SigF
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   Txbuf:        data for trans
;|   len:          data length
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
static void PKE_DataTrans_SigF( uint8_t * Txbuf,uint8_t len )
{
     
    uint8_t i = 0;
    uint8_t j = 0;
    BEGIN_CRITICAL_SECTION(); 
    tx_read_configuration( EE_PKE_TX_CONFIG_BASEPAGE, &PkeTxCfg );
    tx_apply_configuration( &PkeTxCfg );
    
    if ( SUCCESS == tx_enable_Xtal_oscillator( TRUE ) )
    {
        if ( SUCCESS == tx_enable_PLL( TRUE ) )
        {
            if ( SUCCESS == tx_enable_PA( TRUE ) )
            {
                WDRESET;       	
	     //**********************9.6KHz baud with 0.104ms a bit
	        for (i = 0; i < 37; i++)
		{
	            tx_transmit_buffer_encoded_bytes( DataEnc_Man_double, 2, PKE_Pream );	   	   
		}
		cycle_delay_ms(5);
                Rf_TXFream( Txbuf,len );
                
                WDRESET;   
            }
	     tx_enable_PA( FALSE );
    	}
    }
    tx_shutdown();
    
    LED_OFF();
    END_CRITICAL_SECTION();
}
/*----------------------------------------------------------------------------
;| Name:
;|    Polling_DataTrans
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   Txbuf:        data for trans
;|   len:          data length
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
static void  Polling_DataTrans(  uint8_t * Txbuf,uint8_t len )
{
   uint8_t i = 0;
   uint8_t j = 0;
    tx_read_configuration( EE_PKE_TX_CONFIG_BASEPAGE, &PkeTxCfg );
    tx_apply_configuration( &PkeTxCfg );
    
    if ( SUCCESS == tx_enable_Xtal_oscillator( TRUE ) )
    {
        if ( SUCCESS == tx_enable_PLL( TRUE ) )
        {
            if ( SUCCESS == tx_enable_PA( TRUE ) )
            {
    		
    		WDRESET; 
               for (j = 0; j < 37; j++)
                {
                    tx_transmit_buffer_encoded_bytes( DataEnc_Man_double, 2, Polling_Pream );	   	   
                }
               cycle_delay_ms(6);
               for(i = 0; i < 2; i++ )
               {
	           Rf_TXFream( Txbuf,len );
	            if(i < 1)
	            {
	               cycle_delay_ms(6);
	            } 
               }
               WDRESET; 
	     }
	     tx_enable_PA( FALSE );
    	}
    }
    LED_OFF();
    tx_shutdown(); 
}

/*----------------------------------------------------------------------------
;| Name:
;|   StopRssi
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
static uint8_t StopRssi(void)
{
    PCON2.bits.R2MSDET = 1U;            // Dynamic reset of the 2 ms detection 
   
   LFSHCON.val = 0x00U;
    
	// step8:power-off rssi and adc blocks
	phcaiKEyLLGenFunc_RSSI_ADCStop();
	// step9:prepare for 3D LF active data reception
	PRECON3.bits.DISCH1ACT = 0; // enable AGC of 3 channel 
	PRECON4.bits.DISCH2ACT = 0;
	PRECON5.bits.DISCH3ACT = 0;
	PRECON10.bits.QLIM = 1;     // enable QLIM
	//******de-active QFACT if necessary
	// step10:check for battery power-on reset
	if ((BATSYS0.bits.BATRGLEN == 0U) || (BATSYS0.bits.BATRGLRST == 1U))
	{
		Power_Off();;
	}
	
	phcaiKEyLLGenFunc_CS_SetBatPORFlag( FALSE ); // clear BATPORFLAG 
	
	// step11:end critical section
	END_CRITICAL_SECTION();
	// step12:check for VDDA Brown-out
	if (PCON2.bits.VDDABRNREG == 1) 
	{
		return 0;
	}
	
	return 1;
}
/*----------------------------------------------------------------------------
;| Name:
;|   ConvertRssi
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   u16_ADCOffset:     RSSI offset
;|   u16_ADC:           Original RSSI value 
;|   e_range:           RSSI range
;| Return:
;|   u32_ADC:           32bit RSSI vlaue
;| 
-----------------------------------------------------------------------------*/
static uint32_t ConvertRssi(uint16_t u16_ADCOffset, uint16_t u16_ADC, uint8_t e_range)
{
    uint32_t u32_ADC = 0;
    uint32_t u32_ADCOffset = 0;
    
    PCON2.bits.R2MSDET = 1U;            //Dynamic reset of the 2 ms detection 
    
   /* if (u16_ADC < u16_ADCOffset) 
    {
        return (u32_ADC);
    } 
    else 
    {
        u16_ADC -= u16_ADCOffset;
    }
    */
    
	switch ( e_range )
    { 
      /*case RSSI_RANGE_54dB:
        u32_ADC = u16_ADC / 8;
        break;*/

      case 0x08:
        u32_ADC = u16_ADC;
        break;
      case 0x04:
        u32_ADC = u16_ADC * 8;
        break;
      case 0x00:
        u32_ADC = u16_ADC * 64;
        break;
      case 0x02:
        u32_ADC = u16_ADC * 512;
        break;

      case 0x0E:	// added for MISRA-C
      //���� ����break��
      default:
		u32_ADC = 0xFFFF;	// max voltage 1.25V
        break;
    }
	
	return (u32_ADC);
}

/* eof */