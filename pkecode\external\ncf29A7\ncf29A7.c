/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: ncf29A7.c 23740 2019-12-02 10:17:10Z dep10330 $
  $Revision: 23740 $
*/

#include "shared_sfrdef/platform_tokenplus.h"

/**
 * @file
 * Definition of the special function registers (SFR) of the TOKEN-PLUS
 * (NCF29A7/29A8) platform.
 */

/**
 * @addtogroup tokenplushw
 * @{
 */

/*
  CHANGE LOG
  2016-07-22: started
  2016-08-03: added missing SFRs, added bitfields
  2016-08-08: removed DBG* registers, added I3DCON
  2016-08-09: fixed typos, first version compiling with bitfields (ncf29A7.h)
  2016-08-10: fixed bits in P2WRES, added bits P21MRES0 and P21MRES1 in PRESWUP1
  2016-09-05: removed PATRIM (system reg.) and PRECVL, added MSICON2, fixed typos,
              corrected bitfields in IIUCONx, removed PD1_6DB/PD2_6DB/PD3_6DB,
              added legacy control bits (TX*) in ENCCON1,
              changed bit names in PACON,
              fixed IIUCON0 bitfield
              fixed address comment to ADCSUM in .h files
              removed comments to new or changed SFRs, kept in .h
  2017-05-11: added PRESWUP0 reg. bit WDTOFWUPEN introduced in B0
  2017-09-14: changed to typedefs for each register for consistency of library libncf29A7.a
              with header file ncf29A7.h.
  2019-03-18: added bitfields for ULPCON0 and ULPCON1.
  2019-04-09: fixed incorrect bitfields in ASKRMP.
  2019-11-06: added BISTCON register (read-only in USER) since referenced in DS.

 */


volatile SFR_CXPC_t          chess_storage(DM9:0x0014)  CXPC;
volatile SFR_CXSW_t          chess_storage(DM9:0x0016)  CXSW;
volatile SFR_P1INS_t         chess_storage(DM9:0x0018)  P1INS;
volatile SFR_P1OUT_t         chess_storage(DM9:0x0019)  P1OUT;
volatile SFR_P1DIR_t         chess_storage(DM9:0x001A)  P1DIR;
volatile SFR_P1INTDIS_t      chess_storage(DM9:0x001B)  P1INTDIS;
volatile SFR_P2INS_t         chess_storage(DM9:0x001C)  P2INS;
volatile SFR_P2OUT_t         chess_storage(DM9:0x001D)  P2OUT;
volatile SFR_P2DIR_t         chess_storage(DM9:0x001E)  P2DIR;
volatile SFR_P2INTDIS_t      chess_storage(DM9:0x001F)  P2INTDIS;
volatile SFR_P3INS_t         chess_storage(DM9:0x0020)  P3INS;       // NEW
volatile SFR_P3OUT_t         chess_storage(DM9:0x0021)  P3OUT;       // NEW
volatile SFR_P3DIR_t         chess_storage(DM9:0x0022)  P3DIR;       // NEW
volatile SFR_P3INTDIS_t      chess_storage(DM9:0x0023)  P3INTDIS;    // NEW
volatile SFR_IIUCON0_t       chess_storage(DM9:0x0024)  IIUCON0;
volatile SFR_IIUSTAT_t       chess_storage(DM9:0x0025)  IIUSTAT;
volatile SFR_IIUCON1_t       chess_storage(DM9:0x0026)  IIUCON1;
volatile SFR_IIUDAT_t        chess_storage(DM9:0x0027)  IIUDAT;
volatile SFR_IIUSTATE_t      chess_storage(DM9:0x0028)  IIUSTATE;
volatile SFR_IIUCON2_t       chess_storage(DM9:0x0029)  IIUCON2;
volatile SFR_HTCON_t         chess_storage(DM9:0x002A)  HTCON;
volatile SFR_I3DCON_t        chess_storage(DM9:0x0031)  I3DCON;      // ADDED
volatile SFR_AESDAT_t        chess_storage(DM9:0x0032)  AESDAT;
volatile SFR_AESCON_t        chess_storage(DM9:0x0034)  AESCON;
volatile SFR_CRCDAT_t        chess_storage(DM9:0x0035)  CRCDAT;
volatile SFR_CRC8DIN_t       chess_storage(DM9:0x0036)  CRC8DIN;
volatile SFR_GCRCCON0_t      chess_storage(DM9:0x0037)  GCRCCON0;    // NEW
volatile SFR_GCRCPOLY_t      chess_storage(DM9:0x0038)  GCRCPOLY;    // NEW
volatile SFR_GCRCDAT_t       chess_storage(DM9:0x003A)  GCRCDAT;     // NEW
volatile SFR_GCRCDIN_t       chess_storage(DM9:0x003C)  GCRCDIN;     // NEW
volatile SFR_CPUMCCCNT0_t    chess_storage(DM9:0x004C)  CPUMCCCNT0;  // NEW
volatile SFR_CPUMCCCNT1_t    chess_storage(DM9:0x004E)  CPUMCCCNT1;  // NEW
volatile SFR_CPUMCCCON_t     chess_storage(DM9:0x0050)  CPUMCCCON;   // NEW
volatile SFR_WDCON_t         chess_storage(DM9:0x0051)  WDCON;
volatile SFR_CLKCON0_t       chess_storage(DM9:0x0052)  CLKCON0;
volatile SFR_CLKCON1_t       chess_storage(DM9:0x0053)  CLKCON1;
volatile SFR_CLKCON2_t       chess_storage(DM9:0x0054)  CLKCON2;
volatile SFR_CLKCON3_t       chess_storage(DM9:0x0055)  CLKCON3;
volatile SFR_CLKCON4_t       chess_storage(DM9:0x0057)  CLKCON4;
volatile SFR_ADCDAT_t        chess_storage(DM9:0x005A)  ADCDAT;      // EXTENDED
volatile SFR_ADCSUM_t        chess_storage(DM9:0x005C)  ADCSUM;      // NEW
volatile SFR_ADCCON_t        chess_storage(DM9:0x005E)  ADCCON;      // ADDED BITS
volatile SFR_RSSICON_t       chess_storage(DM9:0x0060)  RSSICON;
volatile SFR_ULPADDR_t       chess_storage(DM9:0x0064)  ULPADDR;
volatile SFR_ULPSEL_t        chess_storage(DM9:0x0066)  ULPSEL;
volatile SFR_ULPCON0_t       chess_storage(DM9:0x0068)  ULPCON0;
volatile SFR_ULPDAT_t        chess_storage(DM9:0x0069)  ULPDAT;
volatile SFR_ULPCON1_t       chess_storage(DM9:0x006D)  ULPCON1;
volatile SFR_T0CON0_t        chess_storage(DM9:0x006E)  T0CON0;
volatile SFR_T0CON1_t        chess_storage(DM9:0x006F)  T0CON1;
volatile SFR_T0REG_t         chess_storage(DM9:0x0070)  T0REG;
volatile SFR_T0RLD_t         chess_storage(DM9:0x0072)  T0RLD;
volatile SFR_T1CON0_t        chess_storage(DM9:0x0074)  T1CON0;
volatile SFR_T1CON1_t        chess_storage(DM9:0x0075)  T1CON1;
volatile SFR_T1CON2_t        chess_storage(DM9:0x0076)  T1CON2;
volatile SFR_T1REG_t         chess_storage(DM9:0x0078)  T1REG;
volatile SFR_T1CAP_t         chess_storage(DM9:0x007A)  T1CAP;
volatile SFR_T1CMP_t         chess_storage(DM9:0x007C)  T1CMP;
volatile SFR_T2CON0_t        chess_storage(DM9:0x007E)  T2CON0;
volatile SFR_T2CON1_t        chess_storage(DM9:0x007F)  T2CON1;
volatile SFR_T2REG_t         chess_storage(DM9:0x0080)  T2REG;
volatile SFR_T2RLD_t         chess_storage(DM9:0x0082)  T2RLD;
volatile SFR_RNGDAT_t        chess_storage(DM9:0x0084)  RNGDAT;
volatile SFR_RNGCON_t        chess_storage(DM9:0x0086)  RNGCON;
volatile SFR_INTCON_t        chess_storage(DM9:0x0087)  INTCON;
volatile SFR_INTFLAG0_t      chess_storage(DM9:0x0088)  INTFLAG0;
volatile SFR_INTFLAG1_t      chess_storage(DM9:0x0089)  INTFLAG1;
volatile SFR_INTFLAG2_t      chess_storage(DM9:0x008A)  INTFLAG2;
volatile SFR_INTEN0_t        chess_storage(DM9:0x008B)  INTEN0;
volatile SFR_INTEN1_t        chess_storage(DM9:0x008C)  INTEN1;
volatile SFR_INTEN2_t        chess_storage(DM9:0x008D)  INTEN2;
volatile SFR_SYSINTEN0_t     chess_storage(DM9:0x008E)  SYSINTEN0;
volatile SFR_SYSINTEN1_t     chess_storage(DM9:0x008F)  SYSINTEN1;
volatile SFR_INTSET0_t       chess_storage(DM9:0x0090)  INTSET0;
volatile SFR_INTSET1_t       chess_storage(DM9:0x0091)  INTSET1;
volatile SFR_INTSET2_t       chess_storage(DM9:0x0092)  INTSET2;
volatile SFR_INTCLR0_t       chess_storage(DM9:0x0093)  INTCLR0;
volatile SFR_INTCLR1_t       chess_storage(DM9:0x0094)  INTCLR1;
volatile SFR_INTCLR2_t       chess_storage(DM9:0x0095)  INTCLR2;
volatile SFR_INTVEC_t        chess_storage(DM9:0x0096)  INTVEC;
volatile SFR_LFSHCON_t       chess_storage(DM9:0x0098)  LFSHCON;
volatile SFR_PCON0_t         chess_storage(DM9:0x0099)  PCON0;
volatile SFR_PCON1_t         chess_storage(DM9:0x009A)  PCON1;
volatile SFR_PCON2_t         chess_storage(DM9:0x009B)  PCON2;
volatile SFR_PCON5_t         chess_storage(DM9:0x009E)  PCON5;       // NEW
volatile SFR_BATSYS0_t       chess_storage(DM9:0x00A0)  BATSYS0;     // bit added
volatile SFR_BATSYS1_t       chess_storage(DM9:0x00A1)  BATSYS1;
volatile SFR_PRESWUP0_t      chess_storage(DM9:0x00A2)  PRESWUP0;    // NEW bit in B0
volatile SFR_PRESWUP1_t      chess_storage(DM9:0x00A4)  PRESWUP1;
volatile SFR_PRESWUP2_t      chess_storage(DM9:0x00A6)  PRESWUP2;
volatile SFR_PRESWUP3_t      chess_storage(DM9:0x00A8)  PRESWUP3;    // NEW
volatile SFR_PRESWUP4_t      chess_storage(DM9:0x00AA)  PRESWUP4;    // NEW
volatile SFR_P1WRES_t        chess_storage(DM9:0x00AC)  P1WRES;
volatile SFR_P2WRES_t        chess_storage(DM9:0x00AD)  P2WRES;
volatile SFR_P3WRES_t        chess_storage(DM9:0x00AE)  P3WRES;      // NEW
volatile SFR_LFTUNECH1ACT_t  chess_storage(DM9:0x00AF)  LFTUNECH1ACT;  // NEW
volatile SFR_LFTUNECH2ACT_t  chess_storage(DM9:0x00B0)  LFTUNECH2ACT;  // NEW
volatile SFR_LFTUNECH3ACT_t  chess_storage(DM9:0x00B1)  LFTUNECH3ACT;  // NEW
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B2)  USRBAT0;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B3)  USRBAT1;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B4)  USRBAT2;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B5)  USRBAT3;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B6)  USRBAT4;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B7)  USRBAT5;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B8)  USRBAT6;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B9)  USRBAT7;
volatile SFR_LFTUNECH1IMMO_t chess_storage(DM9:0x00BA)  LFTUNECH1IMMO; // NEW
volatile SFR_LFTUNECH2IMMO_t chess_storage(DM9:0x00BB)  LFTUNECH2IMMO; // NEW
volatile SFR_LFTUNECH3IMMO_t chess_storage(DM9:0x00BC)  LFTUNECH3IMMO; // NEW
volatile SFR_SPI0CON0_t      chess_storage(DM9:0x00C0)  SPI0CON0;
volatile SFR_SPI0CON1_t      chess_storage(DM9:0x00C1)  SPI0CON1;
volatile SFR_SPI0DAT_t       chess_storage(DM9:0x00C2)  SPI0DAT;
volatile SFR_SPI0STAT_t      chess_storage(DM9:0x00C3)  SPI0STAT;
volatile SFR_SPI1CON0_t      chess_storage(DM9:0x00C4)  SPI1CON0;
volatile SFR_SPI1CON1_t      chess_storage(DM9:0x00C5)  SPI1CON1;
volatile SFR_SPI1DAT_t       chess_storage(DM9:0x00C6)  SPI1DAT;
volatile SFR_SPI1STAT_t      chess_storage(DM9:0x00C7)  SPI1STAT;
volatile SFR_P1ALTF_t        chess_storage(DM9:0x00C8)  P1ALTF;
volatile SFR_P2ALTF_t        chess_storage(DM9:0x00CA)  P2ALTF;
volatile SFR_BITCNT_t        chess_storage(DM9:0x00CC)  BITCNT;
volatile SFR_BITSWAP_t       chess_storage(DM9:0x00CE)  BITSWAP;
volatile SFR_LEDCON_t        chess_storage(DM9:0x00CF)  LEDCON;      // NEW
volatile SFR_INTFLAG3_t      chess_storage(DM9:0x00D0)  INTFLAG3;
volatile SFR_INTEN3_t        chess_storage(DM9:0x00D1)  INTEN3;
volatile SFR_INTSET3_t       chess_storage(DM9:0x00D2)  INTSET3;
volatile SFR_INTCLR3_t       chess_storage(DM9:0x00D3)  INTCLR3;
volatile SFR_TXPCON_t        chess_storage(DM9:0x00D4)  TXPCON;
volatile SFR_CLKRSTCON_t     chess_storage(DM9:0x00D5)  CLKRSTCON;
volatile SFR_VCOCALCON_t     chess_storage(DM9:0x00D6)  VCOCALCON;
volatile SFR_PLLCON_t        chess_storage(DM9:0x00D7)  PLLCON;
volatile SFR_TXDAT_t         chess_storage(DM9:0x00D8)  TXDAT;
volatile SFR_TXSPC_t         chess_storage(DM9:0x00DA)  TXSPC;
volatile SFR_ENCCON0_t       chess_storage(DM9:0x00DC)  ENCCON0;
volatile SFR_ENCCON1_t       chess_storage(DM9:0x00DE)  ENCCON1;
volatile SFR_FREQCON0_t      chess_storage(DM9:0x00E0)  FREQCON0;
volatile SFR_FREQCON1_t      chess_storage(DM9:0x00E2)  FREQCON1;
volatile SFR_BRGCON_t        chess_storage(DM9:0x00E4)  BRGCON;
volatile SFR_FSKCON_t        chess_storage(DM9:0x00E6)  FSKCON;
volatile SFR_FSKRMP_t        chess_storage(DM9:0x00E7)  FSKRMP;
volatile SFR_ASKCON_t        chess_storage(DM9:0x00E8)  ASKCON;
volatile SFR_ASKRMP_t        chess_storage(DM9:0x00EA)  ASKRMP;
volatile SFR_PACON_t         chess_storage(DM9:0x00EB)  PACON;     // bit names changed
volatile SFR_PAPWR_t         chess_storage(DM9:0x00EC)  PAPWR;
volatile SFR_PACAPTRIM_t     chess_storage(DM9:0x00ED)  PACAPTRIM; // NEW
volatile SFR_PALIMIT_t       chess_storage(DM9:0x00EE)  PALIMIT;
volatile SFR_IDENT_t         chess_storage(DM9:0x00FE)  IDENT;     // NEW
volatile SFR_PREDAT_t        chess_storage(DM9:0x0100)  PREDAT;
volatile SFR_RTCCON_t        chess_storage(DM9:0x0101)  RTCCON;
volatile SFR_PRECON2_t       chess_storage(DM9:0x0102)  PRECON2;
volatile SFR_PRECON3_t       chess_storage(DM9:0x0103)  PRECON3;
volatile SFR_PRECON4_t       chess_storage(DM9:0x0104)  PRECON4;
volatile SFR_PRECON5_t       chess_storage(DM9:0x0105)  PRECON5;
volatile SFR_PRECON6_t       chess_storage(DM9:0x0106)  PRECON6;
volatile SFR_PRECON7_t       chess_storage(DM9:0x0107)  PRECON7;
volatile SFR_PRECON8_t       chess_storage(DM9:0x0108)  PRECON8;
volatile SFR_PRECON9_t       chess_storage(DM9:0x0109)  PRECON9;
volatile SFR_PREPD_t         chess_storage(DM9:0x010A)  PREPD;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x010C)  USRBATRGL0;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x010D)  USRBATRGL1;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x010E)  USRBATRGL2;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x010F)  USRBATRGL3;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x0110)  USRBATRGL4;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x0111)  USRBATRGL5;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x0112)  USRBATRGL6;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x0113)  USRBATRGL7;
volatile SFR_PRESTAT_t       chess_storage(DM9:0x011C)  PRESTAT;
volatile SFR_WUP1W0_t        chess_storage(DM9:0x011E)  WUP1W0;
volatile SFR_WUP1W1_t        chess_storage(DM9:0x0120)  WUP1W1;
volatile SFR_WUP2W0_t        chess_storage(DM9:0x0122)  WUP2W0;
volatile SFR_WUP2W1_t        chess_storage(DM9:0x0124)  WUP2W1;
volatile SFR_WUP3W0_t        chess_storage(DM9:0x0126)  WUP3W0;
volatile SFR_PRET_t          chess_storage(DM9:0x0128)  PRET;
volatile SFR_PRE3T_t         chess_storage(DM9:0x012A)  PRE3T;
volatile SFR_RTCDAT_t        chess_storage(DM9:0x012C)  RTCDAT;
volatile SFR_PRECON10_t      chess_storage(DM9:0x012E)  PRECON10;
volatile SFR_PRECON11_t      chess_storage(DM9:0x012F)  PRECON11;     // CHANGED CONTENTS
volatile SFR_PREPOLL0_t      chess_storage(DM9:0x0135)  PREPOLL0;
volatile SFR_PREPOLL1_t      chess_storage(DM9:0x0136)  PREPOLL1;
volatile SFR_PRECON12_t      chess_storage(DM9:0x0137)  PRECON12;
volatile SFR_MSICON0_t       chess_storage(DM9:0x0138)  MSICON0;
volatile SFR_MSICON1_t       chess_storage(DM9:0x0139)  MSICON1;
volatile SFR_MSISTAT0_t      chess_storage(DM9:0x013A)  MSISTAT0;
volatile SFR_MSISTAT1_t      chess_storage(DM9:0x013B)  MSISTAT1;
volatile SFR_MSICON2_t       chess_storage(DM9:0x013C)  MSICON2;
volatile SFR_POSTWUPCON_t    chess_storage(DM9:0x013D)  POSTWUPCON;   // NEW
volatile SFR_POSTWUPCOMP_t   chess_storage(DM9:0x013E)  POSTWUPCOMP;  // NEW
volatile SFR_POSTWUPCAL_t    chess_storage(DM9:0x0140)  POSTWUPCAL;   // NEW
volatile SFR_BISTCON_t       chess_storage(DM9:0x016C)  BISTCON;      // added

/*@}*/

/* eof */


