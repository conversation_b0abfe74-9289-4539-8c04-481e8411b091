
// File generated by amnesia version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:23 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\amnesia.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Dindirect_bitf +Orls -NRwL -NRbL -NRbH +Orrmw +Osps timer-258389 mrk3

toolrelease _19R3;
//Children of func_bndl

rd_res_reg_R7 : rd_res_reg, func_bndl {
}
rd_res_reg_R7_B1 : rd_res_reg_R7 {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 810;
    isg : t;
    inp : ( R7 );
    out : ( SSP_r );
    rsc : (1) SSP_r ;
    opn : ( SSP_r_rd_R7_E1 );
    ins : 297;
}

wr_res_reg_R7 : wr_res_reg, func_bndl {
}
wr_res_reg_R7_B1 : wr_res_reg_R7 {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 811;
    isg : t;
    inp : ( SSP_w );
    out : ( R7 );
    rsc : (1) __rsrc_R7_wr_SSP_w_E1 ;
    opn : ( R7_wr_SSP_w_E1 );
    ins : 298;
}
wr_res_reg_R7_B2 : wr_res_reg_R7 {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 812;
    isg : t;
    inp : ( SSP_w );
    out : ( R7 );
    rsc : (1) __rsrc_R7_wr_SSP_w_s1_E1 ;
    opn : ( R7_wr_SSP_w_s1_E1 );
    ins : 42;
}

stack_load_bndl_1 : func_bndl, stack_load {
    arg : ( int8_:o int8_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_load_bndl_B1 : stack_load_bndl_1 {
    rid : 813;
    isg : t;
    inp : ( __spill_DM R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( DM_r );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_r DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 299;
}
stack_load_bndl_B2 : stack_load_bndl_1 {
    rid : 814;
    isg : t;
    inp : ( __spill_DM R7 __CTagu_1_pint3__cstP13_2_0_E1 );
    out : ( DM_r );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_r DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 300;
}

stack_load_bndl_2 : func_bndl, stack_load {
    arg : ( int16_:o int16_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_load_bndl_B3 : stack_load_bndl_2 {
    rid : 815;
    isg : t;
    inp : ( __spill_DMw R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( DMw_r );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_r DM_r1 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 301;
}
stack_load_bndl_B4 : stack_load_bndl_2 {
    rid : 816;
    isg : t;
    inp : ( __spill_DMw R7 __CTagu_1_pint4_step2__cstP13_3_1_E1 );
    out : ( DMw_r );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_r DM_r1 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 302;
}

stack_load_indirect_bndl_1 : func_bndl, stack_load_indirect {
    arg : ( int8_:o int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_load_indirect_bndl_B1 : stack_load_indirect_bndl_1 {
    rid : 817;
    isg : t;
    inp : ( __spill_DM R7 );
    out : ( DM_r );
    rsc : (1) DM_r SSP_r DM_rmw_addr ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 303;
}

stack_load_indirect_bndl_2 : func_bndl, stack_load_indirect {
    arg : ( int16_:o int16_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_load_indirect_bndl_B2 : stack_load_indirect_bndl_2 {
    rid : 818;
    isg : t;
    inp : ( __spill_DMw R7 );
    out : ( DMw_r );
    rsc : (1) DM_r DM_r1 SSP_r DM_rmw_addr ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 304;
}

stack_store_bndl_1 : func_bndl, stack_store {
    arg : ( int8_:o int8_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_store_bndl_B1 : stack_store_bndl_1 {
    rid : 819;
    isg : t;
    inp : ( DM_w R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( __spill_DM );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 305;
}
stack_store_bndl_B2 : stack_store_bndl_1 {
    rid : 820;
    isg : t;
    inp : ( DM_w R7 __CTagu_1_pint3__cstP13_2_0_E1 );
    out : ( __spill_DM );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 306;
}

stack_store_bndl_2 : func_bndl, stack_store {
    arg : ( int16_:o int16_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_store_bndl_B3 : stack_store_bndl_2 {
    rid : 821;
    isg : t;
    inp : ( DMw_w R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( __spill_DMw );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 307;
}
stack_store_bndl_B4 : stack_store_bndl_2 {
    rid : 822;
    isg : t;
    inp : ( DMw_w R7 __CTagu_1_pint4_step2__cstP13_3_1_E1 );
    out : ( __spill_DMw );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 308;
}

stack_store_indirect_bndl_1 : func_bndl, stack_store_indirect {
    arg : ( int8_:o int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_store_indirect_bndl_B1 : stack_store_indirect_bndl_1 {
    rid : 823;
    isg : t;
    inp : ( DM_w R7 );
    out : ( __spill_DM );
    rsc : (1) SSP_r DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 309;
}

stack_store_indirect_bndl_2 : func_bndl, stack_store_indirect {
    arg : ( int16_:o int16_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_store_indirect_bndl_B2 : stack_store_indirect_bndl_2 {
    rid : 824;
    isg : t;
    inp : ( DMw_w R7 );
    out : ( __spill_DMw );
    rsc : (1) SSP_r DM_rmw_addr ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 310;
}

_pl_rd_res_reg_const_wr_res_reg_1 : func_bndl {
    arg : ( int16_:o int16_:i int16_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 1 );
}
_pl_rd_res_reg_const_wr_res_reg_1_B1 : _pl_rd_res_reg_const_wr_res_reg_1 {
    rid : 825;
    isg : t;
    inp : ( __CTa_w1_uint16__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16__cstP16_E1
            a_w1_conv0___CTa_w1_uint16__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 311;
}
_pl_rd_res_reg_const_wr_res_reg_1_B2 : _pl_rd_res_reg_const_wr_res_reg_1 {
    rid : 826;
    isg : t;
    inp : ( __CTa_w1_uint4_0_10__cstP9_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint4_0_10__cstP9_E1
            a_w1_conv0___CTa_w1_uint4_0_10__cstP9_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 312;
}
_pl_rd_res_reg_const_wr_res_reg_1_B3 : _pl_rd_res_reg_const_wr_res_reg_1 {
    rid : 827;
    isg : t;
    inp : ( __CTa_w1_uint16_0_32767__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) mylea a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_lea_as_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_mylea_1_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16_0_32767__cstP16_E1
            a_w1_conv0___CTa_w1_uint16_0_32767__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_dummy_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 313;
}

ret_1 : func_bndl, ret {
    fst : 1;
}
ret_1_B1 : ret_1 {
    rid : 828;
    isg : t;
    opn : ( vd_ret_E1 );
    ins : 189;
}

load__pl_rd_res_reg_const_1 : func_bndl {
    arg : ( int16_:o int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
load__pl_rd_res_reg_const_1_B1 : load__pl_rd_res_reg_const_1 {
    rid : 829;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DMw_r );
    rsc : (1) DM_r DM_r1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1 );
    ins : 314;
}
load__pl_rd_res_reg_const_1_B2 : load__pl_rd_res_reg_const_1 {
    rid : 830;
    isg : t;
    inp : ( __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( DMw_r );
    rsc : (1) DM_r DM_r1 agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1 );
    ins : 315;
}

call_const_1 : func_bndl, call, absolute {
    arg : ( int16_:i );
    fst : 1;
    ist : ( 1 );
}
call_const_1_B1 : call_const_1 {
    rid : 831;
    isg : t;
    inp : ( __CTtrgt_uint16__cstP16_E1 );
    rsc : (1) trgt ;
    opn : ( vd_call_trgt_E1
            trgt_uint16__cstP16_E1
            trgt_conv0___CTtrgt_uint16__cstP16_E1 );
    ins : 316;
}

_mi_rd_res_reg_const_wr_res_reg_1 : func_bndl {
    arg : ( int16_:o int16_:i int16_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 1 );
}
_mi_rd_res_reg_const_wr_res_reg_1_B1 : _mi_rd_res_reg_const_wr_res_reg_1 {
    rid : 832;
    isg : t;
    inp : ( __CTa_w1_uint16__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16__cstP16_E1
            a_w1_conv0___CTa_w1_uint16__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 317;
}
_mi_rd_res_reg_const_wr_res_reg_1_B2 : _mi_rd_res_reg_const_wr_res_reg_1 {
    rid : 833;
    isg : t;
    inp : ( __CTa_w1_uint4_0_10__cstP9_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint4_0_10__cstP9_E1
            a_w1_conv0___CTa_w1_uint4_0_10__cstP9_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 318;
}
_mi_rd_res_reg_const_wr_res_reg_1_B3 : _mi_rd_res_reg_const_wr_res_reg_1 {
    rid : 834;
    isg : t;
    inp : ( __CTa_w1_uint16_1_32768__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) mylea a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_lea_as_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_mylea_1_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16_1_32768__cstP16_E1
            a_w1_conv0___CTa_w1_uint16_1_32768__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_dummy_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 319;
}

_pl_rd_res_reg_const_store_1 : func_bndl {
    arg : ( int8_:o int16_:i int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 );
}
_pl_rd_res_reg_const_store_1_B1 : _pl_rd_res_reg_const_store_1 {
    rid : 835;
    isg : t;
    inp : ( DMw_w __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r agu_0 agu_1 DM_rmw_addr ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 320;
}
_pl_rd_res_reg_const_store_1_B2 : _pl_rd_res_reg_const_store_1 {
    rid : 836;
    isg : t;
    inp : ( DMw_w __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r SP_r agu_0 agu_1 DM_rmw_addr ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 321;
}

//Children of mv_bndl

DMw_w_1_dr_move_Rw_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
DMw_w_1_dr_move_Rw_1_int16__B0 : DMw_w_1_dr_move_Rw_1_int16_ {
    rid : 837;
    isg : t;
    inp : ( RwL );
    out : ( DMw_w );
    rsc : (1) a_w0 DM_w DM_w1 a_w2 ;
    opn : ( a_w0_rd_RwL_class_Rw___RwL_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 322;
}
DMw_w_1_dr_move_Rw_1_int16__B1 : DMw_w_1_dr_move_Rw_1_int16_ {
    rid : 838;
    isg : t;
    inp : ( R46 );
    out : ( DMw_w );
    rsc : (1) a_w0 DM_w DM_w1 a_w2 ;
    opn : ( a_w0_rd_R46_class_Rw___R46_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 323;
}

c_flag_1_dr_move_c_flag_w_1_any : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 839;
    isg : t;
    inp : ( c_flag_w );
    out : ( c_flag );
    rsc : (1) __rsrc_c_flag_wr_c_flag_w_E1 ;
    opn : ( c_flag_wr_c_flag_w_E1 );
    ins : 9;
}

nz_flag_1_dr_move_nz_flag_w_1_any : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 840;
    isg : t;
    inp : ( nz_flag_w );
    out : ( nz_flag );
    rsc : (1) __rsrc_nz_flag_wr_nz_flag_w_E1 ;
    opn : ( nz_flag_wr_nz_flag_w_E1 );
    ins : 324;
}

o_flag_1_dr_move_o_flag_w_1_any : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 841;
    isg : t;
    inp : ( o_flag_w );
    out : ( o_flag );
    rsc : (1) __rsrc_o_flag_wr_o_flag_w_E1 ;
    opn : ( o_flag_wr_o_flag_w_E1 );
    ins : 10;
}

Rw_1_dr_move_DMw_r_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move_DMw_r_1_int16__B0 : Rw_1_dr_move_DMw_r_1_int16_ {
    rid : 842;
    isg : t;
    inp : ( DMw_r );
    out : ( RwL );
    rsc : (1) a_w0 a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w0_copy0_DMw_r_E1
            a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 325;
}
Rw_1_dr_move_DMw_r_1_int16__B1 : Rw_1_dr_move_DMw_r_1_int16_ {
    rid : 843;
    isg : t;
    inp : ( DMw_r );
    out : ( R46 );
    rsc : (1) a_w0 a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w0_copy0_DMw_r_E1
            a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 326;
}

