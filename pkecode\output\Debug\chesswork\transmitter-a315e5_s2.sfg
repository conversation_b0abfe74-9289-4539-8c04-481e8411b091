
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:20 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-a315e5 mrk3

[
    0 : error_t_tx_enable_PA_bool_t typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : b_OnOff typ=int8_ val=0t0 bnd=a sz=1 algn=1 stl=DM tref=bool_t_DM
   21 : res typ=int8_ val=1t0 bnd=a sz=1 algn=1 stl=DM tref=error_t_DM
   22 : TXPCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_TXPCON_t_DM9
   23 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   24 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   25 : __extDM_SFR_TXPCON_t typ=int8_ bnd=b stl=DM
   26 : PACON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PACON_t_DM9
   27 : __extDM_SFR_PACON_t typ=int8_ bnd=b stl=DM
   28 : __extPM_void typ=uint16_ bnd=b stl=PM
   29 : __extDM_void typ=int8_ bnd=b stl=DM
   30 : __extULP_void typ=uint32_ bnd=b stl=ULP
   33 : __ptr_TXPCON typ=int16_ val=0a bnd=m adro=22
   35 : __ptr_PACON typ=int16_ val=0a bnd=m adro=26
   36 : __rt typ=int8_ bnd=p tref=error_t__
   37 : __arg_b_OnOff typ=int8_ bnd=p tref=bool_t__
   42 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   46 : __ct_1t0 typ=int16_ val=1t0 bnd=m
   58 : __ct_102 typ=uint8_ val=102f bnd=m
   60 : void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar typ=int16_ val=0r bnd=m
   78 : __ct_83 typ=uint16_1_32768_ val=83f bnd=m
   80 : void_phcaiKEyLLGenFunc_timer0_delay_us___ushort typ=int16_ val=0r bnd=m
  114 : __ct_38 typ=uint8_ val=38f bnd=m
  121 : __ct_2s0 typ=int16_ val=2s0 bnd=m
  151 : __ct_2s0 typ=int16_ val=2s0 bnd=m
  259 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  274 : __either typ=bool bnd=m
  275 : __trgt typ=rel8_ val=3j bnd=m
  277 : __trgt typ=rel8_ val=9j bnd=m
  278 : __trgt typ=rel8_ val=14j bnd=m
  279 : __seff typ=any bnd=m
  280 : __seff typ=any bnd=m
  281 : __seff typ=any bnd=m
  282 : __seff typ=any bnd=m
  283 : __seff typ=any bnd=m
  284 : __seff typ=any bnd=m
  285 : __seff typ=any bnd=m
  286 : __seff typ=any bnd=m
  287 : __seff typ=any bnd=m
  288 : __seff typ=any bnd=m
  289 : __seff typ=any bnd=m
]
Ferror_t_tx_enable_PA_bool_t {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (b_OnOff.19 var=20) source ()  <30>;
    (res.20 var=21) source ()  <31>;
    (TXPCON.21 var=22) source ()  <32>;
    (__extDM_int8_.22 var=23) source ()  <33>;
    (__extDM_int16_.23 var=24) source ()  <34>;
    (__extDM_SFR_TXPCON_t.24 var=25) source ()  <35>;
    (PACON.25 var=26) source ()  <36>;
    (__extDM_SFR_PACON_t.26 var=27) source ()  <37>;
    (__extPM_void.27 var=28) source ()  <38>;
    (__extDM_void.28 var=29) source ()  <39>;
    (__extULP_void.29 var=30) source ()  <40>;
    (__arg_b_OnOff.36 var=37 stl=RbL off=0) inp ()  <47>;
    (__ct_0t0.457 var=42) const_inp ()  <554>;
    (__ct_1t0.458 var=46) const_inp ()  <555>;
    (__ct_2s0.462 var=151) const_inp ()  <559>;
    <95> {
      (__sp.44 var=19 __seff.499 var=287 stl=c_flag_w __seff.500 var=288 stl=nz_flag_w __seff.501 var=289 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.462 __sp.18 __sp.18)  <602>;
      (__seff.508 var=288 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.500)  <650>;
      (__seff.515 var=287 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.499)  <656>;
      (__seff.516 var=289 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.501)  <657>;
    } stp=0;
    <96> {
      (b_OnOff.58 var=20) _pl_rd_res_reg_const_store_1_B1 (__arg_b_OnOff.513 __ct_0t0.457 b_OnOff.19 __sp.44)  <603>;
      (__arg_b_OnOff.513 var=37 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__arg_b_OnOff.36)  <655>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <69>;
    } #4 off=3 nxt=5
    #5 off=3 nxt=6
    <94> {
      (res.62 var=21) _pl_rd_res_reg_const_store_const_1_B3 (__ct_1t0.458 res.20 __sp.44)  <601>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <73>;
    } #6 off=4 nxt=113
    #113 off=4 nxt=106 tgt=11
    (__ptr_TXPCON.455 var=33) const_inp ()  <552>;
    (void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar.459 var=60) const_inp ()  <556>;
    (__trgt.465 var=277) const_inp ()  <562>;
    <92> {
      (__apl_nz.434 var=259 stl=nz_flag_w __seff.495 var=285 stl=c_flag_w __seff.496 var=286 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B2 (__ct_0t0.457 b_OnOff.58 __sp.44)  <599>;
      (__apl_nz.507 var=259 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.434)  <649>;
      (__seff.517 var=285 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.495)  <658>;
      (__seff.518 var=286 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.496)  <659>;
    } stp=0;
    <93> {
      () cc_eq__jump_const_1_B1 (__apl_nz.506 __trgt.465)  <600>;
      (__apl_nz.506 var=259 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.507)  <648>;
    } stp=2;
    if {
        {
            () if_expr (__either.453)  <105>;
            (__either.453 var=274) undefined ()  <549>;
        } #9
        {
            #11 off=15 nxt=12
            <91> {
              () call_const_1_B1 (void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar.459)  <598>;
            } stp=2;
            <110> {
              (__ct_102.523 var=58 stl=__CTa_b0_int8__cstP24_E1) const_3_B1 ()  <635>;
              (__ct_102.522 var=58 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_1_uint8__B0 (__ct_102.523)  <662>;
            } stp=0;
            call {
                (PACON.99 var=26 TXPCON.100 var=22 __extDM.101 var=17 __extDM_SFR_PACON_t.102 var=27 __extDM_SFR_TXPCON_t.103 var=25 __extDM_int16_.104 var=24 __extDM_int8_.105 var=23 __extDM_void.106 var=29 __extPM.107 var=16 __extPM_void.108 var=28 __extULP.109 var=18 __extULP_void.110 var=30 __vola.111 var=13) Fvoid_phcaiKEyLLGenFunc_CS_SetClkCon___uchar (__ct_102.522 PACON.25 TXPCON.21 __extDM.16 __extDM_SFR_PACON_t.26 __extDM_SFR_TXPCON_t.24 __extDM_int16_.23 __extDM_int8_.22 __extDM_void.28 __extPM.15 __extPM_void.27 __extULP.17 __extULP_void.29 __vola.12)  <112>;
            } #12 off=19 nxt=92
            #92 off=19 nxt=17
            <89> {
              (TXPCON.128 var=22 __vola.129 var=13 __seff.492 var=284 stl=nz_flag_w) load_const__or_const_store_1_B1 (__ptr_TXPCON.455 TXPCON.100 __vola.111)  <596>;
              (__seff.509 var=284 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.492)  <651>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <132>;
            } #17 off=21 nxt=18
            #18 off=21 nxt=19
            (void_phcaiKEyLLGenFunc_timer0_delay_us___ushort.460 var=80) const_inp ()  <557>;
            <88> {
              () call_const_1_B1 (void_phcaiKEyLLGenFunc_timer0_delay_us___ushort.460)  <595>;
            } stp=2;
            <111> {
              (__ct_83.525 var=78 stl=__CTa_w0_uint16__cstP16_E1) const_2_B1 ()  <637>;
              (__ct_83.524 var=78 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B0 (__ct_83.525)  <663>;
            } stp=0;
            call {
                (PACON.138 var=26 TXPCON.139 var=22 __extDM.140 var=17 __extDM_SFR_PACON_t.141 var=27 __extDM_SFR_TXPCON_t.142 var=25 __extDM_int16_.143 var=24 __extDM_int8_.144 var=23 __extDM_void.145 var=29 __extPM.146 var=16 __extPM_void.147 var=28 __extULP.148 var=18 __extULP_void.149 var=30 __vola.150 var=13) Fvoid_phcaiKEyLLGenFunc_timer0_delay_us___ushort (__ct_83.524 PACON.99 TXPCON.128 __extDM.101 __extDM_SFR_PACON_t.102 __extDM_SFR_TXPCON_t.103 __extDM_int16_.104 __extDM_int8_.105 __extDM_void.106 __extPM.107 __extPM_void.108 __extULP.109 __extULP_void.110 __vola.129)  <139>;
            } #19 off=25 nxt=119
            #119 off=25 nxt=25 tgt=27
            (__ptr_PACON.456 var=35) const_inp ()  <553>;
            (__trgt.463 var=275) const_inp ()  <560>;
            <86> {
              (PACON.158 var=26 __vola.159 var=13 __seff.488 var=283 stl=nz_flag_w) load_const__ad_const_cmp_const_cc_eq__jump_const_1_B1 (__ptr_PACON.456 __trgt.463 PACON.138 __vola.150)  <593>;
              (__seff.510 var=283 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.488)  <652>;
            } stp=0;
            if {
                {
                    () if_expr (__either.450)  <181>;
                    (__either.450 var=274) undefined ()  <544>;
                } #23
                {
                } #27 off=28 nxt=42
                {
                    <84> {
                      (res.196 var=21) store_const__pl_rd_res_reg_const_1_B3 (__ct_1t0.458 res.62 __sp.44)  <591>;
                    } stp=0;
                } #25 off=27 nxt=42
                {
                    (res.197 var=21) merge (res.62 res.196)  <186>;
                } #28
            } #22
        } #10
        {
            #106 off=7 nxt=33
            <83> {
              (TXPCON.214 var=22 __vola.215 var=13 __seff.484 var=282 stl=nz_flag_w) load_const__ad_const_store_1_B1 (__ptr_TXPCON.455 TXPCON.21 __vola.12)  <590>;
              (__seff.526 var=282 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.484)  <664>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <205>;
            } #33 off=9 nxt=34
            #34 off=9 nxt=35
            <82> {
              () call_const_1_B1 (void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar.459)  <589>;
            } stp=2;
            <113> {
              (__ct_38.529 var=114 stl=__CTa_b0_int8__cstP24_E1) const_1_B1 ()  <642>;
              (__ct_38.528 var=114 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_1_uint8__B0 (__ct_38.529)  <665>;
            } stp=0;
            call {
                (PACON.224 var=26 TXPCON.225 var=22 __extDM.226 var=17 __extDM_SFR_PACON_t.227 var=27 __extDM_SFR_TXPCON_t.228 var=25 __extDM_int16_.229 var=24 __extDM_int8_.230 var=23 __extDM_void.231 var=29 __extPM.232 var=16 __extPM_void.233 var=28 __extULP.234 var=18 __extULP_void.235 var=30 __vola.236 var=13) Fvoid_phcaiKEyLLGenFunc_CS_SetClkCon___uchar (__ct_38.528 PACON.25 TXPCON.214 __extDM.16 __extDM_SFR_PACON_t.26 __extDM_SFR_TXPCON_t.24 __extDM_int16_.23 __extDM_int8_.22 __extDM_void.28 __extPM.15 __extPM_void.27 __extULP.17 __extULP_void.29 __vola.215)  <212>;
            } #35 off=13 nxt=37
            #37 off=13 tgt=42
            (__trgt.466 var=278) const_inp ()  <563>;
            <79> {
              (res.240 var=21) store_const__pl_rd_res_reg_const_1_B3 (__ct_1t0.458 res.62 __sp.44)  <586>;
            } stp=0;
            <80> {
              () jump_const_1_B1 (__trgt.466)  <587>;
            } stp=1;
        } #29
        {
            (__vola.241 var=13) merge (__vola.159 __vola.236)  <218>;
            (__extPM.242 var=16) merge (__extPM.146 __extPM.232)  <219>;
            (__extDM.243 var=17) merge (__extDM.140 __extDM.226)  <220>;
            (__extULP.244 var=18) merge (__extULP.148 __extULP.234)  <221>;
            (res.245 var=21) merge (res.197 res.240)  <222>;
            (TXPCON.246 var=22) merge (TXPCON.139 TXPCON.225)  <223>;
            (__extDM_int8_.247 var=23) merge (__extDM_int8_.144 __extDM_int8_.230)  <224>;
            (__extDM_int16_.248 var=24) merge (__extDM_int16_.143 __extDM_int16_.229)  <225>;
            (__extDM_SFR_TXPCON_t.249 var=25) merge (__extDM_SFR_TXPCON_t.142 __extDM_SFR_TXPCON_t.228)  <226>;
            (PACON.250 var=26) merge (PACON.158 PACON.224)  <227>;
            (__extDM_SFR_PACON_t.251 var=27) merge (__extDM_SFR_PACON_t.141 __extDM_SFR_PACON_t.227)  <228>;
            (__extPM_void.252 var=28) merge (__extPM_void.147 __extPM_void.233)  <229>;
            (__extDM_void.253 var=29) merge (__extDM_void.145 __extDM_void.231)  <230>;
            (__extULP_void.254 var=30) merge (__extULP_void.149 __extULP_void.235)  <231>;
        } #39
    } #8
    #42 off=28 nxt=-2
    () out (__rt.512)  <242>;
    () sink (__vola.241)  <243>;
    () sink (__extPM.242)  <246>;
    () sink (__extDM.243)  <247>;
    () sink (__extULP.244)  <248>;
    () sink (__sp.263)  <249>;
    () sink (b_OnOff.58)  <250>;
    () sink (res.245)  <251>;
    () sink (TXPCON.246)  <252>;
    () sink (__extDM_int8_.247)  <253>;
    () sink (__extDM_int16_.248)  <254>;
    () sink (__extDM_SFR_TXPCON_t.249)  <255>;
    () sink (PACON.250)  <256>;
    () sink (__extDM_SFR_PACON_t.251)  <257>;
    () sink (__extPM_void.252)  <258>;
    () sink (__extDM_void.253)  <259>;
    () sink (__extULP_void.254)  <260>;
    (__ct_2s0.461 var=121) const_inp ()  <558>;
    <76> {
      (__rt.257 var=36 stl=DM_r) load__pl_rd_res_reg_const_1_B2 (__ct_1t0.458 res.245 __sp.44)  <583>;
      (__rt.512 var=36 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__rt.257)  <654>;
    } stp=0;
    <77> {
      (__sp.263 var=19 __seff.477 var=279 stl=c_flag_w __seff.478 var=280 stl=nz_flag_w __seff.479 var=281 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.461 __sp.44 __sp.44)  <584>;
      (__seff.511 var=280 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.478)  <653>;
      (__seff.520 var=279 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.477)  <660>;
      (__seff.521 var=281 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.479)  <661>;
    } stp=1;
    <78> {
      () ret_1_B1 ()  <585>;
    } stp=2;
    76 -> 77 del=1;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,137:0,0);
3 : (0,137:22,0);
4 : (0,137:22,0);
5 : (0,139:16,2);
6 : (0,139:16,2);
8 : (0,141:2,3);
10 : (0,142:2,4);
11 : (0,144:41,4);
12 : (0,144:4,4);
17 : (0,147:21,7);
18 : (0,149:39,7);
19 : (0,149:1,7);
22 : (0,150:4,8);
25 : (0,152:10,10);
27 : (0,150:4,12);
29 : (0,156:2,17);
33 : (0,157:21,19);
34 : (0,159:41,19);
35 : (0,159:4,19);
37 : (0,160:8,21);
42 : (0,162:2,24);
92 : (0,147:15,6);
106 : (0,157:15,18);
113 : (0,141:12,3);
119 : (0,150:29,8);
----------
69 : (0,137:22,0);
73 : (0,139:16,2);
105 : (0,141:2,3);
112 : (0,144:4,4);
132 : (0,147:21,7);
139 : (0,149:1,7);
181 : (0,150:4,8);
186 : (0,150:4,14);
205 : (0,157:21,19);
212 : (0,159:4,19);
218 : (0,141:2,23);
219 : (0,141:2,23);
220 : (0,141:2,23);
221 : (0,141:2,23);
222 : (0,141:2,23);
223 : (0,141:2,23);
224 : (0,141:2,23);
225 : (0,141:2,23);
226 : (0,141:2,23);
227 : (0,141:2,23);
228 : (0,141:2,23);
229 : (0,141:2,23);
230 : (0,141:2,23);
231 : (0,141:2,23);
583 : (0,162:9,24) (0,139:10,0);
584 : (0,162:2,0) (0,162:2,24);
585 : (0,162:2,24);
586 : (0,160:4,20) (0,139:10,0);
589 : (0,159:4,19);
590 : (0,157:15,18);
591 : (0,152:6,9) (0,139:10,0);
593 : (0,150:19,8) (0,150:29,8) (0,150:4,8);
595 : (0,149:1,7);
596 : (0,147:15,6);
598 : (0,144:4,4);
599 : (0,141:15,3) (0,137:29,0) (0,141:12,3);
600 : (0,141:12,3) (0,141:2,3);
601 : (0,139:10,0) (0,139:16,2);
602 : (0,137:8,0);
603 : (0,137:29,0) (0,137:22,0);
635 : (0,144:41,0);
637 : (0,149:39,0);
642 : (0,159:41,0);

