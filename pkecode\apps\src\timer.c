/*----------------------------------------------------------------------------+
 (c) NXP B.V. 2009-2012. All rights reserved.

 Disclaimer
 1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
    warranties of any kind. NXP makes no warranties to Licensee and shall not
    indemnify Licensee or hold it harmless for any reason related to the NXP
    Software/Source Code or otherwise be liable to the NXP customer. The NXP
    customer acknowledges and agrees that the NXP Software/Source Code is
    provided AS-IS and accepts all risks of utilizing the NXP Software under
    the conditions set forth according to this disclaimer.

 2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
    BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
    FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
    RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
    SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
    INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGDED TO HAVE
    RESULTED FROM ANY DEFECT, ERROR OR OMMISSION IN THE NXP SOFTWARE/SOURCE
    CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
    RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
    THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
    INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
    (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
    AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
    SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGES.

+-----------------------------------------------------------------------------+
|       Project: Generic RKE Protocol                                         |
|                                                                             |
+-----------------------------------------------------------------------------+
| Date      | File Version | Firmware Version | Comment                       |
+-----------------------------------------------------------------------------+
| 14-Jun 12 | 1.00         | 1.00             | First draft version           |
+-----------------------------------------------------------------------------+
| Description                                                                 |
|                                                                             |
| basic timer for the project                                                 |
|                                                                             |
+----------------------------------------------------------------------------*/


//******************************************************************************
// System library include 
//******************************************************************************
#include "phcaiKEyLLGenFunc.h"       

//******************************************************************************
// function include
//******************************************************************************
#include "timer.h"


//******************************************************************************
// local define
//******************************************************************************
#define BIT_TIME_DELAY   31

  uint16_t a=0;
  uint16_t b=0;  
//******************************************************************************
// global functions
//******************************************************************************

void timer_DelayTBit( void )
{

  uint16_t i = BIT_TIME_DELAY*2;                             ///< Const Local Variable for the delay time
   
  while( i>0 )
  {
    nop();
    i--;
  }
}

//******************************************************************************

void timer_DelayTBit_half( void )
{
  uint16_t i = BIT_TIME_DELAY;                               ///< Const Local Variable for the delay time
  //uint16_t i = bit_time;
  while( i>0 )
  {
    nop();
    i--;
  }  
}

//******************************************************************************

void timer_WaitNops( uint16_t i )
{
  //  
  // Clock ~ 2MHz
  //
  // t = 5us + i * 1.5us
  //
  // example:
  // 
  // i         Time  
  // -----------------
  // 10000    15.0 ms
  // 1000      1.5 ms
  // 100     155.0 us
  // 10       20.0 us
  // 1         6.5 us
  // 0         5.0 us
  //
  // 1 nop is 375ns
  // ==> one loop takes 4 clocks ( 4*375us = 1.5us )
  // ==> The initial+jump takes 13 clocks( 13*375us = 5us )
  //
  while( i>0 )
  {
    nop();  
    i--;
  }  
}
/*----------------------------------------------------------------------------
;| Name:
;|   timer_delay_us
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
void timer_delay_us( uint16_t u16_wait )
{
  phcaiKEyLLGenFunc_timer0_delay_us( u16_wait );           // wait microseconds
} 
/*----------------------------------------------------------------------------
;| Name:
;|   timer_delay_ms
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
void timer_delay_ms( uint16_t u16_wait )
{
  phcaiKEyLLGenFunc_timer0_delay_ms( u16_wait );  
}
/*----------------------------------------------------------------------------
;| Name:
;|   cycle_delay_ms
;| 
;| Description:
;|     
;|   
------------------------------------------------------------------------------
;| Parameters:
;|   
;|   
;|   
;| Return:
;|   none:          
;| 
-----------------------------------------------------------------------------*/
void cycle_delay_ms( uint16_t wait )
{     
   if( (wait==110)&&((INTFLAG0.val&0x04) != 0) )
   {
     nop();
   }
     for(a=0;a<wait;a++)
     {
          for(b=0;b<500;b++)
         {
              nop();  
         }
     }
      if( (wait==110)&&((INTFLAG0.val&0x04) != 0) )
   {
     nop();
   }
}

/**********End file********************************************************************/