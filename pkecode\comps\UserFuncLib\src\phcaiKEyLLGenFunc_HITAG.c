/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_HITAG.c 11676 2018-01-02 13:17:01Z dep17622 $
  $Revision: 11676 $

*/

/**
 * @file
 * Implementations of User Functions to support the calculation unit in the IIU
 * (Immobilizer Interface Unit).
 */

/*
 Change Log:

 2011-04-21 (MMr):
   - time optimizations in _HITAG_CryptoLoad() and _HITAG_CryptoGet() by avoiding
     bit field operations.
     Changed 2nd parameter (number of bits) and local variables to 16 bit.

 2011-10-28 (MMr):
   - added function phcaiKEyLLGenFunc_HITAG_3_authent()
   - edited comments

 2013-07-26 (MMr):
   - Improved MISRA-C compliance

 */


#include "phcaiKEyLLGenFunc.h"
#include "phcaiKEyLLGenFunc_CS.h"
#include <string.h>


/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @addtogroup HITAG
 * @{
 */

/*
-------------------------------------------------------------------------------
 Registers used for the HITAG Calculation Unit
-------------------------------------------------------------------------------

 [@ 0x ()] - IIU data register IIUDAT
 [7:0]
 IIUDATA

 [@ 0x ()] - IIU control register IIUCON00 [IIUCON0] (reset value 00h)
 7   | [6:5]      | [4:2]     | [1:0]
 RFU | IIU_MODSEL | IIU_DPSEL | IIU_CLKSEL
 -   | 00         | 010       | 10            --> Port pins, inactive out. level: 0 | SO (shift reg. ser. out.) | peripherial clock (PCLK)

 [@ 0x ()] - IIU control register IIUCON01 [IIUCON1] (reset value 0xh)
 7        | [6:4]  | [3:0]
 IIU_PRIO | RFU    | IIU_BITCNT
 0        | -      | 1000                     --> No bit processing during TXWAIT | 8 bit (max.)

 [@ 0x ()] - IIU status register IIUSTAT
 7          | 6           | 5       | [4:3]      | 2          | 1            | 0
 IIU_LFDATA | IIU_LFDEMEN | IIU_RST | IIU_ENCSEL | IIU_TXWAIT | IIU_FINISHED | IIU_BCNTZERO
 -          | 0           | 0       | 00         | -          | -            | -              --> Disable LF demod. | no IIU reset | disable encoder

 [@ 0x ()] - IIU state register IIUSTATE [IIUSTATE]
 [7:3] | [2:0]
 RFU   | IIU_STATE
 -     | 110 / 000                            --> SHIFTCONT / IDLE

 [@ 0x ()] - HITAG control register HTCON [HTCON]
 [7:6] | 5    | [4:3]       | [2:0]
 RFU   | HTEN | HTOSEL      | HTMODE
 -     | 1    | 00/01/10/11 | 000/010/100/110 --> En. crypto unit | out = 0/HTCRI, bypass mode/HTCRO/HTCRI xor HTCRO | HITAG2: load 16/load 0/LF/OWF
*/

/*-------------------------------------------------------------------------------
  Definitions
 *-------------------------------------------------------------------------------*/

/**
  Maximum BITCNT setting used in this module
  @see phcaiKEyLLGenFunc_HITAG_CryptoLoad
  @see phcaiKEyLLGenFunc_HITAG_CryptoGet
*/
#define MAX_IIU_BITCNT     8U


/*-------------------------------------------------------------------------------
  Functions implementation
 *-------------------------------------------------------------------------------*/


/*-------------------------------------------------------------------------------*/
void phcaiKEyLLGenFunc_HITAG_CryptoInit( void )
/*-------------------------------------------------------------------------------*/
{
  IIUCON0.bits.IIU_MODSEL  = 0U;
  IIUCON0.bits.IIU_DPSEL   = 2U;        /* 010 --> Data out: so (=shift register serial output) */
  IIUCON0.bits.IIU_CLKSEL  = 2U;        /* Peripherial clock (PCLK) */
  /* Note: IIUCON1[6:4] (W0) reads "0" and has no hardware attached, hence bitfield operation allowed. */
  IIUCON1.bits.IIU_PRIO    = 0U;        /* No bit processing during TXWAIT */

  IIUSTAT.bits.IIU_LFDEMEN = 0U;        /* Disable analog LF demodulator */
  IIUSTAT.bits.IIU_ENCSEL  = 0U;        /* Disable encoder     */

  HTCON.bits.HTEN          = 1U;        /* Enable HITAG crypto unit */
  IIUSTAT.bits.IIU_RST     = 1U;        /* Reset of state machine, LF decoder, IIU output signal, interrupt request flags */
}


/*-------------------------------------------------------------------------------*/
void phcaiKEyLLGenFunc_HITAG_CryptoStop( void )
/*-------------------------------------------------------------------------------*/
{
  IIUSTAT.bits.IIU_RST = 1U;            /* Reset of state machine, LF decoder, IIU output signal, interrupt request flags  */
  HTCON.bits.HTEN      = 0U;            /* Disable HITAG crypto unit */
}


/*-------------------------------------------------------------------------------*/
bool_t phcaiKEyLLGenFunc_HITAG_CryptoLoad( const uint8_t * const pu8_data,
                                           uint16_t  u16_NumBits,
                                           uint8_t   u8_cryptoFunc )
/*-------------------------------------------------------------------------------*/
{
  bool_t res = FALSE;
  uint16_t i;
  uint16_t u16_NumBytes = (u16_NumBits+7U) / 8U;

  if ( (u8_cryptoFunc != HT2_LF) && (u8_cryptoFunc != HT3_LF) ) /* only applicable to these modes */
  {
    HTCON.bits.HTMODE = u8_cryptoFunc;
    HTCON.bits.HTOSEL = 0U;                     /* HITAG crypto unit output = 0 */

    for( i = 0U; i < u16_NumBytes; i++ )
    {
      IIUDAT.val = pu8_data[ i ];

      if( i < (u16_NumBytes-1U) )
      {
        /*IIUCON1.bits.IIU_BITCNT = MAX_IIU_BITCNT; */
        IIUCON1.val = MAX_IIU_BITCNT;
        u16_NumBits = u16_NumBits - MAX_IIU_BITCNT;
      }
      else
      {
        /*IIUCON1.bits.IIU_BITCNT = u16_NumBits; */
        IIUCON1.val = (uint8_t)u16_NumBits & 0x0FU;
      }

      IIUSTATE.val = 5U;  /* IIU_STATE = 5 = SHIFT */
      /*INTEN1.bits.IE_IIU = 1;                Enable IIU interrupts if IDLE used */

      /* Wait until IIU_FINISHED received */
      while( ( IIUSTAT.val & 0x02U ) == 0U )    /*while( IIUSTAT.bits.IIU_FINISHED != 1 ) */
      {
        /* go_idle(); */
      }

      /*IIUSTAT.bits.IIU_BCNTZERO = 1;          Clear IIU_BCNTZERO interrupt */
      /*IIUSTAT.bits.IIU_FINISHED = 1;          Clear IIU_FINISHED interrupt */
      IIUSTAT.val |= 0x03U;
    }

    res = TRUE;
  }
  return res;
}


/*-------------------------------------------------------------------------------*/
bool_t phcaiKEyLLGenFunc_HITAG_CryptoGet( uint8_t * pu8_data,
                                          uint16_t  u16_NumBits,
                                          uint8_t   u8_cryptoFunc,
                                          bool_t    b_EXOR)
/*-------------------------------------------------------------------------------*/
{
  bool_t res = FALSE;
  uint16_t i;
  uint16_t u16_NumBytes = (u16_NumBits+7U)/8U;

  if ( (u8_cryptoFunc == HT2_LF) || (u8_cryptoFunc == HT3_LF) )
  {
    HTCON.bits.HTMODE = u8_cryptoFunc;

    if (b_EXOR == TRUE)
    {
      HTCON.bits.HTOSEL = 3U;                /* (11bin) HITAG crypto unit output = HTCRI xor HTCRO */
    }
    else
    {
      HTCON.bits.HTOSEL = 2U;                /* (10bin) HITAG crypto unit output = HTCRO */
    }

    for ( i = 0U; i < u16_NumBytes; i++ )
    {
      IIUDAT.val = pu8_data[ i ];

      if( i < (u16_NumBytes-1U) )
      {
        /*IIUCON1.bits.IIU_BITCNT = MAX_IIU_BITCNT; */
        IIUCON1.val = MAX_IIU_BITCNT;
        u16_NumBits = u16_NumBits - MAX_IIU_BITCNT;
      }
      else
      {
        /*IIUCON1.bits.IIU_BITCNT = u16_NumBits; */
        IIUCON1.val = (uint8_t)u16_NumBits & 0x0FU;
      }

      /* Switch to SHIFT state         */
      IIUSTATE.val = 5U;  /* IIU_STATE = 5 = SHIFT */
      /*INTEN1.bits.IE_IIU = 1;                 Enable IIU interrupts */

      /* Wait until IIU_FINISHED received */
      while( ( IIUSTAT.val & 0x02U ) == 0U )
      {
      }

      /*IIUSTAT.bits.IIU_BCNTZERO = 1;          Clear IIU_BCNTZERO interrupt */
      /*IIUSTAT.bits.IIU_FINISHED = 1;          Clear IIU_FINISHED interrupt */
      IIUSTAT.val |= 0x03U;
      /* read data byte from IIUDAT register and store in output buffer */
      pu8_data[ i ] = IIUDAT.val;
    }

    res = TRUE;
  }
  return res;
}


/*-------------------------------------------------------------------------------*/
void phcaiKEyLLGenFunc_HITAG_3_authent(
    /* inputs: */
    const uint16_t u16_SK_base_page,
    const uint8_t u8arr_Identifier[4],
    const uint8_t u8arr_Challenge[8],
    /* outputs: */
    uint8_t u8arr_MAC[2],
    uint8_t u8arr_Response[6] )
/*-------------------------------------------------------------------------------*/
{
  /*lint -save -e534 */
  uint8_t u8arr_SK[12];
  uint8_t u8arr_constdata[8];

  /* read 96 bits Secret Key (SK), bits 95..0, byte at index 0 contains bits 95..88 */
  /* (about 350 us) */
  phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( &u8arr_SK[0], u16_SK_base_page      );
  phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( &u8arr_SK[4], u16_SK_base_page + 1U );
  phcaiKEyLLGenFunc_CS_ULPEE_ReadPage( &u8arr_SK[8], u16_SK_base_page + 2U );

  phcaiKEyLLGenFunc_HITAG_CryptoInit();

  /************************************************************/
  /* Mix Up1: */
  /************************************************************/

  /* load 64 bit of SK [63.. 0] to calculation unit, use crypto function code Load0  */
  /* (about 160 us)  */
  phcaiKEyLLGenFunc_HITAG_CryptoLoad( &u8arr_SK[4],     64U, HT3_Load0 );
  /* load 32 bit of SK [95..64] to calculation unit, use crypto function code OWF  */
  /* (about 90 us) */
  phcaiKEyLLGenFunc_HITAG_CryptoLoad( &u8arr_SK[0],     32U, HT3_OWF   );
  /* load 64 bit Challenge [63..0] to calculation unit, use crypto function code OWF */
  /* (about 160 us) */
  phcaiKEyLLGenFunc_HITAG_CryptoLoad( u8arr_Challenge,  64U, HT3_OWF   );
  /* load 96 bit SK [95..0] to calculation unit, use crypto function code OWF   */
  /* (about 220 us) */
  phcaiKEyLLGenFunc_HITAG_CryptoLoad( u8arr_SK,         96U, HT3_OWF   );
  /* load 32 bit constant "1" to calculation unit, use crypto function code OWF */
  memset( u8arr_constdata, 0xFF, 4U );
  phcaiKEyLLGenFunc_HITAG_CryptoLoad( u8arr_constdata,  32U, HT3_OWF   );
  /* load 32 bit IDE [31..0] to calculation unit, use crypto function code OWF   */
  phcaiKEyLLGenFunc_HITAG_CryptoLoad( u8arr_Identifier, 32U, HT3_OWF   );

  /***********************************************************************************************/
  /* get 16 bit MAC [15..0] from calculation unit, use crypto function code LF, no XOR with data */
  /***********************************************************************************************/
  phcaiKEyLLGenFunc_HITAG_CryptoGet ( u8arr_MAC,        16U, HT3_LF, FALSE );

  /************************************************************/
  /* Mix Up2: */
  /************************************************************/

  /* load 64 bit of RSK [95..32] to calculation unit, use crypto function code OWF */
  phcaiKEyLLGenFunc_HITAG_CryptoLoad( &u8arr_SK[0],     64U, HT3_OWF );

  /* load 64 bit constant "0" to calculation unit, use crypto function code OWF */
  memset( u8arr_constdata, 0, 8U );
  phcaiKEyLLGenFunc_HITAG_CryptoLoad( u8arr_constdata,  64U, HT3_OWF );

  /***********************************************************************************************/
  /* get 48 bit SR [47..0] from calculation unit, use crypto function code LF, no XOR with data  */
  /***********************************************************************************************/
  phcaiKEyLLGenFunc_HITAG_CryptoGet ( u8arr_Response,   48U, HT3_LF, FALSE );
  /*lint -restore */
}


/*@}*/
/*@}*/

/* eof */
