
#ifndef _DEFS_H_
#define _DEFS_H_

#include "ncf29xx.h"
#include "globvar.h"



//#define debug
// -----------------------------------------------------------------------------------
// Port Definitions
// -----------------------------------------------------------------------------------

#define PORTDIR_INPUT  0                        // Port direction definitions (PxDIR registers)
#define PORTDIR_OUTPUT 1


#define D_P1OUT             0x00                // Port 1 output control register 
                                                // 7    | 6    | 5    | 4    | 3    | 2    | 1    | 0
                                                // P17O | P16O | P15O | P14O | P13O | P12O | P11O | P10O 
                                                // 00000000b --> All: '0'
                                                             
#define D_P1DIR             0x80                 // Port 1 direction control register P1DIR 
                                                // 7    | 6    | 5    | 4    | 3    | 2    | 1    | 0
                                                // P17D | P16D | P15D | P14D | P13D | P12D | P11D | P10D
                                                // 01100000b --> Output: P16,P15
                                                              
#define D_P2OUT             0x00                // Port 2 output control register 
                                                // 7    | 6    | 5    | 4    | 3    | 2    | 1    | 0
                                                // P27O | P26O | P25O | P24O | P23O | P22O | P21O | P20O
                                                // 00000000b --> All: '0'
                                                              
#define D_P2DIR             0x00                // Port 2 direction control register P2DIR 


#define BUTTON_REG_INDEX    0                   // Index of port input register used to scan button states (0=P1INS,1=P2INS)
#define BUTTON_MASK         0x07U//0x0FU               // Ports used for button debouncing

#define ALTERNATIVE_MASK    0x00U

// -----------------------------------------------------------------------------------
// Pull Resistor Definitions
// -----------------------------------------------------------------------------------
// 0000 = 0h : Resistors off; Wake-up on falling edge
// 0010 = 2h : Resistors off; Wake-up on rising edge
// 0100 = 4h : Pull-down resistor activated; Wake-up disabled
// 0110 = 6h : Resistors off; Wake-up disabled
// 1000 = 8h : Pull-up resistor activated; Wake-up on falling edge
// 1010 = Ah : Pull-up resistor activated; Wake-up on rising edge
// 1100 = Ch : Pull-up resistor activated; Wake-up disabled
// 1110 = Eh : RFU (mistake in DS rev 1.9 !)
#define D_PRESWUP0          0x4448U//0x4488U              // Port resistor and wake-up configuration register(P15/14/13/12)
                                
#define D_PRESWUP1          0xC444U//0xC4C4U              // Port resistor and wake-up configuration register(P21/20/17/16)

#define D_PRESWUP2          0x0888U              // Port resistor and wake-up configuration register(RFU/24/23/22)

#define D_P1WRES            0xFFU                // Port 1x pull resistor configuration
#define D_P2WRES            0x3FU                // Port 2x pull resistor configuration


// -----------------------------------------------------------------------------------
// Clock and Timer Definitions
// -----------------------------------------------------------------------------------

#define D_CLKCON0           0x26                // standard CPU clock 2 MHz
#define D_CLKCON0_XTAL      0x66                // CPU clock from XTal: 27.6 MHz /2 /8 = 1.725 MHz 
                                                
#define D_CLKCON0_END       0x02                // CPU clock before power off 250 kHz

#define D_WDCON             0x60                // Watchdog timer control register
                                                // [7:4]      | [3:2]       | 1      | 0
                                                // WDTIM[3:0] | WDMODE[1:0] | WDTRIG | WDCLR
                                                // --> ~1.05s, standard mode


// -----------------------------------------------------------------------------------
// Pre-Processor Register Definitions
// -----------------------------------------------------------------------------------

#define D_RTCCON            0x80                // Preprocessor control register
                                                // 7     | [6:5]        | [4:2]       | [1:0]
                                                // ITRST | RTC_SEL[1:0] | IT_SEL[2:0] | IT_MODE[1:0]
                                                // 1     | 00           | 000         | 00
                                                // --> interval timer reset, no RTC wake up, no ITC wake up, ITC and RTC are stopped

#define D_PRECON2           0x05                // Preprocessor control register
                                                  // 7         | 6      | 5       | 4   | [3:2]       | 1      | 0
                                                // DIGFILRST | PRERST | PDLFACT | RFU | BDRATE[3:2] | RTC_EN | LPRC_EN
                                                // 0         | 0      | 0       | 0   | 01          | 0      | 1
                                                // --> Baudrate 3.9 kbit/s, 180 kHz RC oscillator enabled


// LFACT_0DB_GAIN

//#define D_PRECON3           0x40                // (PDx_6DB = 1) DISable high gain mode !
//#define D_PRECON4           0x40                // (PDx_6DB = 1) DISable high gain mode !
//#define D_PRECON5           0x40                // (PDx_6DB = 1) DISable high gain mode !


// LFACT_6DB_GAIN

#define D_PRECON3           0x00                // (PDx_6DB = 0) ENable high gain mode !
#define D_PRECON4           0x00                // (PDx_6DB = 0) ENable high gain mode !
#define D_PRECON5           0x00                // (PDx_6DB = 0) ENable high gain mode !
                                                

// LFACT_12DB_GAIN

//#define D_PRECON3           0x00                // (PDx_6DB = 0 don't care since GainChX is 10bin!)
//#define D_PRECON4           0x00                // 
//#define D_PRECON5           0x00                // 
                                                


                                                
#define D_PRECON6           0x07                // Preprocessor control register #6
                                                // 7   | [6:5]  | 4        | [3:0]          
                                                // RFU | CVTYPE | ITCLKSEL | LPRC_CAL[3:0]  
                                                // 0   | 00     | 0        | 0111           
                                                // --> Set baseband filter inputs to preamplifiers, 32 kHz crystal oscillator, CVL type = calibrated 8T, calibration of RC oscillator = 0%
                                                
#define D_PRECON7           0xFF
                          //0xFB//WUP1_LEN[4:0] = 11011( 28bit )  
                          //0xEF//WUP1_LEN[4:0] = 01111( 16bit )
                          //0xFF                // Preprocessor control register #7
                                                // [7:5]       | [4:0]
                                                // WUPSEL[2:0] | WUP1_LEN[4:0]
                                                //  111        | 11111        
                                                // --> WUP1,WUP2,WUP3 pattern enabled, WUP1 length = 32 bit
                                                
#define D_PRECON8           0x1F                // Preprocessor control register #8
                                                // 7   | 6   | 5   | [4:0]
                                                // RFU | RFU | RFU | WUP2_LEN[4:0]
                                                //  0     0     0    11111        
                                                // --> WUP2 length = 32 bit

#define D_PRECON9           0x1F                // Preprocessor control register #9
                                                // 7          |  6  |  5   | 4      |  [3:0]
                                                // LFACTSIGPOL| DIGFILMODE | FIFOEN | WUP3_LEN
                                                //  0           0    0        1      1111
                                                // --> Data FIFO enabled, WUP3 length 16 bit

#define D_PRECON10          0x00                // Preprocessor control register #10
                                                // (see latest DS for details)

#define D_PRECON11          0x01                // (POR=0xFC) set G01 = +6 dB mode, set RINMODE=1
                                                // 7:6     | 5:4     | 3:2     | 1:0
                                                // GainCh3 | GainCh2 | GainCh1 | RinMode


#define D_RSSICON           0x00                // RSSI control register
                                                // the only permanent setting: (others set in GFL)
                                                // RSSI_RANGEEXTDIS = 0 (RSSI range extension ON)

#define D_PREPD             0x00                // Preprocessor power down register
                                                // 7          | 6     | [5:4]      | [3:2]      | [1:0]
                                                // AGCSENSRST | PDBB3 | PDCH3[1:0] | PDCH2[1:0] | PDCH1[1:0]
                                                // --> AGC sensitivity reset disabled, power down and bypass 3rd gain stage in baseband filter disabled, all three amplifiers in channel x are enabled

#define D_PRET              0x1D10              // Preprocessor T value register
                                                // [15:8]       | [7:0]
                                                // PRETMAX[7:0] | PRETMIN[7:0]
                                                // --> Default setting

#define D_PRE3T             0x6038              // Preprocessor 3T value register
                                                // [15:8]        | [7:0]
                                                // PRE3TMAX[7:0] | PRE3TMIN[7:0]
                                                // --> Default setting

#define D_WUP1W0            0x445F              // Preprocessor wake-up pattern register
                                                // [15:0]  
                                                // WUP1W0[15:0]
                                                
#define D_WUP1W1            0x2233              // Preprocessor wake-up pattern register
                                                // [31:16]
                                                // WUP1W1[31:16]
                                                
#define D_WUP2W0            0xFFFF              // Preprocessor wake-up pattern register
                                                // [15:0]  
                                                // WUP2W0[15:0]
                                                
#define D_WUP2W1            0xFFFF              // Preprocessor wake-up pattern register
                                                // [31:16]
                                                // WUP2W1[31:16]
                                                
#define D_WUP3W0            0xEFBE              // Preprocessor wake-up pattern register
                                                // [15:0]  
                                                // WUP3W0[15:0]

#define D_LFTUNEVBAT        0x0111              // LF tuning caps in VBAT only case
                                                // LF_CAP_CHx_DIS = 1 : disable / set minimum capacitance value

#define D_MSICON0			0x03
#define D_MSICON1			0x00
/*----------------------------------------------------------------------------*/
/* ULP EEPROM mapping                                                         */
/*----------------------------------------------------------------------------*/

// ULP EEPROM

// Location in EEPROM, where the HITAG-2 secret keys for PKE are stored 
// (two pages)
#define EEPAGE_BASE_HT2_CRYPTO_DATA 48U

// Location of 128 bit secret key for authentication extension 
// of NFC Type 4 tag (four pages)
#define EEPAGE_BASE_NFCTAG_AUTH_KEY 54U

// Pages in which calibration data for temperature sensor is stored
#define EEPAGE_TEMPSENS_CALIB_A     58U
#define EEPAGE_TEMPSENS_CALIB_B     59U

// Page in which the LED blinking parameters are stored
#define EEPAGE_SETTING_LEDBLINK     60U

// Location in EEPROM, where the HITAG-3 secret keys for PKE are stored 
// (3 pages, 61 .. 63)
#define EEPAGE_BASE_HT3_CRYPTO_DATA 61U


// Configuration module pages:
// DCFG A: LF/HF field detection configuration
#define EEPAGE_FLD_CONFIG               0x3D0

// transponder enable and select
#define EEPAGE_TRANSPONDER_CONFIG       0x3D1

//enable the EROM write 
#define EEPAGE_ENABLE_WRITE_EROM        0x3D3

// page containing (in bits 15..0) the value written to LFTUNEVDD register at immo boot
#define EEPAGE_LFTUNE_IMMO              0x3D4

// Page Address of IDE
#define IDE_PAGE                        0x3FF   


//*******************************************************************

// timer delays

#define TIME_BG_POWERUP_US              187           // tBG,PON/?s + 10%

#define TIME_PA_SETTLING_US             83            // tPA_set/?s + 10%

#define TIMER_WAIT_MS_LED_ON_OFF        200           // delay between led on and of at the end of processing


//*******************************************************************
// ADC control
#define D_INSEL                         0x00          // INSEL (bit [15:14])  = 00 means: battery voltage
#define D_REFSEL                        0x00          // REFSEL (bit [13:12]) = 00 means: bandgap
#define D_SAMTIM                        0x03          // SAMTIM (bit [9:8]    = 11 (64 us sampling time)

//*******************************************************************
// Transmitter power control
#define D_TXPCON_DISABLE                0x00          // Disable all previous settings in power on/off register
#define D_TXPCON_XO_ENABLE              0x01          // enable XO regulator
#define D_TXPCON_XTAL_ENABLE            0x09          // Enable bit for crystal oscillator
#define D_TXPCON_XO_CLOCK               0x89          // enable the XO clock ready detector

#define D_TXPCON_PLL_REG_ENABLE         0x8F          // enable the PLL Regulators (analogue part & high-speed digital part)
#define D_TXPCON_FRAC_NPLL_ENABLE       0x9F          // enable the FracNPLL operation

#define D_TXPCON_POWER_ON               0xBF          // enable power amplifier
#define D_TXPCON_POWER_OFF              0x00          // disable all transmitter depending units

// PLL 
#define D_PLLTRIM_HI_MIN_NOISE          0xFF          // Set PLL current to minimize phase noise
#define D_PLLCON_LOCK_DETECT_ENABLE     0x13          // enable pll lock detector 0 (fine phase)
#define D_PLLCON_LOCK_DETECT_DISABLE    0x15          // disable pll lock detector 0
#define D_PLLCON_UNLOCK_DETECT_ENABLE   0x17          // enable pll unlock detector 1

// VCO
#define D_VCO_CALC_ON                   0x01          // Start VCO Calibration
//*******************************************************************
// Transmission
//efine D_ENCCON0_HI_MAN_INV            0x18          // Encoder settings inverted manchester coding 
#define D_ENCCON0_MAN_INV               0x1800U       // Encoder settings inverted manchester coding 

#define D_ENCCON1_16BIT                 0x0000U       // Encoder setting for 16 bit transmission
#define D_ENCCON1_8BIT                  0x0008U       // Encoder setting for 8 bit transmission  

//*******************************************************************
// error codes for function return parameter
#define RESULT_SUCCESS    0
#define RESULT_ERROR      1

//*******************************************************************
// EEPROM page addresses and offsets (see eeprom_setup.h)
//*******************************************************************
// IDE Page
#define EE_IDE_PAGE                      0x03FF

// Immobilizer Secret Key (ISK) Pages
#define EE_ISK0_PAGE                     0x0001
#define EE_ISK1_PAGE                     0x0002
#define EE_ISK2_PAGE                     0x0003
#define EE_ISK3_PAGE                     0x0004     // used for AES only

//***********************************************

// Block 2 (pages 10h..17h) is used for the Remote Sequence Counter

// start page of RSI counter (8 pages)
#define EE_SI_DEFAULT_PAGE               0x0010

//***********************************************

// Base page of UHF config settings for RKE0
#define EE_RKE_TX_CONFIG_BASEPAGE   0x40U

// Base page of UHF config settings for PKE0
#define EE_PKE_TX_CONFIG_BASEPAGE   0x48U

// Base page of UHF config settings for RKE1
#define EE_RKE1_TX_CONFIG_BASEPAGE  0x50U

// Base page of UHF config settings for PKE1
#define EE_PKE1_TX_CONFIG_BASEPAGE  0x58U


// Offsets of register settings per config block
#define EE_TXCONFIG_OFFSET_FREQCON0   0U
#define EE_TXCONFIG_OFFSET_FREQCON1   2U
#define EE_TXCONFIG_OFFSET_PLLCON     4U
#define EE_TXCONFIG_OFFSET_FSKCON     5U
#define EE_TXCONFIG_OFFSET_ASKCON     6U
#define EE_TXCONFIG_OFFSET_BRGCON     8U
#define EE_TXCONFIG_OFFSET_ASKRMP    10U
#define EE_TXCONFIG_OFFSET_FSKRMP    11U
#define EE_TXCONFIG_OFFSET_PACON     12U
#define EE_TXCONFIG_OFFSET_PAPWR     13U
#define EE_TXCONFIG_OFFSET_PATRIM    14U
#define EE_TXCONFIG_OFFSET_PALIMIT   15U
#define EE_TXCONFIG_OFFSET_ENCCON0   16U

//*******************************************************************

/* Functional Macros */

// watchdog reset (clear)
#define WDRESET              WDCON.bits.WDCLR = 1


// indicate beginning of a critical section (code which must not be aborted by LF/HF reset)
#define BEGIN_CRITICAL_SECTION()  g_b_InCriticalSection = TRUE
 
// indicate end of a critical section:
// if an NMI has occurred during the c.s., trigger reset.
// always reset the critical section indicator flag.
#define END_CRITICAL_SECTION()    \
   do \
   { g_b_InCriticalSection = FALSE; \
     if ( g_b_NMI_occurred ) { PCON0.bits.VDDRST = 1U; } \
     else { /* nop */} \
   } while (0)
/**
 * Macro: used as work-around for SFR data corruption
 * (?Dummy read?from a SFR register).
 * Place this after any read access to a VBATREG register,
 * before reading any VDD or VBAT register.
 * Note: SFR is declared as volatile so the read access
 * will be maintained regardless of optimizations by the compiler.
 */
#define CLEAR_VBATREG_SFR_ACCESS() \
   do { \
     uint8_t u8_dummy_read_vddsfr = P1INS.val; \
   } while (0)   

/********LED control**********************************************************/
// The Alternate Function (LED driver) on P17 will be used. (current sink 2 mA)
#define INIT_LED()  //P1ALTF.bits.P17AF=1
#define LED_ON()    P1OUT.bits.P17O=1
#define LED_OFF()   P1OUT.bits.P17O=0
#define LED_XOR()   //P1OUT.bits.P17O= ~P1OUT.bits.P17O

#ifdef debug
#define IOTestH()       P1OUT.bits.P17O=1
#define IOTestL()       P1OUT.bits.P17O=0
#endif
//******EEPROM Init******************************************************
#define HT2E_INIT               ACTIVVE
#define SILAST_PG               0x0018
#define EESYNC_PG               0x0019
#define EEPAGE_SWVERSION        0x001A
#define SWVERSION_Adr           0x001A
#define SWVERSION_STOR_Adr      0x006A
#define EEPAGE_POLLING          0x001B
#define SIFIRST_PG              0x0010
#define SICMP_PG                0x0007
#define POLLINGID_PG            0x0006
#define RSK_PGH                 0x0005
#define RSK_PGL                 0x0004
#define TMCF_PW                 0x0003
#define PSK_PGH                 0x0002
#define PSK_PGL                 0x0001
#define ANT_Adr                 EEPAGE_INNERANT_CONFIG
#define PGRED_LIMIT             0x0000//0x0008
#define EEPAGE_VINH_CONFIG      0x0032
#define EEPAGE_VINL_CONFIG      0x0033
#define EEPAGE_INNERANT_CONFIG  0x0034
#define PID_Adr                 0x001A
#define TMCF_Adr                0x000E
#define TMCF_InitVlu            0x0300
#define GAIN_Adr                0x00D1
//******PKE Data Length******************************************************
#define CRCRSTVAL               0u
#define AUTHLEN                 8u
#define RSSILEN                 11u 
#define E2COUNTLEN              5u
#define AUTHRSSILEN2            12u
#define AUTHRSSILEN3            14u
#define AUTHRSSILEN4            16u
#define POLLIDELEN              5u
#define POLLRSSILEN             14u
#define POLLINGWGLEN            12u
#define POLLING_ACTIVELEN       22u
#define READBATLEN              3u
//******PKE Crypto******************************************************
#define AUTHMODE                0x01
#define AUTHRSSIMODE            0x02
//******RSSI cal gap******************************************************
#define GAPDLY                  0x04
#define RF_CHANEL_GAP           90U
//******RF Mode******************************************************
#define RF_HR_LENG              21u
#define RKEFRQPAG               0x40
#define PKEFRQPAG               0x48

#define BELOWCENTERFRQ          0x01
#define UPCENTERFRQ             0x02
#define RF_MODERKE              0x00
#define RF_MODEPOLLINGSLEEP     0x01
#define RF_MODEPOLLINGACTIVE_L  0x02
#define RF_MODEPOLLINGACTIVE_R  0x03
//******KEY Val********************************************************
#define KEY_PARK_PINVAL         0x08
#define KEY_LOCK_PINVAL         0x04
#define KEY_TRUNK_PINVAL        0x02
#define KEY_UNLOCK_PINVAL       0x01

#define KEY_PARK_VAL            0x01
#define KEY_LOCK_VAL            0x04
#define KEY_TRUNK_VAL           0x08
#define KEY_UNLOCK_VAL          0x02
//******Gain Mode******************************************************
#define GAIN0DB                 0x01
#define GAIN6DB                 0x55
//******RKE isr check result******************************************************
#define RKE_ISR_CHECKED         0x01
#define RKE_ISR_NOTCHECKED      0x00
//******data fault******************************************************
#define ReseveData              0
#endif
#define     ACTIVE      1
#define     INACTIVE    0
/* eof */
