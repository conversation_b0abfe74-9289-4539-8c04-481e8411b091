#ifndef _INTERRUPT_C
#define _INTERRUPT_C
#include "interrupt.h"
#include "phcaiKEyLLGenFunc_CS.h"
#include "phcaiKEyLLGenFunc.h"

#include "defs.h"
#include "SysInit.h"

/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   InterruptInit
;|
;| Description:
;|
;|
;| Format:
;|
;|
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;|
;| Result:
;|   none

-----------------------------------------------------------------------------------------------------------------*/
void InterruptInit( void )
{
    INTCLR0.val = 0xFF;
    INTCLR1.val = 0xFF;
    INTCLR2.val = 0xFF;
    INTCLR3.val = 0xFF;

    INTEN0.val = 0x01; 
    INTEN1.val = 0x00; 
    INTEN2.val = 0x00; 
    INTEN3.val = 0x00; 

}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   Pke_Poll_IDE
;|
;| Description:
;|
;|
;| Format:
;|
;|
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;|
;| Result:
;|   none

-----------------------------------------------------------------------------------------------------------------*/
void PortIntEnable(void)
{

    INTCLR0.val = 0xFF;
    INTCLR1.val = 0xFF;
    INTCLR2.val = 0xFF;
    INTCLR3.val = 0xFF;
    INTVEC.val  = (uint16_t)process_isr;
    INTEN0.val |= 0x04;
    sil(1);
}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   PortIntDisable
;|
;| Description:
;|
;|
;| Format:
;|
;|
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;|
;| Result:
;|   none

-----------------------------------------------------------------------------------------------------------------*/
void PortIntDisable(void)
{

    INTCLR0.val = 0xFF;
    INTCLR1.val = 0xFF;
    INTCLR2.val = 0xFF;
    INTCLR3.val = 0xFF;
    INTVEC.val  = 0x0040;
    INTEN0.val = 0x00;
    sil(2);
}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   Pke_Poll_IDE
;|
;| Description:
;|
;|
;| Format:
;|
;|
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;|
;| Result:
;|   none

-----------------------------------------------------------------------------------------------------------------*/
void LFIntEnable(void)
{

    INTCLR0.val = 0xFF;
    INTCLR1.val = 0xFF;
    INTCLR2.val = 0xFF;
    INTCLR3.val = 0xFF;
    INTVEC.val  = (uint16_t)process_isr;
    INTEN2.val |= 0x02;
    sil(1);
}
/*-----------------------------------------------------------------------------------------------------------------
;| Name:
;|   PortIntDisable
;|
;| Description:
;|
;|
;| Format:
;|
;|
-----------------------------------------------------------------------------------------------------------------
;| Parameters:
;|   none
;|
;| Result:
;|   none

-----------------------------------------------------------------------------------------------------------------*/
void LFIntDisable(void)
{

    INTCLR0.val = 0xFF;
    INTCLR1.val = 0xFF;
    INTCLR2.val = 0xFF;
    INTCLR3.val = 0xFF;
    INTVEC.val  = 0x0040;
    INTEN2.val = 0x00;
    sil(2);
}

#endif