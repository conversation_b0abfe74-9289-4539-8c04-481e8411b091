
// File generated by noodle version P-2019.09#78e58cd307#210222, Sat Mar  2 20:51:11 2024
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/transmitter.c mrk3

// toolrelease _19R3;

"D:/pke/01_code/04_YB23014_SZQPEPS_PEPS_PKE_PRJ_0301/apps/src/transmitter.c"
"D:\pke\01_code\04_YB23014_SZQPEPS_PEPS_PKE_PRJ_0301"

"transmitter-d7a399.sfg"
  : void_tx_read_configuration___ushort___PTxRegSettings_t
  : "tx_read_configuration" global 60 Ofile
  (
    __ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort
    __uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort
  )

"transmitter-e68161.sfg"
  : void_tx_apply_configuration___PTxRegSettings_t
  : "tx_apply_configuration" global 80 Ofile
  (
  )

"transmitter-139fe1.sfg"
  : error_t_tx_enable_Xtal_oscillator_bool_t
  : "tx_enable_Xtal_oscillator" global 101 Ofile
  (
    error_t_phcaiKEyLLGenFunc_CS_UHF_XoStartUp_bool_t_bool_t
  )

"transmitter-70e876.sfg"
  : error_t_tx_enable_PLL_bool_t
  : "tx_enable_PLL" global 117 Ofile
  (
    error_t_phcaiKEyLLGenFunc_CS_UHF_PllStartUp_bool_t_bool_t___uchar
  )

"transmitter-a315e5.sfg"
  : error_t_tx_enable_PA_bool_t
  : "tx_enable_PA" global 137 Ofile
  (
    void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar
    void_phcaiKEyLLGenFunc_timer0_delay_us___ushort
  )

"transmitter-c81fac.sfg"
  : void_tx_shutdown
  : "tx_shutdown" global 166 Ofile
  (
    void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar
  )

"transmitter-51aaef.sfg"
  : void_tx_transmit_buffer_encoded_bytes_DataEnc_t___ushort___P__uchar
  : "tx_transmit_buffer_encoded_bytes" global 176 Ofile
  (
  )

"transmitter-eeb392.sfg"
  : void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar
  : "tx_transmit_buffer_encoded_bits" global 198 Ofile
  (
  )

"transmitter-63d4d7.sfg"
  : void_tx_transmit_encoded_bits_DataEnc_t___ushort___ushort
  : "tx_transmit_encoded_bits" global 233 Ofile
  (
  )

"transmitter-a39eb2.sfg"
  : void_Rf_TXFream___P__uchar___uchar
  : "Rf_TXFream" global 271 Ofile
  (
    void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort
    void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar
  )

"transmitter-196dc6.sfg"
  : void_FHSS_FreqCfg___uchar___uchar
  : "FHSS_FreqCfg" global 346 Ofile
  (
    error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage___P__uchar___ushort
  )

""
  : __ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort
  : "phcaiKEyLLGenFunc_ULPEE_ReadOneWord" global 1 Ofile
  (
  )

""
  : __uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort
  : "phcaiKEyLLGenFunc_ULPEE_ReadOneByte" global 1 Ofile
  (
  )

""
  : error_t_phcaiKEyLLGenFunc_CS_UHF_XoStartUp_bool_t_bool_t
  : "phcaiKEyLLGenFunc_CS_UHF_XoStartUp" global 1 Ofile
  (
  )

""
  : error_t_phcaiKEyLLGenFunc_CS_UHF_PllStartUp_bool_t_bool_t___uchar
  : "phcaiKEyLLGenFunc_CS_UHF_PllStartUp" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar
  : "phcaiKEyLLGenFunc_CS_SetClkCon" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_timer0_delay_us___ushort
  : "phcaiKEyLLGenFunc_timer0_delay_us" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort
  : "phcaiKEyLLGenFunc_CS_ULPEE_ReadPage" global 1 Ofile
  (
  )

""
  : error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage___P__uchar___ushort
  : "phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage" global 1 Ofile
  (
  )

