
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:25 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i timer-d63159 mrk3


// m23, jump target: m1 (next offset: 4)
000000  1 0  "0000010010010111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000001  2 0  "0110111001000011"   // (DM[0]) = _pl_rd_res_reg_const_store_1_B1 (RwL[0],0,DM[0],R7); 
000002  0 0  "0000000000000000"   // /
000003  1 0  "0101101000000100"   // () = jump_const_1_B1 (4); 

// m88;   next: m13 (next offset: 4)

// m13 inline assembly;   next: m17 (next offset: 5)
000004  1 0  "0110100001000000"  .loop_nesting 1 .srcref "C:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3_base/lib" "mrk3_int.h" 604 .srcref "" "apps/src/timer.c" 117  .inline_asm_begin .inline_asm_end // 

// m17;   next: m1 (next offset: 7)
000005  2 0  "0000011010001011" .loop_nesting 1    // (DM[0],c_flag,nz_flag,o_flag) = _mi_load_const__pl_rd_res_reg_const_store_1_B2 (0,DM[0],DM[0],R7); 
000006  0 0  "0000000000000000"   // /

// m1;   next: m87 (next offset: 7)

// m87;   next: m26, jump target: m88 (next offset: 10)
000007  2 0  "0100011010000011"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B2 (0,DM[0],R7); 
000008  0 0  "0000000000000000"   // /
000009  1 0  "0101011111111011"   // () = cc_a__jump_const_1_B1 (c_flag,nz_flag,-5); 

// m26 (next offset: /)
000010  1 0  "0001010010010111" .loop_nesting 0    // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000011  1 0  "0001101111000100"   // () = ret_1_B1 (); 

