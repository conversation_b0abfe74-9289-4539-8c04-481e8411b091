
// File generated by showcolor version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:25 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\showcolor.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Dindirect_bitf -t2 -yRw timer-3dbc9d mrk3

[
    0 : void_cycle_delay_ms___ushort typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : wait typ=int8_ val=0t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   21 : INTFLAG0 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_INTFLAG0_t_DM9
   25 : a typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=uint16_t_DM9
   26 : b typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=uint16_t_DM9
   29 : __ptr_INTFLAG0 typ=int16_ val=0a bnd=m adro=21
   31 : __ptr_a typ=int16_ val=0a bnd=m adro=25
   33 : __ptr_b typ=int16_ val=0a bnd=m adro=26
   34 : __arg_wait typ=int16_ bnd=p tref=uint16_t__
   39 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   61 : __tmp typ=bool bnd=m
   78 : __ct_500 typ=uint16_1_32768_ val=500f bnd=m
   86 : __fch_wait typ=int16_ bnd=m
  105 : __tmp typ=bool bnd=m
  106 : __ct_2s0 typ=int16_ val=2s0 bnd=m
  122 : __ct_2s0 typ=int16_ val=2s0 bnd=m
  130 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  146 : __apl_c typ=uint1_ bnd=m tref=uint1___
  148 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  158 : __true typ=bool val=1f bnd=m
  160 : __either typ=bool bnd=m
  161 : __trgt typ=rel8_ val=0j bnd=m
  162 : __trgt typ=rel8_ val=0j bnd=m
  163 : __trgt typ=rel8_ val=0j bnd=m
  164 : __trgt typ=rel8_ val=0j bnd=m
  165 : __trgt typ=rel8_ val=0j bnd=m
  166 : __trgt typ=rel8_ val=0j bnd=m
  167 : __trgt typ=rel8_ val=0j bnd=m
  168 : __trgt typ=rel8_ val=0j bnd=m
  169 : __trgt typ=rel8_ val=0j bnd=m
  170 : __seff typ=any bnd=m
  171 : __seff typ=any bnd=m
  172 : __seff typ=any bnd=m
  173 : __seff typ=any bnd=m
  174 : __seff typ=any bnd=m
  175 : __seff typ=any bnd=m
  176 : __seff typ=any bnd=m
  177 : __seff typ=any bnd=m
  178 : __seff typ=any bnd=m
  179 : __seff typ=any bnd=m
  180 : __seff typ=any bnd=m
  181 : __seff typ=any bnd=m
  182 : __seff typ=any bnd=m
  183 : __seff typ=any bnd=m
  184 : __seff typ=any bnd=m
  185 : __seff typ=any bnd=m
  186 : __seff typ=any bnd=m
  187 : __seff typ=any bnd=m
  188 : __seff typ=any bnd=m
  189 : __seff typ=any bnd=m
]
Fvoid_cycle_delay_ms___ushort {
    #5 off=0
    (__vola.12 var=13) source ()  <23>;
    (__sp.18 var=19) source ()  <29>;
    (wait.19 var=20) source ()  <30>;
    (INTFLAG0.20 var=21) source ()  <31>;
    (a.24 var=25) source ()  <35>;
    (b.25 var=26) source ()  <36>;
    (__arg_wait.33 var=34 stl=RwL off=0) inp ()  <44>;
    (__ct_0t0.635 var=39) const_inp ()  <742>;
    (__ct_2s0.637 var=122) const_inp ()  <744>;
    <101> {
      (__sp.41 var=19 __seff.693 var=187 stl=c_flag_w __seff.694 var=188 stl=nz_flag_w __seff.695 var=189 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1 (__ct_2s0.637 __sp.18 __sp.18)  <797>;
      (__seff.756 var=188 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.694)  <916>;
      (__seff.757 var=187 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.693)  <917>;
      (__seff.765 var=189 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.695)  <925>;
    } stp=3;
    <102> {
      (wait.50 var=20) _pl_rd_res_reg_const_store_1 (__arg_wait.764 __ct_0t0.635 wait.19 __sp.41)  <798>;
      (__arg_wait.764 var=34 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16_ (__arg_wait.33)  <924>;
    } stp=6;
    call {
        () chess_separator_scheduler ()  <61>;
    } #6 off=1
    #193 off=2
    (__ptr_INTFLAG0.632 var=29) const_inp ()  <739>;
    (__trgt.638 var=161) const_inp ()  <745>;
    <99> {
      (__apl_nz.559 var=130 stl=nz_flag_w __seff.690 var=185 stl=c_flag_w __seff.691 var=186 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1 (__ct_0t0.635 wait.50 __sp.41)  <795>;
      (__seff.753 var=185 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.690)  <913>;
      (__apl_nz.755 var=130 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.559)  <915>;
      (__seff.766 var=186 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.691)  <926>;
    } stp=3;
    <100> {
      () cc_ne__jump_const_1 (__apl_nz.754 __trgt.638)  <796>;
      (__apl_nz.754 var=130 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.755)  <914>;
    } stp=8;
    if {
        {
            () if_expr (__either.616)  <87>;
            (__either.616 var=160) undefined ()  <715>;
        } #9
        {
            (__true.617 var=158) const ()  <716>;
        } #11
        {
            (__either.619 var=160) undefined ()  <719>;
            (__trgt.639 var=162) const_inp ()  <746>;
            <98> {
              (INTFLAG0.80 var=21 __vola.81 var=13 __seff.688 var=184 stl=nz_flag_w) load_const__ad_const_cmp_const_cc_eq__jump_const_1 (__ptr_INTFLAG0.632 __trgt.639 INTFLAG0.20 __vola.12)  <794>;
              (__seff.771 var=184 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.688)  <930>;
            } stp=5;
        } #199 off=3
        {
            (__vola.92 var=13) merge (__vola.12 __vola.81)  <102>;
            (INTFLAG0.93 var=21) merge (INTFLAG0.20 INTFLAG0.80)  <103>;
            (__tmp.604 var=61) merge (__true.617 __either.619)  <698>;
        } #12
    } #8
    if {
        {
            () if_expr (__tmp.604)  <125>;
        } #15
        {
        } #19 off=7
        {
            call {
                (__vola.115 var=13) Fvoid_nop (__vola.92)  <126>;
            } #17 off=5
            #278 off=6
            (__trgt.640 var=163) const_inp ()  <747>;
            <97> {
              () jump_const_1 (__trgt.640)  <793>;
            } stp=5;
        } #274
        {
            (__vola.116 var=13) merge (__vola.92 __vola.115)  <128>;
        } #20
    } #14
    #21 off=8
    (__ptr_a.633 var=31) const_inp ()  <740>;
    <96> {
      (a.120 var=25) store_const_const_1 (__ptr_a.633 a.24)  <792>;
    } stp=3;
    call {
        () chess_separator_scheduler ()  <132>;
    } #22 off=9
    #59 off=10 nxt=-3 tgt=2
    () sink (__vola.116)  <420>;
    () sink (__sp.41)  <426>;
    () sink (wait.50)  <427>;
    () sink (INTFLAG0.93)  <428>;
    () sink (a.120)  <432>;
    () sync_sink (__vola.116) sid=22  <434>;
    () sync_sink (a.120) sid=34  <446>;
    () sync_sink (b.25) sid=35  <447>;
    (__vola.612 var=13) never ()  <710>;
    (a.613 var=25) never ()  <711>;
    (b.614 var=26) never ()  <712>;
    (__ptr_b.634 var=33) const_inp ()  <741>;
    (__trgt.641 var=164) const_inp ()  <748>;
    (__trgt.642 var=165) const_inp ()  <749>;
    (__trgt.646 var=169) const_inp ()  <753>;
    <93> {
      (__fch_wait.362 var=86 stl=DMw_r) load__pl_rd_res_reg_const_1 (__ct_0t0.635 wait.50 __sp.41)  <789>;
      (__fch_wait.763 var=86 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16_ (__fch_wait.362)  <923>;
    } stp=3;
    <95> {
      () jump_const_1 (__trgt.646)  <791>;
    } stp=11;
    <153> {
      (__ct_500.770 var=78 stl=__CTa_w0_uint16__cstP16_E1) const_1 ()  <885>;
      (__ct_500.769 var=78 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768_ (__ct_500.770)  <929>;
    } stp=6;
    do {
        {
            (__vola.146 var=13) entry (__vola.364 __vola.612)  <159>;
            (a.158 var=25) entry (a.388 a.613)  <171>;
            (b.159 var=26) entry (b.390 b.614)  <172>;
        } #28
        {
            #30 off=11
            <92> {
              (b.170 var=26) store_const_const_1 (__ptr_b.634 b.159)  <788>;
            } stp=3;
            do {
                {
                    (__vola.197 var=13) entry (__vola.250 __vola.146)  <211>;
                    (b.210 var=26) entry (b.276 b.170)  <224>;
                } #37
                {
                    call {
                        (__vola.218 var=13) Fvoid_nop (__vola.197)  <232>;
                    } #39 off=13
                    #43 off=14
                    <91> {
                      (b.224 var=26 __seff.679 var=181 stl=c_flag_w __seff.680 var=182 stl=nz_flag_w __seff.681 var=183 stl=o_flag_w) _pl_load_const_const_store_1 (__ptr_b.634 b.210 b.210)  <787>;
                      (__seff.721 var=182 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.680)  <899>;
                      (__seff.722 var=181 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.679)  <900>;
                      (__seff.723 var=183 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.681)  <901>;
                    } stp=3;
                    call {
                        () chess_separator_scheduler ()  <241>;
                    } #45 off=15
                    #215 off=16
                    <89> {
                      (__apl_c.575 var=146 stl=c_flag_w __apl_nz.577 var=148 stl=nz_flag_w __seff.677 var=180 stl=o_flag_w) load_const_cmp_2 (__ct_500.725 __ptr_b.634 b.224)  <785>;
                      (__apl_nz.718 var=148 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.577)  <896>;
                      (__apl_c.720 var=146 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.575)  <898>;
                      (__seff.724 var=180 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.677)  <902>;
                      (__ct_500.725 var=78 stl=a_w1) a_w1_1_dr_move_Rw_1_uint16_1_32768_ (__ct_500.769)  <903>;
                    } stp=3;
                    <90> {
                      () cc_b__jump_const_1 (__apl_c.719 __apl_nz.717 __trgt.641)  <786>;
                      (__apl_nz.717 var=148 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.718)  <895>;
                      (__apl_c.719 var=146 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.720)  <897>;
                    } stp=8;
                } #38
                {
                    () while_expr (__either.622)  <267>;
                    (__vola.250 var=13 __vola.251 var=13) exit (__vola.218)  <268>;
                    (b.276 var=26 b.277 var=26) exit (b.224)  <281>;
                    (__either.622 var=160) undefined ()  <724>;
                } #47
            } #36 rng=[1,2147483647]
            #53 off=17
            <88> {
              (a.339 var=25 __seff.672 var=177 stl=c_flag_w __seff.673 var=178 stl=nz_flag_w __seff.674 var=179 stl=o_flag_w) _pl_load_const_const_store_1 (__ptr_a.633 a.158 a.158)  <784>;
              (__seff.730 var=178 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.673)  <904>;
              (__seff.731 var=177 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.672)  <905>;
              (__seff.736 var=179 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.674)  <910>;
            } stp=3;
            sync {
                (__vola.340 var=13) sync_link (__vola.251) sid=22  <374>;
                (a.352 var=25) sync_link (a.339) sid=34  <386>;
                (b.353 var=26) sync_link (b.277) sid=35  <387>;
            } #2 off=18 nxt=220
            #220 off=19
            <86> {
              (__apl_c.581 var=146 stl=c_flag_w __apl_nz.583 var=148 stl=nz_flag_w __seff.670 var=176 stl=o_flag_w) load_const_cmp_1 (__fch_wait.742 __ptr_a.633 a.352)  <782>;
              (__apl_nz.733 var=148 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.583)  <907>;
              (__apl_c.735 var=146 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.581)  <909>;
              (__seff.737 var=176 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.670)  <911>;
              (__fch_wait.742 var=86 stl=a_w1) a_w1_1_dr_move_Rw_1_int16_ (__fch_wait.763)  <912>;
            } stp=3;
            <87> {
              () cc_b__jump_const_1 (__apl_c.734 __apl_nz.732 __trgt.642)  <783>;
              (__apl_nz.732 var=148 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.733)  <906>;
              (__apl_c.734 var=146 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.735)  <908>;
            } stp=8;
        } #29
        {
            () while_expr (__either.624)  <398>;
            (__vola.364 var=13 __vola.365 var=13) exit (__vola.340)  <399>;
            (a.388 var=25 a.389 var=25) exit (a.352)  <411>;
            (b.390 var=26 b.391 var=26) exit (b.353)  <412>;
            (__either.624 var=160) undefined ()  <727>;
        } #57
    } #27
    #226 off=20
    (__trgt.643 var=166) const_inp ()  <750>;
    <84> {
      (__apl_nz.589 var=130 stl=nz_flag_w __seff.666 var=174 stl=c_flag_w __seff.667 var=175 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1 (__ct_0t0.635 wait.50 __sp.41)  <780>;
      (__seff.758 var=174 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.666)  <918>;
      (__apl_nz.760 var=130 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.589)  <920>;
      (__seff.767 var=175 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.667)  <927>;
    } stp=3;
    <85> {
      () cc_ne__jump_const_1 (__apl_nz.759 __trgt.643)  <781>;
      (__apl_nz.759 var=130 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.760)  <919>;
    } stp=8;
    if {
        {
            () if_expr (__either.626)  <522>;
            (__either.626 var=160) undefined ()  <730>;
        } #63
        {
            (__true.627 var=158) const ()  <731>;
        } #65
        {
            (__either.629 var=160) undefined ()  <734>;
            (__trgt.644 var=167) const_inp ()  <751>;
            <83> {
              (INTFLAG0.477 var=21 __vola.478 var=13 __seff.664 var=173 stl=nz_flag_w) load_const__ad_const_cmp_const_cc_eq__jump_const_1 (__ptr_INTFLAG0.632 __trgt.644 INTFLAG0.93 __vola.365)  <779>;
              (__seff.772 var=173 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.664)  <931>;
            } stp=5;
        } #232 off=21
        {
            (__vola.489 var=13) merge (__vola.365 __vola.478)  <537>;
            (INTFLAG0.490 var=21) merge (INTFLAG0.93 INTFLAG0.477)  <538>;
            (__tmp.607 var=105) merge (__true.627 __either.629)  <702>;
        } #66
    } #62
    if {
        {
            () if_expr (__tmp.607)  <560>;
        } #69
        {
        } #73 off=25
        {
            call {
                (__vola.512 var=13) Fvoid_nop (__vola.489)  <561>;
            } #71 off=23
            #280 off=24
            (__trgt.645 var=168) const_inp ()  <752>;
            <82> {
              () jump_const_1 (__trgt.645)  <778>;
            } stp=5;
        } #276
        {
            (__vola.513 var=13) merge (__vola.489 __vola.512)  <563>;
        } #74
    } #68
    #76 off=26 nxt=-2
    () sink (__vola.513)  <570>;
    () sink (__sp.519)  <576>;
    () sink (wait.50)  <577>;
    () sink (INTFLAG0.490)  <578>;
    () sink (a.389)  <582>;
    () sink (b.391)  <583>;
    (__ct_2s0.636 var=106) const_inp ()  <743>;
    <80> {
      (__sp.519 var=19 __seff.659 var=170 stl=c_flag_w __seff.660 var=171 stl=nz_flag_w __seff.661 var=172 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1 (__ct_2s0.636 __sp.41 __sp.41)  <776>;
      (__seff.761 var=171 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.660)  <921>;
      (__seff.762 var=170 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.659)  <922>;
      (__seff.768 var=172 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.661)  <928>;
    } stp=3;
    <81> {
      () ret_1 ()  <777>;
    } stp=10;
} #0
0 : 'apps/src/timer.c';
----------
0 : (0,177:0,0);
2 : (0,183:5,33);
5 : (0,177:21,0);
6 : (0,177:21,0);
8 : (0,179:18,2);
11 : (0,179:18,4);
14 : (0,179:3,6);
17 : (0,181:5,7);
19 : (0,179:3,10);
21 : (0,183:10,14);
22 : (0,183:10,14);
27 : (0,183:5,15);
29 : (0,183:5,15);
30 : (0,185:15,16);
36 : (0,185:10,17);
38 : (0,185:10,17);
39 : (0,187:14,17);
43 : (0,185:25,0);
45 : (0,185:10,21);
53 : (0,183:21,0);
59 : (0,183:5,37);
62 : (0,190:21,40);
65 : (0,190:21,42);
68 : (0,190:6,44);
71 : (0,192:5,45);
73 : (0,190:6,48);
76 : (0,190:6,51);
193 : (0,179:12,2);
199 : (0,179:41,3);
215 : (0,185:19,24);
220 : (0,183:14,35);
226 : (0,190:15,40);
232 : (0,190:44,41);
----------
61 : (0,177:21,0);
87 : (0,179:18,2);
102 : (0,179:18,5);
103 : (0,179:18,5);
125 : (0,179:3,6);
126 : (0,181:5,7);
128 : (0,179:3,12);
132 : (0,183:10,14);
159 : (0,183:5,15);
171 : (0,183:5,15);
172 : (0,183:5,15);
211 : (0,185:10,17);
224 : (0,185:10,17);
232 : (0,187:14,17);
241 : (0,185:10,21);
267 : (0,185:10,24);
268 : (0,185:10,24);
281 : (0,185:10,24);
398 : (0,183:5,35);
399 : (0,183:5,35);
411 : (0,183:5,35);
412 : (0,183:5,35);
522 : (0,190:21,40);
537 : (0,190:21,43);
538 : (0,190:21,43);
560 : (0,190:6,44);
561 : (0,192:5,45);
563 : (0,190:6,50);
698 : (0,179:18,5);
702 : (0,190:21,43);
776 : (0,190:6,0) (0,190:6,51);
777 : (0,190:6,51);
779 : (0,190:33,41) (0,190:37,41) (0,190:44,41) (0,190:6,44);
780 : (0,190:11,40) (0,177:30,0) (0,190:15,40);
781 : (0,190:15,40) (0,190:21,40);
782 : (0,183:13,35) (0,183:14,35);
783 : (0,183:14,35) (0,183:5,35);
784 : (0,183:21,30) (0,183:20,0);
785 : (0,185:18,24) (0,185:19,24);
786 : (0,185:19,24) (0,185:10,24);
787 : (0,185:25,19) (0,185:24,0);
788 : (0,185:14,15);
789 : (0,183:15,35) (0,177:30,0);
792 : (0,183:9,13);
794 : (0,179:30,3) (0,179:34,3) (0,179:41,3) (0,179:3,6);
795 : (0,179:8,2) (0,177:30,0) (0,179:12,2);
796 : (0,179:12,2) (0,179:18,2);
797 : (0,177:5,0);
798 : (0,177:30,0) (0,177:21,0);
885 : (0,185:19,0);
