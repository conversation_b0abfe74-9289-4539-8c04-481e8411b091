/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: lf_receive.c 31225 2021-03-10 14:44:11Z dep10330 $
  $Revision: 31225 $
*/

/*+----------------------------------------------------------------------------
  | NOTE: The code provided herein is still under development and hence
  |       subject to change.
  +----------------------------------------------------------------------------*/

/**
 * @file
 * Application level LF Active data reception functions for TOKEN family.
 *
 */

#include "phcaiKEyLLGenFunc_CS.h"
#include "phcaiKEyLLGenFunc.h"
#include "ulpeemap.h"
#include "stdmacros.h"
#include "globvar.h"
#include "lf_receive.h"

/*
  Change Log:
  -----------
  2014-01-24 (MMr)
  - started, new procedure in lf_read_byte for hardware FIFO
  2014-10-16 (MMr)
  - NEXTFIFODATAOK renamed to NEXTFIFODATOK acc. to DS
  2014-11-13 (MMr)
  - added lf_read_PREDAT(), to be reviewed!
  - lf_read_byte(): removed initial test of NEWBYTE_OVF
  2015-07-17 (MMr)
  - added the proposed alternative sequence in lf_read_PREDAT, to be evaluated.
  2015-08-12 (MMr)
  - lf_read_byte():
    + removed check for BITFAIL after  m_u8_NextFifoDataReady != 0U
    + for 'defensive programming' consider NEWBYTE_OVF when waiting for PP interrupt
      (not sure if NEWBYTE_OVF might still be set and no further NEWBYTE comes in)
    + removed older code in comments
  - lf_read_PREDAT(): new sequence for clearing NEWBYTE_OVF
  - lf_read_init(): set NEWBYTEOVFHOLD=1 as default
  2016-02-16 (NBn,MMr):
  - updated lf_read_byte to be consistent with flow chart in AN rev1.3.
  - added comments.
  - lf_read_block_crc: if u8_numbytes ==0, directly proceeds with reading CRC byte
  2017-03-15 (MMr):
  - lf_read_block: changed to while loop
  2017-03-28 (MMr):
  - lf_read_byte: PRESTAT register "read-until-stable" changed to equivalent
    loop-free code.
  2017-09-13 (MMr):
  - moved typedefs to user lib module phcaiKEyLLGenFunc_LfActive.h in preparation
    of moving read_byte functions of all types there.
  2017-09-14 (MMr):
  - moved basic functions to user lib module phcaiKEyLLGenFunc_LfActive.c
    and merged with former SRX variant of this module.
  - removed lf_read_byte() (was not called directly in demo application), replaced
    by user library function phcaiKEyLLGenFunc_LfAct_read_byte().
  2018-08-22 (MMr):
  - lf_read_crc: added STOPRX operation to avoid NEWBYTE flag generation past end
    of LF frame on SRX platform.
  2019-03-15 (MMr):
  - lf_read_byte: restored function to receive one byte including CRC update.
  2021-01-13 (MMr):
  - lf_stop: removed.

 */



/*-----------------------------------------------------------------------------------------------*/

/**
  CRC (8-bit), updated during LF data reception in lf_read_block.
  Must be initialized by a call to lf_read_init().
 */
static uint8_t m_u8_CRC;


/*--------------------------*/
/* Function Implementations */
/*--------------------------*/

/*-----------------------------------------------------------------------------------------------*/

LFRCVERROR_t  lf_read_block( uint8_t * pu8_rx_buffer, uint8_t u8_numbytes )
{
  LFRCVERROR_t e_res;

  e_res = phcaiKEyLLGenFunc_LfAct_read_block( pu8_rx_buffer, u8_numbytes );

  if ( ( LF_OK == e_res ) && (u8_numbytes > 0 ) ) {
    m_u8_CRC = phcaiKEyLLGenFunc_Util_CRC8_bytes( pu8_rx_buffer, u8_numbytes, m_u8_CRC );
  }
  return e_res;
}

/*-----------------------------------------------------------------------------------------------*/

LFRCVERROR_t lf_read_byte( uint8_t * pu8_rx_buffer )
{
  uint8_t u8_rcvByte;
  LFRCVERROR_t e_res;

  e_res = phcaiKEyLLGenFunc_LfAct_read_byte( &u8_rcvByte );

  if ( LF_OK == e_res ) {
    *pu8_rx_buffer = u8_rcvByte;
    m_u8_CRC = phcaiKEyLLGenFunc_Util_CRC8_update( u8_rcvByte, m_u8_CRC );
  }
  return e_res;
}

/*-----------------------------------------------------------------------------------------------*/

LFRCVERROR_t lf_read_crc( void )
{
  uint8_t u8_rcvByte;
  LFRCVERROR_t e_result;

  // read the CRC byte
  e_result = phcaiKEyLLGenFunc_LfAct_read_byte( &u8_rcvByte );

  if ( LF_OK == e_result )
  {
    /* compare with locally calculated CRC8 */
    if ( u8_rcvByte != m_u8_CRC )
    {
      e_result = LF_INVCRC;    /* Indicate failure (CRC mismatch) */
    }
  }
  // stop the receiver since after CRC byte no more bytes are expected (SRX only).
  phcaiKEyLLGenFunc_LfAct_stop_rx();

  return e_result;
}

/*-----------------------------------------------------------------------------------------------*/

LFRCVERROR_t lf_read_block_crc( uint8_t * pu8_rx_buffer, uint8_t u8_numbytes )
{
  LFRCVERROR_t e_res;

  if( u8_numbytes > 0 ) {
    e_res = lf_read_block( pu8_rx_buffer, u8_numbytes );
  }
  else {
    e_res = LF_OK;
  }

  if ( e_res == LF_OK ) {
    e_res = lf_read_crc();
  }
  return e_res;
}

/*-----------------------------------------------------------------------------------------------*/

void lf_read_init( void )
{
  phcaiKEyLLGenFunc_LfAct_read_init();
  m_u8_CRC = 0u;
}

/*-----------------------------------------------------------------------------------------------*/

LFRCVERROR_t lf_error_code( void )
{
  return phcaiKEyLLGenFunc_LfAct_get_rx_errorcode();
}

/*-----------------------------------------------------------------------------------------------*/

uint8_t lf_get_crc( void )
{
  return m_u8_CRC;
}

/*-----------------------------------------------------------------------------------------------*/

/* eof */
