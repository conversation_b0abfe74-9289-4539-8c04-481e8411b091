/*
  -----------------------------------------------------------------------------
  Copyright 2010 - 2020  NXP
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, <PERSON>FF<PERSON>IATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_NBRSSI.h 28748 2020-09-21 16:18:07Z dep10330 $
  $Revision: 28748 $

*/


/**
 * @file
 * Declarations of User Library Functions related to Narrowband RSSI measurements.
 */

#ifndef PHCAIKEYLLGENFUNC_NBRSSI_H
#define PHCAIKEYLLGENFUNC_NBRSSI_H

#include "types.h"
#include "phcaiKEyLLGenFunc_Platform.h"
#include "phcaiKEyLLGenFunc_RSSI.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup NBRSSI  Narrowband RSSI support functions
 * Support functions for Narrowband RSSI measurements in SRX types.
 * @{
 */

/**
 *  Enumeration type for NBRSSI channel selection.
 *  Channels are flags which can be or-combined for channel summation.
 */
typedef enum
{
  /** Keep existing channel selection */
  NBRSSI_CH_AUTO         = 0x00u,  /* not used currently */
  /** Channel 1 */
  NBRSSI_CH1             = 0x01u,
  /** Channel 2 */
  NBRSSI_CH2             = 0x02u,
  /** Channel 3 */
  NBRSSI_CH3             = 0x04u,
  /** Invalid channel selection */
  NBRSSI_CH_INVALID      = 0x08u
}
phcaiKEyLLGenFunc_NbRssiChannel_t;

/**
 *  Enumeration type for NBRSSI result selection
 */
typedef enum
{
  NBRSSI_AVG        = 0x00u,
  NBRSSI_QMEAN      = 0x01u,
  NBRSSI_MIN        = 0x02u,
  NBRSSI_MAX        = 0x03u,
  NBRSSI_RAW        = 0x04u,
  NBRSSI_FREQ_ERR   = 0x05u
}
phcaiKEyLLGenFunc_RssiValue_t;

/**
 *  NBRSSI Filter chain configuration mask
 */
#define NBRSSI_FILT_CFG_MASK      0x70u


/**
 *  Enumeration type for NBRSSI filter chain configurations
 */
typedef enum
{
  /** Filter configuration A  */
  NBRSSI_FILT_CFG_A      = 0u,
  /** Filter configuration B */
  NBRSSI_FILT_CFG_B      = 4u,
  /** Filter configuration C */
  NBRSSI_FILT_CFG_C      = 3u,
  /** Filter configuration D */
  NBRSSI_FILT_CFG_D      = 7u,
}
phcaiKEyLLGenFunc_NbrssiFilt_t;

/**
 *  Enumeration type for NBRSSI Near-Range Extension strategy configuration
 */
typedef enum
{
  /** Near-Range Extension not used  */
  NBRSSI_NRE_CFG_NONE       = 0x00u,
  /** Near-Range Extension used with -18dB range */
  NBRSSI_NRE_CFG_m18dB      = 0x01u,
  /** Near-Range Extension used with -18dB or 0dB range */
  NBRSSI_NRE_CFG_m18dB_0dB  = 0x02u,
}
phcaiKEyLLGenFunc_NbrssiNre_t;

/**
 *  Bit mask for NBRSSI Near-Range Extension flag in range variable
 */
#define NBRSSI_NEAR_RANGE_EXT_BIT_MASK    (0x80u)

/**
 *  Enumeration type for NBRSSI error codes (bit patterns, may be OR combined)
 */
typedef enum
{
  NBRSSI_OK                       = 0x00u,
  /** SDADC overload */
  NBRSSI_ERROR_SDADC_DIG_OVERLOAD = 0x01u,
  /** SDADC overflow */
  NBRSSI_ERROR_SDADC_ANA_OVERLOAD = 0x02u,
  /** NBRSSI in-band disturber */
  NBRSSI_ERROR_INBAND_DISTURBER   = 0x04u,
  /** NBRSSI phase speed to high */
  NBRSSI_ERROR_PHASE_SPEED_HIGH   = 0x08u,
  /** NBRSSI strong disturber */
  NBRSSI_ERROR_STRONG_DISTURBER   = 0x10u
}
phcaiKEyLLGenFunc_NbrssiError_t;



/**
 *  Global variable to pass NBRSSI error code to application
 */
extern phcaiKEyLLGenFunc_NbrssiError_t e_nbrssiError;

/**
 *  Structure type for NBRSSI results
 */
typedef struct
{
  phcaiKEyLLGenFunc_RssiRange_t    en_range;
  uint16_t                         u16_avg;
  uint16_t                         u16_qmean;
  uint16_t                         u16_min;
  uint16_t                         u16_max;
  uint16_t                         u16_freq_offset;
  phcaiKEyLLGenFunc_NbrssiError_t  e_nbrssiError;
}
phcaiKEyLLGenFunc_NbrssiResult_t;

/**
 * @defgroup NBRSSI_RANGE NBRSSI overrange detectors
 * Following settings are proposed for the RSSI overrange detectors
 * (RSSI_OVFxxDB_LVL):
 * (1) Setting for range detection with w.c. 83.333kHz interferer: 8 (nom. 75% ADC-FS)
 * (2) Setting for range detection with w.c. 62.5kHz interferer: 3 (nom. 50% ADC-FS)
 * (3) Setting for overrange detection: 12 (nom. 95% ADC-FS)
 * For the lab validation and characterization, we will use only setting (1)
 * for range detection and setting (3) for overrange detection. Setting (2)
 * will give reduced S2J performance and shall therefore only be used in the
 * application, if really required (i.e. a 62.5kHz interferer is indeed expected).
 * @{
 */

/**
 *  Overflow indicator level configurations for range selection
 */
#ifndef RSSI_OVF36DB_LVL_RANGE
/** 36dB range indicator level */
#define RSSI_OVF36DB_LVL_RANGE (8u)
#endif
#ifndef RSSI_OVF18DB_LVL_RANGE
/** 18dB range indicator level */
#define RSSI_OVF18DB_LVL_RANGE (8u)
#endif
#ifndef RSSI_OVF0DB_LVL_RANGE
/** 0dB/-18dB range indicator level */
#define RSSI_OVF0DB_LVL_RANGE (8u)
#endif

/**
 *  Overflow indicator level configurations for SDADC overload evaluation
 */
#ifndef RSSI_OVF36DB_LVL_OVERLOAD
/** 36dB overload indicator level */
#define RSSI_OVF36DB_LVL_OVERLOAD (12u)
#endif
#ifndef RSSI_OVF18DB_LVL_OVERLOAD
/** 18dB overload indicator level */
#define RSSI_OVF18DB_LVL_OVERLOAD (12u)
#endif
#ifndef RSSI_OVF0DB_LVL_OVERLOAD
/** 0dB/-18dB overload indicator level */
#define RSSI_OVF0DB_LVL_OVERLOAD (12u)
#endif
/*@}*/


/**
 *  Define tolerance limit around NBRSSI average for NBRSSI qmean result. Used
 *  for in-band disturber detection.
 *  DS recommends 3 when averaging factor is at least 16.
 */
#define NBRSSI_AVG_DIFF   (3u)

/**
 *  Define lower limit for NBRSSI average result, for which NBRSSI frequency
 *  offset measurement is still considered valid.
 */
#define NBRSSI_AVG_MIN    (16u)

/**
 *  SDADC run-length configuration. The ADC can get stuck by an over-voltage on
 *  its input. This could happen by a wrong chosen range setting in the analogue
 *  part or by a disturber burst, not present at the range selection procedure.
 *  NBRSSI_ADC_RUNLEN defines, if you want to have a fast warning
 *  (NBRSSI_ADC_RUNLEN =0: also when ADC is stuck only shortly and than
 *  recovered) or if you like to accept short disturbance by the ADC
 *  (NBRSSI_ADC_RUNLEN=7).
 */
#define NBRSSI_ADC_RUNLEN (1u)

/**
  Initial setting of NB-RSSI control register RSSICON0:
  ch. 1 selected; SDADC input termination mode; power-on
 */
// RSSI control register RSSICON0 (16 bit)
// |  15  14  13  | 12 | 11 10  9   |   8    | 7        | 6  5  4 | 3  2  1  | 0
// | RSSI_CHANSEL |  0 | RSSI_RANGE | RDT/W0 | RSSI_PON | 0  0  0 | RSSI_OVF | RSSI_RST
// |   0   0   1  |  0 |  1  1  1   |   0    |     1    | 0  0  0 | 0        | 0
#define D_RSSICON0_STUP      0x2E80u

/**
 * Initial setting of ADC control register.
 *
 * 1.25 V (Vref) ADC reference required for NBRSSI overflow limiters.
 * INSEL2 INSEL1 INSEL0 REFSEL2|REFSEL1 REFSEL0 SAMTIM1 SAMTIM0|TSENSEN BATMEASEN BG1V2BUFEN BG1V2EN |SAMTIMEXT POWERON CONVRESET CONVSTART|
 *    0      0      0      0   |   0       0      0        0   |   0       0          0         1    |    0        0        1         0    |
 *                         0   |                           0   |                                1    |                                2    |
 */
#define D_ADCCON_NBRSSI               0x0012u

/*
 RSSI control register RSSICON3
 RSSI_RVV_INTEN = 0, RSSI_OVF36DB_LVL = 7.
 */
#define D_RSSICON2_POR                  0x07u

/*
 RSSI control register RSSICON3
 RSSI_FILT_CFG is set to D_RSSI_FILT_CFG, SDADC_OVERLOAD=1, SDADC_OVL_CFG=1 .
 */
#define D_RSSICON3_POR                  0x31u

 /*
 RSSI control register RSSICON5
 |   7    |   6    |     5            |       4            | 3      |     2       |    1   |     0       |
 | RFU W0 | RFU W0 | RSSI_OSC_CALDONE | RSSI_OSC_TRIMSTART | RDT W1 | RSSI_OSC_EN | RFU W0 | RSSI_LDO_EN |
 |    0       0          (ro)         |       0            |   1    |     1       |   0    |     1       |
 */
#define D_RSSICON5_STUP                 0x0Du

 /*
 RSSI control register RSSICON7
 |    7     6      5   |       4      |       3        |      2      |      1       |     0      |
 |  RSSI_VAL_SEL[2..0] |   RSSI_VALID | RSSI_RAW_VALID |   RSSI_BUSY | SDADC_ENABLE | RSSI_START |
 Warning: Bits RSSI_VALID and RSSI_RAW_VALID are of type "R/W1->0",
 hence, writing to other bit fields in this register will cleaar both flags (if =1)!
  */
/* RSSICON7 POR value */
#define D_RSSICON7_POR                  0x00u
/* Bit patterns for RSSICON7 */
#define RSSICON7_BIT_RSSI_START         0x01u
#define RSSICON7_BIT_SDADC_ENABLE       0x02u
#define RSSICON7_BIT_RSSI_BUSY          0x04u
#define RSSICON7_BIT_RSSI_RAW_VALID     0x08u
#define RSSICON7_BIT_RSSI_VALID         0x10u
#define RSSICON7_BIT_RSSI_VAL_SEL       0xE0u
/* Mask out the _VALID bits */
#define RSSICON7_MASK_CTRLBITS          0xE7u
/* Mask out the RSSI_VAL_SEL bits */
#define RSSICON7_MASK_VALSEL            0x1Fu

/*-----------------------------------------------------------------------------------------------*/

/**
 * Initialize, power-up and configure the RSSI gain chain and SDADC
 * for subsequent NB-RSSI operation.
 * VDDA regulator must have been enabled beforehand!
 * Sets ADCCON register and RSSICON0 bitfields RSSI_RANGE=input termination mode,
 * and RSSI_CHANSEL = channel1.
 * Returns SUCCESS if RSSI_OSC_CALDONE is 1 and VDDABRNREG is 0 at end of sequence.
 *
 * @return error indicator.
 * @see phcaiKEyLLGenFunc_NBRSSI_ADCStop
 */
error_t phcaiKEyLLGenFunc_NBRSSI_ADCInit( void );


/**
 * Resets and stops the ADC and resets RSSICON register.
 */
void phcaiKEyLLGenFunc_NBRSSI_ADCStop( void );

/*-----------------------------------------------------------------------------------------------*/


/**
 * Run an RSSI channel measurement.
 * Function phcaiKEyLLGenFunc_NBRSSI_ADCInit() must have been called before.
 * [tbf]

 *
 *
 * @param[in] u16_num_ADC_samples    Number of samples (ADC conversions) to be averaged.
 * @return The resulting unsigned 10 bit integer value (not compensated).
 *
 * @see phcaiKEyLLGenFunc_ADC_run_averaged
 */
uint16_t phcaiKEyLLGenFunc_NBRSSI_MeasChannel( uint16_t u16_num_ADC_samples );


/**
 * Run an RSSI measurement of a given 3D LF active channel,
 * and select or detect the appropriate gain or attenuation setting.
 *
 * @param[in]  e_ChannelSel    channel selection, @see phcaiKEyLLGenFunc_RssiChannel_t
 * @param[in]  e_range         setting for RSSI_RANGE
 *                             in RSSICON register.
 * @param[in]  b_autoRangeInd  if TRUE, select gain automatically.
 *
 * @return range (gain) information, @see phcaiKEyLLGenFunc_RssiRange_t.
 */
phcaiKEyLLGenFunc_RssiRange_t
phcaiKEyLLGenFunc_NBRSSI_SelectRange( phcaiKEyLLGenFunc_RssiChannel_t   e_ChannelSel,
                                      phcaiKEyLLGenFunc_RssiRange_t     e_range,
                                      bool_t                            b_autoRangeInd );


/**
 * Return last NBRSSI full results to application.
 *
 * @return
 */
phcaiKEyLLGenFunc_NbrssiResult_t phcaiKEyLLGenFunc_NBRSSI_LastResults( void );


/**
 * Return last NBRSSI error code.
 *
 * @return Error code.
 * @see phcaiKEyLLGenFunc_NbrssiResult_t
 */
phcaiKEyLLGenFunc_NbrssiError_t phcaiKEyLLGenFunc_NBRSSI_LastError( void );


/**
 * Convert channel selection flags (phcaiKEyLLGenFunc_NbRssiChannel_t) to
 * higher-level RSSI channel section type (phcaiKEyLLGenFunc_RssiChannel_t).
 * @see phcaiKEyLLGenFunc_RssiChannel_t
 * @see phcaiKEyLLGenFunc_NbRssiChannel_t
 */
phcaiKEyLLGenFunc_RssiChannel_t
phcaiKEyLLGenFunc_NBRSSI_ChannelFlagsToEnum( phcaiKEyLLGenFunc_NbRssiChannel_t en_channelflags );

/*@}*/
/*@}*/

#endif
