/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_ULPEE.c 20054 2019-05-10 10:59:25Z dep10330 $
  $Revision: 20054 $
*/


/**
 * @file
 * Implementation of User Functions related to ULP EEPROM.
 */

/*
  Change Log:

  2011-04-21 (MMr):
    - set ULPCON0.ULPPON = 1 in _ULPEE_Activate*
  2011-07-18 (MMr):
    - adjusted RNG startup delay acc. to DS
  2011-11-21 (MMr):
    - in phcaiKEyLLGenFunc_RNG_Start():
      added waiting until RNGRUN becomes 0
  2012-05-14 (MMr):
    - added function phcaiKEyLLGenFunc_ULPEE_ModuleIsActivated(),
    - improved comments
  2012-08-23 (MMr):
    - moved RNG functions to Util module.
    - added phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait().
  2013-07-29 (MMr):
  - Improved MISRA-C compliance
  2015-09-03 (MMr):
  - Improved MISRA-C compliance (fct. phcaiKEyLLGenFunc_ULPEE_ReadOneWord)
  2016-08-09 (MMr):
  - started port to TOKEN-PLUS/ACTIC5G-PLUS, only switches adapted. Register handling to be checked.
  2017-04-05 (MMr):
  - added phcaiKEyLLGenFunc_ULPEE_ActivateModuleRangeWait
  2018-09-21 (MMr):
  - phcaiKEyLLGenFunc_ULPEE_ActivateModuleRangeWait: added support for module 15
  2019-03-18 (MMr):
  - make use of bitfields in ULPCON0 accesses.
  - lowercases all 'u' suffixes.
 */

#include "types.h"
#include "phcaiKEyLLGenFunc_ULPEE.h"
#include "phcaiKEyLLGenFunc_CS.h"
#include "phcaiKEyLLGenFunc_Timer.h"
#include "phcaiKEyLLGenFunc_TimeConst.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @addtogroup EEPROM
 * @{
 */


/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_ULPEE_ActivateModule(const uint16_t u16_byte_address)
{
  uint8_t u8_module;

  ULPCON0.bits.ULPPON = 1u; /* ULPPON=1, Power-on selected ULP EEPROM modules */

  u8_module = (uint8_t) ( u16_byte_address  / 256u );

  if (u8_module <= 7u)
  {
    ULPSEL.byte.lo |= (0x01u << u8_module);
  }
  else if ( u8_module == 15u )
  {
    ULPSEL.byte.hi = 0x80u;
  }
  else
  {
    /* error, invalid module number, ignored */
  }
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait( const uint16_t u16_byte_address )
{
  uint8_t  u8_module;
  uint16_t u16_ulpsel_bit = 0x0000u;

  u8_module = (uint8_t) ( u16_byte_address / 256u );

  if ( u8_module <= 7u )
  {
    u16_ulpsel_bit = ( (uint16_t) 0x01u << u8_module );
  }
  else if ( u8_module == 15u )
  {
    u16_ulpsel_bit = 0x8000u;
  }
  else
  {
    /* error, invalid module number, ignored */
  }

  /* ULPPON = 0 or module not yet activated ? */
  if ( ( ULPCON0.bits.ULPPON == 0u ) || ( ( ULPSEL.val & u16_ulpsel_bit ) == 0u ) )
  {
    ULPCON0.bits.ULPPON = 1u;
    ULPSEL.val  |= u16_ulpsel_bit;
    phcaiKEyLLGenFunc_timer0_delay_us( t_ULP_PON_us );
  }
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_ULPEE_ActivateModuleRangeWait( const uint16_t u16_start_byte_address,
                                                      const uint16_t u16_end_byte_address  )
{
  uint8_t  u8_modnr, u8_module1, u8_module2;
  uint16_t u16_ulpsel_mask = 0x0000u;

  u8_module1 = (uint8_t) ( u16_start_byte_address / 256u );
  u8_module2 = (uint8_t) ( u16_end_byte_address   / 256u );

  if (    ( u8_module1 <= 7u )
       && ( u8_module2 <= 7u )
       && ( u8_module1 <= u8_module2 )
     )
  {
    for ( u8_modnr = u8_module1; u8_modnr <= u8_module2; u8_modnr++ ) {
      u16_ulpsel_mask |= ( (uint16_t) 0x0001u << u8_modnr );
    }
  }

  if ( ( u8_module1 == 15u ) && ( u8_module2 == 15u ) ) {
    u16_ulpsel_mask |= 0x8000u;
  }

  /* ULPPON = 0 or module not yet activated ? */
  if ( ( ULPCON0.bits.ULPPON == 0u ) || ( ( ULPSEL.val & u16_ulpsel_mask ) != u16_ulpsel_mask ) )
  {
    ULPCON0.bits.ULPPON = 1u;
    ULPSEL.val  |= u16_ulpsel_mask;
    phcaiKEyLLGenFunc_timer0_delay_us( t_ULP_PON_us );
  }
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_ULPEE_DeactivateModule(const uint16_t u16_byte_address)
{
  uint8_t u8_module;

  u8_module = (uint8_t) ( u16_byte_address / 256u );

  if ( u8_module <= 7u )
  {
    ULPSEL.byte.lo &=  ~(uint8_t)( 0x01u << u8_module);
  }
  else if ( u8_module == 15u )
  {
    ULPSEL.byte.hi &= 0x7FU;
  }
  else
  {
    /* error, invalid module number, ignored */
  }
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_ULPEE_ActivateAllModules(void)
{
  ULPCON0.bits.ULPPON = 1u; /* ULPPON=1, Power-on selected ULP EEPROM modules */
  ULPSEL.val = 0x80FFu;     /* all modules powered on */
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules(void)
{
  ULPSEL.val = 0x0000u;
}

/*-----------------------------------------------------------------------------------------------*/

bool_t phcaiKEyLLGenFunc_ULPEE_ModuleIsActivated( const uint16_t u16_byte_address )
{
  uint8_t u8_module;
  bool_t  e_res = FALSE;

  u8_module = (uint8_t) ( u16_byte_address / 256u );

  if ( ( ULPCON0.bits.ULPPON == 1u ) )
  {
    if ( u8_module <= 7u )
    {
      if ( ULPSEL.byte.lo & (uint8_t)(0x01u << u8_module) )
      {
        e_res = TRUE;
      }
    }
    else if ( u8_module == 15u )
    {
      if ( ULPSEL.byte.hi & 0x80u )
      {
        e_res = TRUE;
      }
    }
    else
    {
      /* error, invalid module number, ignored */
    }
  }
  return e_res;
}

/*-----------------------------------------------------------------------------------------------*/

uint8_t phcaiKEyLLGenFunc_ULPEE_ReadOneByte( const uint16_t u16_byte_address )
{
  uint8_t u8arr_buffer[2];
  phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait( u16_byte_address );

  phcaiKEyLLGenFunc_CS_ULPEE_ReadBytes( u8arr_buffer, u16_byte_address, 1u );
  return u8arr_buffer[0];
}

/*-----------------------------------------------------------------------------------------------*/

uint16_t phcaiKEyLLGenFunc_ULPEE_ReadOneWord( const uint16_t u16_byte_address )
{
  uint8_t u8arr_buffer[2];
  phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait( u16_byte_address      );
  phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait( u16_byte_address + 1u );

  phcaiKEyLLGenFunc_CS_ULPEE_ReadBytes( u8arr_buffer, u16_byte_address, 2u );
  return (uint16_t)( (uint16_t)u8arr_buffer[0] | (uint16_t)( (uint16_t)u8arr_buffer[1] << 8 ) );
}


/*@}*/
/*@}*/

/* eof */
