
// File generated by amnesia version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:20 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\amnesia.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Dindirect_bitf +Orls -NRwL -NRbL -NRbH +Orrmw +Osps transmitter-eeb392 mrk3

toolrelease _19R3;
//Children of func_bndl

rd_res_reg_R7 : rd_res_reg, func_bndl {
}
rd_res_reg_R7_B1 : rd_res_reg_R7 {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 810;
    isg : t;
    inp : ( R7 );
    out : ( SSP_r );
    rsc : (1) SSP_r ;
    opn : ( SSP_r_rd_R7_E1 );
    ins : 297;
}

wr_res_reg_R7 : wr_res_reg, func_bndl {
}
wr_res_reg_R7_B1 : wr_res_reg_R7 {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 811;
    isg : t;
    inp : ( SSP_w );
    out : ( R7 );
    rsc : (1) __rsrc_R7_wr_SSP_w_E1 ;
    opn : ( R7_wr_SSP_w_E1 );
    ins : 298;
}
wr_res_reg_R7_B2 : wr_res_reg_R7 {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 812;
    isg : t;
    inp : ( SSP_w );
    out : ( R7 );
    rsc : (1) __rsrc_R7_wr_SSP_w_s1_E1 ;
    opn : ( R7_wr_SSP_w_s1_E1 );
    ins : 42;
}

stack_load_bndl_1 : func_bndl, stack_load {
    arg : ( int8_:o int8_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_load_bndl_B1 : stack_load_bndl_1 {
    rid : 813;
    isg : t;
    inp : ( __spill_DM R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( DM_r );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_r DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 299;
}
stack_load_bndl_B2 : stack_load_bndl_1 {
    rid : 814;
    isg : t;
    inp : ( __spill_DM R7 __CTagu_1_pint3__cstP13_2_0_E1 );
    out : ( DM_r );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_r DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 300;
}

stack_load_bndl_2 : func_bndl, stack_load {
    arg : ( int16_:o int16_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_load_bndl_B3 : stack_load_bndl_2 {
    rid : 815;
    isg : t;
    inp : ( __spill_DMw R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( DMw_r );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_r DM_r1 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 301;
}
stack_load_bndl_B4 : stack_load_bndl_2 {
    rid : 816;
    isg : t;
    inp : ( __spill_DMw R7 __CTagu_1_pint4_step2__cstP13_3_1_E1 );
    out : ( DMw_r );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_r DM_r1 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 302;
}

stack_load_indirect_bndl_1 : func_bndl, stack_load_indirect {
    arg : ( int8_:o int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_load_indirect_bndl_B1 : stack_load_indirect_bndl_1 {
    rid : 817;
    isg : t;
    inp : ( __spill_DM R7 );
    out : ( DM_r );
    rsc : (1) DM_r SSP_r DM_rmw_addr ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 303;
}

stack_load_indirect_bndl_2 : func_bndl, stack_load_indirect {
    arg : ( int16_:o int16_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_load_indirect_bndl_B2 : stack_load_indirect_bndl_2 {
    rid : 818;
    isg : t;
    inp : ( __spill_DMw R7 );
    out : ( DMw_r );
    rsc : (1) DM_r DM_r1 SSP_r DM_rmw_addr ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 304;
}

stack_store_bndl_1 : func_bndl, stack_store {
    arg : ( int8_:o int8_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_store_bndl_B1 : stack_store_bndl_1 {
    rid : 819;
    isg : t;
    inp : ( DM_w R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( __spill_DM );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 305;
}
stack_store_bndl_B2 : stack_store_bndl_1 {
    rid : 820;
    isg : t;
    inp : ( DM_w R7 __CTagu_1_pint3__cstP13_2_0_E1 );
    out : ( __spill_DM );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 306;
}

stack_store_bndl_2 : func_bndl, stack_store {
    arg : ( int16_:o int16_:i int16_:i any:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
stack_store_bndl_B3 : stack_store_bndl_2 {
    rid : 821;
    isg : t;
    inp : ( DMw_w R7 __CTagu_1_uint16_0_32767__cstP16_E1 );
    out : ( __spill_DMw );
    rsc : (1) agu_1 agu_2 SSP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 307;
}
stack_store_bndl_B4 : stack_store_bndl_2 {
    rid : 822;
    isg : t;
    inp : ( DMw_w R7 __CTagu_1_pint4_step2__cstP13_3_1_E1 );
    out : ( __spill_DMw );
    rsc : (1) agu_1 agu_2 SSP_r SP_r agu_0 DM_rmw_addr ;
    opn : ( agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 308;
}

stack_store_indirect_bndl_1 : func_bndl, stack_store_indirect {
    arg : ( int8_:o int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_store_indirect_bndl_B1 : stack_store_indirect_bndl_1 {
    rid : 823;
    isg : t;
    inp : ( DM_w R7 );
    out : ( __spill_DM );
    rsc : (1) SSP_r DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 309;
}

stack_store_indirect_bndl_2 : func_bndl, stack_store_indirect {
    arg : ( int16_:o int16_:i int16_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
stack_store_indirect_bndl_B2 : stack_store_indirect_bndl_2 {
    rid : 824;
    isg : t;
    inp : ( DMw_w R7 );
    out : ( __spill_DMw );
    rsc : (1) SSP_r DM_rmw_addr ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            rd_res_reg_R7_B1
            DM_rmw_addr_conv0_SSP_r_E1 );
    ins : 310;
}

_pl_rd_res_reg_const_wr_res_reg_1 : func_bndl {
    arg : ( int16_:o int16_:i int16_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 1 );
}
_pl_rd_res_reg_const_wr_res_reg_1_B1 : _pl_rd_res_reg_const_wr_res_reg_1 {
    rid : 825;
    isg : t;
    inp : ( __CTa_w1_uint16__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16__cstP16_E1
            a_w1_conv0___CTa_w1_uint16__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 311;
}
_pl_rd_res_reg_const_wr_res_reg_1_B2 : _pl_rd_res_reg_const_wr_res_reg_1 {
    rid : 826;
    isg : t;
    inp : ( __CTa_w1_uint16_0_32767__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) mylea a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_lea_as_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_mylea_1_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16_0_32767__cstP16_E1
            a_w1_conv0___CTa_w1_uint16_0_32767__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_dummy_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 312;
}

ret_1 : func_bndl, ret {
    fst : 1;
}
ret_1_B1 : ret_1 {
    rid : 827;
    isg : t;
    opn : ( vd_ret_E1 );
    ins : 189;
}

store_const_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 );
}
store_const_1_B1 : store_const_1 {
    rid : 828;
    isg : t;
    inp : ( DM_rmw_addr DM );
    out : ( DM );
    rsc : (1) a_w0 a_w2 DM_w DM_w1 ;
    opn : ( DMw_st_DMw_w_DM_rmw_addr_E1
            a_w0_uint16__cstP16_E1
            a_w0_conv0___CTa_w0_uint16__cstP16_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 313;
}

jump_const_1 : func_bndl, jump, relative {
    arg : ( rel8_:i );
    fst : 1;
    ist : ( 1 );
}
jump_const_1_B1 : jump_const_1 {
    rid : 829;
    isg : t;
    inp : ( __CToffs_rel8__cstP8_E1 );
    rsc : (1) offs ;
    opn : ( vd_jump_offs_E1
            offs_rel8__cstP8_E1
            offs_copy0___CToffs_rel8__cstP8_E1 );
    ins : 176;
}

load__pl_rd_res_reg_const_cmp_const_1 : func_bndl {
    arg : ( uint1_:o uint2_:o int16_:i int8_:i int16_:i any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 );
}
load__pl_rd_res_reg_const_cmp_const_1_B1 : load__pl_rd_res_reg_const_cmp_const_1 {
    rid : 830;
    isg : t;
    inp : ( __CTagu_1_uint9__cstP13_8P16_7_0_E1 DM R7 );
    out : ( c_flag_w nz_flag_w o_flag_w );
    rsc : (1) DM_r DM_r1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 c_flag_w o_flag_w nz_flag_w a_w0 a_w1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint9__cstP13_8P16_7_0_E1
            agu_1_conv0___CTagu_1_uint9__cstP13_8P16_7_0_E1
            vd_cmp_a_w0_a_w1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint8__cstP24_E1
            a_w1_conv0___CTa_w1_uint8__cstP24_E1 );
    ins : 314;
}
load__pl_rd_res_reg_const_cmp_const_1_B2 : load__pl_rd_res_reg_const_cmp_const_1 {
    rid : 831;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( c_flag_w nz_flag_w o_flag_w );
    rsc : (1) DM_r DM_r1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 c_flag_w o_flag_w nz_flag_w a_w0 a_w1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            vd_cmp_a_w0_a_w1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint4_0_10__cstP9_E1
            a_w1_conv0___CTa_w1_uint4_0_10__cstP9_E1 );
    ins : 315;
}
load__pl_rd_res_reg_const_cmp_const_1_B3 : load__pl_rd_res_reg_const_cmp_const_1 {
    rid : 832;
    isg : t;
    inp : ( __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( c_flag_w nz_flag_w o_flag_w );
    rsc : (1) DM_r DM_r1 agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 c_flag_w o_flag_w nz_flag_w a_w0 a_w1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            vd_cmp_a_w0_a_w1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint3__cstP10_E1
            a_w1_conv0___CTa_w1_uint3__cstP10_E1 );
    ins : 316;
}

cc_a__jump_const_1 : func_bndl, jump, relative {
    arg : ( uint1_:i uint2_:i rel8_:i );
    fst : 1;
    ist : ( 1 1 1 );
}
cc_a__jump_const_1_B1 : cc_a__jump_const_1 {
    rid : 833;
    isg : t;
    inp : ( c_flag_r nz_flag_r __CToffs_rel8__cstP8_E1 );
    rsc : (1) tcc offs ;
    opn : ( tcc_cc_a_c_flag_r_nz_flag_r_ccu_E1
            vd_jump_tcc_offs_E1
            offs_rel8__cstP8_E1
            offs_copy0___CToffs_rel8__cstP8_E1 );
    ins : 317;
}

load_const__ad_const_cmp_const_cc_ne__jump_const_1 : func_bndl, jump, relative {
    arg : ( int8_:o uint16_:o int16_:i rel8_:i int8_:i uint16_:i any:o );
    fst : 1;
    ist : ( 1 2 1 1 );
    ost : ( 1 1 1 );
}
load_const__ad_const_cmp_const_cc_ne__jump_const_1_B1 : load_const__ad_const_cmp_const_cc_ne__jump_const_1 {
    rid : 834;
    isg : t;
    inp : ( __CTDM_rmw_addr_uint9__cstP7_8P16_7_0_E1 __CToffs_rel8__cstP8_E2 DM9 PM );
    out : ( DM9 PM nz_flag_w );
    rsc : (1) DM_r DM_rmw_addr a_b2 a_b0 a_b1 dummy_c_w dummy_o_w nz_flag_w a_b3 __nz_flag_pipe_w ,
          (2) tcc __nz_flag_pipe_r offs ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP7_8P16_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP7_8P16_7_0_E1
            a_b2_bwand_a_b0_a_b1_alu_E1
            a_b0_copy0_DM_r_E1
            a_b1_uint8__cstP24_E1
            a_b1_conv0___CTa_b1_uint8__cstP24_E1
            vd_cmp_a_b2_a_b3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1
            a_b3_int8__cstV0_E1
            a_b3_copy0___CTa_b3_int8__cstV0_E1
            tcc_cc_ne_nz_flag_pipe_ccu_E2
            nz_flag_pipe_copy0_nz_flag_w_E1
            _pipe_nz_flag_pipe_E1
            vd_jump_tcc_offs_E2
            offs_rel8__cstP8_E2
            offs_copy0___CToffs_rel8__cstP8_E2 );
    ins : 318;
}

_mi_load_const__pl_rd_res_reg_const_store_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int8_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 1 1 );
}
_mi_load_const__pl_rd_res_reg_const_store_1_B1 : _mi_load_const__pl_rd_res_reg_const_store_1 {
    rid : 835;
    isg : t;
    inp : ( __CTagu_1_uint9__cstP13_8P16_7_0_E1 DM DM R7 );
    out : ( DM c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w DM_r DM_r1 a_w0 a_w1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 DM_w DM_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint8__cstP24_E1
            a_w1_conv0___CTa_w1_uint8__cstP24_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint9__cstP13_8P16_7_0_E1
            agu_1_conv0___CTagu_1_uint9__cstP13_8P16_7_0_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 319;
}
_mi_load_const__pl_rd_res_reg_const_store_1_B2 : _mi_load_const__pl_rd_res_reg_const_store_1 {
    rid : 836;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM DM R7 );
    out : ( DM c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w DM_r DM_r1 a_w0 a_w1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 DM_w DM_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint4_0_10__cstP9_E1
            a_w1_conv0___CTa_w1_uint4_0_10__cstP9_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 320;
}
_mi_load_const__pl_rd_res_reg_const_store_1_B3 : _mi_load_const__pl_rd_res_reg_const_store_1 {
    rid : 837;
    isg : t;
    inp : ( __CTagu_1_pint4_step2__cstP13_3_1_E1 DM DM R7 );
    out : ( DM c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w DM_r DM_r1 a_w0 a_w1 agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 DM_w DM_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint3__cstP10_E1
            a_w1_conv0___CTa_w1_uint3__cstP10_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 321;
}

_pl_load__pl_rd_res_reg_const_1 : func_bndl {
    arg : ( int16_:o int16_:i int16_:i int8_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 1 1 );
}
_pl_load__pl_rd_res_reg_const_1_B1 : _pl_load__pl_rd_res_reg_const_1 {
    rid : 838;
    isg : t;
    inp : ( a_w0 __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( a_w2 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w DM_r DM_r1 a_w1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_add_a_w1_a_w0_c_flag_w_o_flag_w_alu_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w1_copy0_DMw_r_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 322;
}
_pl_load__pl_rd_res_reg_const_1_B2 : _pl_load__pl_rd_res_reg_const_1 {
    rid : 839;
    isg : t;
    inp : ( a_w0 __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( a_w2 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w DM_r DM_r1 a_w1 agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_add_a_w1_a_w0_c_flag_w_o_flag_w_alu_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w1_copy0_DMw_r_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 323;
}

load_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int8_:i int8_:i int8_:i int8_:i int8_:i int8_:i int8_:i int8_:i );
    fst : 1;
    ist : ( 1 1 1 1 1 1 1 1 1 1 );
    ost : ( 1 );
}
load_1_B1 : load_1 {
    rid : 840;
    isg : t;
    inp : ( DM_rmw_addr DM9 DM9 DM9 DM DM DM DM DM DM );
    out : ( DM_r );
    rsc : (1) DM_r ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1 );
    ins : 324;
}

store_const_2 : func_bndl {
    arg : ( int8_:o uint16_:o int8_:i int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 );
}
store_const_2_B1 : store_const_2 {
    rid : 841;
    isg : t;
    inp : ( DM_w __CTDM_rmw_addr_uint16__cstP16_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_uint16__cstP16_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint16__cstP16_E1 );
    ins : 325;
}
store_const_2_B2 : store_const_2 {
    rid : 842;
    isg : t;
    inp : ( DM_w __CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_rmw_addr ;
    opn : ( DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP2_8_6P6_5_0_E1 );
    ins : 326;
}

_pl_load_const__pl_rd_res_reg_const_store_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int8_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 1 1 );
}
_pl_load_const__pl_rd_res_reg_const_store_1_B1 : _pl_load_const__pl_rd_res_reg_const_store_1 {
    rid : 844;
    isg : t;
    inp : ( __CTagu_1_uint9__cstP13_8P16_7_0_E1 DM DM R7 );
    out : ( DM c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w DM_r DM_r1 a_w0 a_w1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 DM_w DM_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint8__cstP24_E1
            a_w1_conv0___CTa_w1_uint8__cstP24_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint9__cstP13_8P16_7_0_E1
            agu_1_conv0___CTagu_1_uint9__cstP13_8P16_7_0_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 327;
}
_pl_load_const__pl_rd_res_reg_const_store_1_B2 : _pl_load_const__pl_rd_res_reg_const_store_1 {
    rid : 845;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM DM R7 );
    out : ( DM c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w DM_r DM_r1 a_w0 a_w1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 DM_w DM_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint4_0_10__cstP9_E1
            a_w1_conv0___CTa_w1_uint4_0_10__cstP9_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 328;
}
_pl_load_const__pl_rd_res_reg_const_store_1_B3 : _pl_load_const__pl_rd_res_reg_const_store_1 {
    rid : 846;
    isg : t;
    inp : ( __CTagu_1_pint4_step2__cstP13_3_1_E1 DM DM R7 );
    out : ( DM c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w DM_r DM_r1 a_w0 a_w1 agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 DM_w DM_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint3__cstP10_E1
            a_w1_conv0___CTa_w1_uint3__cstP10_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 329;
}

load__pl_rd_res_reg_const_1 : func_bndl {
    arg : ( int16_:o int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
load__pl_rd_res_reg_const_1_B1 : load__pl_rd_res_reg_const_1 {
    rid : 847;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DMw_r );
    rsc : (1) DM_r DM_r1 agu_2 DM_rmw_addr SSP_r agu_0 agu_1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1 );
    ins : 330;
}
load__pl_rd_res_reg_const_1_B2 : load__pl_rd_res_reg_const_1 {
    rid : 848;
    isg : t;
    inp : ( __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( DMw_r );
    rsc : (1) DM_r DM_r1 agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1 );
    ins : 331;
}

load_const_bf_mov_const_const_store_1 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 );
}
load_const_bf_mov_const_const_store_1_B1 : load_const_bf_mov_const_const_store_1 {
    rid : 851;
    isg : t;
    inp : ( a_w1 __CTDM_rmw_addr_uint9__cstP12_8P24_7_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_r DM_r1 DM_rmw_addr a_w2 a_w0 t_ww t_wp DM_w DM_w1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP12_8P24_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP12_8P24_7_0_E1
            a_w2_bf_mov_a_w0_a_w1_t_ww_t_wp_alu_E1
            a_w0_copy0_DMw_r_E1
            t_ww_uint4__cstP16_E1
            t_ww_copy0___CTt_ww_uint4__cstP16_E1
            t_wp_uint4__cstP20_E1
            t_wp_copy0___CTt_wp_uint4__cstP20_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 332;
}

cc_be__jump_const_1 : func_bndl, jump, relative {
    arg : ( uint1_:i uint2_:i rel8_:i );
    fst : 1;
    ist : ( 1 1 1 );
}
cc_be__jump_const_1_B1 : cc_be__jump_const_1 {
    rid : 855;
    isg : t;
    inp : ( c_flag_r nz_flag_r __CToffs_rel8__cstP8_E1 );
    rsc : (1) tcc offs ;
    opn : ( tcc_cc_be_c_flag_r_nz_flag_r_ccu_E1
            vd_jump_tcc_offs_E1
            offs_rel8__cstP8_E1
            offs_copy0___CToffs_rel8__cstP8_E1 );
    ins : 333;
}

load_cmp_const_1 : func_bndl {
    arg : ( uint1_:o uint2_:o int16_:i int8_:i any:o );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 1 1 );
}
load_cmp_const_1_B1 : load_cmp_const_1 {
    rid : 856;
    isg : t;
    inp : ( DM_rmw_addr DM );
    out : ( c_flag_w nz_flag_w o_flag_w );
    rsc : (1) DM_r DM_r1 c_flag_w o_flag_w nz_flag_w a_w0 a_w1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            vd_cmp_a_w0_a_w1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint16__cstP16_E1
            a_w1_conv0___CTa_w1_uint16__cstP16_E1 );
    ins : 334;
}
load_cmp_const_1_B2 : load_cmp_const_1 {
    rid : 857;
    isg : t;
    inp : ( DM_rmw_addr DM );
    out : ( c_flag_w nz_flag_w o_flag_w );
    rsc : (1) DM_r DM_r1 c_flag_w o_flag_w nz_flag_w a_w0 a_w1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            vd_cmp_a_w0_a_w1_c_flag_w_o_flag_w_nz_flag_w_agu_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint4_0_10__cstP9_E1
            a_w1_conv0___CTa_w1_uint4_0_10__cstP9_E1 );
    ins : 335;
}

cc_ae__jump_const_1 : func_bndl, jump, relative {
    arg : ( uint1_:i uint2_:i rel8_:i );
    fst : 1;
    ist : ( 1 1 1 );
}
cc_ae__jump_const_1_B1 : cc_ae__jump_const_1 {
    rid : 858;
    isg : t;
    inp : ( c_flag_r nz_flag_r __CToffs_rel8__cstP8_E1 );
    rsc : (1) tcc offs ;
    opn : ( tcc_cc_ae_c_flag_r_nz_flag_r_ccu_E1
            vd_jump_tcc_offs_E1
            offs_rel8__cstP8_E1
            offs_copy0___CToffs_rel8__cstP8_E1 );
    ins : 336;
}

load__mi_const_store_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int8_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 1 );
}
load__mi_const_store_1_B1 : load__mi_const_store_1 {
    rid : 859;
    isg : t;
    inp : ( DM_rmw_addr DM DM );
    out : ( DM c_flag_w nz_flag_w o_flag_w );
    rsc : (1) DM_r DM_r1 a_w2 c_flag_w o_flag_w a_w0 a_w1 DM_w DM_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w2_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint16__cstP16_E1
            a_w1_conv0___CTa_w1_uint16__cstP16_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 337;
}
load__mi_const_store_1_B2 : load__mi_const_store_1 {
    rid : 860;
    isg : t;
    inp : ( DM_rmw_addr DM DM );
    out : ( DM c_flag_w nz_flag_w o_flag_w );
    rsc : (1) DM_r DM_r1 a_w2 c_flag_w o_flag_w a_w0 a_w1 DM_w DM_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            a_w2_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            a_w0_copy0_DMw_r_E1
            a_w1_uint4_0_10__cstP9_E1
            a_w1_conv0___CTa_w1_uint4_0_10__cstP9_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 338;
}

load_const_bf_mov_const_const_store_2 : func_bndl {
    arg : ( int8_:o uint16_:o uint16_1_32768_:i int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 );
}
load_const_bf_mov_const_const_store_2_B1 : load_const_bf_mov_const_const_store_2 {
    rid : 881;
    isg : t;
    inp : ( a_w1 __CTDM_rmw_addr_uint9__cstP12_8P24_7_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_r DM_r1 DM_rmw_addr a_w2 a_w0 t_ww t_wp DM_w DM_w1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP12_8P24_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP12_8P24_7_0_E1
            a_w2_bf_mov_a_w0_a_w1_t_ww_t_wp_alu_E1
            a_w0_copy0_DMw_r_E1
            t_ww_uint4__cstP16_E1
            t_ww_copy0___CTt_ww_uint4__cstP16_E1
            t_wp_uint4__cstP20_E1
            t_wp_copy0___CTt_wp_uint4__cstP20_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 332;
}

const_1 : func_bndl {
    arg : ( uint16_1_32768_:o );
    fst : 1;
    ost : ( 1 );
}
const_1_B1 : const_1 {
    rid : 882;
    isg : t;
    out : ( __CTa_w0_uint16__cstP16_E1 );
    opn : ( a_w0_uint16__cstP16_E1 );
    ins : 339;
}
const_1_B2 : const_1 {
    rid : 883;
    isg : t;
    out : ( a_w0 );
    rsc : (1) a_w0 ;
    opn : ( a_w0_uint4_0_10__cstP9_E1
            a_w0_conv0___CTa_w0_uint4_0_10__cstP9_E1 );
    ins : 340;
}

_pl_rd_res_reg_const_store_const_1 : func_bndl {
    arg : ( int8_:o int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
_pl_rd_res_reg_const_store_const_1_B1 : _pl_rd_res_reg_const_store_const_1 {
    rid : 885;
    isg : t;
    inp : ( __CTagu_1_uint9__cstP13_8P16_7_0_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r agu_0 agu_1 DM_rmw_addr a_w0 a_w2 DM_w DM_w1 ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint9__cstP13_8P16_7_0_E1
            agu_1_conv0___CTagu_1_uint9__cstP13_8P16_7_0_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1
            a_w0_uint8__cstP24_E1
            a_w0_conv0___CTa_w0_uint8__cstP24_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 341;
}
_pl_rd_res_reg_const_store_const_1_B2 : _pl_rd_res_reg_const_store_const_1 {
    rid : 886;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r agu_0 agu_1 DM_rmw_addr a_w0 a_w2 DM_w DM_w1 ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1
            a_w0_uint4_0_10__cstP9_E1
            a_w0_conv0___CTa_w0_uint4_0_10__cstP9_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 342;
}
_pl_rd_res_reg_const_store_const_1_B3 : _pl_rd_res_reg_const_store_const_1 {
    rid : 887;
    isg : t;
    inp : ( __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r SP_r agu_0 agu_1 DM_rmw_addr a_w0 a_w2 DM_w DM_w1 ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1
            a_w0_uint3__cstP10_E1
            a_w0_conv0___CTa_w0_uint3__cstP10_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 343;
}

_pl_rd_res_reg_const_store_1 : func_bndl {
    arg : ( int8_:o int16_:i int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 );
}
_pl_rd_res_reg_const_store_1_B1 : _pl_rd_res_reg_const_store_1 {
    rid : 890;
    isg : t;
    inp : ( DMw_w __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r agu_0 agu_1 DM_rmw_addr ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 344;
}
_pl_rd_res_reg_const_store_1_B2 : _pl_rd_res_reg_const_store_1 {
    rid : 891;
    isg : t;
    inp : ( DMw_w __CTagu_1_pint4_step2__cstP13_3_1_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r SP_r agu_0 agu_1 DM_rmw_addr ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint4_step2__cstP13_3_1_E1
            agu_1_conv0___CTagu_1_pint4_step2__cstP13_3_1_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 345;
}

_pl_rd_res_reg_const_1 : func_bndl {
    arg : ( int16_:o int16_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 );
    ost : ( 1 1 1 1 );
}
_pl_rd_res_reg_const_1_B1 : _pl_rd_res_reg_const_1 {
    rid : 892;
    isg : t;
    inp : ( __CTa_w1_uint16_0_32767__cstP16_E1 R7 );
    out : ( a_w2 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) mylea a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_lea_as_add_a_w0_a_w1_c_flag_w_o_flag_w_alu_mylea_1_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16_0_32767__cstP16_E1
            a_w1_conv0___CTa_w1_uint16_0_32767__cstP16_E1
            vd_cmp_dummy_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 346;
}

load__pl_rd_res_reg_const_update_lo_1 : func_bndl {
    arg : ( int16_:o int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 );
}
load__pl_rd_res_reg_const_update_lo_1_B1 : load__pl_rd_res_reg_const_update_lo_1 {
    rid : 893;
    isg : t;
    inp : ( __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( my_cv_int16_t1 );
    rsc : (1) DM_r agu_2 DM_rmw_addr SSP_r agu_0 agu_1 __rsrc_RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 a_b2 __RbL_a_b2_wad my_cv_int16_t1 a_b0 my_cv_int16_t0 ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            my_cv_int16_t1_update_lo_my_cv_int16_t0_a_b0cvB1
            a_b0_copy0_DM_r_E1
            my_cv_int16_t0_rd_RwL___RwL_my_cv_int16_t0_radcvB1 );
    ins : 347;
}
load__pl_rd_res_reg_const_update_lo_1_B2 : load__pl_rd_res_reg_const_update_lo_1 {
    rid : 894;
    isg : t;
    inp : ( __CTagu_1_pint3__cstP13_2_0_E1 DM R7 );
    out : ( my_cv_int16_t1 );
    rsc : (1) DM_r agu_2 DM_rmw_addr SSP_r SP_r agu_0 agu_1 __rsrc_RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 a_b2 __RbL_a_b2_wad my_cv_int16_t1 a_b0 my_cv_int16_t0 ;
    opn : ( DM_r_ld_DM_DM_rmw_addr_E1
            agu_2_add_agu_0_agu_1_agu_E1
            DM_rmw_addr_conv0_agu_2_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1
            my_cv_int16_t1_update_lo_my_cv_int16_t0_a_b0cvB1
            a_b0_copy0_DM_r_E1
            my_cv_int16_t0_rd_RwL___RwL_my_cv_int16_t0_radcvB1 );
    ins : 348;
}

update_hi_const_1 : func_bndl {
    arg : ( int16_:o int16_:i );
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
update_hi_const_1_B1 : update_hi_const_1 {
    rid : 895;
    isg : t;
    inp : ( my_cv_int16_t4 );
    out : ( my_cv_int16_t5 );
    rsc : (1) __rsrc_RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 a_b2 __RbH_a_b2_wad my_cv_int16_t5 a_b0 ;
    opn : ( my_cv_int16_t5_update_hi_my_cv_int16_t4_a_b0cvB1
            a_b0_int8__cstP24_E1
            a_b0_copy0___CTa_b0_int8__cstP24_E1 );
    ins : 349;
}
update_hi_const_1_B2 : update_hi_const_1 {
    rid : 896;
    isg : t;
    inp : ( my_cv_int16_t4 );
    out : ( my_cv_int16_t5 );
    rsc : (1) __rsrc_RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 a_b2 __RbH_a_b2_wad my_cv_int16_t5 a_b0 ;
    opn : ( my_cv_int16_t5_update_hi_my_cv_int16_t4_a_b0cvB1
            a_b0_uint4_0_10__cstP9_E1
            a_b0_conv0___CTa_b0_uint4_0_10__cstP9_E1 );
    ins : 350;
}

load_const_bf_mov_const_const_store_3 : func_bndl {
    arg : ( int8_:o uint16_:o int16_:i int16_:i int8_:i uint16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 1 );
}
load_const_bf_mov_const_const_store_3_B1 : load_const_bf_mov_const_const_store_3 {
    rid : 897;
    isg : t;
    inp : ( a_w1 __CTDM_rmw_addr_uint9__cstP12_8P24_7_0_E1 DM9 PM );
    out : ( DM9 PM );
    rsc : (1) DM_r DM_r1 DM_rmw_addr a_w2 a_w0 t_ww t_wp DM_w DM_w1 ;
    opn : ( DMw_r_ld_DMw_DM_rmw_addr_E1
            DM_rmw_addr_uint9__cstP12_8P24_7_0_E1
            DM_rmw_addr_conv0___CTDM_rmw_addr_uint9__cstP12_8P24_7_0_E1
            a_w2_bf_mov_a_w0_a_w1_t_ww_t_wp_alu_E1
            a_w0_copy0_DMw_r_E1
            t_ww_uint4__cstP16_E1
            t_ww_copy0___CTt_ww_uint4__cstP16_E1
            t_wp_uint4__cstP20_E1
            t_wp_copy0___CTt_wp_uint4__cstP20_E1
            DMw_st_DMw_w_DM_rmw_addr_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 351;
}

_pl_rd_res_reg_const_store_2 : func_bndl {
    arg : ( int8_:o int8_:i int16_:i int8_:i int16_:i );
    fst : 1;
    ist : ( 1 1 1 1 );
    ost : ( 1 );
}
_pl_rd_res_reg_const_store_2_B1 : _pl_rd_res_reg_const_store_2 {
    rid : 900;
    isg : t;
    inp : ( DM_w __CTagu_1_uint16_0_32767__cstP16_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r agu_0 agu_1 DM_rmw_addr ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            agu_0_copy0_SSP_r_E1
            agu_1_uint16_0_32767__cstP16_E1
            agu_1_conv0___CTagu_1_uint16_0_32767__cstP16_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 352;
}
_pl_rd_res_reg_const_store_2_B2 : _pl_rd_res_reg_const_store_2 {
    rid : 901;
    isg : t;
    inp : ( DM_w __CTagu_1_pint3__cstP13_2_0_E1 DM R7 );
    out : ( DM );
    rsc : (1) agu_2 SSP_r SP_r agu_0 agu_1 DM_rmw_addr ;
    opn : ( agu_2_add_agu_0_agu_1_agu_E1
            rd_res_reg_R7_B1
            SP_r_copy0_SSP_r_E1
            agu_0_copy0_SP_r_E1
            agu_1_pint3__cstP13_2_0_E1
            agu_1_conv0___CTagu_1_pint3__cstP13_2_0_E1
            DM_st_DM_w_DM_rmw_addr_E1
            DM_rmw_addr_conv0_agu_2_E1 );
    ins : 353;
}

_mi_rd_res_reg_const_wr_res_reg_1 : func_bndl {
    arg : ( int16_:o int16_:i int16_:i int16_:i any:o any:o any:o );
    fst : 1;
    ist : ( 1 1 1 );
    ost : ( 1 1 1 1 );
}
_mi_rd_res_reg_const_wr_res_reg_1_B1 : _mi_rd_res_reg_const_wr_res_reg_1 {
    rid : 904;
    isg : t;
    inp : ( __CTa_w1_uint16__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16__cstP16_E1
            a_w1_conv0___CTa_w1_uint16__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 354;
}
_mi_rd_res_reg_const_wr_res_reg_1_B2 : _mi_rd_res_reg_const_wr_res_reg_1 {
    rid : 905;
    isg : t;
    inp : ( __CTa_w1_uint16_1_32768__cstP16_E1 R7 R7 );
    out : ( R7 c_flag_w nz_flag_w o_flag_w );
    rsc : (1) mylea a_w2 c_flag_w o_flag_w SSP_r a_w0 a_w1 __rsrc_R7_wr_SSP_w_E1 SSP_w dummy_c_w dummy_o_w nz_flag_w ;
    opn : ( a_w2_lea_as_sub_a_w0_a_w1_c_flag_w_o_flag_w_alu_mylea_1_E1
            rd_res_reg_R7_B1
            a_w0_copy0_SSP_r_E1
            a_w1_uint16_1_32768__cstP16_E1
            a_w1_conv0___CTa_w1_uint16_1_32768__cstP16_E1
            wr_res_reg_R7_B1
            SSP_w_copy0_a_w2_E1
            vd_cmp_dummy_a_w2_a_w3_dummy_c_w_dummy_o_w_nz_flag_w_agu_E1 );
    ins : 355;
}

//Children of mv_bndl

nz_flag_1_dr_move_nz_flag_w_1_any : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 906;
    isg : t;
    inp : ( nz_flag_w );
    out : ( nz_flag );
    rsc : (1) __rsrc_nz_flag_wr_nz_flag_w_E1 ;
    opn : ( nz_flag_wr_nz_flag_w_E1 );
    ins : 356;
}

c_flag_1_dr_move_c_flag_w_1_any : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 907;
    isg : t;
    inp : ( c_flag_w );
    out : ( c_flag );
    rsc : (1) __rsrc_c_flag_wr_c_flag_w_E1 ;
    opn : ( c_flag_wr_c_flag_w_E1 );
    ins : 9;
}

o_flag_1_dr_move_o_flag_w_1_any : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 908;
    isg : t;
    inp : ( o_flag_w );
    out : ( o_flag );
    rsc : (1) __rsrc_o_flag_wr_o_flag_w_E1 ;
    opn : ( o_flag_wr_o_flag_w_E1 );
    ins : 10;
}

nz_flag_r_1_dr_move_nz_flag_1_uint2_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 909;
    isg : t;
    inp : ( nz_flag );
    out : ( nz_flag_r );
    rsc : (1) nz_flag_r ;
    opn : ( nz_flag_r_rd_nz_flag_E1 );
    ins : 93;
}

nz_flag_1_dr_move_nz_flag_w_1_uint2_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 910;
    isg : t;
    inp : ( nz_flag_w );
    out : ( nz_flag );
    rsc : (1) __rsrc_nz_flag_wr_nz_flag_w_E1 ;
    opn : ( nz_flag_wr_nz_flag_w_E1 );
    ins : 356;
}

c_flag_r_1_dr_move_c_flag_1_uint1_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 911;
    isg : t;
    inp : ( c_flag );
    out : ( c_flag_r );
    rsc : (1) c_flag_r ;
    opn : ( c_flag_r_rd_c_flag_E1 );
    ins : 92;
}

c_flag_1_dr_move_c_flag_w_1_uint1_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 912;
    isg : t;
    inp : ( c_flag_w );
    out : ( c_flag );
    rsc : (1) __rsrc_c_flag_wr_c_flag_w_E1 ;
    opn : ( c_flag_wr_c_flag_w_E1 );
    ins : 9;
}

a_w0_1_dr_move_Rw_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
a_w0_1_dr_move_Rw_1_int16__B0 : a_w0_1_dr_move_Rw_1_int16_ {
    rid : 913;
    isg : t;
    inp : ( RwL );
    out : ( a_w0 );
    rsc : (1) a_w0 ;
    opn : ( a_w0_rd_RwL_class_Rw___RwL_a_w0_rad_E1 );
    ins : 357;
}
a_w0_1_dr_move_Rw_1_int16__B1 : a_w0_1_dr_move_Rw_1_int16_ {
    rid : 914;
    isg : t;
    inp : ( R46 );
    out : ( a_w0 );
    rsc : (1) a_w0 ;
    opn : ( a_w0_rd_R46_class_Rw___R46_a_w0_rad_E1 );
    ins : 358;
}

Rw_1_dr_move_DMw_r_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move_DMw_r_1_int16__B0 : Rw_1_dr_move_DMw_r_1_int16_ {
    rid : 915;
    isg : t;
    inp : ( DMw_r );
    out : ( RwL );
    rsc : (1) a_w0 a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w0_copy0_DMw_r_E1
            a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 359;
}
Rw_1_dr_move_DMw_r_1_int16__B1 : Rw_1_dr_move_DMw_r_1_int16_ {
    rid : 916;
    isg : t;
    inp : ( DMw_r );
    out : ( R46 );
    rsc : (1) a_w0 a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w0_copy0_DMw_r_E1
            a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 360;
}

DM_rmw_addr_1_dr_move_R46_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 917;
    isg : t;
    inp : ( R46 );
    out : ( DM_rmw_addr );
    rsc : (1) DM_rmw_addr __DM_rmw_addr_r_int16_ ;
    opn : ( __DM_rmw_addr_r_int16__rd_R46___R46___DM_rmw_addr_r_int16__rad_E1
            DM_rmw_addr_conv0___DM_rmw_addr_r_int16__E1 );
    ins : 361;
}

Rw_1_dr_move_a_w2_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move_a_w2_1_int16__B0 : Rw_1_dr_move_a_w2_1_int16_ {
    rid : 918;
    isg : t;
    inp : ( a_w2 );
    out : ( RwL );
    rsc : (1) __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 362;
}
Rw_1_dr_move_a_w2_1_int16__B1 : Rw_1_dr_move_a_w2_1_int16_ {
    rid : 919;
    isg : t;
    inp : ( a_w2 );
    out : ( R46 );
    rsc : (1) __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 363;
}

DM_w_1_dr_move_Rb_1_int8_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
DM_w_1_dr_move_Rb_1_int8__B0 : DM_w_1_dr_move_Rb_1_int8_ {
    rid : 920;
    isg : t;
    inp : ( RbL );
    out : ( DM_w );
    rsc : (1) a_b2 a_b0 DM_w ;
    opn : ( a_b0_rd_RbL_class_Rb___RbL_a_b0_rad_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1 );
    ins : 364;
}
DM_w_1_dr_move_Rb_1_int8__B1 : DM_w_1_dr_move_Rb_1_int8_ {
    rid : 921;
    isg : t;
    inp : ( RbH );
    out : ( DM_w );
    rsc : (1) a_b2 a_b0 DM_w ;
    opn : ( a_b0_rd_RbH_class_Rb___RbH_a_b0_rad_E1
            a_b2_copy0_a_b0_E1
            DM_w_copy0_a_b2_E1 );
    ins : 365;
}

Rb_1_dr_move_DM_r_1_int8_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rb_1_dr_move_DM_r_1_int8__B0 : Rb_1_dr_move_DM_r_1_int8_ {
    rid : 922;
    isg : t;
    inp : ( DM_r );
    out : ( RbL );
    rsc : (1) __RbL_a_b2_wad a_b2 a_b0 __rsrc_RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 ;
    opn : ( a_b0_copy0_DM_r_E1
            a_b2_copy0_a_b0_E1
            RbL_class_Rb_wr_a_b2___RbL_a_b2_wad_E1 );
    ins : 366;
}
Rb_1_dr_move_DM_r_1_int8__B1 : Rb_1_dr_move_DM_r_1_int8_ {
    rid : 923;
    isg : t;
    inp : ( DM_r );
    out : ( RbH );
    rsc : (1) a_b2 __RbH_a_b2_wad a_b0 __rsrc_RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 ;
    opn : ( a_b0_copy0_DM_r_E1
            a_b2_copy0_a_b0_E1
            RbH_class_Rb_wr_a_b2___RbH_a_b2_wad_E1 );
    ins : 367;
}

a_w1_1_dr_move_Rw_1_uint16_1_32768_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
a_w1_1_dr_move_Rw_1_uint16_1_32768__B0 : a_w1_1_dr_move_Rw_1_uint16_1_32768_ {
    rid : 924;
    isg : t;
    inp : ( RwL );
    out : ( a_w1 );
    rsc : (1) a_w1 ;
    opn : ( a_w1_rd_RwL_class_Rw___RwL_a_w1_rad_E1 );
    ins : 368;
}
a_w1_1_dr_move_Rw_1_uint16_1_32768__B1 : a_w1_1_dr_move_Rw_1_uint16_1_32768_ {
    rid : 925;
    isg : t;
    inp : ( R46 );
    out : ( a_w1 );
    rsc : (1) a_w1 ;
    opn : ( a_w1_rd_R46_class_Rw___R46_a_w1_rad_E1 );
    ins : 369;
}

DMw_w_1_dr_move_Rw_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
DMw_w_1_dr_move_Rw_1_int16__B0 : DMw_w_1_dr_move_Rw_1_int16_ {
    rid : 926;
    isg : t;
    inp : ( RwL );
    out : ( DMw_w );
    rsc : (1) a_w0 DM_w DM_w1 a_w2 ;
    opn : ( a_w0_rd_RwL_class_Rw___RwL_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 370;
}
DMw_w_1_dr_move_Rw_1_int16__B1 : DMw_w_1_dr_move_Rw_1_int16_ {
    rid : 927;
    isg : t;
    inp : ( R46 );
    out : ( DMw_w );
    rsc : (1) a_w0 DM_w DM_w1 a_w2 ;
    opn : ( a_w0_rd_R46_class_Rw___R46_a_w0_rad_E1
            a_w2_copy0_a_w0_E1
            DMw_w_copy0_a_w2_E1 );
    ins : 371;
}

my_cv_int16_t4_1_dr_move_RwL_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 928;
    isg : t;
    inp : ( RwL );
    out : ( my_cv_int16_t4 );
    rsc : (1) my_cv_int16_t4 ;
    opn : ( my_cv_int16_t4_rd_RwL___RwL_my_cv_int16_t4_radcvB1 );
    ins : 286;
}

RwL_1_dr_move_my_cv_int16_t1_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 929;
    isg : t;
    inp : ( my_cv_int16_t1 );
    out : ( RwL );
    rsc : (1) __RwL_my_cv_int16_t1_wad ;
    opn : ( RwL_wr_my_cv_int16_t1___RwL_my_cv_int16_t1_wadcvB1 );
    ins : 282;
}

a_w1_1_dr_move_Rw_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
a_w1_1_dr_move_Rw_1_int16__B0 : a_w1_1_dr_move_Rw_1_int16_ {
    rid : 930;
    isg : t;
    inp : ( RwL );
    out : ( a_w1 );
    rsc : (1) a_w1 ;
    opn : ( a_w1_rd_RwL_class_Rw___RwL_a_w1_rad_E1 );
    ins : 368;
}
a_w1_1_dr_move_Rw_1_int16__B1 : a_w1_1_dr_move_Rw_1_int16_ {
    rid : 931;
    isg : t;
    inp : ( R46 );
    out : ( a_w1 );
    rsc : (1) a_w1 ;
    opn : ( a_w1_rd_R46_class_Rw___R46_a_w1_rad_E1 );
    ins : 369;
}

RwL_1_dr_move_my_cv_int16_t5_1_int16_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
    rid : 932;
    isg : t;
    inp : ( my_cv_int16_t5 );
    out : ( RwL );
    rsc : (1) __RwL_my_cv_int16_t5_wad ;
    opn : ( RwL_wr_my_cv_int16_t5___RwL_my_cv_int16_t5_wadcvB1 );
    ins : 286;
}

Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ : mv_bndl {
    fst : 1;
    ist : ( 1 );
    ost : ( 1 );
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B0 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ {
    rid : 933;
    isg : t;
    inp : ( __CTa_w0_uint16__cstP16_E1 );
    out : ( RwL );
    rsc : (1) a_w0 a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w0_conv0___CTa_w0_uint16__cstP16_E1
            a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 372;
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B1 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ {
    rid : 934;
    isg : t;
    inp : ( __CTa_w0_uint16__cstP16_E1 );
    out : ( R46 );
    rsc : (1) a_w0 a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w0_conv0___CTa_w0_uint16__cstP16_E1
            a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 373;
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ {
    rid : 935;
    isg : t;
    inp : ( a_w0 );
    out : ( RwL );
    rsc : (1) a_w2 __RwL_a_w2_wad __rsrc_RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 ;
    opn : ( a_w2_copy0_a_w0_E1
            RwL_class_Rw_wr_a_w2___RwL_a_w2_wad_E1 );
    ins : 374;
}
Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B3 : Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768_ {
    rid : 936;
    isg : t;
    inp : ( a_w0 );
    out : ( R46 );
    rsc : (1) a_w2 __R46_a_w2_wad __rsrc_R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 ;
    opn : ( a_w2_copy0_a_w0_E1
            R46_class_Rw_wr_a_w2___R46_a_w2_wad_E1 );
    ins : 375;
}

