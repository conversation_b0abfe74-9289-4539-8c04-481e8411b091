/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_ADC.h 23740 2019-12-02 10:17:10Z dep10330 $
  $Revision: 23740 $
*/

/**
 * @file
 * Declarations of the stubs to call ADC specific system functions (ROM library).
 */

#ifndef PHCAIKEYLLGENFUNC_CS_ADC_H
#define PHCAIKEYLLGENFUNC_CS_ADC_H

#include "types.h"

/**
 * @defgroup genfunclib_stubs_adc ADC
 * Caller stubs for ADC specific API functions placed
 * in ROM and executed in system mode.
 * @{
 */

/**
 * Enable the external input to the ADC (set BISTCON.DCBUS=1).
 *
 * @see phcaiKEyLLGenFunc_CS_ADC_disable_ext_input()
 */
void phcaiKEyLLGenFunc_CS_ADC_enable_ext_input(void);

/**
 * Disable the external input to the ADC (set BISTCON.DCBUS=0).
 *
 * @see phcaiKEyLLGenFunc_CS_ADC_enable_ext_input()
 */
void phcaiKEyLLGenFunc_CS_ADC_disable_ext_input(void);

#if defined(PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL) && (PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL == CONFIG_YES)
/**
 * Permanently powers on the ADC block.
 * Applies mainly to the TOKEN platform. In TOKEN-PLUS and -SRX, it is not really needed since
 * the ADC hardware supports averaging and only provided for compatibility.
 *
 * @note By default, the ADC block is powered on and off automatically. The user
 * may use this syscall to save time for a series ADC conversions,
 * as it skips the time needed by the ADC block to power on or off at each ADC conversion.
 *
 * @note The user needs to manually disable the power afterwards to gate the
 * power supplied to the ADC block. Otherwise the power is not gated.
 *
 * @see phcaiKEyLLGenFunc_CS_ADC_poweroff()
 */
void phcaiKEyLLGenFunc_CS_ADC_poweron(void);

/**
 * Powers off the ADC block.
 *
 * @note By default, the ADC block is powered on and off automatically. The user
 * may use this syscall to save time for a series ADC conversions,
 * as it skips the time needed by the ADC block to power on or off at each ADC conversion.
 *
 * @note The user needs to manually disable the power afterwards to gate the
 * power supplied to the ADC block. Otherwise the power is not gated.
 *
 * @see phcaiKEyLLGenFunc_CS_ADC_poweron()
 */
void phcaiKEyLLGenFunc_CS_ADC_poweroff(void);

#endif

/**
 * Clear the VDDABRNFLAG/VDDABRNREG trim value.
 * Intended to avoid possible VDDA brown-out when switching on loads like the RSSI gain chain.
 *
 * @see phcaiKEyLLGenFunc_CS_clear_VDDABRNOUT()
 */
void phcaiKEyLLGenFunc_CS_clear_VDDABRNOUT(void);

/**
 * Restore the VDDABRNOUT trim value.
 * Reads and restores the VDDA trim register based on the config ULPEE contents.
 *
 * @note Depending on the device mode and configuration, the device might read
 * the PTRIM0/VDDABRNOUT trim from either the auxiliary trim range or from
 * the NXP proprietary ULPEE range.
 */
void phcaiKEyLLGenFunc_CS_restore_VDDABRNOUT(void);

/*@}*/

#endif
