
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-63d4d7 mrk3


// m3;   next: m4 (next offset: 3)
000000  1 0  "0000010010110111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (6,R7,R7); 
000001  2 0  "0110111001010011"   // (DM[0]) = _pl_rd_res_reg_const_store_2_B1 (RwL[2],0,DM[0],R7); 
000002  0 0  "0000000000000000"   // /

// m4 chess_separator_scheduler;   next: m5 (next offset: 3)

// m5;   next: m6 (next offset: 4)
000003  1 0  "0110111101001000"   // (DM[2]) = _pl_rd_res_reg_const_store_2_B2 (RwL[1],2,DM[2],R7); 

// m6 chess_separator_scheduler;   next: m7 (next offset: 4)

// m7;   next: m8 (next offset: 5)
000004  1 0  "0110101101000011"   // (DM[4]) = _pl_rd_res_reg_const_store_1_B2 (RbL[0],4,DM[4],R7); 

// m8 chess_separator_scheduler;   next: m162 (next offset: 5)

// m162;   next: m235, jump target: m41 (next offset: 7)
000005  1 0  "0100011110000000"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B3 (2,DM[2],R7); 
000006  1 0  "0101011000011000"   // () = cc_be__jump_const_1_B1 (c_flag,nz_flag,24); 

// m235;   next: m16 (next offset: 11)
000007  1 0  "0110101100000011"   // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B2 (4,DM[4],R7); 
000008  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000009  2 0  "0110111111110000"   // (DM9,PM) = load_const_bf_mov_const_const_store_2_B1 (RwL[0],0,DM9,PM); 
000010  0 0  "0011110000000000"   // /

// m16 chess_separator_scheduler;   next: m176 (next offset: 11)

// m176;   next: m182, jump target: m244 (next offset: 15)
000011  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (2,R7); 
000012  0 0  "0000000000000010"   // /
000013  1 0  "0100010111100000"   // (c_flag,nz_flag,o_flag) = load_cmp_const_2_B2 (R46[0],DM[2]); 
000014  1 0  "0101100000001010"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,10); 

// m182;   next: m36, jump target: m116 (next offset: 17)
000015  1 0  "0100010111100000"   // (nz_flag,c_flag,o_flag) = load_cmp_const_1_B2 (R46[0],DM[2]); 
000016  1 0  "0101000000000011"   // () = cc_eq__jump_const_1_B1 (nz_flag,3); 

// m36 (next offset: 19)
000017  1 0  "0001010010110111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (6,R7,R7); 
000018  1 0  "0001101111000100"   // () = ret_1_B1 (); 

// m116, jump target: m39 (next offset: 24)
000019  2 0  "0110110000000100"   // (R46[0]) = const_1_B1 (0); 
000020  0 0  "0000000000000000"   // /
000021  2 0  "0010010000001000"   // (DM9,PM,nz_flag) = load__ad_const_store_1_B1 (R46[0],DM9,PM); 
000022  0 0  "1111111111110000"   // /
000023  1 0  "0101101000000100"   // () = jump_const_1_B1 (4); 

// m244;   next: m39 (next offset: 27)
000024  1 0  "0110111100100000"   // (R46[0]) = load__pl_rd_res_reg_const_1_B2 (2,DM[2],R7); 
000025  2 0  "0110111111110100"   // (DM9,PM) = load_const_bf_mov_const_const_store_1_B1 (R46[0],0,DM9,PM); 
000026  0 0  "0100000000000000"   // /

// m39;   next: m44 (next offset: 30)
000027  2 0  "0110111000100011"   // (R46[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000028  0 0  "0000000000000000"   // /
000029  1 0  "1100010000001100"   // (DM9,PM) = store_const_1_B2 (R46[0],0,DM9,PM); 

// m41;   next: m44 (next offset: 30)

// m44 (next offset: /)
000030  1 0  "0001010010110111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (6,R7,R7); 
000031  1 0  "0001101111000100"   // () = ret_1_B1 (); 

