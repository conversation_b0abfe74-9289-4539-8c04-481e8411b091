/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_UHF.h 12264 2018-02-15 13:30:43Z dep10330 $
  $Revision: 12264 $
*/

/**
 * @file
 * Declarations of the stubs to call UHF specific system functions (ROM library).
 */

#ifndef PHCAIKEYLLGENFUNC_CS_UHF_H
#define PHCAIKEYLLGENFUNC_CS_UHF_H

#include "types.h"
#include "phcaiKEyLLGenFunc_SCI.h"

/**
 * @defgroup genfunclib_stubs_uhf UHF
 * Caller stubs for UHF specific API functions placed
 * in ROM and executed in system mode.
 * @{
 */

#if defined(PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY) && (PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY == CONFIG_YES)
/**
 * Sets the status of FORCE_XO_READY.
 *
 * @param[in] value Defines whether the value of XO_READY shall be forced to 1
 */
void phcaiKEyLLGenFunc_CS_UHF_ForceXoReady(const bool_t value);
#endif

#if defined(PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED) && (PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED == CONFIG_YES)
/**
 * Set the status of FORCE_PLL_LOCK_DETECTED.
 *
 * @param[in] value Defines whether the value of PLL_LOCK_DETECTED shall be forced to 1
 */
void phcaiKEyLLGenFunc_CS_UHF_ForcePllLockDetected(const bool_t value);
#endif

#if defined(PHFL_CONFIG_HAVE_XO_START_UP) && (PHFL_CONFIG_HAVE_XO_START_UP == CONFIG_YES)
/**
 * Sets up and starts the crystal oscillator.
 * This syscall is obsolete and not recommended for new designs.
 * TXPCON register bits VDDXOEN and XOEN are set to 1.
 * If parameter @p validate is ::TRUE, also bit XO_READY_EN is set to 1.
 * CPU clock setting (CLKCON0) is temporarily changed to AUXCLK (1 MHz)
 * and restored at exit. Sets MRCOSC_EN=1 to avoid that MainRC clock is
 * disabled automatically.
 * Clears all UHF interrupts (INTFLAG3 register).
 *
 * @param[in] validate Defines whether the function shall perform a XO
 *                     ready validation (::TRUE) or not (::FALSE).
 * @param[in] force_xo_ready Defines whether XO_READY shall be forced to 1 or not.
 * @return Error indicator (::SUCCESS in case of success, ::ERROR in case the
 * "XO ready" validation timed out).
 *
 * @note Available in NCF29Ax types. See application notes for details.
 */
error_t phcaiKEyLLGenFunc_CS_UHF_XoStartUp(bool_t validate, bool_t force_xo_ready);
#endif

#if defined(PHFL_CONFIG_HAVE_PLL_START_UP) && (PHFL_CONFIG_HAVE_PLL_START_UP == CONFIG_YES)
/**
 * Sets up and starts the PLL.
 * This syscall is obsolete and not recommended for new designs.
 * PLLCON bit 0 (FIXDIV_DIV2_EN) must be set beforehand.
 * If LOCK_FIXDIV is set to 1 in the configuration ULPEE, FIXDIV_DIV2_EN
 * is being forced to 1 internally.
 * Changes PLLCON bits as follows.
 * - PLL_LOCK_DETECT_EN is set to 1.
 * - PLL_LOCK_DETECT_BLOCK is set to 1.
 * - PLL_LOCK_DETECT_MODE is set to 1.
 * - FIXDIV_DIV2_EN and LOCK_DET_T are kept unchanged.
 * Clears IF_PLLLOCK before exit. Clears all UHF interrupts (INTFLAG3 register).
 *
 * TXPCON register bits VDDPLLEN, VDDHSEN and PLLEN are set to 1.
 * CPU clock setting (CLKCON0) is temporarily changed to AUXCLK (1 MHz)
 * and restored at exit. Set MRCOSC_EN=1 to avoid that MainRC clock is
 * disabled automatically.
 *
 * @param[in] calibrate Defines whether the function shall perform
 *                      a VCO calibration (::TRUE) or not (::FALSE).
 * @param[in] force_pll_lock_ready Defines whether the value of PLL_LOCK_READY
 *                                 shall be forced to 1 or not.
 * @param[in] cal_idac_ctrl The value to be written to CAL_IDAC_CTRL[4:0]
 * in the VCO calibration control register (VCOCALCON), if parameter calibrate
 * is set to ::FALSE.
 * @return Error indicator (::SUCCESS on success, ::ERROR in case the
 * VCO calibration timed out)
 *
 * @note Available in NCF29Ax. See Application Note CAI1401 for details.
 */
error_t phcaiKEyLLGenFunc_CS_UHF_PllStartUp(bool_t calibrate,
  bool_t force_pll_lock_ready, uint8_t cal_idac_ctrl);
#endif

/**
 * Sets or clears the RF_GATE.
 *
 * @param[in] rfgate_off Defines whether FIXDIV_PA_EN shall be set or cleared.
 */
void phcaiKEyLLGenFunc_CS_UHF_SetRfGate(bool_t rfgate_off);

/**
 * Sets or clears the PA_IN_GATE (PA_PLL_IN_EN bit in the TXPATEST SFR).
 *
 * @param[in] paingate_off Defines whether PA_PLL_IN_EN shall be set or cleared.
 */
void phcaiKEyLLGenFunc_CS_UHF_SetPAInGate(bool_t paingate_off);

/**
 * Sets the value of the CP_ICP bits in the PLLTRIM SFR.
 *
 * @param[in] value The value to be written to CP_ICP (lower 4 bits).
 */
void phcaiKEyLLGenFunc_CS_UHF_SetCP_ICP(uint8_t value);

/**
 * Sets the value of the lower 5 bits (VDDXOFTRIM, VDDXOCTRIM) in the PTRIMXO SFR.
 *
 * @param[in] value The value to be written to PTRIMXO (lower 5 bits).
 */
void phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_XO(uint8_t value);

/**
 * Sets the value of the lower 5 bits (VDDPLLFTRIM, VDDPLLCTRIM) in the PTRIMPLL SFR.
 *
 * @param[in] value The value to be written to PTRIMPLL (lower 5 bits).
 */
void phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_PLL(uint8_t value);

/**
 * Sets the value of the lower 5 bits (VDDHSFTRIM, VDDHSCTRIM) in the PTRIMHS SFR.
 *
 * @param[in] value The value to be written to PTRIMHS (lower 5 bits).
 */
void phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_HS(uint8_t value);

#if defined(PHFL_CONFIG_HAVE_PA_BITFIELDS) && (PHFL_CONFIG_HAVE_PA_BITFIELDS == CONFIG_YES)
/**
 * Sets one of the UHF bitfields to the desired value.
 *
 * \param[in] bitfield A UHF bitfield.
 * \param[in] value Selected value.
 */
void phcaiKEyLLGenFunc_CS_sm2_pa_set(PA_bitfield_selector_t bitfield, uint8_t value);

/**
 * Returns the value of a UHF bitfield.
 *
 * \param[in] bitfield A UHF bitfield.
 * @return Value in the bitfield.
 */
uint8_t phcaiKEyLLGenFunc_CS_sm2_pa_get(PA_bitfield_selector_t bitfield);
#endif

/**
 * Reloads the CP_ICP trim value from CFEE trim data.
 */
void phcaiKEyLLGenFunc_CS_CP_ICP_TRIM(void);

/*@}*/

#endif
