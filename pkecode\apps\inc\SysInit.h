
#ifndef _SysInit_H_ 
#define _SysInit_H_


/**
 * Hardware initialization: watchdog and CPU clock.
 */
extern void CPUClock_Init ( void );


/**
 *  Hardware initialization: port configuration and port functions
 */
extern void GPIO_Init( void );

extern int8_t Vbat_Check( uint16_t bat_threshold );
 

/**
 * Initialization of registers in VBAT and VBATREG domains with default values (see defs.h). 
 * Note: only call for device initialization after VBAT POR, 
 * otherwise RTC function will be switched off.
 */
extern void WUP_Init( void );


/**
 * Refresh of registers in VBAT and VBATREG domains, 
 * registers used for RTC and ITC function are maintained.
 * Called after RKE operation and every PKE command for safety.
 */
extern void hw_refresh_VBAT_VBATREG_registers();


/*Performs a reset of the VBAT domain AND re-reads the trim data for the VBAT domain.*/
extern void BatReset(void);


/**
 * Power-off function. 
 * Switches all ports to plain input mode, 
 * switches CPU clock to AUXCLK 250 kHz, 
 * waits for port states to settle,
 * performs VDD reset (power management gets reset, controller re-starts
 * in case of wake-up event).
 */
extern void Power_Off( void ) property(never_returns);


/**
 * Read IDE (32 bit) from ULPEE to four battery-powered registers (IDEn_STORAGE_VBATREG[4]).
 * ULPEE module gets powered up if not yet activated. 
 * Also copies to RAM variable g_u8arr_IDE[4].
 */
extern void read_IDE_from_ULPEE( void );


/**
 * Copy IDE from battery-powered registers (IDEn_STORAGE_VBATREG) 
 * to RAM variable g_u8arr_IDE[4].
 */
extern void restore_IDE_in_RAM  ( void );


/**
 * LF NMI (Non Maskable Interrupt) handler
 */
extern void LF_NMI( void ) property(isr);


/**
 * 
 */
extern uint8_t hw_get_button_code( void );



#endif



/* eof */
