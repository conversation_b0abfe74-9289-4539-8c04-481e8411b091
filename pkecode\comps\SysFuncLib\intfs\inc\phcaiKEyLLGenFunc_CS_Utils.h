/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_Utils.h 12264 2018-02-15 13:30:43Z dep10330 $
  $Revision: 12264 $
*/

/**
 * @file
 * Declarations of functions used by the system function caller stubs.
 */

/**
 * @defgroup genfunclib_stubs_utils Utils
 * Global variables and utility functions.
 * @{
 */

#ifndef PHCAIKEYLLGENFUNC_CS_UTILS_H
#define PHCAIKEYLLGENFUNC_CS_UTILS_H

#include "types.h"
#include "phcaiKEyLLGenFunc_SCI.h"

/**
 * Parameter passed from the transponder emulation to the user software when
 * entering the USER state.
 */
extern uint8_t chess_storage(DM9:HITAG_USER_PARAM_ADDR) hitagpro_user_param;

/** Holds the function parameters of the respective syscall function. */
extern phcaiKEyLLGenFunc_SCI_Func_Params_t chess_storage(DM9:GENFUNC_PARAMS_ADDR)
  phcaiKEyLLGenFunc_Func_Params;


#ifndef _lint

/**
 * @brief Wraps assembler code to invoke syscall 8.
 *
 * @param[in] r0l Lower byte of the word passed to the system call dispatcher.
 * @param[in] r0h Higher byte of the word passed to the system call dispatcher.
 */
inline assembly void call_syscall_8(uint8_t chess_storage(R0L) r0l,
  uint8_t chess_storage(R0H) r0h)
  property(volatile)
{
  asm_begin
  SYS #8
  asm_end
}
/**
 * @brief Wraps assembler code to invoke syscall @p i.
 *
 * The functions ensures that registers read by the user application
 * after the syscall's termination are restored.
 *
 * @param[in] i System call to be invoked
 */
inline assembly
void call_syscall(int chess_storage() i)
  property(volatile)
{
  asm_begin
  SYS #i
  asm_end
}

#else

/* dummy functions for PC-Lint */

void call_syscall_8( uint8_t r0l, uint8_t r0h );
void call_syscall( int i );

#endif


#endif

/*@}*/
/*@}*/
