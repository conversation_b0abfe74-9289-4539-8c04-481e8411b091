
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:24 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i timer-258389 mrk3

[
    0 : void_timer_delay_ms___ushort typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : u16_wait typ=int8_ val=0t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   21 : __extPM_void typ=uint16_ bnd=b stl=PM
   22 : __extDM_void typ=int8_ bnd=b stl=DM
   23 : __extULP_void typ=uint32_ bnd=b stl=ULP
   25 : __arg_u16_wait typ=int16_ bnd=p tref=uint16_t__
   30 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   33 : __fch_u16_wait typ=int16_ bnd=m
   34 : void_phcaiKEyLLGenFunc_timer0_delay_ms___ushort typ=int16_ val=0r bnd=m
   36 : __ct_2s0 typ=int16_ val=2s0 bnd=m
   43 : __ct_2s0 typ=int16_ val=2s0 bnd=m
   44 : __seff typ=any bnd=m
   45 : __seff typ=any bnd=m
   46 : __seff typ=any bnd=m
   47 : __seff typ=any bnd=m
   48 : __seff typ=any bnd=m
   49 : __seff typ=any bnd=m
]
Fvoid_timer_delay_ms___ushort {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (u16_wait.19 var=20) source ()  <30>;
    (__extPM_void.20 var=21) source ()  <31>;
    (__extDM_void.21 var=22) source ()  <32>;
    (__extULP_void.22 var=23) source ()  <33>;
    (__arg_u16_wait.24 var=25 stl=RwL off=0) inp ()  <35>;
    (__ct_0t0.75 var=30) const_inp ()  <95>;
    (__ct_2s0.78 var=43) const_inp ()  <98>;
    <30> {
      (__sp.32 var=19 __seff.85 var=47 stl=c_flag_w __seff.86 var=48 stl=nz_flag_w __seff.87 var=49 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.78 __sp.18 __sp.18)  <103>;
      (__seff.92 var=47 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.85)  <125>;
      (__seff.93 var=48 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.86)  <126>;
      (__seff.94 var=49 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.87)  <127>;
    } stp=0;
    <31> {
      (u16_wait.39 var=20) _pl_rd_res_reg_const_store_1_B1 (__arg_u16_wait.90 __ct_0t0.75 u16_wait.19 __sp.32)  <104>;
      (__arg_u16_wait.90 var=25 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__arg_u16_wait.24)  <124>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <50>;
    } #4 off=3 nxt=5
    #5 off=3 nxt=6
    (void_phcaiKEyLLGenFunc_timer0_delay_ms___ushort.76 var=34) const_inp ()  <96>;
    <28> {
      (__fch_u16_wait.40 var=33 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.75 u16_wait.39 __sp.32)  <101>;
      (__fch_u16_wait.98 var=33 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_u16_wait.40)  <131>;
    } stp=0;
    <29> {
      () call_const_1_B1 (void_phcaiKEyLLGenFunc_timer0_delay_ms___ushort.76)  <102>;
    } stp=2;
    call {
        (__extDM.44 var=17 __extDM_void.45 var=22 __extPM.46 var=16 __extPM_void.47 var=21 __extULP.48 var=18 __extULP_void.49 var=23 __vola.50 var=13) Fvoid_phcaiKEyLLGenFunc_timer0_delay_ms___ushort (__fch_u16_wait.98 __extDM.16 __extDM_void.21 __extPM.15 __extPM_void.20 __extULP.17 __extULP_void.22 __vola.12)  <56>;
    } #6 off=7 nxt=9
    #9 off=7 nxt=-2
    () sink (__vola.50)  <64>;
    () sink (__extPM.46)  <67>;
    () sink (__extDM.44)  <68>;
    () sink (__extULP.48)  <69>;
    () sink (__sp.56)  <70>;
    () sink (u16_wait.39)  <71>;
    () sink (__extPM_void.47)  <72>;
    () sink (__extDM_void.45)  <73>;
    () sink (__extULP_void.49)  <74>;
    (__ct_2s0.77 var=36) const_inp ()  <97>;
    <26> {
      (__sp.56 var=19 __seff.80 var=44 stl=c_flag_w __seff.81 var=45 stl=nz_flag_w __seff.82 var=46 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.77 __sp.32 __sp.32)  <99>;
      (__seff.95 var=44 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.80)  <128>;
      (__seff.96 var=45 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.81)  <129>;
      (__seff.97 var=46 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.82)  <130>;
    } stp=0;
    <27> {
      () ret_1_B1 ()  <100>;
    } stp=1;
} #0
0 : 'apps/src/timer.c';
----------
0 : (0,157:0,0);
3 : (0,157:21,0);
4 : (0,157:21,0);
5 : (0,159:37,2);
6 : (0,159:2,2);
9 : (0,160:0,3);
----------
50 : (0,157:21,0);
56 : (0,159:2,2);
99 : (0,160:0,0) (0,160:0,3);
100 : (0,160:0,3);
101 : (0,159:37,2) (0,157:30,0);
102 : (0,159:2,2);
103 : (0,157:5,0);
104 : (0,157:30,0) (0,157:21,0);

