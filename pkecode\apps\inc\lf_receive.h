/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: lf_receive.h 31225 2021-03-10 14:44:11Z dep10330 $
  $Revision: 31225 $
*/

//+-----------------------------------------------------------------------------
//| NOTE: The code provided herein is still under development and hence
//|       subject to change.
//+-----------------------------------------------------------------------------

#ifndef _LF_RECEIVE_H_
#define _LF_RECEIVE_H_

#include "types.h"


/*-----------------------------------------------------------------------------*/
/* Definitions                                                                 */
/*-----------------------------------------------------------------------------*/



/*----------------------------------------------------------------------------*/
/* Function Declarations                                                      */
/*----------------------------------------------------------------------------*/


/**
 * Receives a number of bytes from the LF receiver, and calculates a CRC-8 checksum.
 *
 * @param[out]  pu8_Buffer       Pointer to RAM-address where the received data will be stored.
 * @param[in]   u8_NumOfBytes    Number of data bytes to be read from the LF interface.
 *
 * @return      Result code, also available by lf_error_code().
 * @see LFRCVERROR_t
 */
LFRCVERROR_t lf_read_block( uint8_t * pu8_rx_buffer, uint8_t u8_numbytes );


/**
 * Receives a single data byte from the LF receiver, and updates the CRC-8 checksum.
 *
 * @param[out]  pu8_Buffer       Pointer to RAM-address where the received data will be stored.
 *
 * @return      Result code, also available by lf_error_code().
 * @see LFRCVERROR_t
 */
LFRCVERROR_t lf_read_byte( uint8_t * pu8_rx_buffer );


/**
 * Reads final CRC byte from the LF receiver, and compares with the internally
 * calculated CRC.
 *
 * @param[out]  none
 * @param[in]   none
 *
 * @return      Result code, either LF_OK or LF_INVCRC if not matching.
 *              Also available by lf_error_code().
 * @see LFRCVERROR_t
 */
LFRCVERROR_t lf_read_crc( void );


/**
 * Receives a number of bytes (can be 0) from the LF receiver, calculates a
 * CRC-8 checksum, and receives and checks the CRC byte.
 * Receive buffer must provide space for data (not for the CRC byte).
 *
 * @param[out]  none
 * @param[in]   pu8_Buffer       Pointer to RAM-address where the received data will be stored.
 * @param[in]   u8_NumOfBytes    Number of data bytes (>= 0) to be read from the LF interface,
 *                               excluding the CRC byte.
 *
 * @return      Result code, also available by lf_error_code().
 * @see LFRCVERROR_t
 */
LFRCVERROR_t lf_read_block_crc( uint8_t * pu8_rx_buffer, uint8_t u8_numbytes );


/**
 * Initializes the LF data reception.
 * Clears CRC, error code and (on TOKEN / T.-PLUS) stored "FIFO data ready" flag
 * (m_u8_NextFifoDataReady), sets default value of NEWBYTEOVFHOLD.
 */
void lf_read_init( void );


/**
 * Returns the last error code.
 *
 * @return last error code, 00h: success, other: error during data reception
 * @see LFRCVERROR_t
 */
LFRCVERROR_t lf_error_code( void );


/**
 * Return current CRC-8 value.
 *
 * @return 8-bit CRC value.
 */
uint8_t lf_get_crc( void );


#endif

/* eof */
