
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-196dc6 mrk3

[
 -179 : __adr_FreqCfg typ=int16_ bnd=m adro=23
  -55 : __adr_FreqCfg typ=int16_ bnd=m adro=23
    0 : void_FHSS_FreqCfg___uchar___uchar typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : pag typ=int8_ val=0t0 bnd=a sz=1 algn=1 stl=DM tref=uint8_t_DM
   21 : FrqMode typ=int8_ val=1t0 bnd=a sz=1 algn=1 stl=DM tref=uint8_t_DM
   22 : i typ=int8_ val=2t0 bnd=a sz=1 algn=1 stl=DM tref=uint8_t_DM
   23 : FreqCfg typ=int8_ val=3t0 bnd=a sz=4 algn=1 stl=DM tref=__A4__uchar_DM
   24 : RF_FreqLCfg typ=int8_ bnd=i sz=4 algn=1 stl=DM tref=__A4__uchar_DM
   25 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   26 : RF_FreqHCfg typ=int8_ bnd=i sz=4 algn=1 stl=DM tref=__A4__uchar_DM
   27 : __extPM_void typ=uint16_ bnd=b stl=PM
   28 : __extDM_void typ=int8_ bnd=b stl=DM
   29 : __extULP_void typ=uint32_ bnd=b stl=ULP
   32 : __ptr_RF_FreqLCfg typ=int16_ val=0a bnd=m adro=24
   34 : __ptr_RF_FreqHCfg typ=int16_ val=0a bnd=m adro=26
   35 : __arg_FrqMode typ=int8_ bnd=p tref=uint8_t__
   36 : __arg_pag typ=int8_ bnd=p tref=uint8_t__
   41 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   45 : __ct_1t0 typ=int16_ val=1t0 bnd=m
   49 : __ct_2t0 typ=int16_ val=2t0 bnd=m
   53 : __ct_3t0 typ=int16_ val=3t0 bnd=m
   55 : __adr_FreqCfg typ=int16_ bnd=m adro=23
   93 : __tmp typ=int16_ bnd=m
   95 : __fch_RF_FreqLCfg typ=int8_ bnd=m
   97 : __tmp typ=int16_ bnd=m
   98 : __tmp typ=int16_ bnd=m
  123 : __tmp typ=int16_ bnd=m
  125 : __fch_RF_FreqHCfg typ=int8_ bnd=m
  127 : __tmp typ=int16_ bnd=m
  128 : __tmp typ=int16_ bnd=m
  141 : __tmp typ=int16_ bnd=m
  142 : error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage___P__uchar___ushort typ=int16_ val=0r bnd=m
  144 : __tmp typ=int8_ bnd=m
  145 : __ct_8s0 typ=int16_ val=8s0 bnd=m
  165 : __ct_8s0 typ=int16_ val=8s0 bnd=m
  172 : __ct_4t0 typ=int16_ val=4t0 bnd=m
  174 : __ct_5t0 typ=int16_ val=5t0 bnd=m
  176 : __ct_6t0 typ=int16_ val=6t0 bnd=m
  178 : __ct_3t0 typ=int16_ val=3t0 bnd=m
  187 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  190 : __apl_r typ=int16_ bnd=m tref=__sint__
  195 : __apl_c typ=uint1_ bnd=m tref=uint1___
  197 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  200 : __apl_r typ=int16_ bnd=m tref=__uint__
  210 : __either typ=bool bnd=m
  211 : __trgt typ=rel8_ val=-12j bnd=m
  212 : __trgt typ=rel8_ val=-12j bnd=m
  213 : __trgt typ=rel8_ val=34j bnd=m
  215 : __trgt typ=rel8_ val=20j bnd=m
  216 : __trgt typ=rel8_ val=17j bnd=m
  217 : __seff typ=any bnd=m
  218 : __seff typ=any bnd=m
  219 : __seff typ=any bnd=m
  223 : __seff typ=any bnd=m
  224 : __seff typ=any bnd=m
  225 : __seff typ=any bnd=m
  226 : __seff typ=any bnd=m
  227 : __seff typ=any bnd=m
  228 : __seff typ=any bnd=m
  229 : __seff typ=any bnd=m
  230 : __seff typ=any bnd=m
  231 : __seff typ=any bnd=m
  232 : __seff typ=any bnd=m
  233 : __seff typ=any bnd=m
  234 : __seff typ=any bnd=m
  235 : __seff typ=any bnd=m
  236 : __seff typ=any bnd=m
  237 : __seff typ=any bnd=m
  238 : __seff typ=any bnd=m
  239 : __seff typ=any bnd=m
  240 : __seff typ=any bnd=m
  244 : __seff typ=any bnd=m
  245 : __seff typ=any bnd=m
  246 : __seff typ=any bnd=m
  248 : __side_effect typ=any bnd=m
]
Fvoid_FHSS_FreqCfg___uchar___uchar {
    #5 off=0 nxt=6
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (pag.19 var=20) source ()  <30>;
    (FrqMode.20 var=21) source ()  <31>;
    (i.21 var=22) source ()  <32>;
    (FreqCfg.22 var=23) source ()  <33>;
    (RF_FreqLCfg.23 var=24) source ()  <34>;
    (__extDM_int8_.24 var=25) source ()  <35>;
    (RF_FreqHCfg.25 var=26) source ()  <36>;
    (__extPM_void.26 var=27) source ()  <37>;
    (__extDM_void.27 var=28) source ()  <38>;
    (__extULP_void.28 var=29) source ()  <39>;
    (__arg_FrqMode.34 var=35 stl=RbL off=0) inp ()  <45>;
    (__arg_pag.37 var=36 stl=RbH off=0) inp ()  <48>;
    (__ct_0t0.817 var=41) const_inp ()  <946>;
    (__ct_1t0.818 var=45) const_inp ()  <947>;
    (__ct_2t0.819 var=49) const_inp ()  <948>;
    (__ct_3t0.820 var=53) const_inp ()  <949>;
    (__ct_8s0.823 var=165) const_inp ()  <952>;
    (__ct_3t0.827 var=178) const_inp ()  <956>;
    <126> {
      (__sp.45 var=19 __seff.915 var=244 stl=c_flag_w __seff.916 var=245 stl=nz_flag_w __seff.917 var=246 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_8s0.823 __sp.18 __sp.18)  <1040>;
      (__seff.972 var=245 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.916)  <1219>;
      (__seff.973 var=244 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.915)  <1220>;
      (__seff.978 var=246 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.917)  <1225>;
    } stp=0;
    <127> {
      (pag.69 var=20) _pl_rd_res_reg_const_store_1_B1 (__arg_pag.977 __ct_0t0.817 pag.19 __sp.45)  <1041>;
      (__arg_pag.977 var=36 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B1 (__arg_pag.37)  <1224>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <80>;
    } #6 off=3 nxt=7
    #7 off=3 nxt=8
    <125> {
      (FrqMode.71 var=21) _pl_rd_res_reg_const_store_1_B2 (__arg_FrqMode.976 __ct_1t0.818 FrqMode.20 __sp.45)  <1039>;
      (__arg_FrqMode.976 var=35 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__arg_FrqMode.34)  <1223>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <82>;
    } #8 off=4 nxt=9
    #9 off=4 nxt=10
    <124> {
      (i.75 var=22) store_const__pl_rd_res_reg_const_1_B3 (__ct_2t0.819 i.21 __sp.45)  <1038>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <86>;
    } #10 off=5 nxt=11
    #11 off=5 nxt=12
    <122> {
      (FreqCfg.82 var=23) store_const__pl_rd_res_reg_const_1_B3 (__ct_3t0.820 FreqCfg.22 __sp.45)  <1036>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <93>;
    } #12 off=6 nxt=13
    #13 off=6 nxt=14
    (__ct_4t0.824 var=172) const_inp ()  <953>;
    <121> {
      (FreqCfg.89 var=23) store_const__pl_rd_res_reg_const_1_B3 (__ct_4t0.824 FreqCfg.82 __sp.45)  <1035>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <100>;
    } #14 off=7 nxt=15
    #15 off=7 nxt=16
    (__ct_5t0.825 var=174) const_inp ()  <954>;
    <120> {
      (FreqCfg.96 var=23) store_const__pl_rd_res_reg_const_1_B3 (__ct_5t0.825 FreqCfg.89 __sp.45)  <1034>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <107>;
    } #16 off=8 nxt=17
    #17 off=8 nxt=19
    (__ct_6t0.826 var=176) const_inp ()  <955>;
    <119> {
      (FreqCfg.103 var=23) store_const__pl_rd_res_reg_const_1_B3 (__ct_6t0.826 FreqCfg.96 __sp.45)  <1033>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <115>;
    } #19 off=9 nxt=192
    #192 off=9 nxt=216 tgt=24
    (__trgt.832 var=215) const_inp ()  <961>;
    <117> {
      (__apl_nz.753 var=187 stl=nz_flag_w __seff.902 var=239 stl=c_flag_w __seff.903 var=240 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_3_B3 (__ct_1t0.818 FrqMode.71 __sp.45)  <1031>;
      (__seff.969 var=239 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.902)  <1216>;
      (__apl_nz.971 var=187 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.753)  <1218>;
      (__seff.979 var=240 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.903)  <1226>;
    } stp=0;
    <118> {
      () cc_eq__jump_const_1_B1 (__apl_nz.970 __trgt.832)  <1032>;
      (__apl_nz.970 var=187 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.971)  <1217>;
    } stp=1;
    if {
        {
            () if_expr (__either.813)  <148>;
            (__either.813 var=210) undefined ()  <941>;
        } #22
        {
            #24 off=30 nxt=25
            <116> {
              (i.139 var=22) store_const__pl_rd_res_reg_const_1_B3 (__ct_2t0.819 i.75 __sp.45)  <1030>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <152>;
            } #25 off=31 nxt=248
            #248 off=31 nxt=211
            (__ptr_RF_FreqLCfg.815 var=32) const_inp ()  <944>;
            (__trgt.828 var=211) const_inp ()  <957>;
            <166> {
              (__adr_FreqCfg.995 var=-55 stl=a_w2 __side_effect.996 var=248 stl=c_flag_w __side_effect.998 var=248 stl=nz_flag_w __side_effect.1000 var=248 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_3t0.820 __sp.45)  <1129>;
              (__adr_FreqCfg.994 var=-55 stl=R46 off=1) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_FreqCfg.995)  <1235>;
              (__side_effect.997 var=248 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.996)  <1236>;
              (__side_effect.999 var=248 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.998)  <1237>;
              (__side_effect.1001 var=248 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.1000)  <1238>;
            } stp=0;
            do {
                {
                    (i.182 var=22) entry (i.267 i.139)  <196>;
                    (FreqCfg.183 var=23) entry (FreqCfg.269 FreqCfg.103)  <197>;
                } #31
                {
                    <106> {
                      (__apl_c.767 var=195 stl=c_flag_w __apl_nz.769 var=197 stl=nz_flag_w __seff.885 var=232 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B3 (__ct_2t0.819 i.216 __sp.45)  <1020>;
                      (__apl_nz.941 var=197 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.769)  <1191>;
                      (__apl_c.943 var=195 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.767)  <1193>;
                      (__seff.964 var=232 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.885)  <1214>;
                    } stp=11;
                    <107> {
                      () cc_b__jump_const_1_B1 (__apl_c.942 __apl_nz.940 __trgt.828)  <1021>;
                      (__apl_nz.940 var=197 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.941)  <1190>;
                      (__apl_c.942 var=195 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.943)  <1192>;
                    } stp=12;
                    <108> {
                      (i.216 var=22 __seff.887 var=233 stl=c_flag_w __seff.888 var=234 stl=nz_flag_w __seff.889 var=235 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_2t0.819 i.182 i.182 __sp.45)  <1022>;
                      (__seff.944 var=234 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.888)  <1194>;
                      (__seff.945 var=233 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.887)  <1195>;
                      (__seff.963 var=235 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.889)  <1213>;
                    } stp=10;
                    <109> {
                      (__apl_r.757 var=190 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B2 (__ct_2t0.819 i.182 __sp.45)  <1023>;
                      (__apl_r.954 var=190 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.757)  <1204>;
                    } stp=0;
                    <110> {
                      (__apl_r.762 var=190 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B2 (__ct_2t0.819 i.182 __sp.45)  <1024>;
                      (__apl_r.949 var=190 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.762)  <1199>;
                    } stp=3;
                    <111> {
                      (__tmp.759 var=93 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.953)  <1025>;
                      (__apl_r.953 var=190 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.954)  <1203>;
                      (__tmp.957 var=93 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.759)  <1207>;
                    } stp=1;
                    <112> {
                      (__tmp.764 var=97 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.948)  <1026>;
                      (__apl_r.948 var=190 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.949)  <1198>;
                      (__tmp.951 var=97 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.764)  <1201>;
                    } stp=4;
                    <113> {
                      (__tmp.206 var=98 stl=a_w2 __seff.895 var=236 stl=c_flag_w __seff.896 var=237 stl=nz_flag_w __seff.897 var=238 stl=o_flag_w) _pl_1_B2 (__adr_FreqCfg.965 __tmp.950)  <1027>;
                      (__seff.946 var=237 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.896)  <1196>;
                      (__seff.947 var=236 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.895)  <1197>;
                      (__tmp.950 var=97 stl=a_w0) a_w0_a_w1_1_dr_move_Rw_1_int16__B0 (__tmp.951)  <1200>;
                      (__seff.952 var=238 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.897)  <1202>;
                      (__tmp.960 var=98 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.206)  <1210>;
                      (__adr_FreqCfg.965 var=55 stl=a_w1) a_w0_a_w1_1_dr_move_Rw_1_int16__B3 (__adr_FreqCfg.994)  <1215>;
                    } stp=7;
                    <114> {
                      (__fch_RF_FreqLCfg.203 var=95 stl=DM_r) _pl_const_load_1_B1 (__tmp.955 __ptr_RF_FreqLCfg.815 RF_FreqLCfg.23)  <1028>;
                      (__tmp.955 var=93 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__tmp.956)  <1205>;
                      (__fch_RF_FreqLCfg.962 var=95 stl=RbL off=1) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_RF_FreqLCfg.203)  <1212>;
                    } stp=5;
                    <115> {
                      (FreqCfg.208 var=23) store_1_B1 (__fch_RF_FreqLCfg.961 __tmp.958 FreqCfg.183)  <1029>;
                      (__tmp.958 var=98 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__tmp.959)  <1208>;
                      (__fch_RF_FreqLCfg.961 var=95 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_RF_FreqLCfg.962)  <1211>;
                    } stp=9;
                    <150> {
                      (__tmp.956 var=93 stl=R46 off=0) Rw_1_dr_move_Rw_1_int16__B0 (__tmp.957)  <1206>;
                    } stp=2;
                    <151> {
                      (__tmp.959 var=98 stl=R46 off=0) Rw_1_dr_move_Rw_1_int16__B0 (__tmp.960)  <1209>;
                    } stp=8;
                } #211 off=33 nxt=256 tgt=211
                {
                    () while_expr (__either.806)  <265>;
                    (i.267 var=22 i.268 var=22) exit (i.216)  <275>;
                    (FreqCfg.269 var=23 FreqCfg.270 var=23) exit (FreqCfg.208)  <276>;
                    (__either.806 var=210) undefined ()  <930>;
                } #41
            } #30 rng=[1,2147483647]
            #256 off=46 nxt=240
        } #23
        {
            #216 off=11 nxt=50 tgt=240
            (__trgt.830 var=213) const_inp ()  <959>;
            <104> {
              (__apl_nz.775 var=187 stl=nz_flag_w __seff.881 var=230 stl=c_flag_w __seff.882 var=231 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_2_B3 (__ct_1t0.818 FrqMode.71 __sp.45)  <1018>;
              (__seff.1031 var=230 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.881)  <1265>;
              (__apl_nz.1033 var=187 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.775)  <1267>;
              (__seff.1034 var=231 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.882)  <1268>;
            } stp=0;
            <105> {
              () cc_ne__jump_const_1_B1 (__apl_nz.1032 __trgt.830)  <1019>;
              (__apl_nz.1032 var=187 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.1033)  <1266>;
            } stp=1;
            if {
                {
                    () if_expr (__either.810)  <423>;
                    (__either.810 var=210) undefined ()  <936>;
                } #48
                {
                } #71 nxt=253
                {
                    #50 off=13 nxt=51
                    <103> {
                      (i.392 var=22) store_const__pl_rd_res_reg_const_1_B3 (__ct_2t0.819 i.75 __sp.45)  <1017>;
                    } stp=0;
                    call {
                        () chess_separator_scheduler ()  <427>;
                    } #51 off=14 nxt=249
                    #249 off=14 nxt=235
                    (__ptr_RF_FreqHCfg.816 var=34) const_inp ()  <945>;
                    (__trgt.829 var=212) const_inp ()  <958>;
                    <183> {
                      (__adr_FreqCfg.1036 var=55 stl=a_w2 __side_effect.1037 var=248 stl=c_flag_w __side_effect.1039 var=248 stl=nz_flag_w __side_effect.1041 var=248 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_3t0.820 __sp.45)  <1178>;
                      (__adr_FreqCfg.1035 var=55 stl=R46 off=1) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_FreqCfg.1036)  <1269>;
                      (__side_effect.1038 var=248 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.1037)  <1270>;
                      (__side_effect.1040 var=248 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.1039)  <1271>;
                      (__side_effect.1042 var=248 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.1041)  <1272>;
                    } stp=0;
                    do {
                        {
                            (i.435 var=22) entry (i.520 i.392)  <471>;
                            (FreqCfg.436 var=23) entry (FreqCfg.522 FreqCfg.103)  <472>;
                        } #57
                        {
                            <93> {
                              (__apl_c.789 var=195 stl=c_flag_w __apl_nz.791 var=197 stl=nz_flag_w __seff.864 var=223 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B3 (__ct_2t0.819 i.469 __sp.45)  <1007>;
                              (__apl_nz.1003 var=197 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.791)  <1240>;
                              (__apl_c.1005 var=195 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.789)  <1242>;
                              (__seff.1026 var=223 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.864)  <1263>;
                            } stp=11;
                            <94> {
                              () cc_b__jump_const_1_B1 (__apl_c.1004 __apl_nz.1002 __trgt.829)  <1008>;
                              (__apl_nz.1002 var=197 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.1003)  <1239>;
                              (__apl_c.1004 var=195 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.1005)  <1241>;
                            } stp=12;
                            <95> {
                              (i.469 var=22 __seff.866 var=224 stl=c_flag_w __seff.867 var=225 stl=nz_flag_w __seff.868 var=226 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_2t0.819 i.435 i.435 __sp.45)  <1009>;
                              (__seff.1006 var=225 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.867)  <1243>;
                              (__seff.1007 var=224 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.866)  <1244>;
                              (__seff.1025 var=226 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.868)  <1262>;
                            } stp=10;
                            <96> {
                              (__apl_r.779 var=190 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B2 (__ct_2t0.819 i.435 __sp.45)  <1010>;
                              (__apl_r.1016 var=190 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.779)  <1253>;
                            } stp=0;
                            <97> {
                              (__apl_r.784 var=190 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B2 (__ct_2t0.819 i.435 __sp.45)  <1011>;
                              (__apl_r.1011 var=190 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.784)  <1248>;
                            } stp=3;
                            <98> {
                              (__tmp.781 var=123 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.1015)  <1012>;
                              (__apl_r.1015 var=190 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.1016)  <1252>;
                              (__tmp.1019 var=123 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.781)  <1256>;
                            } stp=1;
                            <99> {
                              (__tmp.786 var=127 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.1010)  <1013>;
                              (__apl_r.1010 var=190 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.1011)  <1247>;
                              (__tmp.1013 var=127 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.786)  <1250>;
                            } stp=4;
                            <100> {
                              (__tmp.459 var=128 stl=a_w2 __seff.874 var=227 stl=c_flag_w __seff.875 var=228 stl=nz_flag_w __seff.876 var=229 stl=o_flag_w) _pl_1_B2 (__adr_FreqCfg.1027 __tmp.1012)  <1014>;
                              (__seff.1008 var=228 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.875)  <1245>;
                              (__seff.1009 var=227 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.874)  <1246>;
                              (__tmp.1012 var=127 stl=a_w0) a_w0_a_w1_1_dr_move_Rw_1_int16__B0 (__tmp.1013)  <1249>;
                              (__seff.1014 var=229 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.876)  <1251>;
                              (__tmp.1022 var=128 stl=RwL off=0) Rw_1_dr_move_a_w2_1_int16__B0 (__tmp.459)  <1259>;
                              (__adr_FreqCfg.1027 var=55 stl=a_w1) a_w0_a_w1_1_dr_move_Rw_1_int16__B3 (__adr_FreqCfg.1035)  <1264>;
                            } stp=7;
                            <101> {
                              (__fch_RF_FreqHCfg.456 var=125 stl=DM_r) _pl_const_load_1_B1 (__tmp.1017 __ptr_RF_FreqHCfg.816 RF_FreqHCfg.25)  <1015>;
                              (__tmp.1017 var=123 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__tmp.1018)  <1254>;
                              (__fch_RF_FreqHCfg.1024 var=125 stl=RbL off=1) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_RF_FreqHCfg.456)  <1261>;
                            } stp=5;
                            <102> {
                              (FreqCfg.461 var=23) store_1_B1 (__fch_RF_FreqHCfg.1023 __tmp.1020 FreqCfg.436)  <1016>;
                              (__tmp.1020 var=128 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__tmp.1021)  <1257>;
                              (__fch_RF_FreqHCfg.1023 var=125 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_RF_FreqHCfg.1024)  <1260>;
                            } stp=9;
                            <177> {
                              (__tmp.1018 var=123 stl=R46 off=0) Rw_1_dr_move_Rw_1_int16__B0 (__tmp.1019)  <1255>;
                            } stp=2;
                            <178> {
                              (__tmp.1021 var=128 stl=R46 off=0) Rw_1_dr_move_Rw_1_int16__B0 (__tmp.1022)  <1258>;
                            } stp=8;
                        } #235 off=16 nxt=252 tgt=235
                        {
                            () while_expr (__either.808)  <540>;
                            (i.520 var=22 i.521 var=22) exit (i.469)  <550>;
                            (FreqCfg.522 var=23 FreqCfg.523 var=23) exit (FreqCfg.461)  <551>;
                            (__either.808 var=210) undefined ()  <933>;
                        } #67
                    } #56 rng=[1,2147483647]
                    #252 off=29 nxt=253
                } #49
                {
                    (i.619 var=22) merge (i.75 i.521)  <675>;
                    (FreqCfg.620 var=23) merge (FreqCfg.103 FreqCfg.523)  <676>;
                } #72
            } #47
            #253 off=29 tgt=240
            (__trgt.833 var=216) const_inp ()  <962>;
            <91> {
              () jump_const_1_B1 (__trgt.833)  <1005>;
            } stp=0;
        } #45
        {
            (i.646 var=22) merge (i.268 i.619)  <702>;
            (FreqCfg.647 var=23) merge (FreqCfg.270 FreqCfg.620)  <703>;
        } #73
    } #21
    #240 off=46 nxt=75
    (error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage___P__uchar___ushort.821 var=142) const_inp ()  <950>;
    <87> {
      (__apl_r.795 var=200 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B1 (__ct_0t0.817 pag.69 __sp.45)  <1001>;
      (__apl_r.981 var=200 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.795)  <1228>;
    } stp=0;
    <88> {
      (__tmp.797 var=141 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.980)  <1002>;
      (__apl_r.980 var=200 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.981)  <1227>;
      (__tmp.985 var=141 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.797)  <1230>;
    } stp=2;
    <90> {
      () call_const_1_B1 (error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage___P__uchar___ushort.821)  <1004>;
    } stp=5;
    <162> {
      (__adr_FreqCfg.987 var=-179 stl=a_w2 __side_effect.988 var=248 stl=c_flag_w __side_effect.990 var=248 stl=nz_flag_w __side_effect.992 var=248 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_3t0.827 __sp.45)  <1121>;
      (__adr_FreqCfg.986 var=-179 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_FreqCfg.987)  <1231>;
      (__side_effect.989 var=248 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.988)  <1232>;
      (__side_effect.991 var=248 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.990)  <1233>;
      (__side_effect.993 var=248 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.992)  <1234>;
    } stp=3;
    call {
        (__tmp.670 var=144 stl=RbL off=0 FreqCfg.673 var=23 __extDM.674 var=17 __extDM_int8_.675 var=25 __extDM_void.676 var=28 __extPM.677 var=16 __extPM_void.678 var=27 __extULP.679 var=18 __extULP_void.680 var=29 __vola.681 var=13) Ferror_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage___P__uchar___ushort (__adr_FreqCfg.986 __tmp.985 FreqCfg.647 __extDM.16 __extDM_int8_.24 __extDM_void.27 __extPM.15 __extPM_void.26 __extULP.17 __extULP_void.28 __vola.12)  <727>;
    } #75 off=53 nxt=78
    #78 off=53 nxt=-2
    () sink (__vola.681)  <737>;
    () sink (__extPM.677)  <740>;
    () sink (__extDM.674)  <741>;
    () sink (__extULP.679)  <742>;
    () sink (__sp.687)  <743>;
    () sink (pag.69)  <744>;
    () sink (FrqMode.71)  <745>;
    () sink (i.646)  <746>;
    () sink (FreqCfg.673)  <747>;
    () sink (__extDM_int8_.675)  <749>;
    () sink (__extPM_void.678)  <751>;
    () sink (__extDM_void.676)  <752>;
    () sink (__extULP_void.680)  <753>;
    (__ct_8s0.822 var=145) const_inp ()  <951>;
    <85> {
      (__sp.687 var=19 __seff.853 var=217 stl=c_flag_w __seff.854 var=218 stl=nz_flag_w __seff.855 var=219 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_8s0.822 __sp.45 __sp.45)  <999>;
      (__seff.974 var=218 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.854)  <1221>;
      (__seff.975 var=217 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.853)  <1222>;
      (__seff.984 var=219 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.855)  <1229>;
    } stp=0;
    <86> {
      () ret_1_B1 ()  <1000>;
    } stp=1;
    150 -> 110 del=0;
    114 -> 151 del=0;
    177 -> 97 del=0;
    101 -> 178 del=0;
    115 -> 108 del=-1999;
    108 -> 106 del=-1999;
    102 -> 95 del=-1999;
    95 -> 93 del=-1999;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,346:0,0);
5 : (0,346:35,0);
6 : (0,346:35,0);
7 : (0,346:19,0);
8 : (0,346:19,0);
9 : (0,348:17,3);
10 : (0,348:17,3);
11 : (0,349:27,5);
12 : (0,349:27,5);
13 : (0,349:27,6);
14 : (0,349:27,6);
15 : (0,349:27,7);
16 : (0,349:27,7);
17 : (0,349:27,8);
19 : (0,349:27,8);
21 : (0,350:4,9);
23 : (0,351:4,10);
24 : (0,352:14,11);
25 : (0,352:14,11);
30 : (0,352:8,12);
45 : (0,357:9,26);
47 : (0,357:9,26);
49 : (0,358:4,27);
50 : (0,359:15,28);
51 : (0,359:15,28);
56 : (0,359:9,29);
71 : (0,365:4,43);
75 : (0,368:4,48);
78 : (0,369:0,49);
192 : (0,350:16,9);
211 : (0,352:18,19);
216 : (0,357:21,26);
235 : (0,359:19,36);
240 : (0,368:4,48);
----------
80 : (0,346:35,0);
82 : (0,346:19,0);
86 : (0,348:17,3);
93 : (0,349:27,5);
100 : (0,349:27,6);
107 : (0,349:27,7);
115 : (0,349:27,8);
148 : (0,350:4,9);
152 : (0,352:14,11);
196 : (0,352:8,12);
197 : (0,352:8,12);
265 : (0,352:8,19);
275 : (0,352:8,19);
276 : (0,352:8,19);
423 : (0,357:9,26);
427 : (0,359:15,28);
471 : (0,359:9,29);
472 : (0,359:9,29);
540 : (0,359:9,36);
550 : (0,359:9,36);
551 : (0,359:9,36);
675 : (0,357:9,45);
676 : (0,357:9,45);
702 : (0,350:4,47);
703 : (0,350:4,47);
727 : (0,368:4,48);
999 : (0,369:0,0) (0,369:0,49);
1000 : (0,369:0,49);
1001 : (0,368:61,48) (0,346:43,0) (0,368:59,48);
1002 : (0,368:59,48);
1004 : (0,368:4,48);
1007 : (0,359:18,36) (0,348:12,0) (0,359:19,36);
1008 : (0,359:19,36) (0,359:9,36);
1009 : (0,359:23,31) (0,359:22,0) (0,348:12,0);
1010 : (0,361:36,29) (0,348:12,0) (0,361:35,29) (0,361:35,0);
1011 : (0,361:19,29) (0,348:12,0) (0,361:18,29) (0,361:18,0);
1012 : (0,361:35,29);
1013 : (0,361:18,29);
1014 : (0,361:18,29);
1015 : (0,361:35,29);
1016 : (0,361:18,29);
1017 : (0,359:14,27) (0,348:12,0);
1018 : (0,357:13,26) (0,346:27,0) (0,357:21,26);
1019 : (0,357:21,26) (0,357:9,26);
1020 : (0,352:17,19) (0,348:12,0) (0,352:18,19);
1021 : (0,352:18,19) (0,352:8,19);
1022 : (0,352:22,14) (0,352:21,0) (0,348:12,0);
1023 : (0,354:36,12) (0,348:12,0) (0,354:35,12) (0,354:35,0);
1024 : (0,354:19,12) (0,348:12,0) (0,354:18,12) (0,354:18,0);
1025 : (0,354:35,12);
1026 : (0,354:18,12);
1027 : (0,354:18,12);
1028 : (0,354:35,12);
1029 : (0,354:18,12);
1030 : (0,352:13,10) (0,348:12,0);
1031 : (0,350:8,9) (0,346:27,0) (0,350:16,9);
1032 : (0,350:16,9) (0,350:4,9);
1033 : (0,349:27,7) (0,349:27,0) (0,349:12,0);
1034 : (0,349:27,6) (0,349:27,0) (0,349:12,0);
1035 : (0,349:27,5) (0,349:27,0) (0,349:12,0);
1036 : (0,349:12,0) (0,349:27,4);
1038 : (0,348:12,0) (0,348:17,3);
1039 : (0,346:27,0) (0,346:19,0);
1040 : (0,346:5,0);
1041 : (0,346:43,0) (0,346:35,0);
1121 : (0,349:12,0);
1129 : (0,349:12,0);
1178 : (0,349:12,0);
1206 : (0,354:35,0);
1209 : (0,354:18,0);
1255 : (0,361:35,0);
1258 : (0,361:18,0);

