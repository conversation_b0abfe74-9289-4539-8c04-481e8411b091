#include "types.h"
#include "defs.h"


typedef enum
{
  DataEnc_NRZ_single = 0,
  DataEnc_Man_single = 1,
  DataEnc_NRZ_double = 2,
  DataEnc_Man_double = 3
}
DataEnc_t;

/**
 * Structured type to collect all UHF related settings. 
 */
typedef struct
{
  uint16_t Cfg_FREQCON0;
  uint16_t Cfg_FREQCON1;
  uint8_t  Cfg_PLLCON;    // only set FIXDIV_DIV2_EN
  uint16_t Cfg_ASKCON; 
  uint8_t  Cfg_FSKCON; 
  uint16_t Cfg_BRGCON; 
  uint8_t  Cfg_ASKRMP; 
  uint8_t  Cfg_FSKRMP; 
  uint8_t  Cfg_PACON;  
  uint8_t  Cfg_PAPWR;  
  uint8_t  Cfg_PATRIM; 
  uint8_t  Cfg_PALIMIT;
  uint16_t Cfg_ENCCON0;   // initial value for ENCCON0
}
TxRegSettings_t;

 
extern void    tx_read_configuration( uint16_t u16_EeBasePage, TxRegSettings_t * ps_ActTxRegSettings );

extern void    tx_apply_configuration( const TxRegSettings_t * ps_ActTxRegSettings );

extern error_t tx_enable_Xtal_oscillator( bool_t b_OnOff );

extern error_t tx_enable_PLL( bool_t b_OnOff );

extern error_t tx_enable_PA ( bool_t b_OnOff );

extern void    tx_shutdown( void );

extern void    tx_transmit_buffer_encoded_bytes( DataEnc_t e_encoding, uint16_t u16_numbytes, const uint8_t * pu8_buffer );

extern void    tx_transmit_buffer_encoded_bits(  DataEnc_t e_encoding, uint16_t u16_numbits,  const uint8_t * pu8_buffer );

extern void    tx_transmit_encoded_bits( DataEnc_t e_encoding, uint16_t u16_numbits, uint16_t u16_data );

extern void    Rf_TXFream( uint8_t * Txbuf,uint8_t len);

extern void FHSS_FreqCfg( uint8_t FrqMode,uint8_t pag );

//extern void RKE_RegConfig( uint8_t mode );
/* eof */


