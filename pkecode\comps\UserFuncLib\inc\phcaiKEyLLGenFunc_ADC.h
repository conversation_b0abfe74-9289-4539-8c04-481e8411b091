/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_ADC.h 31225 2021-03-10 14:44:11Z dep10330 $
  $Revision: 31225 $
*/

/**
 * @file
 * Declarations of User Functions to support the on-chip ADC (Analog to Digital Converter).
 */

#ifndef PHCAIKEYLLGENFUNC_ADC_H_
#define PHCAIKEYLLGENFUNC_ADC_H_

#include "types.h"
#include "phcaiKEyLLGenFunc_Platform.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup ADC  ADC support functions
 * Support functions for on-chip ADC in TOKEN family.
 * Note, RSSI related ADC support is found in module phcaiKEyLLGenFunc_RSSI.c .
 * @{
 */

/*-----------------------------------------------------------------------------------------------*/

/**
 * ADC input sources.
 * Matching settings defined for ADCCON register bits INSEL[2..0].
 */
#if defined( PLATFORM_TOKEN )

typedef enum
{
  /** Battery voltage measurement */
  ADC_INPUTSEL_VBAT        = 0u,
  /** External input measurement, voltage applied on P14 and P13 */
  ADC_INPUTSEL_EXTERNAL    = 1u,
  ADC_INPUTSEL_RFU2        = 2u,
  /** Temperature sensor measurement */
  ADC_INPUTSEL_TEMPSENS    = 3u,
  /** RSSI measurement */
  ADC_INPUTSEL_RSSI        = 4u,
  ADC_INPUTSEL_RFU5        = 5u,
  ADC_INPUTSEL_RFU6        = 6u,
  ADC_INPUTSEL_RFU7        = 7u
}
ADC_INPUTSEL_t;

#elif defined( PLATFORM_SMART2A )

typedef enum
{
  /** Battery voltage measurement */
  ADC_INPUTSEL_VBAT        = 0u,
  ADC_INPUTSEL_RFU1        = 1u,
  ADC_INPUTSEL_RFU2        = 2u,
  /** Temperature sensor measurement */
  ADC_INPUTSEL_TEMPSENS    = 3u,
  ADC_INPUTSEL_RFU4        = 4u,
  ADC_INPUTSEL_RFU5        = 5u,
  ADC_INPUTSEL_RFU6        = 6u,
  ADC_INPUTSEL_RFU7        = 7u
}
ADC_INPUTSEL_t;

#elif defined( PLATFORM_TOKENPLUS )

typedef enum
{
  /** Battery voltage measurement */
  ADC_INPUTSEL_VBAT        = 0u,
  /** External input measurement, voltage applied on P16 and P11;
      P11 and P16 are configured in analog mode and the settings
      P1DIR.1 and P1DIR.6 are ignored. */
  ADC_INPUTSEL_EXTERNAL1   = 1u,
  ADC_INPUTSEL_RFU2        = 2u,
  /** Temperature sensor measurement */
  ADC_INPUTSEL_TEMPSENS    = 3u,
  /** RSSI measurement */
  ADC_INPUTSEL_RSSI        = 4u,
  ADC_INPUTSEL_RFU5        = 5u,
  /** External input measurement, voltage applied on P16 and P32 (HVQFN40 only);
      P16 and P32 are automatically configured in analog mode and the settings
      P1DIR.6 and P3DIR.2 are ignored. */
  ADC_INPUTSEL_EXTERNAL2   = 6u,
  /** External input measurement, voltage applied on P16 and P15;
      P15 and P16 are automatically configured in analog mode and the settings
      P1DIR.5 and P1DIR.6 are ignored. */
  ADC_INPUTSEL_EXTERNAL3   = 7u
}
ADC_INPUTSEL_t;

#elif defined( PLATFORM_TOKENSRX_COMMON )

/* Note, basically same as in TOKEN-PLUS, but only HVQFN40 package is available. */
typedef enum
{
  /** Battery voltage measurement */
  ADC_INPUTSEL_VBAT        = 0u,
  /** External input measurement, voltage applied on P16 and P11;
      P11 and P16 are configured in analog mode and the settings
      P1DIR.1 and P1DIR.6 are ignored. */
  ADC_INPUTSEL_EXTERNAL1   = 1u,
  ADC_INPUTSEL_RFU2        = 2u,
  /** Temperature sensor measurement */
  ADC_INPUTSEL_TEMPSENS    = 3u,
  /** RSSI measurement */
  ADC_INPUTSEL_RSSI        = 4u,
  ADC_INPUTSEL_RFU5        = 5u,
  /** External input measurement, voltage applied on P16 and P32;
      P16 and P32 are automatically configured in analog mode and the settings
      P1DIR.6 and P3DIR.2 are ignored. */
  ADC_INPUTSEL_EXTERNAL2   = 6u,
  /** External input measurement, voltage applied on P16 and P15;
      P15 and P16 are automatically configured in analog mode and the settings
      P1DIR.5 and P1DIR.6 are ignored. */
  ADC_INPUTSEL_EXTERNAL3   = 7u
}
ADC_INPUTSEL_t;
#endif


/**
 * ADC reference selection (REFSEL[2..0] in ADCCON register)
 */
#if defined( PLATFORM_TOKEN ) || defined( PLATFORM_TOKENPLUS )
typedef enum
{
  /** Buffered bandgap reference voltage, Vref = VADC,ref
      (for battery voltage measurement) */
  ADC_REFSEL_BANDGAP       = 0u,
  /** VBAT and VSS */
  ADC_REFSEL_VBAT_VSS      = 1u,
  /** External reference, applied on P17_LED and P12;
      P12 and P17 are automatically configured in analog mode and the settings
      P1DIR.2 and P1DIR.7 are ignored */
  ADC_REFSEL_EXTERNAL      = 2u,
  /** Optimized reference selection for temperature measurement */
  ADC_REFSEL_TEMPSENS      = 3u,
  /** Buffered bandgap reference voltage, Vref = VADC,ref (for RSSI measurement) */
  ADC_REFSEL_BANDGAPRSSI   = 4u,
  ADC_REFSEL_RFU5          = 5u,
  ADC_REFSEL_RFU6          = 6u,
  ADC_REFSEL_RFU7          = 7u
}
ADC_REFSEL_t;

#elif defined( PLATFORM_SMART2A )

typedef enum
{
  /** Buffered bandgap reference voltage, Vref = VADC,ref */
  ADC_REFSEL_BANDGAP       = 0u,
  /** VBAT and VSS */
  ADC_REFSEL_VBAT_VSS      = 1u,
  ADC_REFSEL_RFU2          = 2u,
  /** Optimized reference selection for temperature measurement */
  ADC_REFSEL_TEMPSENS      = 3u,
  ADC_REFSEL_RFU4          = 4u,
  ADC_REFSEL_RFU5          = 5u,
  ADC_REFSEL_RFU6          = 6u,
  ADC_REFSEL_RFU7          = 7u
}
ADC_REFSEL_t;

#elif defined( PLATFORM_TOKENSRX_COMMON )

typedef enum
{
  /** Buffered bandgap reference voltage, Vref = VADC,ref
      (for battery voltage measurement) */
  ADC_REFSEL_BANDGAP       = 0u,
  /** VBAT and VSS */
  ADC_REFSEL_VBAT_VSS      = 1u,
  /** External reference, applied on P17_LED and P12;
      P12 and P17 are automatically configured in analog mode and the settings
      P1DIR.2 and P1DIR.7 are ignored. */
  ADC_REFSEL_EXTERNAL      = 2u,
  /** Optimized reference selection for temperature measurement */
  ADC_REFSEL_TEMPSENS      = 3u,
  ADC_REFSEL_RFU4          = 4u,
  ADC_REFSEL_RFU5          = 5u,
  ADC_REFSEL_RFU6          = 6u,
  ADC_REFSEL_RFU7          = 7u
}
ADC_REFSEL_t;

#endif



#ifndef PLATFORM_HAS_ADC_EXT_SAMTIM

/**
 * ADC sampling time selection (SAMTIM[1..0] in ADCCON register)
 */
typedef enum
{
  ADC_SAMPTIME_01US        = 0u, /**  1 �s */
  ADC_SAMPTIME_08US        = 1u, /**  8 �s */
  ADC_SAMPTIME_16US        = 2u, /** 16 �s */
  ADC_SAMPTIME_64US        = 3u  /** 64 �s */
}
ADC_SAMTIM_t;

// Binary mask for setting SAMTIM bits in ADCCON
//   15  |  14  |  13  |  12   |  11   |  10   |  9    |   8   |   7   |   6     |    5     |   4    |    3    |   2   |    1    |    0    |
// INSEL2 INSEL1 INSEL0 REFSEL2|REFSEL1 REFSEL0 SAMTIM1 SAMTIM0|TSENSEN BATMEASEN BG1V2BUFEN BG1V2EN |    X        X    CONVRESET CONVSTART|
//    1      1      1      1   |   1       1      0        0   |   1       1          1         1    |    1        1        1         1    |
//hex:                     F   |                           C   |                                F    |                                F    |
#define ADC_SAMTIM_ALL_MASK 0xFCFFu

#endif


#ifdef PLATFORM_HAS_ADC_EXT_SAMTIM

// On PLUS and SRX types, additional sampling time settings with SAMTIMEXT=1.
typedef enum
{
  ADC_SAMPTIME_01US        = 0u, /**  1 �s */
  ADC_SAMPTIME_02US        = 1u, /**  2 �s */
  ADC_SAMPTIME_04US        = 2u, /**  4 �s */
  ADC_SAMPTIME_08US        = 3u, /**  8 �s */
  ADC_SAMPTIME_12US        = 4u, /** 12 �s */
  ADC_SAMPTIME_16US        = 5u, /** 16 �s */
  ADC_SAMPTIME_32US        = 6u, /** 32 �s */
  ADC_SAMPTIME_64US        = 7u  /** 64 �s */
}
ADC_SAMTIM_t;


// Binary mask for setting SAMTIM bits in ADCCON
//   15  |  14  |  13  |  12   |  11   |  10   |  9    |   8   |   7   |   6     |    5     |   4    |    3    |   2   |    1    |    0    |
// INSEL2 INSEL1 INSEL0 REFSEL2|REFSEL1 REFSEL0 SAMTIM1 SAMTIM0|TSENSEN BATMEASEN BG1V2BUFEN BG1V2EN |SAMTIMEXT POWERON CONVRESET CONVSTART|
//    1      1      1      1   |   1       1      0        0   |   1       1          1         1    |    0        1        1         1    |
//hex:                     F   |                           C   |                                F    |                                7    |
#define ADC_SAMTIM_ALL_MASK 0xFCF7u

#endif


/**
 * Mask for extraction of valid ADC result bits in ADCDAT register.
 * (For PLUS types, only if no summation was used.)
 */
#define ADCDATA_VALID_MASK  0x03FFu

/**
 * Result value from ADC_measure_VBAT() representing minimum VBAT (2.0 V) for the TOKEN devices.
 *
 * Example: 520 * VADC,REF(Typ)/512 * 3/2 = 520 * 1.25V / 512 ~= 1.90 V
 * Note: Acc. to TOKEN prelim. DS 2.00, GVBAT = 3.54 (min) 3.65 (typ) 3.78 (max) mV/LSB
 * means safe threshold: ceil( 2000 / 3.54 ) = 565 .
 */
#define VBAT_MIN_VALUE      565u

// Measured typical values on one sample
#define VBAT_VALUE_3600mV   968U
#define VBAT_VALUE_3000mV   822U
#define VBAT_VALUE_2350mV   642U
#define VBAT_VALUE_2100mV   573U
#define VBAT_VALUE_2000mV   546U
#define VBAT_VALUE_1900mV   520U
#define VBAT_VALUE_1800mV   489U
#define VBAT_VALUE_1700mV   462U

#define PowerDown     VBAT_VALUE_2100mV
#define LowPower      VBAT_VALUE_2350mV


/**
 * Stores number of ADC samples counted in function phcaiKEyLLGenFunc_ADC_run_until_t2int
 * (only if DEBUG defined).
 */
extern uint16_t g_u16_num_adc_samples;

/*-----------------------------------------------------------------------------------------------*/



/**
 * Enable the VDDA regulator and the ADC bandgap reference.
 * Checks the VDDABRNREG (if available on platform).
 * If XO clock selected by ADCCLKSEL, checks XO_READY flag.
 * Success is returned if VDDABRNREG==0 and ( ADCCLKSEL == 0 or ( ADCCLKSEL == 1 and XO_READY == 1 )).
 *
 * @return SUCCESS if checks are OK, else ERROR.
 */
error_t phcaiKEyLLGenFunc_ADC_enable( void );

/**
 * Sets the ADC sampling time to any allowed setting for the given type.
 *
 * @param[in] e_samplingtime   ADC sampling time setting as enum type.
 * @see ADC_SAMTIM_t
 */
void phcaiKEyLLGenFunc_ADC_SetSamplingTime( ADC_SAMTIM_t en_samplingtime );

/**
 * Switch off the ADC bandgap and VDDA regulator.
 */
void phcaiKEyLLGenFunc_ADC_poweroff( void );


/**
 * Run a single A/D conversion with parameters set in ADCCON.
 * Waits for completion in IDLE mode until ADC interrupt.
 * Function phcaiKEyLLGenFunc_ADC_enable should be invoked beforehand.
 *
 * @return The resulting ADCDATA value (10 bit unsigned).
 * @see phcaiKEyLLGenFunc_ADC_run_averaged
 */
uint16_t phcaiKEyLLGenFunc_ADC_run( void );


/**
 * Run a series of A/D conversions (in direct sequence) with parameters set in ADCCON,
 * and calculates the un-weighted average.
 * ADC results are added (in a 32 bit variable) and divided by the number of samples,
 * returning as a result:
 * result = ( sum_{ i=1 to N } ( ADCDATA[i] ) ) / N
 * (N = u16_num_samples, i = index of sample).
 * No rounding is performed at the divide operation.
 *
 * For TOKEN platform:
 * Implemented in optimized assembly code, unless NO_ASM_FOR_ADC_AVERAGING is defined.
 * Start address aligned to even PM address to ensure constant executing timing.
 * The 2 ms detector is serviced while waiting for each single conversion to complete.
 * It is recommended to enable continuous power-on mode, to avoid the power-up cycles
 * with every conversion. See app note AN-SCA1609.
 * Measured 980 �s with CLKCON0=0x22 (RCCLK, 4 MInstr/s), 8 �s SAMTIM, continuous ADC
 * power-on, assembly code, 59 samples.
 *
 * For TOKEN-PLUS and -SRX platforms:
 * Using hardware summation feature, with division by number of samples.
 * Only up to 256 samples are supported at the moment (normally sufficient).
 * The 2 ms detector is serviced while waiting for the conversion to complete.
 * Note: setting the ADCPOWERON is not necessary, since the ADC remains in power-on
 * during the hardware summation sequence.
 * Measured: TODO ca 1 ms with 128 samples and 4 �s SAMTIM.
 *
 * @param[in] u16_num_samples Number of samples taken and averaged. Must be greater than 0
 *            and less or equal 256.
 * @return The resulting averaged ADCDATA value (10 bit unsigned).
 * @see phcaiKEyLLGenFunc_ADC_run_until_t2int
 */
uint16_t phcaiKEyLLGenFunc_ADC_run_averaged( uint16_t u16_num_samples );

/**
 * Run a series of A/D conversions (in direct sequence) with parameters set in ADCCON,
 * and calculates the un-weighted average.
 * ADC results are added (in a 32 bit variable) and divided by the number of samples,
 * returning as a result:
 * result = ( sum_{ i=1 to N } ( ADCDATA[i] ) ) / N
 * (N = u16_num_samples, i = index of sample).
 * No rounding is performed at the divide operation.
 *
 * For TOKEN platform:
 * Implemented in optimized assembly code, unless NO_ASM_FOR_ADC_AVERAGING is defined.
 * Start address aligned to even PM address to ensure constant executing timing.
 * The 2 ms detector is serviced while waiting for each single conversion to complete.
 * It is recommended to enable continuous power-on mode, to avoid the power-up cycles
 * with every conversion. See app note AN-SCA1609.
 *
 * For TOKEN-PLUS and -SRX platforms:
 * Using hardware summation feature, with division by number of samples.
 * Only up to 256 samples are supported at the moment (normally sufficient).
 * The 2 ms detector is serviced while waiting for the conversion to complete.
 * Note: setting the ADCPOWERON is not necessary, since the ADC remains in power-on
 * during the hardware summation sequence.
 *
 * @param[in] u16_num_samples Number of samples taken and averaged. Must be greater than 0
 *            and less or equal 256.
 * @param[in] u16_NumFractionalBits Number of fractional bits to be returned. Must be greater than 0
 *            and less or equal 10.
 * @return The resulting averaged ADCDATA value (10 bit unsigned) plus additional fractional bits.
 * @see phcaiKEyLLGenFunc_ADC_run_averaged
 */
 uint16_t phcaiKEyLLGenFunc_ADC_run_averaged_fractBits( uint16_t u16_num_samples, uint16_t u16_NumFractionalBits );

/**
 * Run a series of A/D conversions (in direct sequence) with parameters set in ADCCON,
 * until Timer2 interrupt flag IF_T2 set, and calculates the un-weighted average.
 * Caller is responsible to handle Timer2 setup and clear interrupt flag.
 * At minimum one conversion is performed (even if IF_T2 is already set before entry).
 *
 * Implemented in optimized assembly code, unless NO_ASM_FOR_ADC_AVERAGING is defined.
 * Start address aligned to even PM address to ensure constant executing timing.
 * In DEBUG configuration build, the resulting number of samples is stored in global
 * variable g_u16_num_adc_samples.
 * This 16 bit sample counter is not protected against overflow.
 * No rounding is performed at the divide operation.
 * The 2 ms detector is serviced while waiting for each single conversion to complete.
 *
 * Note: hardware summation cannot be used here since it cannot be stopped unless by
 * an ADC reset.
 *
 * Measured with CLKCON0=0x22 (4    MInstr/s) and timer2 trigger after 1 ms (AUXRC clock)    : 59 samples
 * Measured with CLKCON0=0x26 (2    MInstr/s) and timer2 trigger after 1 ms (AUXRC clock)    : 49..52 samples
 * Measured with CLKCON0=0x62 (3.45 MInstr/s) and timer2 trigger after 1 ms (both XTal clock): 54 samples
 *
 * @return The resulting averaged ADCDATA value (10 bit unsigned).
 * @see phcaiKEyLLGenFunc_ADC_run_averaged
 */
uint16_t phcaiKEyLLGenFunc_ADC_run_until_t2int( void );


/**
 * Measures the battery supply voltage. Voltage divider is switched on before and off after the ADC conversion.
 * Function phcaiKEyLLGenFunc_ADC_enable should be invoked beforehand.
 * Interpretation: VBATmeas = ( ADCDATA * Vref/512 ) * 3/2
 *
 * @return The resulting ADCDATA value (10 bit unsigned).
 * @see phcaiKEyLLGenFunc_ADC_check_VbatMin
 */
uint16_t phcaiKEyLLGenFunc_ADC_measure_VBAT( void );


/**
 * Measures the temperature sensor. Sensor is switched on before and off after the ADC conversion.
 * Function phcaiKEyLLGenFunc_ADC_enable should be invoked beforehand.
 *
 * Measured Temp. = Tref + ( ADCDATA - OFF_TADC ) / GTADC
 * GTADC (Typ.)= 6.54 LSB/K, OFF_TADC (Typ.) = 450 (ADCDATA measured at T=Tref)
 *
 * @return The resulting ADCDATA value (10 bit unsigned).
 */
uint16_t phcaiKEyLLGenFunc_ADC_measure_Temp( void );



/**
 * Check the VBAT voltage against the given minimum and returns the result.
 *
 * @param[in]  u16_AdcMinValue
 * @param[out] pu16_VBAT_result  Result of the VBAT ADC measurement (10 bit unsigned).
 * @return     SUCCESS if ADC output is not less than given minimum value.
 * @see phcaiKEyLLGenFunc_ADC_measure_VBAT
 */
error_t phcaiKEyLLGenFunc_ADC_check_VbatMin( uint16_t u16_AdcMinValue, uint16_t * pu16_VBAT_result );


#endif

/* @} */
/* @} */

/* eof */
