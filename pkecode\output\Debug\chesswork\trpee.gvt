
// File generated by noodle version P-2019.09#78e58cd307#210222, Fri Nov  3 15:38:56 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/trpee.c mrk3

[
  169 : EE_PSK0 typ=uint32_ val=1f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  170 : EE_PSK1 typ=uint32_ val=2f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  171 : EE_TMCF_PW typ=uint32_ val=3f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  172 : EE_RSKL typ=uint32_ val=4f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  173 : EE_RSKH typ=uint32_ val=5f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  174 : EE_USER2 typ=uint32_ val=6f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  175 : EE_USER3 typ=uint32_ val=7f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  176 : EE_SICMP typ=uint32_ val=24f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  177 : EE_MEMCFG0 typ=uint32_ val=120f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  178 : EE_MEMCFG1 typ=uint32_ val=121f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  179 : EE_MEMCFG2 typ=uint32_ val=122f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  180 : EE_MEMCFG3 typ=uint32_ val=123f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  181 : EE_MEMIDS typ=uint32_ val=127f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  182 : EE_SI_PAGE0 typ=uint32_ val=16f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  183 : EE_SI_PAGE1 typ=uint32_ val=17f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  184 : EE_SI_PAGE2 typ=uint32_ val=18f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  185 : EE_SI_PAGE3 typ=uint32_ val=19f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  186 : EE_SI_PAGE4 typ=uint32_ val=20f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  187 : EE_SI_PAGE5 typ=uint32_ val=21f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  188 : EE_SI_PAGE6 typ=uint32_ val=22f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  189 : EE_SI_PAGE7 typ=uint32_ val=23f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  190 : EE_LFTUNEVDD typ=uint32_ val=980f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  191 : EE_DCFG typ=uint32_ val=977f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  192 : LF_HF_FLD_CONFIG typ=uint32_ val=976f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  193 : EE_CFG_ENERWR typ=uint32_ val=979f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  194 : VINH_CONFIG typ=uint32_ val=50f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  195 : VINL_CONFIG typ=uint32_ val=51f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  196 : ANT_CONFIG typ=uint32_ val=52f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  197 : SYNC_CONFIG typ=uint32_ val=25f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  198 : SWVERSION_CONFIG typ=uint32_ val=26f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
  199 : POLLING_CONFIG typ=uint32_ val=27f bnd=g sz=1 algn=1 stl=ULP tref=ulp32_t_ULP
]
__trpee_sttc {
} #0
----------
----------

