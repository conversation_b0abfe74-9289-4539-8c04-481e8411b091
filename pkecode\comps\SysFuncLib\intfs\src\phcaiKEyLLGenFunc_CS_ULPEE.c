/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_ULPEE.c 11676 2018-01-02 13:17:01Z dep17622 $
  $Revision: 11676 $
*/

/**
 * @file
 * Implementation of the stubs to call ULPEE specific KEyLink Lite General Functions
 * placed in ROM.
 */

#include "phcaiKEyLLGenFunc_CS_ULPEE.h"
#include "phcaiKEyLLGenFunc_CS_Utils.h"
#include "phcaiKEyLLGenFunc_SCI.h"

/*
  Change Log
  2017-10-18 (MMr):
  - improved comments

 */

error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(
  const uint8_t * const data, const uint16_t page_address)
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.data = data;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.addr = page_address;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.wait = TRUE;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.return_val;
}

error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait(
  const uint8_t * const data, const uint16_t page_address)
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.data = data;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.addr = page_address;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.wait = FALSE;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.return_val;
}

error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage_LE(
  const uint8_t * const data, const uint16_t page_address)
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE_LE;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.data = data;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.addr = page_address;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.wait = TRUE;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.return_val;
}

error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait_LE(
  const uint8_t * const data, const uint16_t page_address)
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE_LE;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.data = data;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.addr = page_address;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.wait = FALSE;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.ulpee_write_pg.return_val;
}

void phcaiKEyLLGenFunc_CS_ULPEE_ReadPage_LE(
  uint8_t* const data, const uint16_t page_address)
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_ULPEE_READ_PAGE_LE;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_read_pg.data = data;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_read_pg.addr = page_address;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(
  uint8_t* const data, const uint16_t page_address)
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_ULPEE_READ_PAGE;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_read_pg.data = data;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_read_pg.addr = page_address;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_ULPEE_ReadBytes(
  uint8_t* const data, const uint16_t byte_address, const uint16_t num_bytes)
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_ULPEE_READ_BYTES;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_read_bytes.data = data;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_read_bytes.byte_addr = byte_address;
  phcaiKEyLLGenFunc_Func_Params.params.ulpee_read_bytes.num_bytes = num_bytes;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_ULPEE_SetPROGEN()
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_ULPEE_SET_PROGEN;
  call_syscall(5);
}

