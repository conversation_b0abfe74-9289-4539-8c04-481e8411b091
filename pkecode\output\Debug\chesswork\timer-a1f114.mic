
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:24 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i timer-a1f114 mrk3


// m6;   next: m42 (next offset: 3)
000000  1 0  "0000010010010111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000001  2 0  "0110110000110011"   // (DM[0]) = _pl_rd_res_reg_const_store_const_1_B1 (0,DM[0],R7); 
000002  0 0  "0000000000111110"   // /

// m42;   next: m13 (next offset: 3)

// m13 inline assembly;   next: m17 (next offset: 4)
000003  1 0  "0110100001000000"  .loop_nesting 1 .srcref "C:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3_base/lib" "mrk3_int.h" 604 .srcref "" "apps/src/timer.c" 73  .inline_asm_begin .inline_asm_end // 

// m17;   next: m19 (next offset: 6)
000004  2 0  "0000011010001011" .loop_nesting 1    // (DM[0],c_flag,nz_flag,o_flag) = _mi_load_const__pl_rd_res_reg_const_store_1_B2 (0,DM[0],DM[0],R7); 
000005  0 0  "0000000000000000"   // /

// m19 chess_separator_scheduler;   next: m41 (next offset: 6)

// m41;   next: m26, jump target: m42 (next offset: 9)
000006  2 0  "0100011010000011"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B2 (0,DM[0],R7); 
000007  0 0  "0000000000000000"   // /
000008  1 0  "0101011111111011"   // () = cc_a__jump_const_1_B1 (c_flag,nz_flag,-5); 

// m26 (next offset: /)
000009  1 0  "0001010010010111" .loop_nesting 0    // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000010  1 0  "0001101111000100"   // () = ret_1_B1 (); 

