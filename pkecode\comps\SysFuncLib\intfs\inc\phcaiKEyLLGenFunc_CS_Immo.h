/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_Immo.h 12264 2018-02-15 13:30:43Z dep10330 $
  $Revision: 12264 $
*/

/**
 * @file
 * Declarations of the stubs to call immobilizer specific system functions (ROM library).
 */

#ifndef PHCAIKEYLLGENFUNC_CS_IMMO_H
#define PHCAIKEYLLGENFUNC_CS_IMMO_H

#include "types.h"
#include "phcaiKEyLLGenFunc_SCI.h"

/**
 * @defgroup genfunclib_stubs_immo Immobilizer
 * Declarations of the stubs to call immobilizer specific system
 * functions (ROM library).
 * @{
 */

/**
 * Starts the pre-configured monolithic transponder emulation in ROM.
 */
void phcaiKEyLLGenFunc_CS_Start_Monolith_Transp(void);

/**
 * @brief Initializes the modular immobilizer emulation.
 *
 * The system call handler first checks if the selected immobilizer
 * is available in ROM. In the positive case, the immobilizer's
 * initialization routine is executed. In the negative case, an error is
 * returned.
 *
 * @param[in] immo_type Immobilizer type to be initialized.
 * @return Error indicator.
 * @note Uses timer 0.
 * @see phcaiHITAGCommon_ModImmo_Init
 */
error_t phcaiKEyLLGenFunc_CS_Immo_Init(
  const phcaiKEyLLGenFunc_SCI_Immo_Type_t immo_type);

/**
 * Resets the modular immobilizer emulation.
 *
 * @return Error indicator.
 *
 * @note Use this function only after phcaiKEyLLGenFunc_CS_Immo_Init()
 * has been called before.
 * @note Uses timer 0.
 *
 * @see phcaiHITAGCommon_ModImmo_Reset()
 */
error_t phcaiKEyLLGenFunc_CS_Immo_Reset(void);

/**
 * Sets the modular immobilizer into a state to wait for incoming data.
 * Up to 18 bytes can be received for HT-PRO, or 16 bytes for HT-AES.
 *
 * @param[out] rvc_buff Pointer to the receive buffer.
 * @param[out] len Number of received bits.
 * @return Error indicator.
 *
 * @note Uses timer 0.
 * @see phcaiKEyLLGenFunc_CS_Immo_ExecuteCmd
 */
error_t phcaiKEyLLGenFunc_CS_Immo_ReceiveCmd(
  uint8_t* const rvc_buff, uint8_t* const len);

/**
 * Uses the modular immobilizer to process a previously received command.
 *
 * @param[in] rcv_data Pointer to the data to be processed.
 * @param[in] len Number of data bits to process.
 * @return Error indicator.
 * @note Uses timer 0.
 */
void phcaiKEyLLGenFunc_CS_Immo_ExecuteCmd(
  uint8_t* const rcv_data, const uint8_t len);

/*@}*/

#endif
