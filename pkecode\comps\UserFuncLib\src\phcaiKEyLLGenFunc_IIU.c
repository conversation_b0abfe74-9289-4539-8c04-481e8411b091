/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_IIU.c 24707 2020-01-23 11:35:51Z dep10330 $
  $Revision: 24707 $
*/

/*+----------------------------------------------------------------------------- */
/*| NOTE: The code provided herein is still under development and hence          */
/*|       subject to change.                                                     */
/*+----------------------------------------------------------------------------- */

/**
 * @file
 * Definitions of User Functions to access the
 * Immobilizer Interface Unit (IIU).
 *
 * !!! NOT FULLY TESTED !!! USE AT YOUR OWN RISK !!!
 */

/*
 Change Log:

  2012-02-20 (MMr):
  - created
  2012-06-15 (MMr):
  - Receive and send functioning, tidied up
  2013-07-26 (MMr):
  - Improved MISRA-C compliance
  - removed phcaiKEyLLGenFunc_Iiu_wait()
  - added decl. of phcaiKEyLLGenFunc_Iiu_SetEqualizer() to header file
  2014-06-12 (MMr):
  - adaptation to TOKEN platform (only minor changes, IIU registers are compatible)
  2015-09-07 (MMr):
  - fixed: incomplete compile switches for setting I3DCON register
  2016-07-15 (MMr):
  - improved MISRA-C (PC-Lint) compliance.
  2017-20-27 (MMr):
  - Register I3DCON defined locally in this module only for TOKEN (included in standard
    SFR definitions for PLUS and SRX types)
  2020-01-21 (MMr):
  - phcaiKEyLLGenFunc_Iiu_ReceiveData: adapted parameters to match signature of system call
    phcaiKEyLLGenFunc_CS_IIU_receive.
 */


#include "phcaiKEyLLGenFunc.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @addtogroup IIU
 * @{
 */


/**
 * Bit pattern to mask out invalid bits of the IIUDAT register.
 */
static const uint8_t u8arr_iiu_receive_mask[8] =
  { 0x00u, 0x01u, 0x03u, 0x07u, 0x0FU, 0x1FU, 0x3FU, 0x7FU };


/** Holds the equalizer bit pattern. */
/* init with STANDARD_EQ; */
static phcaiKEyLLGenFunc_Iiu_EQ_t e_phcaiKEyLLGenFunc_Iiu_Equalizer;

/** Holds the equalizer length. */
/* init with STANDARD_EQ_LEN; */
static uint8_t  u8_phcaiKEyLLGenFunc_Iiu_Equalizer_len;


/** The modulation depth (strong/standard). */
static phcaiKEyLLGenFunc_Modulation_t e_mod_depth = STANDARD_MOD;

/**
 SFR used for 3D types to enable automatic clock selection.
 Not yet documented in TOKEN DS, but for PLUS and SRX.
 */
#ifdef PLATFORM_TOKEN
static volatile union SFR_byte chess_storage(DM9:0x2B)   I3DCON;
#endif

/*-----------------------------------------------------------------------------------------------*/

/**
 * Functions to access the CRC unit from outside the IIU (used by HTPro).
 *
 * @param[in] data Input data for the CRC unit.
 */
static inline void phcaiKEyLLGenFunc_Ciu_WriteCRC8Data(uint8_t data);

/**
 *  Sets the CRCDAT register to a given value.
 *
 * @param[in] init_data Value to be written to the CRCDAT register.
 */
static inline void phcaiKEyLLGenFunc_Ciu_InitCRC8(uint8_t init_data);

/**
 * Reads data from the CRC unit.
 *
 * @return The current value of the CRCDAT register.
 */
static inline uint8_t phcaiKEyLLGenFunc_Ciu_ReadCRC8(void);

/** Function to enable interrupt depending on USER or SYSTEM MODE  */
static inline void phcaiKEyLLGenFunc_Iiu_EnableIRQ(void);

/** Function to disable interrupt depending on USER or SYSTEM MODE  */
static inline void phcaiKEyLLGenFunc_Iiu_DisableIRQ(void);

/*-----------------------------------------------------------------------------------------------*/

static inline void phcaiKEyLLGenFunc_Ciu_WriteCRC8Data(uint8_t data)
{
  CRC8DIN.val = data;
}

static inline void phcaiKEyLLGenFunc_Ciu_InitCRC8(uint8_t init_data)
{
  CRCDAT.val     = init_data;
}

static inline uint8_t phcaiKEyLLGenFunc_Ciu_ReadCRC8(void)
{
  return CRCDAT.val;
}

static inline void phcaiKEyLLGenFunc_Iiu_EnableIRQ(void)
{
  INTEN1.val |= 0x01u;
}

static inline void phcaiKEyLLGenFunc_Iiu_DisableIRQ(void)
{
  INTEN1.val &= 0xFEU;
}


/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_LFClk_Timer0_Start( const uint16_t u16_cycles )
{
  /* Select LF Clock. */
  //CLKCON3.val = 0x03u;
  CLKCON3.bits.TMUX0C = 0x03u;
  /* Reset timer 0. */
  T0CON0.val = 0x02u;
  /* Select TMUX0, prescaler 2^0. */
  T0CON1.val = 0x20u;
  /* Set reload value to cycles. */
  T0RLD.val = u16_cycles;
  /* Clear interrupt flag. */
  INTCLR0.val = 0x08u;
  /* Start timer 0 in single shot mode. */
  T0CON0.val = 0x05u;
}

/*-----------------------------------------------------------------------------------------------*/

/* Stay in IDLE mode until IIU interrupt occurs. */
static void phcaiKEyLLGenFunc_Iiu_IdleUntilIRQ( void )
{
  while ((INTFLAG1.val & 0x01u) == 0x00u)
  {
    go_idle();
  }
}


/*--- Exported Functions ------------------------------------------------------------------------*/


void phcaiKEyLLGenFunc_Iiu_Reset( void )
{
  /* HT2Crypto disabled, bypassing */
  HTCON.val = 0x08u;
  /* IDLE */
  IIUSTATE.val = (uint8_t)IIUSTATE_IDLE;
  /* Standard or strong LF modulator */
  IIUCON0.val  = (uint8_t)e_mod_depth;

#ifdef PLATFORM_TOKEN
  /* Activate 3D-IMMO automatic clock selection. */
  I3DCON.val = 0x08u;
#endif

// prevent Lint warning, I3DCON "symbol not accessed"
#ifdef _lint
  P1INS.val = I3DCON.val;
#endif
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Iiu_Init( void )
{
  e_mod_depth = STANDARD_MOD;
  phcaiKEyLLGenFunc_Iiu_SetModMode(IIU_ENC_MANCHESTER);
  e_phcaiKEyLLGenFunc_Iiu_Equalizer      = STANDARD_EQ;
  u8_phcaiKEyLLGenFunc_Iiu_Equalizer_len = STANDARD_EQ_LEN;
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Iiu_SetEqualizer( phcaiKEyLLGenFunc_Iiu_EQ_t e_EqType )
{
  switch ( e_EqType )
  {
    case MODIFIED_EQ:
      e_phcaiKEyLLGenFunc_Iiu_Equalizer      = MODIFIED_EQ;
      u8_phcaiKEyLLGenFunc_Iiu_Equalizer_len = MODIFIED_EQ_LEN;
      break;

    case STANDARD_EQ:
    default:
      e_phcaiKEyLLGenFunc_Iiu_Equalizer      = STANDARD_EQ;
      u8_phcaiKEyLLGenFunc_Iiu_Equalizer_len = STANDARD_EQ_LEN;
      break;
  }
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Iiu_SetModMode(const phcaiKEyLLGenFunc_Iiu_Encoding_t e_encoding)
{
  /* Set IENC1 and IENC0 to 0 */
  IIUSTAT.val &= 0xE7u;
  switch ( e_encoding )
  {
  case IIU_ENC_PLAIN:
    /* IENC = 0x01u */
    IIUSTAT.val |= 0x08u;
    break;

  case IIU_ENC_MANCHESTER:
    /* IENC = 0x02u */
    IIUSTAT.val |= 0x10u;
    break;

  case IIU_ENC_CDP:
    /* IENC = 0x03u */
    IIUSTAT.val |= 0x18u;
    break;

  case IIU_ENC_OFF:
  default:
    /* IENC = 0x00u */
    break;
  }
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Iiu_SetModulation( phcaiKEyLLGenFunc_Modulation_t e_mod )
{
  e_mod_depth = (phcaiKEyLLGenFunc_Modulation_t) ( (uint8_t) e_mod & 0x20u );  /* apply only to bit 5 in IIUCON0 */
}

/*-----------------------------------------------------------------------------------------------*/

error_t phcaiKEyLLGenFunc_Iiu_ReceiveData(
  uint8_t * const   pu8_data,
  const     uint8_t u8_buffer_size,
  uint8_t * const   pu8_num_rcvd_bits )
{
  error_t result = SUCCESS;
  uint8_t u8_add_bits;
  uint8_t i = 0u;
  uint8_t u8_tmpdata;

  /* Wait until timer (t-wait, Bs) has expired. */
  phcaiKEyLLGenFunc_Timer0_Wait();

  *pu8_num_rcvd_bits = 0u;
  phcaiKEyLLGenFunc_Ciu_InitCRC8(0x00u);
  /* Enable demodulator (IIU_LFDEMEN=1), clear IIU_TXWAIT, Reset */
  IIUSTAT.val |= 0x64u;
  IIUCON0.val &= 0xE3u;
  /* wait 30 us for demodulator after reset */
  phcaiKEyLLGenFunc_timer0_delay_us( 30u );

#if defined(NCF29A1) || defined(NCF29A3) || defined(NCF2953)
  /* Activate 3D-IMMO automatic clock selection. */
  I3DCON.val = 0x08u;
#endif

  /* Reset IIU again to remove "Demod-Enable-Spike" */
  IIUSTAT.val |= 0x20u;
  /* RXWAIT */
  IIUSTATE.val = (uint8_t)IIUSTATE_RXWAIT;
  phcaiKEyLLGenFunc_Iiu_EnableIRQ();
  IIUCON1.val = 8u; // bit count
  phcaiKEyLLGenFunc_Iiu_IdleUntilIRQ();

  /* ITXWAIT set -> stop condition detected */
  while ((IIUSTAT.val & 0x04u) == 0x00u)
  {
    *pu8_num_rcvd_bits += 8u;
    if ((IIUCON1.val != 0x00u))
    {
      result = ERROR;
      while ((IIUSTAT.val & 0x04u) == 0x00u) /* wait until IIU_TXWAIT == 1 */
      {
      }
    }
    else
    {
      u8_tmpdata = IIUDAT.val;
      IIUCON1.val = 8u;
      //if (*pu8_num_rcvd_bits <= u8_buffer_size)
      if ( i < u8_buffer_size )
      {
        pu8_data[i] = u8_tmpdata;
      }
      phcaiKEyLLGenFunc_Ciu_WriteCRC8Data( u8_tmpdata );
      i++;
      phcaiKEyLLGenFunc_Iiu_IdleUntilIRQ();
    }
  }
  /* disable demdulator */
  IIUSTAT.val &= 0xBFu;

  *pu8_num_rcvd_bits += (8u - IIUCON1.val);
  //if (*pu8_num_rcvd_bits > u8_buffer_size)
  if ( i >= u8_buffer_size)
  {
    result = ERROR;
  }
  else
  {
    u8_add_bits = (*pu8_num_rcvd_bits) % 8u;
    if ((u8_add_bits) > 0x00u)
    {
      pu8_data[i] = IIUDAT.val & (u8arr_iiu_receive_mask[u8_add_bits]);
      phcaiKEyLLGenFunc_Ciu_WriteCRC8Data( pu8_data[i] );
    }
  }

  /* reset to 0 otherwise transmit is started immediatley */
  IIUCON1.val = 0u;

  phcaiKEyLLGenFunc_Iiu_DisableIRQ();

  return result;
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Iiu_SendData_CRC(
  const uint8_t* const pu8_data,
  const uint8_t  u8_num_bytes,
  const bool_t   e_WithCRC )
{
  uint8_t i;
  uint8_t u8_tmpdata;

  /* activate modulator, data out: so */
  IIUCON0.val |= 0x48u;
  /* not inverted */
  IIUCON0.val &= 0xEBu;

  phcaiKEyLLGenFunc_Iiu_EnableIRQ();

  /* transmit EQ or EQM */
  IIUDAT.val  = (uint8_t)e_phcaiKEyLLGenFunc_Iiu_Equalizer;
  IIUCON1.val = (uint8_t)u8_phcaiKEyLLGenFunc_Iiu_Equalizer_len;

  u8_tmpdata = pu8_data[0];
  phcaiKEyLLGenFunc_Iiu_IdleUntilIRQ();

  if (u8_num_bytes != 0u)
  {
    IIUDAT.val = u8_tmpdata;
    IIUCON1.val = 8u;
    phcaiKEyLLGenFunc_Ciu_WriteCRC8Data( u8_tmpdata );
    for ( i = 1u; i < u8_num_bytes; i++ )
    {
      u8_tmpdata = pu8_data[i];
      phcaiKEyLLGenFunc_Iiu_IdleUntilIRQ();
      IIUDAT.val = u8_tmpdata;
      IIUCON1.val = 8u;
      phcaiKEyLLGenFunc_Ciu_WriteCRC8Data( u8_tmpdata );
    }
    phcaiKEyLLGenFunc_Iiu_IdleUntilIRQ();
  }

  if ( TRUE == e_WithCRC )
  {
    /* get CRC byte and transmit */
    IIUDAT.val = phcaiKEyLLGenFunc_Ciu_ReadCRC8();
    IIUCON1.val = 8u;
    phcaiKEyLLGenFunc_Iiu_IdleUntilIRQ();
  }

  IIUCON1.val = 0x00u;
  /* Wait until Tx finished */
  while( (IIUSTATE_t)IIUSTATE.val != IIUSTATE_IDLE)
  {
    phcaiKEyLLGenFunc_Iiu_IdleUntilIRQ();
  }
  IIUCON1.val = 0x00u;

  phcaiKEyLLGenFunc_Iiu_DisableIRQ();

  /* Disable modulator, Data out: rxd */
  IIUCON0.val &= 0xA3U;

  /* Start Timer (t-wait, Bs) for 200us = 25 LF_CLOCK cycles.
   - 10 cycles for setup of timer. */
  phcaiKEyLLGenFunc_LFClk_Timer0_Start( 15u );
}


/*@}*/
/*@}*/

/* eof */
