
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:29 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i SysInit-c6cb69 mrk3

[
 -148 : __adr_Temp_Buf typ=int16_ bnd=m adro=20
    0 : void_WUP_Init typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : Temp_Buf typ=int8_ val=0t0 bnd=a sz=4 algn=1 stl=DM tref=__A4__uchar_DM
   21 : pollingid typ=int8_ val=4t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   22 : swversion typ=int8_ val=6t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   23 : g_b_InCriticalSection typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=bool_t_DM9
   24 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   25 : PRESWUP0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESWUP0_t_DM9
   26 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   27 : __extDM_SFR_PRESWUP0_t typ=int8_ bnd=b stl=DM
   28 : PRESWUP1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESWUP1_t_DM9
   29 : __extDM_SFR_PRESWUP1_t typ=int8_ bnd=b stl=DM
   30 : PRESWUP2 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESWUP2_t_DM9
   31 : __extDM_SFR_word typ=int8_ bnd=b stl=DM
   32 : P1WRES typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P1WRES_t_DM9
   33 : __extDM_SFR_P1WRES_t typ=int8_ bnd=b stl=DM
   34 : P2WRES typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_P2WRES_t_DM9
   35 : __extDM_SFR_P2WRES_t typ=int8_ bnd=b stl=DM
   36 : RTCCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_RTCCON_t_DM9
   37 : __extDM_SFR_RTCCON_t typ=int8_ bnd=b stl=DM
   38 : LFTUNEVBAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_LFTUNEVBAT_t_DM9
   39 : __extDM_SFR_LFTUNEVBAT_t typ=int8_ bnd=b stl=DM
   40 : PRECON2 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON2_t_DM9
   41 : __extDM_SFR_PRECON2_t typ=int8_ bnd=b stl=DM
   42 : PRECON3 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON3_t_DM9
   43 : __extDM_SFR_PRECON3_t typ=int8_ bnd=b stl=DM
   44 : PRECON4 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON4_t_DM9
   45 : __extDM_SFR_PRECON4_t typ=int8_ bnd=b stl=DM
   46 : PRECON5 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON5_t_DM9
   47 : __extDM_SFR_PRECON5_t typ=int8_ bnd=b stl=DM
   48 : PRECON6 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON6_t_DM9
   49 : __extDM_SFR_PRECON6_t typ=int8_ bnd=b stl=DM
   50 : PRECON7 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON7_t_DM9
   51 : __extDM_SFR_PRECON7_t typ=int8_ bnd=b stl=DM
   52 : PRECON8 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON8_t_DM9
   53 : __extDM_SFR_PRECON8_t typ=int8_ bnd=b stl=DM
   54 : PRECON9 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON9_t_DM9
   55 : __extDM_SFR_PRECON9_t typ=int8_ bnd=b stl=DM
   56 : PRECON10 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON10_t_DM9
   57 : __extDM_SFR_PRECON10_t typ=int8_ bnd=b stl=DM
   58 : PRECON11 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON11_t_DM9
   59 : __extDM_SFR_PRECON11_t typ=int8_ bnd=b stl=DM
   60 : PREPD typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PREPD_t_DM9
   61 : __extDM_SFR_byte typ=int8_ bnd=b stl=DM
   62 : PRET typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRET_t_DM9
   63 : PRE3T typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRE3T_t_DM9
   64 : WUP1W0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP1W0_t_DM9
   65 : WUP1W1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP1W1_t_DM9
   66 : WUP2W0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP2W0_t_DM9
   67 : KEY_PID typ=int8_ bnd=e algn=1 stl=DM
   68 : WUP2W1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP2W1_t_DM9
   69 : KEY_ID typ=int8_ bnd=e algn=1 stl=DM
   70 : WUP3W0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP3W0_t_DM9
   71 : PRESTAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESTAT_t_DM9
   72 : __extDM_SFR_PRESTAT_t typ=int8_ bnd=b stl=DM
   73 : g_b_NMI_occurred typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=bool_t_DM9
   74 : PCON0 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PCON0_t_DM9
   75 : __extDM_SFR_PCON0_t typ=int8_ bnd=b stl=DM
   76 : __extPM_void typ=uint16_ bnd=b stl=PM
   77 : __extDM_void typ=int8_ bnd=b stl=DM
   78 : __extULP_void typ=uint32_ bnd=b stl=ULP
   81 : __ptr_g_b_InCriticalSection typ=int16_ val=0a bnd=m adro=23
   83 : __ptr_PRESWUP0 typ=int16_ val=0a bnd=m adro=25
   85 : __ptr_PRESWUP1 typ=int16_ val=0a bnd=m adro=28
   87 : __ptr_PRESWUP2 typ=int16_ val=0a bnd=m adro=30
   89 : __ptr_P1WRES typ=int16_ val=0a bnd=m adro=32
   91 : __ptr_P2WRES typ=int16_ val=0a bnd=m adro=34
   93 : __ptr_RTCCON typ=int16_ val=0a bnd=m adro=36
   95 : __ptr_LFTUNEVBAT typ=int16_ val=0a bnd=m adro=38
   97 : __ptr_PRECON2 typ=int16_ val=0a bnd=m adro=40
   99 : __ptr_PRECON3 typ=int16_ val=0a bnd=m adro=42
  101 : __ptr_PRECON4 typ=int16_ val=0a bnd=m adro=44
  103 : __ptr_PRECON5 typ=int16_ val=0a bnd=m adro=46
  105 : __ptr_PRECON6 typ=int16_ val=0a bnd=m adro=48
  107 : __ptr_PRECON7 typ=int16_ val=0a bnd=m adro=50
  109 : __ptr_PRECON8 typ=int16_ val=0a bnd=m adro=52
  111 : __ptr_PRECON9 typ=int16_ val=0a bnd=m adro=54
  113 : __ptr_PRECON10 typ=int16_ val=0a bnd=m adro=56
  115 : __ptr_PRECON11 typ=int16_ val=0a bnd=m adro=58
  117 : __ptr_PREPD typ=int16_ val=0a bnd=m adro=60
  119 : __ptr_PRET typ=int16_ val=0a bnd=m adro=62
  121 : __ptr_PRE3T typ=int16_ val=0a bnd=m adro=63
  123 : __ptr_WUP1W0 typ=int16_ val=0a bnd=m adro=64
  125 : __ptr_WUP1W1 typ=int16_ val=0a bnd=m adro=65
  127 : __ptr_WUP2W0 typ=int16_ val=0a bnd=m adro=66
  129 : __ptr_KEY_PID typ=int16_ val=0a bnd=m adro=67
  131 : __ptr_WUP2W1 typ=int16_ val=0a bnd=m adro=68
  133 : __ptr_KEY_ID typ=int16_ val=0a bnd=m adro=69
  135 : __ptr_WUP3W0 typ=int16_ val=0a bnd=m adro=70
  137 : __ptr_PRESTAT typ=int16_ val=0a bnd=m adro=71
  139 : __ptr_g_b_NMI_occurred typ=int16_ val=0a bnd=m adro=73
  141 : __ptr_PCON0 typ=int16_ val=0a bnd=m adro=74
  146 : __ct_0t0 typ=int16_ val=0t0 bnd=m
  148 : __adr_Temp_Buf typ=int16_ bnd=m adro=20
  150 : __ct_4t0 typ=int16_ val=4t0 bnd=m
  154 : __ct_6t0 typ=int16_ val=6t0 bnd=m
  186 : __ct_17480 typ=uint16_1_32768_ val=17480f bnd=m
  191 : __ct_50244 typ=int16_ val=-15292f bnd=m
  196 : __ct_2184 typ=uint16_1_32768_ val=2184f bnd=m
  216 : __ct_273 typ=uint16_1_32768_ val=273f bnd=m
  276 : __ct_7440 typ=uint16_1_32768_ val=7440f bnd=m
  281 : __ct_24632 typ=uint16_1_32768_ val=24632f bnd=m
  286 : __ct_17503 typ=uint16_1_32768_ val=17503f bnd=m
  291 : __ct_8755 typ=uint16_1_32768_ val=8755f bnd=m
  299 : __fch_KEY_PID typ=int8_ bnd=m
  320 : __fch_KEY_PID typ=int8_ bnd=m
  330 : __fch_KEY_PID typ=int8_ bnd=m
  340 : __fch_KEY_PID typ=int8_ bnd=m
  350 : __fch_KEY_ID typ=int8_ bnd=m
  371 : __fch_KEY_ID typ=int8_ bnd=m
  381 : __fch_KEY_ID typ=int8_ bnd=m
  391 : __fch_KEY_ID typ=int8_ bnd=m
  398 : __ct_26 typ=uint16_1_32768_ val=26f bnd=m
  400 : __ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort typ=int16_ val=0r bnd=m
  402 : __tmp typ=int16_ bnd=m
  420 : __fch_pollingid typ=int16_ bnd=m
  468 : __ct_14 typ=uint16_1_32768_ val=14f bnd=m
  472 : __tmp typ=int16_ bnd=m
  479 : __ct_3 typ=uint16_1_32768_ val=3f bnd=m
  481 : void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort typ=int16_ val=0r bnd=m
  483 : __ct_4 typ=uint16_1_32768_ val=4f bnd=m
  485 : void_timer_delay_ms___ushort typ=int16_ val=0r bnd=m
  499 : __ct_3 typ=uint16_1_32768_ val=3f bnd=m
  501 : error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort typ=int16_ val=0r bnd=m
  503 : __tmp typ=int8_ bnd=m
  504 : __ct_4 typ=uint16_1_32768_ val=4f bnd=m
  533 : __ct_8s0 typ=int16_ val=8s0 bnd=m
  558 : __tmp typ=int8_ bnd=m
  561 : __tmp typ=int8_ bnd=m
  565 : __tmp typ=int8_ bnd=m
  568 : __tmp typ=int8_ bnd=m
  573 : __tmp typ=int8_ bnd=m
  574 : __tmp typ=int8_ bnd=m
  575 : __fch_pollingid typ=int8_ bnd=m
  586 : __ct_8s0 typ=int16_ val=8s0 bnd=m
  616 : __ptr_KEY_PID__a3 typ=int16_ val=3a bnd=m adro=67
  617 : __ptr_KEY_ID__a3 typ=int16_ val=3a bnd=m adro=69
  618 : __ptr_WUP1W0__a1 typ=int16_ val=1a bnd=m adro=64
  619 : __ptr_WUP1W1__a1 typ=int16_ val=1a bnd=m adro=65
  620 : __ptr_WUP2W0__a1 typ=int16_ val=1a bnd=m adro=66
  621 : __ptr_KEY_PID__a2 typ=int16_ val=2a bnd=m adro=67
  622 : __ptr_KEY_PID__a1 typ=int16_ val=1a bnd=m adro=67
  623 : __ptr_WUP2W1__a1 typ=int16_ val=1a bnd=m adro=68
  624 : __ptr_KEY_ID__a2 typ=int16_ val=2a bnd=m adro=69
  625 : __ptr_KEY_ID__a1 typ=int16_ val=1a bnd=m adro=69
  626 : __ptr_WUP3W0__a1 typ=int16_ val=1a bnd=m adro=70
  627 : __ct_1t0 typ=int16_ val=1t0 bnd=m
  629 : __ct_2t0 typ=int16_ val=2t0 bnd=m
  631 : __ct_3t0 typ=int16_ val=3t0 bnd=m
  710 : __rt typ=int8_ bnd=m tref=__uchar__
  731 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  746 : __either typ=bool bnd=m
  747 : __trgt typ=rel8_ val=19j bnd=m
  749 : __trgt typ=rel8_ val=4j bnd=m
  751 : __seff typ=any bnd=m
  752 : __seff typ=any bnd=m
  753 : __seff typ=any bnd=m
  754 : __seff typ=any bnd=m
  755 : __seff typ=any bnd=m
  756 : __seff typ=any bnd=m
  757 : __seff typ=any bnd=m
  758 : __seff typ=any bnd=m
  759 : __seff typ=any bnd=m
  760 : __seff typ=any bnd=m
  761 : __seff typ=any bnd=m
  762 : __seff typ=any bnd=m
  763 : __seff typ=any bnd=m
  764 : __seff typ=any bnd=m
  765 : __seff typ=any bnd=m
  766 : __seff typ=any bnd=m
  767 : __seff typ=any bnd=m
  768 : __seff typ=any bnd=m
  769 : __seff typ=any bnd=m
  770 : __seff typ=any bnd=m
  771 : __seff typ=any bnd=m
  772 : __seff typ=any bnd=m
  773 : __seff typ=any bnd=m
  774 : __seff typ=any bnd=m
  775 : __seff typ=any bnd=m
  780 : __side_effect typ=any bnd=m
]
Fvoid_WUP_Init {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (Temp_Buf.19 var=20) source ()  <30>;
    (pollingid.20 var=21) source ()  <31>;
    (swversion.21 var=22) source ()  <32>;
    (g_b_InCriticalSection.22 var=23) source ()  <33>;
    (__extDM_int8_.23 var=24) source ()  <34>;
    (PRESWUP0.24 var=25) source ()  <35>;
    (__extDM_int16_.25 var=26) source ()  <36>;
    (__extDM_SFR_PRESWUP0_t.26 var=27) source ()  <37>;
    (PRESWUP1.27 var=28) source ()  <38>;
    (__extDM_SFR_PRESWUP1_t.28 var=29) source ()  <39>;
    (PRESWUP2.29 var=30) source ()  <40>;
    (__extDM_SFR_word.30 var=31) source ()  <41>;
    (P1WRES.31 var=32) source ()  <42>;
    (__extDM_SFR_P1WRES_t.32 var=33) source ()  <43>;
    (P2WRES.33 var=34) source ()  <44>;
    (__extDM_SFR_P2WRES_t.34 var=35) source ()  <45>;
    (RTCCON.35 var=36) source ()  <46>;
    (__extDM_SFR_RTCCON_t.36 var=37) source ()  <47>;
    (LFTUNEVBAT.37 var=38) source ()  <48>;
    (__extDM_SFR_LFTUNEVBAT_t.38 var=39) source ()  <49>;
    (PRECON2.39 var=40) source ()  <50>;
    (__extDM_SFR_PRECON2_t.40 var=41) source ()  <51>;
    (PRECON3.41 var=42) source ()  <52>;
    (__extDM_SFR_PRECON3_t.42 var=43) source ()  <53>;
    (PRECON4.43 var=44) source ()  <54>;
    (__extDM_SFR_PRECON4_t.44 var=45) source ()  <55>;
    (PRECON5.45 var=46) source ()  <56>;
    (__extDM_SFR_PRECON5_t.46 var=47) source ()  <57>;
    (PRECON6.47 var=48) source ()  <58>;
    (__extDM_SFR_PRECON6_t.48 var=49) source ()  <59>;
    (PRECON7.49 var=50) source ()  <60>;
    (__extDM_SFR_PRECON7_t.50 var=51) source ()  <61>;
    (PRECON8.51 var=52) source ()  <62>;
    (__extDM_SFR_PRECON8_t.52 var=53) source ()  <63>;
    (PRECON9.53 var=54) source ()  <64>;
    (__extDM_SFR_PRECON9_t.54 var=55) source ()  <65>;
    (PRECON10.55 var=56) source ()  <66>;
    (__extDM_SFR_PRECON10_t.56 var=57) source ()  <67>;
    (PRECON11.57 var=58) source ()  <68>;
    (__extDM_SFR_PRECON11_t.58 var=59) source ()  <69>;
    (PREPD.59 var=60) source ()  <70>;
    (__extDM_SFR_byte.60 var=61) source ()  <71>;
    (PRET.61 var=62) source ()  <72>;
    (PRE3T.62 var=63) source ()  <73>;
    (WUP1W0.63 var=64) source ()  <74>;
    (WUP1W1.64 var=65) source ()  <75>;
    (WUP2W0.65 var=66) source ()  <76>;
    (KEY_PID.66 var=67) source ()  <77>;
    (WUP2W1.67 var=68) source ()  <78>;
    (KEY_ID.68 var=69) source ()  <79>;
    (WUP3W0.69 var=70) source ()  <80>;
    (PRESTAT.70 var=71) source ()  <81>;
    (__extDM_SFR_PRESTAT_t.71 var=72) source ()  <82>;
    (g_b_NMI_occurred.72 var=73) source ()  <83>;
    (PCON0.73 var=74) source ()  <84>;
    (__extDM_SFR_PCON0_t.74 var=75) source ()  <85>;
    (__extPM_void.75 var=76) source ()  <86>;
    (__extDM_void.76 var=77) source ()  <87>;
    (__extULP_void.77 var=78) source ()  <88>;
    (__ct_0t0.2029 var=146) const_inp ()  <2046>;
    (__ct_4t0.2030 var=150) const_inp ()  <2047>;
    (__ct_8s0.2037 var=586) const_inp ()  <2054>;
    <402> {
      (__sp.146 var=19 __seff.2242 var=773 stl=c_flag_w __seff.2243 var=774 stl=nz_flag_w __seff.2244 var=775 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_8s0.2037 __sp.18 __sp.18)  <2264>;
      (__seff.2318 var=774 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2243)  <2511>;
      (__seff.2361 var=773 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2242)  <2553>;
      (__seff.2362 var=775 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2244)  <2554>;
    } stp=0;
    <404> {
      (Temp_Buf.171 var=20) store_const_4_B2 (__adr_Temp_Buf.2391 Temp_Buf.19)  <2266>;
      (__adr_Temp_Buf.2391 var=-148 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_Temp_Buf.2392)  <2574>;
    } stp=3;
    <491> {
      (__adr_Temp_Buf.2393 var=-148 stl=a_w2 __side_effect.2394 var=780 stl=c_flag_w __side_effect.2396 var=780 stl=nz_flag_w __side_effect.2398 var=780 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_0t0.2029 __sp.146)  <2435>;
      (__adr_Temp_Buf.2392 var=-148 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_Temp_Buf.2393)  <2575>;
      (__side_effect.2395 var=780 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__side_effect.2394)  <2576>;
      (__side_effect.2397 var=780 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2396)  <2577>;
      (__side_effect.2399 var=780 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2398)  <2578>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <182>;
    } #4 off=4 nxt=5
    #5 off=4 nxt=6
    (__ct_1t0.2049 var=627) const_inp ()  <2066>;
    <401> {
      (Temp_Buf.178 var=20) store_const__pl_rd_res_reg_const_1_B3 (__ct_1t0.2049 Temp_Buf.171 __sp.146)  <2263>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <189>;
    } #6 off=5 nxt=7
    #7 off=5 nxt=8
    (__ct_2t0.2050 var=629) const_inp ()  <2067>;
    <400> {
      (Temp_Buf.185 var=20) store_const__pl_rd_res_reg_const_1_B3 (__ct_2t0.2050 Temp_Buf.178 __sp.146)  <2262>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <196>;
    } #8 off=6 nxt=9
    #9 off=6 nxt=11
    (__ct_3t0.2051 var=631) const_inp ()  <2068>;
    <399> {
      (Temp_Buf.192 var=20) store_const__pl_rd_res_reg_const_1_B3 (__ct_3t0.2051 Temp_Buf.185 __sp.146)  <2261>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <204>;
    } #11 off=7 nxt=12
    #12 off=7 nxt=13
    <398> {
      (pollingid.196 var=21) _pl_rd_res_reg_const_store_const_1_B3 (__ct_4t0.2030 pollingid.20 __sp.146)  <2260>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <208>;
    } #13 off=8 nxt=14
    #14 off=8 nxt=15
    (__ct_6t0.2031 var=154) const_inp ()  <2048>;
    <397> {
      (swversion.200 var=22) _pl_rd_res_reg_const_store_const_1_B3 (__ct_6t0.2031 swversion.21 __sp.146)  <2259>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <212>;
    } #15 off=9 nxt=16
    #16 off=9 nxt=17
    (__ptr_g_b_InCriticalSection.1998 var=81) const_inp ()  <2015>;
    <396> {
      (g_b_InCriticalSection.204 var=23) store_const_const_11_B1 (__ptr_g_b_InCriticalSection.1998 g_b_InCriticalSection.22)  <2258>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <216>;
    } #17 off=11 nxt=18
    #18 off=11 nxt=19
    (__ptr_PRESWUP0.1999 var=83) const_inp ()  <2016>;
    <394> {
      (PRESWUP0.211 var=25 __vola.212 var=13) store_const_2_B2 (__ct_17480.2400 __ptr_PRESWUP0.1999 PRESWUP0.24 __vola.12)  <2256>;
      (__ct_17480.2400 var=186 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_17480.2401)  <2579>;
    } stp=2;
    <495> {
      (__ct_17480.2402 var=186 stl=__CTa_w0_uint16__cstP16_E1) const_12_B1 ()  <2444>;
      (__ct_17480.2401 var=186 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_17480.2402)  <2580>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <223>;
    } #19 off=14 nxt=20
    #20 off=14 nxt=21
    (__ptr_PRESWUP1.2000 var=85) const_inp ()  <2017>;
    <392> {
      (PRESWUP1.219 var=28 __vola.220 var=13) store_const_3_B2 (__ct_50244.2403 __ptr_PRESWUP1.2000 PRESWUP1.27 __vola.212)  <2254>;
      (__ct_50244.2403 var=191 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__ct_50244.2404)  <2581>;
    } stp=2;
    <496> {
      (__ct_50244.2405 var=191 stl=__CTa_w0_uint16__cstP16_E1) const_11_B1 ()  <2447>;
      (__ct_50244.2404 var=191 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ct_50244.2405)  <2582>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <230>;
    } #21 off=17 nxt=22
    #22 off=17 nxt=23
    (__ptr_PRESWUP2.2001 var=87) const_inp ()  <2018>;
    <390> {
      (PRESWUP2.227 var=30 __vola.228 var=13) store_const_2_B2 (__ct_2184.2406 __ptr_PRESWUP2.2001 PRESWUP2.29 __vola.220)  <2252>;
      (__ct_2184.2406 var=196 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_2184.2407)  <2583>;
    } stp=2;
    <497> {
      (__ct_2184.2408 var=196 stl=__CTa_w0_uint16__cstP16_E1) const_10_B1 ()  <2450>;
      (__ct_2184.2407 var=196 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_2184.2408)  <2584>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <237>;
    } #23 off=20 nxt=24
    #24 off=20 nxt=25
    (__ptr_P1WRES.2002 var=89) const_inp ()  <2019>;
    <389> {
      (P1WRES.235 var=32 __vola.236 var=13) store_const_const_6_B1 (__ptr_P1WRES.2002 P1WRES.31 __vola.228)  <2251>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <244>;
    } #25 off=22 nxt=26
    #26 off=22 nxt=27
    (__ptr_P2WRES.2003 var=91) const_inp ()  <2020>;
    <388> {
      (P2WRES.243 var=34 __vola.244 var=13) store_const_const_10_B1 (__ptr_P2WRES.2003 P2WRES.33 __vola.236)  <2250>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <251>;
    } #27 off=24 nxt=28
    #28 off=24 nxt=29
    (__ptr_RTCCON.2004 var=93) const_inp ()  <2021>;
    <387> {
      (RTCCON.251 var=36 __vola.252 var=13) store_const_const_9_B1 (__ptr_RTCCON.2004 RTCCON.35 __vola.244)  <2249>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <258>;
    } #29 off=26 nxt=30
    #30 off=26 nxt=31
    (__ptr_LFTUNEVBAT.2005 var=95) const_inp ()  <2022>;
    <385> {
      (LFTUNEVBAT.259 var=38 __vola.260 var=13) store_const_2_B2 (__ct_273.2409 __ptr_LFTUNEVBAT.2005 LFTUNEVBAT.37 __vola.252)  <2247>;
      (__ct_273.2409 var=216 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_273.2410)  <2585>;
    } stp=2;
    <498> {
      (__ct_273.2411 var=216 stl=__CTa_w0_uint16__cstP16_E1) const_9_B1 ()  <2453>;
      (__ct_273.2410 var=216 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_273.2411)  <2586>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <265>;
    } #31 off=29 nxt=32
    #32 off=29 nxt=33
    (__ptr_PRECON2.2006 var=97) const_inp ()  <2023>;
    <384> {
      (PRECON2.267 var=40 __vola.268 var=13) store_const_const_8_B1 (__ptr_PRECON2.2006 PRECON2.39 __vola.260)  <2246>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <272>;
    } #33 off=31 nxt=34
    #34 off=31 nxt=35
    (__ptr_PRECON3.2007 var=99) const_inp ()  <2024>;
    <383> {
      (PRECON3.275 var=42 __vola.276 var=13) store_const_const_3_B1 (__ptr_PRECON3.2007 PRECON3.41 __vola.268)  <2245>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <279>;
    } #35 off=33 nxt=36
    #36 off=33 nxt=37
    (__ptr_PRECON4.2008 var=101) const_inp ()  <2025>;
    <382> {
      (PRECON4.283 var=44 __vola.284 var=13) store_const_const_3_B1 (__ptr_PRECON4.2008 PRECON4.43 __vola.276)  <2244>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <286>;
    } #37 off=35 nxt=38
    #38 off=35 nxt=39
    (__ptr_PRECON5.2009 var=103) const_inp ()  <2026>;
    <381> {
      (PRECON5.291 var=46 __vola.292 var=13) store_const_const_3_B1 (__ptr_PRECON5.2009 PRECON5.45 __vola.284)  <2243>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <293>;
    } #39 off=37 nxt=40
    #40 off=37 nxt=41
    (__ptr_PRECON6.2010 var=105) const_inp ()  <2027>;
    <380> {
      (PRECON6.299 var=48 __vola.300 var=13) store_const_const_7_B1 (__ptr_PRECON6.2010 PRECON6.47 __vola.292)  <2242>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <300>;
    } #41 off=39 nxt=42
    #42 off=39 nxt=43
    (__ptr_PRECON7.2011 var=107) const_inp ()  <2028>;
    <379> {
      (PRECON7.307 var=50 __vola.308 var=13) store_const_const_6_B1 (__ptr_PRECON7.2011 PRECON7.49 __vola.300)  <2241>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <307>;
    } #43 off=41 nxt=44
    #44 off=41 nxt=45
    (__ptr_PRECON8.2012 var=109) const_inp ()  <2029>;
    <378> {
      (PRECON8.315 var=52 __vola.316 var=13) store_const_const_5_B1 (__ptr_PRECON8.2012 PRECON8.51 __vola.308)  <2240>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <314>;
    } #45 off=43 nxt=46
    #46 off=43 nxt=47
    (__ptr_PRECON9.2013 var=111) const_inp ()  <2030>;
    <377> {
      (PRECON9.323 var=54 __vola.324 var=13) store_const_const_5_B1 (__ptr_PRECON9.2013 PRECON9.53 __vola.316)  <2239>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <321>;
    } #47 off=45 nxt=48
    #48 off=45 nxt=49
    (__ptr_PRECON10.2014 var=113) const_inp ()  <2031>;
    <376> {
      (PRECON10.331 var=56 __vola.332 var=13) store_const_const_3_B1 (__ptr_PRECON10.2014 PRECON10.55 __vola.324)  <2238>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <328>;
    } #49 off=47 nxt=50
    #50 off=47 nxt=51
    (__ptr_PRECON11.2015 var=115) const_inp ()  <2032>;
    <375> {
      (PRECON11.339 var=58 __vola.340 var=13) store_const_const_4_B1 (__ptr_PRECON11.2015 PRECON11.57 __vola.332)  <2237>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <335>;
    } #51 off=49 nxt=52
    #52 off=49 nxt=53
    (__ptr_PREPD.2016 var=117) const_inp ()  <2033>;
    <374> {
      (PREPD.347 var=60 __vola.348 var=13) store_const_const_3_B1 (__ptr_PREPD.2016 PREPD.59 __vola.340)  <2236>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <342>;
    } #53 off=51 nxt=54
    #54 off=51 nxt=55
    (__ptr_PRET.2017 var=119) const_inp ()  <2034>;
    <372> {
      (PRET.355 var=62 __vola.356 var=13) store_const_2_B2 (__ct_7440.2412 __ptr_PRET.2017 PRET.61 __vola.348)  <2234>;
      (__ct_7440.2412 var=276 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_7440.2413)  <2587>;
    } stp=2;
    <499> {
      (__ct_7440.2414 var=276 stl=__CTa_w0_uint16__cstP16_E1) const_8_B1 ()  <2456>;
      (__ct_7440.2413 var=276 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_7440.2414)  <2588>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <349>;
    } #55 off=54 nxt=56
    #56 off=54 nxt=57
    (__ptr_PRE3T.2018 var=121) const_inp ()  <2035>;
    <370> {
      (PRE3T.363 var=63 __vola.364 var=13) store_const_2_B2 (__ct_24632.2415 __ptr_PRE3T.2018 PRE3T.62 __vola.356)  <2232>;
      (__ct_24632.2415 var=281 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_24632.2416)  <2589>;
    } stp=2;
    <500> {
      (__ct_24632.2417 var=281 stl=__CTa_w0_uint16__cstP16_E1) const_7_B1 ()  <2459>;
      (__ct_24632.2416 var=281 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_24632.2417)  <2590>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <356>;
    } #57 off=57 nxt=58
    #58 off=57 nxt=59
    (__ptr_WUP1W0.2019 var=123) const_inp ()  <2036>;
    <368> {
      (WUP1W0.371 var=64 __vola.372 var=13) store_const_2_B2 (__ct_17503.2418 __ptr_WUP1W0.2019 WUP1W0.63 __vola.364)  <2230>;
      (__ct_17503.2418 var=286 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_17503.2419)  <2591>;
    } stp=2;
    <501> {
      (__ct_17503.2420 var=286 stl=__CTa_w0_uint16__cstP16_E1) const_6_B1 ()  <2462>;
      (__ct_17503.2419 var=286 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_17503.2420)  <2592>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <363>;
    } #59 off=60 nxt=60
    #60 off=60 nxt=61
    (__ptr_WUP1W1.2020 var=125) const_inp ()  <2037>;
    <366> {
      (WUP1W1.379 var=65 __vola.380 var=13) store_const_2_B2 (__ct_8755.2421 __ptr_WUP1W1.2020 WUP1W1.64 __vola.372)  <2228>;
      (__ct_8755.2421 var=291 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_8755.2422)  <2593>;
    } stp=2;
    <502> {
      (__ct_8755.2423 var=291 stl=__CTa_w0_uint16__cstP16_E1) const_5_B1 ()  <2465>;
      (__ct_8755.2422 var=291 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_8755.2423)  <2594>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <370>;
    } #61 off=63 nxt=212
    #212 off=63 nxt=63
    (__ptr_WUP2W0.2021 var=127) const_inp ()  <2038>;
    (__ptr_KEY_PID__a3.2038 var=616) const_inp ()  <2055>;
    <361> {
      (__fch_KEY_PID.384 var=299 stl=DM_r) load_const_1_B1 (__ptr_KEY_PID__a3.2038 KEY_PID.66)  <2223>;
      (__fch_KEY_PID.2338 var=299 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_PID.384)  <2531>;
    } stp=0;
    <362> {
      (__tmp.388 var=565 stl=a_b2 __seff.2175 var=769 stl=nz_flag_w) _ad_const_1_B1 (__fch_KEY_PID.2337)  <2224>;
      (__seff.2317 var=769 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2175)  <2510>;
      (__fch_KEY_PID.2337 var=299 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__fch_KEY_PID.2338)  <2530>;
      (__tmp.2340 var=565 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.388)  <2533>;
    } stp=2;
    <363> {
      (__rt.1939 var=710 stl=a_b2 __seff.2177 var=770 stl=c_flag_w __seff.2178 var=771 stl=nz_flag_w) shl_const_1_B1 (__tmp.2339)  <2225>;
      (__seff.2316 var=771 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2178)  <2509>;
      (__tmp.2339 var=565 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__tmp.2340)  <2532>;
      (__seff.2341 var=770 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2177)  <2534>;
      (__rt.2343 var=710 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__rt.1939)  <2536>;
    } stp=4;
    <364> {
      (__tmp.394 var=558 stl=a_b2 __seff.2180 var=772 stl=nz_flag_w) _or_const_1_B1 (__rt.2342)  <2226>;
      (__seff.2315 var=772 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2180)  <2508>;
      (__rt.2342 var=710 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__rt.2343)  <2535>;
      (__tmp.2345 var=558 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.394)  <2538>;
    } stp=5;
    <365> {
      (WUP2W0.403 var=66 __vola.404 var=13) store_const_1_B2 (__tmp.2344 __ptr_WUP2W0.2021 WUP2W0.65 __vola.380)  <2227>;
      (__tmp.2344 var=558 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.2345)  <2537>;
    } stp=7;
    call {
        () chess_separator_scheduler ()  <393>;
    } #63 off=71 nxt=64
    #64 off=71 nxt=65
    (__ptr_WUP2W0__a1.2042 var=620) const_inp ()  <2059>;
    (__ptr_KEY_PID__a2.2043 var=621) const_inp ()  <2060>;
    <359> {
      (__fch_KEY_PID.408 var=320 stl=DM_r) load_const_1_B1 (__ptr_KEY_PID__a2.2043 KEY_PID.66)  <2221>;
      (__fch_KEY_PID.2347 var=320 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_PID.408)  <2540>;
    } stp=0;
    <360> {
      (WUP2W0.416 var=66 __vola.417 var=13) store_const_1_B2 (__fch_KEY_PID.2346 __ptr_WUP2W0__a1.2042 WUP2W0.403 __vola.404)  <2222>;
      (__fch_KEY_PID.2346 var=320 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_PID.2347)  <2539>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <405>;
    } #65 off=74 nxt=66
    #66 off=74 nxt=67
    (__ptr_WUP2W1.2023 var=131) const_inp ()  <2040>;
    (__ptr_KEY_PID__a1.2044 var=622) const_inp ()  <2061>;
    <357> {
      (__fch_KEY_PID.421 var=330 stl=DM_r) load_const_1_B1 (__ptr_KEY_PID__a1.2044 KEY_PID.66)  <2219>;
      (__fch_KEY_PID.2349 var=330 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_PID.421)  <2542>;
    } stp=0;
    <358> {
      (WUP2W1.429 var=68 __vola.430 var=13) store_const_1_B2 (__fch_KEY_PID.2348 __ptr_WUP2W1.2023 WUP2W1.67 __vola.417)  <2220>;
      (__fch_KEY_PID.2348 var=330 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_PID.2349)  <2541>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <417>;
    } #67 off=77 nxt=68
    #68 off=77 nxt=69
    (__ptr_KEY_PID.2022 var=129) const_inp ()  <2039>;
    (__ptr_WUP2W1__a1.2045 var=623) const_inp ()  <2062>;
    <355> {
      (__fch_KEY_PID.434 var=340 stl=DM_r) load_const_1_B1 (__ptr_KEY_PID.2022 KEY_PID.66)  <2217>;
      (__fch_KEY_PID.2351 var=340 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_PID.434)  <2544>;
    } stp=0;
    <356> {
      (WUP2W1.442 var=68 __vola.443 var=13) store_const_1_B2 (__fch_KEY_PID.2350 __ptr_WUP2W1__a1.2045 WUP2W1.429 __vola.430)  <2218>;
      (__fch_KEY_PID.2350 var=340 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_PID.2351)  <2543>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <429>;
    } #69 off=80 nxt=228
    #228 off=80 nxt=71
    (__ptr_KEY_ID__a3.2039 var=617) const_inp ()  <2056>;
    <350> {
      (__fch_KEY_ID.447 var=350 stl=DM_r) load_const_1_B1 (__ptr_KEY_ID__a3.2039 KEY_ID.68)  <2212>;
      (__fch_KEY_ID.2331 var=350 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_ID.447)  <2524>;
    } stp=0;
    <351> {
      (__tmp.451 var=568 stl=a_b2 __seff.2156 var=765 stl=nz_flag_w) _ad_const_1_B1 (__fch_KEY_ID.2330)  <2213>;
      (__seff.2314 var=765 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2156)  <2507>;
      (__fch_KEY_ID.2330 var=350 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__fch_KEY_ID.2331)  <2523>;
      (__tmp.2333 var=568 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.451)  <2526>;
    } stp=2;
    <352> {
      (__rt.1949 var=710 stl=a_b2 __seff.2158 var=766 stl=c_flag_w __seff.2159 var=767 stl=nz_flag_w) shl_const_1_B1 (__tmp.2332)  <2214>;
      (__seff.2313 var=767 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2159)  <2506>;
      (__tmp.2332 var=568 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__tmp.2333)  <2525>;
      (__seff.2334 var=766 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2158)  <2527>;
      (__rt.2336 var=710 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__rt.1949)  <2529>;
    } stp=4;
    <353> {
      (__tmp.457 var=561 stl=a_b2 __seff.2161 var=768 stl=nz_flag_w) _or_const_1_B1 (__rt.2335)  <2215>;
      (__seff.2312 var=768 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2161)  <2505>;
      (__rt.2335 var=710 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__rt.2336)  <2528>;
      (__tmp.2353 var=561 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.457)  <2546>;
    } stp=5;
    <354> {
      (WUP1W0.466 var=64 __vola.467 var=13) store_const_1_B2 (__tmp.2352 __ptr_WUP1W0.2019 WUP1W0.371 __vola.443)  <2216>;
      (__tmp.2352 var=561 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.2353)  <2545>;
    } stp=7;
    call {
        () chess_separator_scheduler ()  <452>;
    } #71 off=88 nxt=72
    #72 off=88 nxt=73
    (__ptr_WUP1W0__a1.2040 var=618) const_inp ()  <2057>;
    (__ptr_KEY_ID__a2.2046 var=624) const_inp ()  <2063>;
    <348> {
      (__fch_KEY_ID.471 var=371 stl=DM_r) load_const_1_B1 (__ptr_KEY_ID__a2.2046 KEY_ID.68)  <2210>;
      (__fch_KEY_ID.2355 var=371 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_ID.471)  <2548>;
    } stp=0;
    <349> {
      (WUP1W0.479 var=64 __vola.480 var=13) store_const_1_B2 (__fch_KEY_ID.2354 __ptr_WUP1W0__a1.2040 WUP1W0.466 __vola.467)  <2211>;
      (__fch_KEY_ID.2354 var=371 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_ID.2355)  <2547>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <464>;
    } #73 off=91 nxt=74
    #74 off=91 nxt=75
    (__ptr_KEY_ID__a1.2047 var=625) const_inp ()  <2064>;
    <346> {
      (__fch_KEY_ID.484 var=381 stl=DM_r) load_const_1_B1 (__ptr_KEY_ID__a1.2047 KEY_ID.68)  <2208>;
      (__fch_KEY_ID.2357 var=381 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_ID.484)  <2550>;
    } stp=0;
    <347> {
      (WUP1W1.492 var=65 __vola.493 var=13) store_const_1_B2 (__fch_KEY_ID.2356 __ptr_WUP1W1.2020 WUP1W1.379 __vola.480)  <2209>;
      (__fch_KEY_ID.2356 var=381 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_ID.2357)  <2549>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <476>;
    } #75 off=94 nxt=76
    #76 off=94 nxt=77
    (__ptr_KEY_ID.2024 var=133) const_inp ()  <2041>;
    (__ptr_WUP1W1__a1.2041 var=619) const_inp ()  <2058>;
    <344> {
      (__fch_KEY_ID.497 var=391 stl=DM_r) load_const_1_B1 (__ptr_KEY_ID.2024 KEY_ID.68)  <2206>;
      (__fch_KEY_ID.2359 var=391 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_ID.497)  <2552>;
    } stp=0;
    <345> {
      (WUP1W1.505 var=65 __vola.506 var=13) store_const_1_B2 (__fch_KEY_ID.2358 __ptr_WUP1W1__a1.2041 WUP1W1.492 __vola.493)  <2207>;
      (__fch_KEY_ID.2358 var=391 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_ID.2359)  <2551>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <488>;
    } #77 off=97 nxt=78
    #78 off=97 nxt=79
    (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.2032 var=400) const_inp ()  <2049>;
    <343> {
      () call_const_1_B1 (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.2032)  <2205>;
    } stp=2;
    <503> {
      (__ct_26.2425 var=398 stl=__CTa_w0_uint16__cstP16_E1) const_4_B1 ()  <2467>;
      (__ct_26.2424 var=398 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B0 (__ct_26.2425)  <2595>;
    } stp=0;
    call {
        (__tmp.512 var=402 stl=RwL off=0 KEY_ID.515 var=69 KEY_PID.516 var=67 LFTUNEVBAT.517 var=38 P1WRES.518 var=32 P2WRES.519 var=34 PCON0.520 var=74 PRE3T.521 var=63 PRECON10.522 var=56 PRECON11.523 var=58 PRECON2.524 var=40 PRECON3.525 var=42 PRECON4.526 var=44 PRECON5.527 var=46 PRECON6.528 var=48 PRECON7.529 var=50 PRECON8.530 var=52 PRECON9.531 var=54 PREPD.532 var=60 PRESTAT.533 var=71 PRESWUP0.534 var=25 PRESWUP1.535 var=28 PRESWUP2.536 var=30 PRET.537 var=62 RTCCON.538 var=36 WUP1W0.539 var=64 WUP1W1.540 var=65 WUP2W0.541 var=66 WUP2W1.542 var=68 WUP3W0.543 var=70 __extDM.544 var=17 __extDM_SFR_LFTUNEVBAT_t.545 var=39 __extDM_SFR_P1WRES_t.546 var=33 __extDM_SFR_P2WRES_t.547 var=35 __extDM_SFR_PCON0_t.548 var=75 __extDM_SFR_PRECON10_t.549 var=57 __extDM_SFR_PRECON11_t.550 var=59 __extDM_SFR_PRECON2_t.551 var=41 __extDM_SFR_PRECON3_t.552 var=43 __extDM_SFR_PRECON4_t.553 var=45 __extDM_SFR_PRECON5_t.554 var=47 __extDM_SFR_PRECON6_t.555 var=49 __extDM_SFR_PRECON7_t.556 var=51 __extDM_SFR_PRECON8_t.557 var=53 __extDM_SFR_PRECON9_t.558 var=55 __extDM_SFR_PRESTAT_t.559 var=72 __extDM_SFR_PRESWUP0_t.560 var=27 __extDM_SFR_PRESWUP1_t.561 var=29 __extDM_SFR_RTCCON_t.562 var=37 __extDM_SFR_byte.563 var=61 __extDM_SFR_word.564 var=31 __extDM_int16_.565 var=26 __extDM_int8_.566 var=24 __extDM_void.567 var=77 __extPM.568 var=16 __extPM_void.569 var=76 __extULP.570 var=18 __extULP_void.571 var=78 g_b_InCriticalSection.572 var=23 g_b_NMI_occurred.573 var=73 __vola.574 var=13) F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort (__ct_26.2424 KEY_ID.68 KEY_PID.66 LFTUNEVBAT.259 P1WRES.235 P2WRES.243 PCON0.73 PRE3T.363 PRECON10.331 PRECON11.339 PRECON2.267 PRECON3.275 PRECON4.283 PRECON5.291 PRECON6.299 PRECON7.307 PRECON8.315 PRECON9.323 PREPD.347 PRESTAT.70 PRESWUP0.211 PRESWUP1.219 PRESWUP2.227 PRET.355 RTCCON.251 WUP1W0.479 WUP1W1.505 WUP2W0.416 WUP2W1.442 WUP3W0.69 __extDM.16 __extDM_SFR_LFTUNEVBAT_t.38 __extDM_SFR_P1WRES_t.32 __extDM_SFR_P2WRES_t.34 __extDM_SFR_PCON0_t.74 __extDM_SFR_PRECON10_t.56 __extDM_SFR_PRECON11_t.58 __extDM_SFR_PRECON2_t.40 __extDM_SFR_PRECON3_t.42 __extDM_SFR_PRECON4_t.44 __extDM_SFR_PRECON5_t.46 __extDM_SFR_PRECON6_t.48 __extDM_SFR_PRECON7_t.50 __extDM_SFR_PRECON8_t.52 __extDM_SFR_PRECON9_t.54 __extDM_SFR_PRESTAT_t.71 __extDM_SFR_PRESWUP0_t.26 __extDM_SFR_PRESWUP1_t.28 __extDM_SFR_RTCCON_t.36 __extDM_SFR_byte.60 __extDM_SFR_word.30 __extDM_int16_.25 __extDM_int8_.23 __extDM_void.76 __extPM.15 __extPM_void.75 __extULP.17 __extULP_void.77 g_b_InCriticalSection.204 g_b_NMI_occurred.72 __vola.506)  <495>;
    } #79 off=101 nxt=80
    #80 off=101 nxt=81
    <341> {
      (pollingid.576 var=21) store__pl_rd_res_reg_const_1_B2 (__tmp.2364 __ct_4t0.2030 pollingid.196 __sp.146)  <2203>;
      (__tmp.2364 var=402 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__tmp.512)  <2555>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <499>;
    } #81 off=102 nxt=245
    #245 off=102 nxt=83
    (__ptr_WUP3W0.2025 var=135) const_inp ()  <2042>;
    <336> {
      (__fch_pollingid.1785 var=575 stl=a_b0) load__pl_rd_res_reg_const___uchar_1_B2 (__ct_4t0.2030 pollingid.576 __sp.146)  <2198>;
      (__fch_pollingid.2366 var=575 stl=RbL off=0) Rb_1_dr_move_a_b0_1_int8__B0 (__fch_pollingid.1785)  <2557>;
    } stp=0;
    <337> {
      (__tmp.580 var=574 stl=a_b2 __seff.2135 var=761 stl=nz_flag_w) _ad_const_1_B1 (__fch_pollingid.2365)  <2199>;
      (__seff.2323 var=761 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2135)  <2516>;
      (__fch_pollingid.2365 var=575 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__fch_pollingid.2366)  <2556>;
      (__tmp.2368 var=574 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.580)  <2559>;
    } stp=1;
    <338> {
      (__rt.1959 var=710 stl=a_b2 __seff.2137 var=762 stl=c_flag_w __seff.2138 var=763 stl=nz_flag_w) shl_const_1_B1 (__tmp.2367)  <2200>;
      (__seff.2322 var=763 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2138)  <2515>;
      (__tmp.2367 var=574 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__tmp.2368)  <2558>;
      (__seff.2369 var=762 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2137)  <2560>;
      (__rt.2371 var=710 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__rt.1959)  <2562>;
    } stp=3;
    <339> {
      (__tmp.586 var=573 stl=a_b2 __seff.2140 var=764 stl=nz_flag_w) _or_const_1_B1 (__rt.2370)  <2201>;
      (__seff.2321 var=764 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2140)  <2514>;
      (__rt.2370 var=710 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__rt.2371)  <2561>;
      (__tmp.2373 var=573 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.586)  <2564>;
    } stp=4;
    <340> {
      (WUP3W0.595 var=70 __vola.596 var=13) store_const_1_B2 (__tmp.2372 __ptr_WUP3W0.2025 WUP3W0.543 __vola.574)  <2202>;
      (__tmp.2372 var=573 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.2373)  <2563>;
    } stp=6;
    call {
        () chess_separator_scheduler ()  <518>;
    } #83 off=109 nxt=176
    #176 off=109 nxt=87
    (__ptr_WUP3W0__a1.2048 var=626) const_inp ()  <2065>;
    <334> {
      (__fch_pollingid.597 var=420 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_4t0.2030 pollingid.576 __sp.146)  <2196>;
      (__fch_pollingid.2375 var=420 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_pollingid.597)  <2566>;
    } stp=0;
    <335> {
      (WUP3W0.609 var=70 __vola.610 var=13) store_extract_hi_const_1_B2 (__fch_pollingid.2374 __ptr_WUP3W0__a1.2048 WUP3W0.595 __vola.596)  <2197>;
      (__fch_pollingid.2374 var=420 stl=__cvT4) __cvT4_1_dr_move_RwL_1_int16_ (__fch_pollingid.2375)  <2565>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <538>;
    } #87 off=111 nxt=183
    #183 off=111 nxt=91
    <333> {
      (PRECON2.627 var=40 __vola.628 var=13 __seff.2129 var=760 stl=nz_flag_w) load_const__ad_const_store_2_B1 (__ptr_PRECON2.2006 PRECON2.524 __vola.610)  <2195>;
      (__seff.2320 var=760 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2129)  <2513>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <557>;
    } #91 off=113 nxt=190
    #190 off=113 nxt=93
    <332> {
      (PRECON2.648 var=40 __vola.649 var=13 __seff.2126 var=759 stl=nz_flag_w) load_const__ad_const_store_1_B1 (__ptr_PRECON2.2006 PRECON2.627 __vola.628)  <2194>;
      (__seff.2319 var=759 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2126)  <2512>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <569>;
    } #93 off=115 nxt=94
    #94 off=115 nxt=95
    (__ptr_PRESTAT.2026 var=137) const_inp ()  <2043>;
    <331> {
      (PRESTAT.659 var=71 __vola.660 var=13) store_const_const_2_B1 (__ptr_PRESTAT.2026 PRESTAT.533 __vola.649)  <2193>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <576>;
    } #95 off=117 nxt=96
    #96 off=117 nxt=97
    <330> {
      () call_const_1_B1 (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.2032)  <2192>;
    } stp=2;
    <504> {
      (__ct_14.2427 var=468 stl=__CTa_w0_uint16__cstP16_E1) const_3_B1 ()  <2469>;
      (__ct_14.2426 var=468 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B0 (__ct_14.2427)  <2596>;
    } stp=0;
    call {
        (__tmp.666 var=472 stl=RwL off=0 KEY_ID.669 var=69 KEY_PID.670 var=67 LFTUNEVBAT.671 var=38 P1WRES.672 var=32 P2WRES.673 var=34 PCON0.674 var=74 PRE3T.675 var=63 PRECON10.676 var=56 PRECON11.677 var=58 PRECON2.678 var=40 PRECON3.679 var=42 PRECON4.680 var=44 PRECON5.681 var=46 PRECON6.682 var=48 PRECON7.683 var=50 PRECON8.684 var=52 PRECON9.685 var=54 PREPD.686 var=60 PRESTAT.687 var=71 PRESWUP0.688 var=25 PRESWUP1.689 var=28 PRESWUP2.690 var=30 PRET.691 var=62 RTCCON.692 var=36 WUP1W0.693 var=64 WUP1W1.694 var=65 WUP2W0.695 var=66 WUP2W1.696 var=68 WUP3W0.697 var=70 __extDM.698 var=17 __extDM_SFR_LFTUNEVBAT_t.699 var=39 __extDM_SFR_P1WRES_t.700 var=33 __extDM_SFR_P2WRES_t.701 var=35 __extDM_SFR_PCON0_t.702 var=75 __extDM_SFR_PRECON10_t.703 var=57 __extDM_SFR_PRECON11_t.704 var=59 __extDM_SFR_PRECON2_t.705 var=41 __extDM_SFR_PRECON3_t.706 var=43 __extDM_SFR_PRECON4_t.707 var=45 __extDM_SFR_PRECON5_t.708 var=47 __extDM_SFR_PRECON6_t.709 var=49 __extDM_SFR_PRECON7_t.710 var=51 __extDM_SFR_PRECON8_t.711 var=53 __extDM_SFR_PRECON9_t.712 var=55 __extDM_SFR_PRESTAT_t.713 var=72 __extDM_SFR_PRESWUP0_t.714 var=27 __extDM_SFR_PRESWUP1_t.715 var=29 __extDM_SFR_RTCCON_t.716 var=37 __extDM_SFR_byte.717 var=61 __extDM_SFR_word.718 var=31 __extDM_int16_.719 var=26 __extDM_int8_.720 var=24 __extDM_void.721 var=77 __extPM.722 var=16 __extPM_void.723 var=76 __extULP.724 var=18 __extULP_void.725 var=78 g_b_InCriticalSection.726 var=23 g_b_NMI_occurred.727 var=73 __vola.728 var=13) F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort (__ct_14.2426 KEY_ID.515 KEY_PID.516 LFTUNEVBAT.517 P1WRES.518 P2WRES.519 PCON0.520 PRE3T.521 PRECON10.522 PRECON11.523 PRECON2.648 PRECON3.525 PRECON4.526 PRECON5.527 PRECON6.528 PRECON7.529 PRECON8.530 PRECON9.531 PREPD.532 PRESTAT.659 PRESWUP0.534 PRESWUP1.535 PRESWUP2.536 PRET.537 RTCCON.538 WUP1W0.539 WUP1W1.540 WUP2W0.541 WUP2W1.542 WUP3W0.609 __extDM.544 __extDM_SFR_LFTUNEVBAT_t.545 __extDM_SFR_P1WRES_t.546 __extDM_SFR_P2WRES_t.547 __extDM_SFR_PCON0_t.548 __extDM_SFR_PRECON10_t.549 __extDM_SFR_PRECON11_t.550 __extDM_SFR_PRECON2_t.551 __extDM_SFR_PRECON3_t.552 __extDM_SFR_PRECON4_t.553 __extDM_SFR_PRECON5_t.554 __extDM_SFR_PRECON6_t.555 __extDM_SFR_PRECON7_t.556 __extDM_SFR_PRECON8_t.557 __extDM_SFR_PRECON9_t.558 __extDM_SFR_PRESTAT_t.559 __extDM_SFR_PRESWUP0_t.560 __extDM_SFR_PRESWUP1_t.561 __extDM_SFR_RTCCON_t.562 __extDM_SFR_byte.563 __extDM_SFR_word.564 __extDM_int16_.565 __extDM_int8_.566 __extDM_void.567 __extPM.568 __extPM_void.569 __extULP.570 __extULP_void.571 g_b_InCriticalSection.572 g_b_NMI_occurred.573 __vola.660)  <583>;
    } #97 off=121 nxt=261
    #261 off=121 nxt=102 tgt=116
    (__trgt.2052 var=747) const_inp ()  <2069>;
    <327> {
      (__apl_nz.1976 var=731 stl=nz_flag_w) _ad_const_cmp_const_1_B1 (__tmp.2378)  <2189>;
      (__apl_nz.2325 var=731 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1976)  <2518>;
      (__tmp.2378 var=472 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__tmp.666)  <2567>;
    } stp=0;
    <328> {
      () cc_eq__jump_const_1_B1 (__apl_nz.2324 __trgt.2052)  <2190>;
      (__apl_nz.2324 var=731 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2325)  <2517>;
    } stp=2;
    if {
        {
            () if_expr (__either.1993)  <695>;
            (__either.1993 var=746) undefined ()  <2007>;
        } #100
        {
        } #116 off=142 nxt=268
        {
            #102 off=124 nxt=103
            (void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort.2033 var=481) const_inp ()  <2050>;
            <326> {
              () call_const_1_B1 (void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort.2033)  <2188>;
            } stp=3;
            <505> {
              (__ct_3.2429 var=479 stl=a_w0) const_2_B2 ()  <2471>;
              (__ct_3.2428 var=479 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_3.2429)  <2597>;
            } stp=0;
            <509> {
              (__adr_Temp_Buf.2437 var=148 stl=a_w2 __side_effect.2438 var=780 stl=c_flag_w __side_effect.2440 var=780 stl=nz_flag_w __side_effect.2442 var=780 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_0t0.2029 __sp.146)  <2479>;
              (__adr_Temp_Buf.2436 var=148 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_Temp_Buf.2437)  <2601>;
              (__side_effect.2439 var=780 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__side_effect.2438)  <2602>;
              (__side_effect.2441 var=780 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2440)  <2603>;
              (__side_effect.2443 var=780 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2442)  <2604>;
            } stp=1;
            call {
                (KEY_ID.844 var=69 KEY_PID.845 var=67 LFTUNEVBAT.846 var=38 P1WRES.847 var=32 P2WRES.848 var=34 PCON0.849 var=74 PRE3T.850 var=63 PRECON10.851 var=56 PRECON11.852 var=58 PRECON2.853 var=40 PRECON3.854 var=42 PRECON4.855 var=44 PRECON5.856 var=46 PRECON6.857 var=48 PRECON7.858 var=50 PRECON8.859 var=52 PRECON9.860 var=54 PREPD.861 var=60 PRESTAT.862 var=71 PRESWUP0.863 var=25 PRESWUP1.864 var=28 PRESWUP2.865 var=30 PRET.866 var=62 RTCCON.867 var=36 Temp_Buf.868 var=20 WUP1W0.869 var=64 WUP1W1.870 var=65 WUP2W0.871 var=66 WUP2W1.872 var=68 WUP3W0.873 var=70 __extDM.874 var=17 __extDM_SFR_LFTUNEVBAT_t.875 var=39 __extDM_SFR_P1WRES_t.876 var=33 __extDM_SFR_P2WRES_t.877 var=35 __extDM_SFR_PCON0_t.878 var=75 __extDM_SFR_PRECON10_t.879 var=57 __extDM_SFR_PRECON11_t.880 var=59 __extDM_SFR_PRECON2_t.881 var=41 __extDM_SFR_PRECON3_t.882 var=43 __extDM_SFR_PRECON4_t.883 var=45 __extDM_SFR_PRECON5_t.884 var=47 __extDM_SFR_PRECON6_t.885 var=49 __extDM_SFR_PRECON7_t.886 var=51 __extDM_SFR_PRECON8_t.887 var=53 __extDM_SFR_PRECON9_t.888 var=55 __extDM_SFR_PRESTAT_t.889 var=72 __extDM_SFR_PRESWUP0_t.890 var=27 __extDM_SFR_PRESWUP1_t.891 var=29 __extDM_SFR_RTCCON_t.892 var=37 __extDM_SFR_byte.893 var=61 __extDM_SFR_word.894 var=31 __extDM_int16_.895 var=26 __extDM_int8_.896 var=24 __extDM_void.897 var=77 __extPM.898 var=16 __extPM_void.899 var=76 __extULP.900 var=18 __extULP_void.901 var=78 g_b_InCriticalSection.902 var=23 g_b_NMI_occurred.903 var=73 __vola.904 var=13) Fvoid_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort (__adr_Temp_Buf.2436 __ct_3.2428 KEY_ID.669 KEY_PID.670 LFTUNEVBAT.671 P1WRES.672 P2WRES.673 PCON0.674 PRE3T.675 PRECON10.676 PRECON11.677 PRECON2.678 PRECON3.679 PRECON4.680 PRECON5.681 PRECON6.682 PRECON7.683 PRECON8.684 PRECON9.685 PREPD.686 PRESTAT.687 PRESWUP0.688 PRESWUP1.689 PRESWUP2.690 PRET.691 RTCCON.692 Temp_Buf.192 WUP1W0.693 WUP1W1.694 WUP2W0.695 WUP2W1.696 WUP3W0.697 __extDM.698 __extDM_SFR_LFTUNEVBAT_t.699 __extDM_SFR_P1WRES_t.700 __extDM_SFR_P2WRES_t.701 __extDM_SFR_PCON0_t.702 __extDM_SFR_PRECON10_t.703 __extDM_SFR_PRECON11_t.704 __extDM_SFR_PRECON2_t.705 __extDM_SFR_PRECON3_t.706 __extDM_SFR_PRECON4_t.707 __extDM_SFR_PRECON5_t.708 __extDM_SFR_PRECON6_t.709 __extDM_SFR_PRECON7_t.710 __extDM_SFR_PRECON8_t.711 __extDM_SFR_PRECON9_t.712 __extDM_SFR_PRESTAT_t.713 __extDM_SFR_PRESWUP0_t.714 __extDM_SFR_PRESWUP1_t.715 __extDM_SFR_RTCCON_t.716 __extDM_SFR_byte.717 __extDM_SFR_word.718 __extDM_int16_.719 __extDM_int8_.720 __extDM_void.721 __extPM.722 __extPM_void.723 __extULP.724 __extULP_void.725 g_b_InCriticalSection.726 g_b_NMI_occurred.727 __vola.728)  <703>;
            } #103 off=129 nxt=105
            #105 off=129 nxt=106
            (void_timer_delay_ms___ushort.2034 var=485) const_inp ()  <2051>;
            <324> {
              () call_const_1_B1 (void_timer_delay_ms___ushort.2034)  <2186>;
            } stp=1;
            <506> {
              (__ct_4.2431 var=483 stl=a_w0) const_1_B2 ()  <2473>;
              (__ct_4.2430 var=483 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_4.2431)  <2598>;
            } stp=0;
            call {
                (KEY_ID.910 var=69 KEY_PID.911 var=67 LFTUNEVBAT.912 var=38 P1WRES.913 var=32 P2WRES.914 var=34 PCON0.915 var=74 PRE3T.916 var=63 PRECON10.917 var=56 PRECON11.918 var=58 PRECON2.919 var=40 PRECON3.920 var=42 PRECON4.921 var=44 PRECON5.922 var=46 PRECON6.923 var=48 PRECON7.924 var=50 PRECON8.925 var=52 PRECON9.926 var=54 PREPD.927 var=60 PRESTAT.928 var=71 PRESWUP0.929 var=25 PRESWUP1.930 var=28 PRESWUP2.931 var=30 PRET.932 var=62 RTCCON.933 var=36 Temp_Buf.934 var=20 WUP1W0.935 var=64 WUP1W1.936 var=65 WUP2W0.937 var=66 WUP2W1.938 var=68 WUP3W0.939 var=70 __extDM.940 var=17 __extDM_SFR_LFTUNEVBAT_t.941 var=39 __extDM_SFR_P1WRES_t.942 var=33 __extDM_SFR_P2WRES_t.943 var=35 __extDM_SFR_PCON0_t.944 var=75 __extDM_SFR_PRECON10_t.945 var=57 __extDM_SFR_PRECON11_t.946 var=59 __extDM_SFR_PRECON2_t.947 var=41 __extDM_SFR_PRECON3_t.948 var=43 __extDM_SFR_PRECON4_t.949 var=45 __extDM_SFR_PRECON5_t.950 var=47 __extDM_SFR_PRECON6_t.951 var=49 __extDM_SFR_PRECON7_t.952 var=51 __extDM_SFR_PRECON8_t.953 var=53 __extDM_SFR_PRECON9_t.954 var=55 __extDM_SFR_PRESTAT_t.955 var=72 __extDM_SFR_PRESWUP0_t.956 var=27 __extDM_SFR_PRESWUP1_t.957 var=29 __extDM_SFR_RTCCON_t.958 var=37 __extDM_SFR_byte.959 var=61 __extDM_SFR_word.960 var=31 __extDM_int16_.961 var=26 __extDM_int8_.962 var=24 __extDM_void.963 var=77 __extPM.964 var=16 __extPM_void.965 var=76 __extULP.966 var=18 __extULP_void.967 var=78 g_b_InCriticalSection.968 var=23 g_b_NMI_occurred.969 var=73 __vola.970 var=13) Fvoid_timer_delay_ms___ushort (__ct_4.2430 KEY_ID.844 KEY_PID.845 LFTUNEVBAT.846 P1WRES.847 P2WRES.848 PCON0.849 PRE3T.850 PRECON10.851 PRECON11.852 PRECON2.853 PRECON3.854 PRECON4.855 PRECON5.856 PRECON6.857 PRECON7.858 PRECON8.859 PRECON9.860 PREPD.861 PRESTAT.862 PRESWUP0.863 PRESWUP1.864 PRESWUP2.865 PRET.866 RTCCON.867 Temp_Buf.868 WUP1W0.869 WUP1W1.870 WUP2W0.871 WUP2W1.872 WUP3W0.873 __extDM.874 __extDM_SFR_LFTUNEVBAT_t.875 __extDM_SFR_P1WRES_t.876 __extDM_SFR_P2WRES_t.877 __extDM_SFR_PCON0_t.878 __extDM_SFR_PRECON10_t.879 __extDM_SFR_PRECON11_t.880 __extDM_SFR_PRECON2_t.881 __extDM_SFR_PRECON3_t.882 __extDM_SFR_PRECON4_t.883 __extDM_SFR_PRECON5_t.884 __extDM_SFR_PRECON6_t.885 __extDM_SFR_PRECON7_t.886 __extDM_SFR_PRECON8_t.887 __extDM_SFR_PRECON9_t.888 __extDM_SFR_PRESTAT_t.889 __extDM_SFR_PRESWUP0_t.890 __extDM_SFR_PRESWUP1_t.891 __extDM_SFR_RTCCON_t.892 __extDM_SFR_byte.893 __extDM_SFR_word.894 __extDM_int16_.895 __extDM_int8_.896 __extDM_void.897 __extPM.898 __extPM_void.899 __extULP.900 __extULP_void.901 g_b_InCriticalSection.902 g_b_NMI_occurred.903 __vola.904)  <711>;
            } #106 off=132 nxt=108
            #108 off=132 nxt=109
            <322> {
              (Temp_Buf.984 var=20 __seff.2117 var=758 stl=nz_flag_w) load__pl_rd_res_reg_const__ad_const_store_1_B1 (__ct_0t0.2029 Temp_Buf.934 Temp_Buf.934 __sp.146)  <2184>;
              (__seff.2326 var=758 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2117)  <2519>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <726>;
            } #109 off=134 nxt=110
            #110 off=134 nxt=111
            (error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort.2035 var=501) const_inp ()  <2052>;
            <321> {
              () call_const_1_B1 (error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort.2035)  <2183>;
            } stp=3;
            <507> {
              (__ct_3.2433 var=499 stl=a_w0) const_2_B2 ()  <2475>;
              (__ct_3.2432 var=499 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_3.2433)  <2599>;
            } stp=0;
            <513> {
              (__adr_Temp_Buf.2445 var=148 stl=a_w2 __side_effect.2446 var=780 stl=c_flag_w __side_effect.2448 var=780 stl=nz_flag_w __side_effect.2450 var=780 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_0t0.2029 __sp.146)  <2487>;
              (__adr_Temp_Buf.2444 var=148 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_Temp_Buf.2445)  <2605>;
              (__side_effect.2447 var=780 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__side_effect.2446)  <2606>;
              (__side_effect.2449 var=780 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2448)  <2607>;
              (__side_effect.2451 var=780 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2450)  <2608>;
            } stp=1;
            call {
                (__tmp.991 var=503 stl=RbL off=0 KEY_ID.994 var=69 KEY_PID.995 var=67 LFTUNEVBAT.996 var=38 P1WRES.997 var=32 P2WRES.998 var=34 PCON0.999 var=74 PRE3T.1000 var=63 PRECON10.1001 var=56 PRECON11.1002 var=58 PRECON2.1003 var=40 PRECON3.1004 var=42 PRECON4.1005 var=44 PRECON5.1006 var=46 PRECON6.1007 var=48 PRECON7.1008 var=50 PRECON8.1009 var=52 PRECON9.1010 var=54 PREPD.1011 var=60 PRESTAT.1012 var=71 PRESWUP0.1013 var=25 PRESWUP1.1014 var=28 PRESWUP2.1015 var=30 PRET.1016 var=62 RTCCON.1017 var=36 Temp_Buf.1018 var=20 WUP1W0.1019 var=64 WUP1W1.1020 var=65 WUP2W0.1021 var=66 WUP2W1.1022 var=68 WUP3W0.1023 var=70 __extDM.1024 var=17 __extDM_SFR_LFTUNEVBAT_t.1025 var=39 __extDM_SFR_P1WRES_t.1026 var=33 __extDM_SFR_P2WRES_t.1027 var=35 __extDM_SFR_PCON0_t.1028 var=75 __extDM_SFR_PRECON10_t.1029 var=57 __extDM_SFR_PRECON11_t.1030 var=59 __extDM_SFR_PRECON2_t.1031 var=41 __extDM_SFR_PRECON3_t.1032 var=43 __extDM_SFR_PRECON4_t.1033 var=45 __extDM_SFR_PRECON5_t.1034 var=47 __extDM_SFR_PRECON6_t.1035 var=49 __extDM_SFR_PRECON7_t.1036 var=51 __extDM_SFR_PRECON8_t.1037 var=53 __extDM_SFR_PRECON9_t.1038 var=55 __extDM_SFR_PRESTAT_t.1039 var=72 __extDM_SFR_PRESWUP0_t.1040 var=27 __extDM_SFR_PRESWUP1_t.1041 var=29 __extDM_SFR_RTCCON_t.1042 var=37 __extDM_SFR_byte.1043 var=61 __extDM_SFR_word.1044 var=31 __extDM_int16_.1045 var=26 __extDM_int8_.1046 var=24 __extDM_void.1047 var=77 __extPM.1048 var=16 __extPM_void.1049 var=76 __extULP.1050 var=18 __extULP_void.1051 var=78 g_b_InCriticalSection.1052 var=23 g_b_NMI_occurred.1053 var=73 __vola.1054 var=13) Ferror_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort (__adr_Temp_Buf.2444 __ct_3.2432 KEY_ID.910 KEY_PID.911 LFTUNEVBAT.912 P1WRES.913 P2WRES.914 PCON0.915 PRE3T.916 PRECON10.917 PRECON11.918 PRECON2.919 PRECON3.920 PRECON4.921 PRECON5.922 PRECON6.923 PRECON7.924 PRECON8.925 PRECON9.926 PREPD.927 PRESTAT.928 PRESWUP0.929 PRESWUP1.930 PRESWUP2.931 PRET.932 RTCCON.933 Temp_Buf.984 WUP1W0.935 WUP1W1.936 WUP2W0.937 WUP2W1.938 WUP3W0.939 __extDM.940 __extDM_SFR_LFTUNEVBAT_t.941 __extDM_SFR_P1WRES_t.942 __extDM_SFR_P2WRES_t.943 __extDM_SFR_PCON0_t.944 __extDM_SFR_PRECON10_t.945 __extDM_SFR_PRECON11_t.946 __extDM_SFR_PRECON2_t.947 __extDM_SFR_PRECON3_t.948 __extDM_SFR_PRECON4_t.949 __extDM_SFR_PRECON5_t.950 __extDM_SFR_PRECON6_t.951 __extDM_SFR_PRECON7_t.952 __extDM_SFR_PRECON8_t.953 __extDM_SFR_PRECON9_t.954 __extDM_SFR_PRESTAT_t.955 __extDM_SFR_PRESWUP0_t.956 __extDM_SFR_PRESWUP1_t.957 __extDM_SFR_RTCCON_t.958 __extDM_SFR_byte.959 __extDM_SFR_word.960 __extDM_int16_.961 __extDM_int8_.962 __extDM_void.963 __extPM.964 __extPM_void.965 __extULP.966 __extULP_void.967 g_b_InCriticalSection.968 g_b_NMI_occurred.969 __vola.970)  <734>;
            } #111 off=139 nxt=113
            #113 off=139 nxt=114
            <319> {
              () call_const_1_B1 (void_timer_delay_ms___ushort.2034)  <2181>;
            } stp=1;
            <508> {
              (__ct_4.2435 var=504 stl=a_w0) const_1_B2 ()  <2477>;
              (__ct_4.2434 var=504 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_4.2435)  <2600>;
            } stp=0;
            call {
                (KEY_ID.1060 var=69 KEY_PID.1061 var=67 LFTUNEVBAT.1062 var=38 P1WRES.1063 var=32 P2WRES.1064 var=34 PCON0.1065 var=74 PRE3T.1066 var=63 PRECON10.1067 var=56 PRECON11.1068 var=58 PRECON2.1069 var=40 PRECON3.1070 var=42 PRECON4.1071 var=44 PRECON5.1072 var=46 PRECON6.1073 var=48 PRECON7.1074 var=50 PRECON8.1075 var=52 PRECON9.1076 var=54 PREPD.1077 var=60 PRESTAT.1078 var=71 PRESWUP0.1079 var=25 PRESWUP1.1080 var=28 PRESWUP2.1081 var=30 PRET.1082 var=62 RTCCON.1083 var=36 Temp_Buf.1084 var=20 WUP1W0.1085 var=64 WUP1W1.1086 var=65 WUP2W0.1087 var=66 WUP2W1.1088 var=68 WUP3W0.1089 var=70 __extDM.1090 var=17 __extDM_SFR_LFTUNEVBAT_t.1091 var=39 __extDM_SFR_P1WRES_t.1092 var=33 __extDM_SFR_P2WRES_t.1093 var=35 __extDM_SFR_PCON0_t.1094 var=75 __extDM_SFR_PRECON10_t.1095 var=57 __extDM_SFR_PRECON11_t.1096 var=59 __extDM_SFR_PRECON2_t.1097 var=41 __extDM_SFR_PRECON3_t.1098 var=43 __extDM_SFR_PRECON4_t.1099 var=45 __extDM_SFR_PRECON5_t.1100 var=47 __extDM_SFR_PRECON6_t.1101 var=49 __extDM_SFR_PRECON7_t.1102 var=51 __extDM_SFR_PRECON8_t.1103 var=53 __extDM_SFR_PRECON9_t.1104 var=55 __extDM_SFR_PRESTAT_t.1105 var=72 __extDM_SFR_PRESWUP0_t.1106 var=27 __extDM_SFR_PRESWUP1_t.1107 var=29 __extDM_SFR_RTCCON_t.1108 var=37 __extDM_SFR_byte.1109 var=61 __extDM_SFR_word.1110 var=31 __extDM_int16_.1111 var=26 __extDM_int8_.1112 var=24 __extDM_void.1113 var=77 __extPM.1114 var=16 __extPM_void.1115 var=76 __extULP.1116 var=18 __extULP_void.1117 var=78 g_b_InCriticalSection.1118 var=23 g_b_NMI_occurred.1119 var=73 __vola.1120 var=13) Fvoid_timer_delay_ms___ushort (__ct_4.2434 KEY_ID.994 KEY_PID.995 LFTUNEVBAT.996 P1WRES.997 P2WRES.998 PCON0.999 PRE3T.1000 PRECON10.1001 PRECON11.1002 PRECON2.1003 PRECON3.1004 PRECON4.1005 PRECON5.1006 PRECON6.1007 PRECON7.1008 PRECON8.1009 PRECON9.1010 PREPD.1011 PRESTAT.1012 PRESWUP0.1013 PRESWUP1.1014 PRESWUP2.1015 PRET.1016 RTCCON.1017 Temp_Buf.1018 WUP1W0.1019 WUP1W1.1020 WUP2W0.1021 WUP2W1.1022 WUP3W0.1023 __extDM.1024 __extDM_SFR_LFTUNEVBAT_t.1025 __extDM_SFR_P1WRES_t.1026 __extDM_SFR_P2WRES_t.1027 __extDM_SFR_PCON0_t.1028 __extDM_SFR_PRECON10_t.1029 __extDM_SFR_PRECON11_t.1030 __extDM_SFR_PRECON2_t.1031 __extDM_SFR_PRECON3_t.1032 __extDM_SFR_PRECON4_t.1033 __extDM_SFR_PRECON5_t.1034 __extDM_SFR_PRECON6_t.1035 __extDM_SFR_PRECON7_t.1036 __extDM_SFR_PRECON8_t.1037 __extDM_SFR_PRECON9_t.1038 __extDM_SFR_PRESTAT_t.1039 __extDM_SFR_PRESWUP0_t.1040 __extDM_SFR_PRESWUP1_t.1041 __extDM_SFR_RTCCON_t.1042 __extDM_SFR_byte.1043 __extDM_SFR_word.1044 __extDM_int16_.1045 __extDM_int8_.1046 __extDM_void.1047 __extPM.1048 __extPM_void.1049 __extULP.1050 __extULP_void.1051 g_b_InCriticalSection.1052 g_b_NMI_occurred.1053 __vola.1054)  <744>;
            } #114 off=142 nxt=271
            #271 off=142 nxt=268
        } #101
        {
            (__vola.1121 var=13) merge (__vola.728 __vola.1120)  <746>;
            (__extPM.1122 var=16) merge (__extPM.722 __extPM.1114)  <747>;
            (__extDM.1123 var=17) merge (__extDM.698 __extDM.1090)  <748>;
            (__extULP.1124 var=18) merge (__extULP.724 __extULP.1116)  <749>;
            (Temp_Buf.1125 var=20) merge (Temp_Buf.192 Temp_Buf.1084)  <750>;
            (g_b_InCriticalSection.1126 var=23) merge (g_b_InCriticalSection.726 g_b_InCriticalSection.1118)  <751>;
            (__extDM_int8_.1127 var=24) merge (__extDM_int8_.720 __extDM_int8_.1112)  <752>;
            (PRESWUP0.1128 var=25) merge (PRESWUP0.688 PRESWUP0.1079)  <753>;
            (__extDM_int16_.1129 var=26) merge (__extDM_int16_.719 __extDM_int16_.1111)  <754>;
            (__extDM_SFR_PRESWUP0_t.1130 var=27) merge (__extDM_SFR_PRESWUP0_t.714 __extDM_SFR_PRESWUP0_t.1106)  <755>;
            (PRESWUP1.1131 var=28) merge (PRESWUP1.689 PRESWUP1.1080)  <756>;
            (__extDM_SFR_PRESWUP1_t.1132 var=29) merge (__extDM_SFR_PRESWUP1_t.715 __extDM_SFR_PRESWUP1_t.1107)  <757>;
            (PRESWUP2.1133 var=30) merge (PRESWUP2.690 PRESWUP2.1081)  <758>;
            (__extDM_SFR_word.1134 var=31) merge (__extDM_SFR_word.718 __extDM_SFR_word.1110)  <759>;
            (P1WRES.1135 var=32) merge (P1WRES.672 P1WRES.1063)  <760>;
            (__extDM_SFR_P1WRES_t.1136 var=33) merge (__extDM_SFR_P1WRES_t.700 __extDM_SFR_P1WRES_t.1092)  <761>;
            (P2WRES.1137 var=34) merge (P2WRES.673 P2WRES.1064)  <762>;
            (__extDM_SFR_P2WRES_t.1138 var=35) merge (__extDM_SFR_P2WRES_t.701 __extDM_SFR_P2WRES_t.1093)  <763>;
            (RTCCON.1139 var=36) merge (RTCCON.692 RTCCON.1083)  <764>;
            (__extDM_SFR_RTCCON_t.1140 var=37) merge (__extDM_SFR_RTCCON_t.716 __extDM_SFR_RTCCON_t.1108)  <765>;
            (LFTUNEVBAT.1141 var=38) merge (LFTUNEVBAT.671 LFTUNEVBAT.1062)  <766>;
            (__extDM_SFR_LFTUNEVBAT_t.1142 var=39) merge (__extDM_SFR_LFTUNEVBAT_t.699 __extDM_SFR_LFTUNEVBAT_t.1091)  <767>;
            (PRECON2.1143 var=40) merge (PRECON2.678 PRECON2.1069)  <768>;
            (__extDM_SFR_PRECON2_t.1144 var=41) merge (__extDM_SFR_PRECON2_t.705 __extDM_SFR_PRECON2_t.1097)  <769>;
            (PRECON3.1145 var=42) merge (PRECON3.679 PRECON3.1070)  <770>;
            (__extDM_SFR_PRECON3_t.1146 var=43) merge (__extDM_SFR_PRECON3_t.706 __extDM_SFR_PRECON3_t.1098)  <771>;
            (PRECON4.1147 var=44) merge (PRECON4.680 PRECON4.1071)  <772>;
            (__extDM_SFR_PRECON4_t.1148 var=45) merge (__extDM_SFR_PRECON4_t.707 __extDM_SFR_PRECON4_t.1099)  <773>;
            (PRECON5.1149 var=46) merge (PRECON5.681 PRECON5.1072)  <774>;
            (__extDM_SFR_PRECON5_t.1150 var=47) merge (__extDM_SFR_PRECON5_t.708 __extDM_SFR_PRECON5_t.1100)  <775>;
            (PRECON6.1151 var=48) merge (PRECON6.682 PRECON6.1073)  <776>;
            (__extDM_SFR_PRECON6_t.1152 var=49) merge (__extDM_SFR_PRECON6_t.709 __extDM_SFR_PRECON6_t.1101)  <777>;
            (PRECON7.1153 var=50) merge (PRECON7.683 PRECON7.1074)  <778>;
            (__extDM_SFR_PRECON7_t.1154 var=51) merge (__extDM_SFR_PRECON7_t.710 __extDM_SFR_PRECON7_t.1102)  <779>;
            (PRECON8.1155 var=52) merge (PRECON8.684 PRECON8.1075)  <780>;
            (__extDM_SFR_PRECON8_t.1156 var=53) merge (__extDM_SFR_PRECON8_t.711 __extDM_SFR_PRECON8_t.1103)  <781>;
            (PRECON9.1157 var=54) merge (PRECON9.685 PRECON9.1076)  <782>;
            (__extDM_SFR_PRECON9_t.1158 var=55) merge (__extDM_SFR_PRECON9_t.712 __extDM_SFR_PRECON9_t.1104)  <783>;
            (PRECON10.1159 var=56) merge (PRECON10.676 PRECON10.1067)  <784>;
            (__extDM_SFR_PRECON10_t.1160 var=57) merge (__extDM_SFR_PRECON10_t.703 __extDM_SFR_PRECON10_t.1095)  <785>;
            (PRECON11.1161 var=58) merge (PRECON11.677 PRECON11.1068)  <786>;
            (__extDM_SFR_PRECON11_t.1162 var=59) merge (__extDM_SFR_PRECON11_t.704 __extDM_SFR_PRECON11_t.1096)  <787>;
            (PREPD.1163 var=60) merge (PREPD.686 PREPD.1077)  <788>;
            (__extDM_SFR_byte.1164 var=61) merge (__extDM_SFR_byte.717 __extDM_SFR_byte.1109)  <789>;
            (PRET.1165 var=62) merge (PRET.691 PRET.1082)  <790>;
            (PRE3T.1166 var=63) merge (PRE3T.675 PRE3T.1066)  <791>;
            (WUP1W0.1167 var=64) merge (WUP1W0.693 WUP1W0.1085)  <792>;
            (WUP1W1.1168 var=65) merge (WUP1W1.694 WUP1W1.1086)  <793>;
            (WUP2W0.1169 var=66) merge (WUP2W0.695 WUP2W0.1087)  <794>;
            (KEY_PID.1170 var=67) merge (KEY_PID.670 KEY_PID.1061)  <795>;
            (WUP2W1.1171 var=68) merge (WUP2W1.696 WUP2W1.1088)  <796>;
            (KEY_ID.1172 var=69) merge (KEY_ID.669 KEY_ID.1060)  <797>;
            (WUP3W0.1173 var=70) merge (WUP3W0.697 WUP3W0.1089)  <798>;
            (PRESTAT.1174 var=71) merge (PRESTAT.687 PRESTAT.1078)  <799>;
            (__extDM_SFR_PRESTAT_t.1175 var=72) merge (__extDM_SFR_PRESTAT_t.713 __extDM_SFR_PRESTAT_t.1105)  <800>;
            (g_b_NMI_occurred.1176 var=73) merge (g_b_NMI_occurred.727 g_b_NMI_occurred.1119)  <801>;
            (PCON0.1177 var=74) merge (PCON0.674 PCON0.1065)  <802>;
            (__extDM_SFR_PCON0_t.1178 var=75) merge (__extDM_SFR_PCON0_t.702 __extDM_SFR_PCON0_t.1094)  <803>;
            (__extPM_void.1179 var=76) merge (__extPM_void.723 __extPM_void.1115)  <804>;
            (__extDM_void.1180 var=77) merge (__extDM_void.721 __extDM_void.1113)  <805>;
            (__extULP_void.1181 var=78) merge (__extULP_void.725 __extULP_void.1117)  <806>;
        } #117
    } #99
    #268 off=142 nxt=196 tgt=131
    (__ptr_g_b_NMI_occurred.2027 var=139) const_inp ()  <2044>;
    (__trgt.2054 var=749) const_inp ()  <2071>;
    <315> {
      (g_b_InCriticalSection.1288 var=23) store_const_const_1_B1 (__ptr_g_b_InCriticalSection.1998 g_b_InCriticalSection.1126)  <2177>;
    } stp=0;
    <316> {
      (__seff.2111 var=755 stl=c_flag_w __seff.2112 var=756 stl=nz_flag_w __seff.2113 var=757 stl=o_flag_w) load_const_cmp_const_cc_eq__jump_const_1_B1 (__ptr_g_b_NMI_occurred.2027 __trgt.2054 g_b_NMI_occurred.1176)  <2178>;
      (__seff.2327 var=756 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2112)  <2520>;
      (__seff.2385 var=755 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2111)  <2568>;
      (__seff.2386 var=757 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2113)  <2569>;
    } stp=2;
    if {
        {
            () if_expr (__either.1996)  <1022>;
            (__either.1996 var=746) undefined ()  <2012>;
        } #125
        {
        } #131 off=148 nxt=136
        {
            (__ptr_PCON0.2028 var=141) const_inp ()  <2045>;
            <313> {
              (PCON0.1413 var=74 __vola.1414 var=13 __seff.2109 var=754 stl=nz_flag_w) load_const__or_const_store_1_B1 (__ptr_PCON0.2028 PCON0.1177 __vola.1121)  <2175>;
              (__seff.2328 var=754 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2109)  <2521>;
            } stp=0;
        } #196 off=146 nxt=136
        {
            (__vola.1418 var=13) merge (__vola.1121 __vola.1414)  <1042>;
            (PCON0.1419 var=74) merge (PCON0.1177 PCON0.1413)  <1043>;
        } #132
    } #124
    #136 off=148 nxt=-2
    () sink (__vola.1418)  <1157>;
    () sink (__extPM.1122)  <1160>;
    () sink (__extDM.1123)  <1161>;
    () sink (__extULP.1124)  <1162>;
    () sink (__sp.1634)  <1163>;
    () sink (Temp_Buf.1125)  <1164>;
    () sink (pollingid.576)  <1165>;
    () sink (swversion.200)  <1166>;
    () sink (g_b_InCriticalSection.1288)  <1167>;
    () sink (__extDM_int8_.1127)  <1168>;
    () sink (PRESWUP0.1128)  <1169>;
    () sink (__extDM_int16_.1129)  <1170>;
    () sink (__extDM_SFR_PRESWUP0_t.1130)  <1171>;
    () sink (PRESWUP1.1131)  <1172>;
    () sink (__extDM_SFR_PRESWUP1_t.1132)  <1173>;
    () sink (PRESWUP2.1133)  <1174>;
    () sink (__extDM_SFR_word.1134)  <1175>;
    () sink (P1WRES.1135)  <1176>;
    () sink (__extDM_SFR_P1WRES_t.1136)  <1177>;
    () sink (P2WRES.1137)  <1178>;
    () sink (__extDM_SFR_P2WRES_t.1138)  <1179>;
    () sink (RTCCON.1139)  <1180>;
    () sink (__extDM_SFR_RTCCON_t.1140)  <1181>;
    () sink (LFTUNEVBAT.1141)  <1182>;
    () sink (__extDM_SFR_LFTUNEVBAT_t.1142)  <1183>;
    () sink (PRECON2.1143)  <1184>;
    () sink (__extDM_SFR_PRECON2_t.1144)  <1185>;
    () sink (PRECON3.1145)  <1186>;
    () sink (__extDM_SFR_PRECON3_t.1146)  <1187>;
    () sink (PRECON4.1147)  <1188>;
    () sink (__extDM_SFR_PRECON4_t.1148)  <1189>;
    () sink (PRECON5.1149)  <1190>;
    () sink (__extDM_SFR_PRECON5_t.1150)  <1191>;
    () sink (PRECON6.1151)  <1192>;
    () sink (__extDM_SFR_PRECON6_t.1152)  <1193>;
    () sink (PRECON7.1153)  <1194>;
    () sink (__extDM_SFR_PRECON7_t.1154)  <1195>;
    () sink (PRECON8.1155)  <1196>;
    () sink (__extDM_SFR_PRECON8_t.1156)  <1197>;
    () sink (PRECON9.1157)  <1198>;
    () sink (__extDM_SFR_PRECON9_t.1158)  <1199>;
    () sink (PRECON10.1159)  <1200>;
    () sink (__extDM_SFR_PRECON10_t.1160)  <1201>;
    () sink (PRECON11.1161)  <1202>;
    () sink (__extDM_SFR_PRECON11_t.1162)  <1203>;
    () sink (PREPD.1163)  <1204>;
    () sink (__extDM_SFR_byte.1164)  <1205>;
    () sink (PRET.1165)  <1206>;
    () sink (PRE3T.1166)  <1207>;
    () sink (WUP1W0.1167)  <1208>;
    () sink (WUP1W1.1168)  <1209>;
    () sink (WUP2W0.1169)  <1210>;
    () sink (KEY_PID.1170)  <1211>;
    () sink (WUP2W1.1171)  <1212>;
    () sink (KEY_ID.1172)  <1213>;
    () sink (WUP3W0.1173)  <1214>;
    () sink (PRESTAT.1174)  <1215>;
    () sink (__extDM_SFR_PRESTAT_t.1175)  <1216>;
    () sink (g_b_NMI_occurred.1176)  <1217>;
    () sink (PCON0.1419)  <1218>;
    () sink (__extDM_SFR_PCON0_t.1178)  <1219>;
    () sink (__extPM_void.1179)  <1220>;
    () sink (__extDM_void.1180)  <1221>;
    () sink (__extULP_void.1181)  <1222>;
    (__ct_8s0.2036 var=533) const_inp ()  <2053>;
    <311> {
      (__sp.1634 var=19 __seff.2104 var=751 stl=c_flag_w __seff.2105 var=752 stl=nz_flag_w __seff.2106 var=753 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_8s0.2036 __sp.146 __sp.146)  <2173>;
      (__seff.2329 var=752 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2105)  <2522>;
      (__seff.2387 var=751 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2104)  <2570>;
      (__seff.2388 var=753 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2106)  <2571>;
    } stp=0;
    <312> {
      () ret_1_B1 ()  <2174>;
    } stp=1;
} #0
0 : 'apps/src/SysInit.c';
----------
0 : (0,114:0,0);
3 : (0,116:26,2);
4 : (0,116:26,2);
5 : (0,116:26,3);
6 : (0,116:26,3);
7 : (0,116:26,4);
8 : (0,116:26,4);
9 : (0,116:26,5);
11 : (0,116:26,5);
12 : (0,117:24,6);
13 : (0,117:24,6);
14 : (0,118:24,7);
15 : (0,118:24,7);
16 : (0,119:23,9);
17 : (0,119:23,9);
18 : (0,122:15,10);
19 : (0,122:15,10);
20 : (0,123:15,11);
21 : (0,123:15,11);
22 : (0,124:15,12);
23 : (0,124:15,12);
24 : (0,125:15,13);
25 : (0,125:15,13);
26 : (0,126:15,14);
27 : (0,126:15,14);
28 : (0,129:15,15);
29 : (0,129:15,15);
30 : (0,133:17,16);
31 : (0,133:17,16);
32 : (0,137:17,17);
33 : (0,137:17,17);
34 : (0,138:17,18);
35 : (0,138:17,18);
36 : (0,139:17,19);
37 : (0,139:17,19);
38 : (0,140:17,20);
39 : (0,140:17,20);
40 : (0,141:17,21);
41 : (0,141:17,21);
42 : (0,142:17,22);
43 : (0,142:17,22);
44 : (0,143:17,23);
45 : (0,143:17,23);
46 : (0,144:17,24);
47 : (0,144:17,24);
48 : (0,145:17,25);
49 : (0,145:17,25);
50 : (0,146:17,26);
51 : (0,146:17,26);
52 : (0,147:17,27);
53 : (0,147:17,27);
54 : (0,154:17,28);
55 : (0,154:17,28);
56 : (0,155:17,29);
57 : (0,155:17,29);
58 : (0,157:17,30);
59 : (0,157:17,30);
60 : (0,158:17,31);
61 : (0,158:17,31);
63 : (0,165:17,32);
64 : (0,166:17,33);
65 : (0,166:17,33);
66 : (0,167:17,34);
67 : (0,167:17,34);
68 : (0,168:17,35);
69 : (0,168:17,35);
71 : (0,170:17,36);
72 : (0,171:17,37);
73 : (0,171:17,37);
74 : (0,172:17,38);
75 : (0,172:17,38);
76 : (0,173:17,39);
77 : (0,173:17,39);
78 : (0,175:56,39);
79 : (0,175:14,39);
80 : (0,175:12,40);
81 : (0,175:12,40);
83 : (0,176:17,41);
87 : (0,179:14,43);
91 : (0,180:14,45);
93 : (0,180:25,46);
94 : (0,184:14,47);
95 : (0,184:14,47);
96 : (0,186:50,47);
97 : (0,186:8,47);
99 : (0,186:2,47);
101 : (0,187:2,48);
102 : (0,188:49,48);
103 : (0,188:4,48);
105 : (0,189:25,49);
106 : (0,189:4,49);
108 : (0,190:16,51);
109 : (0,190:16,51);
110 : (0,191:66,51);
111 : (0,191:4,51);
113 : (0,192:25,52);
114 : (0,192:4,52);
116 : (0,186:2,55);
124 : (0,209:43,59);
131 : (0,209:104,64);
136 : (0,210:0,69);
176 : (0,177:13,41);
183 : (0,179:14,43);
190 : (0,180:14,45);
196 : (0,209:79,61);
212 : (0,165:13,31);
228 : (0,170:13,35);
245 : (0,176:13,40);
261 : (0,186:60,47);
268 : (0,209:43,59);
----------
182 : (0,116:26,2);
189 : (0,116:26,3);
196 : (0,116:26,4);
204 : (0,116:26,5);
208 : (0,117:24,6);
212 : (0,118:24,7);
216 : (0,119:23,9);
223 : (0,122:15,10);
230 : (0,123:15,11);
237 : (0,124:15,12);
244 : (0,125:15,13);
251 : (0,126:15,14);
258 : (0,129:15,15);
265 : (0,133:17,16);
272 : (0,137:17,17);
279 : (0,138:17,18);
286 : (0,139:17,19);
293 : (0,140:17,20);
300 : (0,141:17,21);
307 : (0,142:17,22);
314 : (0,143:17,23);
321 : (0,144:17,24);
328 : (0,145:17,25);
335 : (0,146:17,26);
342 : (0,147:17,27);
349 : (0,154:17,28);
356 : (0,155:17,29);
363 : (0,157:17,30);
370 : (0,158:17,31);
393 : (0,165:17,32);
405 : (0,166:17,33);
417 : (0,167:17,34);
429 : (0,168:17,35);
452 : (0,170:17,36);
464 : (0,171:17,37);
476 : (0,172:17,38);
488 : (0,173:17,39);
495 : (0,175:14,39);
499 : (0,175:12,40);
518 : (0,176:17,41);
538 : (0,179:14,43);
557 : (0,180:14,45);
569 : (0,180:25,46);
576 : (0,184:14,47);
583 : (0,186:8,47);
695 : (0,186:2,47);
703 : (0,188:4,48);
711 : (0,189:4,49);
726 : (0,190:16,51);
734 : (0,191:4,51);
744 : (0,192:4,52);
746 : (0,186:2,57);
747 : (0,186:2,57);
748 : (0,186:2,57);
749 : (0,186:2,57);
750 : (0,186:2,57);
751 : (0,186:2,57);
752 : (0,186:2,57);
753 : (0,186:2,57);
754 : (0,186:2,57);
755 : (0,186:2,57);
756 : (0,186:2,57);
757 : (0,186:2,57);
758 : (0,186:2,57);
759 : (0,186:2,57);
760 : (0,186:2,57);
761 : (0,186:2,57);
762 : (0,186:2,57);
763 : (0,186:2,57);
764 : (0,186:2,57);
765 : (0,186:2,57);
766 : (0,186:2,57);
767 : (0,186:2,57);
768 : (0,186:2,57);
769 : (0,186:2,57);
770 : (0,186:2,57);
771 : (0,186:2,57);
772 : (0,186:2,57);
773 : (0,186:2,57);
774 : (0,186:2,57);
775 : (0,186:2,57);
776 : (0,186:2,57);
777 : (0,186:2,57);
778 : (0,186:2,57);
779 : (0,186:2,57);
780 : (0,186:2,57);
781 : (0,186:2,57);
782 : (0,186:2,57);
783 : (0,186:2,57);
784 : (0,186:2,57);
785 : (0,186:2,57);
786 : (0,186:2,57);
787 : (0,186:2,57);
788 : (0,186:2,57);
789 : (0,186:2,57);
790 : (0,186:2,57);
791 : (0,186:2,57);
792 : (0,186:2,57);
793 : (0,186:2,57);
794 : (0,186:2,57);
795 : (0,186:2,57);
796 : (0,186:2,57);
797 : (0,186:2,57);
798 : (0,186:2,57);
799 : (0,186:2,57);
800 : (0,186:2,57);
801 : (0,186:2,57);
802 : (0,186:2,57);
803 : (0,186:2,57);
804 : (0,186:2,57);
805 : (0,186:2,57);
806 : (0,186:2,57);
1022 : (0,209:43,59);
1042 : (0,209:43,66);
1043 : (0,209:43,66);
2173 : (0,210:0,0) (0,210:0,69);
2174 : (0,210:0,69);
2175 : (0,209:79,61);
2177 : (0,209:8,58);
2178 : (0,209:48,59) (0,209:43,59);
2181 : (0,192:4,52);
2183 : (0,191:4,51);
2184 : (0,190:26,50) (0,116:10,0) (0,190:29,50) (0,190:12,50);
2186 : (0,189:4,49);
2188 : (0,188:4,48);
2189 : (0,186:52,47) (0,186:60,47);
2190 : (0,186:60,47) (0,186:2,47);
2192 : (0,186:8,47);
2193 : (0,184:9,46);
2194 : (0,180:14,45);
2195 : (0,179:14,43);
2196 : (0,177:20,41) (0,117:11,0);
2197 : (0,177:13,41) (0,177:17,41);
2198 : (0,176:20,40) (0,117:11,0);
2199 : (0,176:29,40);
2200 : (0,176:35,40);
2201 : (0,176:39,40);
2202 : (0,176:13,40);
2203 : (0,175:2,39) (0,117:11,0);
2205 : (0,175:14,39);
2206 : (0,173:25,38);
2207 : (0,173:13,38);
2208 : (0,172:25,37);
2209 : (0,172:13,37);
2210 : (0,171:25,36);
2211 : (0,171:13,36);
2212 : (0,170:26,35);
2213 : (0,170:29,35);
2214 : (0,170:35,35);
2215 : (0,170:39,35);
2216 : (0,170:13,35);
2217 : (0,168:26,34);
2218 : (0,168:13,34);
2219 : (0,167:26,33);
2220 : (0,167:13,33);
2221 : (0,166:26,32);
2222 : (0,166:13,32);
2223 : (0,165:27,31);
2224 : (0,165:30,31);
2225 : (0,165:36,31);
2226 : (0,165:40,31);
2227 : (0,165:13,31);
2228 : (0,158:8,30);
2230 : (0,157:8,29);
2232 : (0,155:7,28);
2234 : (0,154:6,27);
2236 : (0,147:7,26);
2237 : (0,146:10,25);
2238 : (0,145:10,24);
2239 : (0,144:9,23);
2240 : (0,143:9,22);
2241 : (0,142:9,21);
2242 : (0,141:9,20);
2243 : (0,140:9,19);
2244 : (0,139:9,18);
2245 : (0,138:9,17);
2246 : (0,137:9,16);
2247 : (0,133:12,15);
2249 : (0,129:8,14);
2250 : (0,126:8,13);
2251 : (0,125:8,12);
2252 : (0,124:10,11);
2254 : (0,123:10,10);
2256 : (0,122:10,9);
2258 : (0,119:1,8);
2259 : (0,118:11,0) (0,118:24,7);
2260 : (0,117:11,0) (0,117:24,6);
2261 : (0,116:26,4) (0,116:26,0) (0,116:10,0);
2262 : (0,116:26,3) (0,116:26,0) (0,116:10,0);
2263 : (0,116:26,2) (0,116:26,0) (0,116:10,0);
2264 : (0,114:5,0);
2266 : (0,116:26,1);
2435 : (0,116:10,0);
2444 : (0,122:15,0);
2447 : (0,123:15,0);
2450 : (0,124:15,0);
2453 : (0,133:17,0);
2456 : (0,154:17,0);
2459 : (0,155:17,0);
2462 : (0,157:17,0);
2465 : (0,158:17,0);
2467 : (0,175:56,0);
2469 : (0,186:50,0);
2471 : (0,188:49,0);
2473 : (0,189:25,0);
2475 : (0,191:66,0);
2477 : (0,192:25,0);
2479 : (0,116:10,0);
2487 : (0,116:10,0);

