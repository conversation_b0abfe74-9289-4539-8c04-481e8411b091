/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLink_config.h 23740 2019-12-02 10:17:10Z dep10330 $
  $Revision: 23740 $
*/

/**
 * @file
 * Features definitions for various KEyLink family members. It will be used as an assembler
 * include and as a C file include, and thus it has only preprocessor definitions.
 */

#ifndef PHCAIKEYLINK_CONFIG_H
#define PHCAIKEYLINK_CONFIG_H

/*@{*/

/* Note: KL3D (NCF2951,2952,2971,2972) not yet supported. */

/* SMART2A types */
#if defined(NCF2961) || defined(NCF2922)
#define SMART2A
#endif

/* TOKEN or ACTIC5G types */
#if  defined(NCF29A1) || defined(NCF29A2) \
  || defined(NCF29A3) || defined(NCF29A4) \
  || defined(NCF2953) || defined(NCF2954)
#define TOKEN
#endif

/* TOKEN-PLUS or ACTIC5G-PLUS types */
#if  defined(NCF29A7) || defined(NCF29A8) \
  || defined(NCF2957) || defined(NCF2958)
#define TOKENPLUS
#endif

/* TOKEN-SRX or ACTIC-SRX types */
#if  defined(NCF29AA) || defined(NCF295A)
#define TOKENSRX
#endif

/* TOKEN-SRX2 or ACTIC-SRX2 types */
#if  defined(NCF29AE) || defined(NCF295E)
#define TOKENSRX2
#endif

/* IMPACT types (same hardware and ROM code as SRX2 but different configuration) */
#if  defined(NCF29AC) || defined(NCF295C)
#define IMPACT
#endif

/**
 * Marks a feature as enabled
 */
#define CONFIG_YES 1u

/**
 * Marks a feature as disabled
 */
#define CONFIG_NO 0u

#ifndef ROMCODE_VERSION
/*
  ROMCODE_VERSION          The major version of the ROM code that this executable
                           is intended to work with.
                           This switch is mainly needed to select the correct
                           parameters for system calls.
                           If not defined, a compile error will occur.

                           The command line tool mrk3link.exe can be used to obtain
                           version information by the 'version' command.
                           The ROM code version is displayed (first hex byte) after
                           "Version item no. 03: ID 1000".

   Example (NCF29A1 v0c with ROM RC005):
     2-Link>v
     18 Version items read from target CPU.
     Version item no. 01: ID 0000, Version 00.02
     Version item no. 02: ID 0001, Version 06.00
     Version item no. 03: ID 1000, Version 04.06
     Version item no. 04: ID 1001, Version 07.01
     Version item no. 05: ID 1002, Version 03.00
     Version item no. 06: ID 1003, Version 05.03
     [...]
   ROMCODE_VERSION should be set to 4.

   Example (NCF29A7 B0 with ROM RC002):
     2-Link>v
     18 Version items read from target CPU.
     Version item no. 01: ID 0000, Version 01.00
     Version item no. 02: ID 0001, Version 11.00
     Version item no. 03: ID 1000, Version 01.06
     Version item no. 04: ID 1001, Version 07.03
     Version item no. 05: ID 1002, Version 03.01
     Version item no. 06: ID 1003, Version 05.05
     [...]
   ROMCODE_VERSION should be set to 1.



*/
#error  No ROM version specified, must set preprocessor symbol ROMCODE_VERSION to ROM Code major version
#endif

/* platform and compiler check */
#ifndef __tct_mrk3e__
#error "This project is intended to be compiled for a MRK-IIIe only."
#endif


#if defined(SMART2A)

  /* system characteristics */
  #define CONFIG_SFR_SPACE                               384u
  #define CONFIG_EROM_CELL_SIZE_IN_KBYTES                 16
  #define CONFIG_EROM_CONFIG_CELL_SIZE_STEP_IN_KBYTES      4
  #define CONFIG_RAM_SIZE_BYTES                        0x800

  /* feature control */
  #define PHFL_CONFIG_HAVE_LFCLOCK_DOUBLER                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_LFCLOCK_INPUT_FOR_SYSTIMER0       (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_WRITE_EROM                        (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_READ_EROM                         (CONFIG_YES)
  #define PHFL_CONFIG_UHF_IRQS_IN_INT3_SFR                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_XO_START_UP                       (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_PLL_START_UP                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_RFGATE                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PAINGATE                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_CP_ICP                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_XO                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_PLL                     (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_HS                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CS_CP_ICP_TRIM                    (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_RT_CONFIGURABLE_EROM_SIZE         (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_XSFRPG0_LOCK              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_FIXDIV_LOCK               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU1E                             (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_TOKEN_CPU_EXCEPTIONS              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CGAESDIS                  (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_BISTCON_ENABLE_DCBUS_EXT          (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CPUAUXDIVSEL              (CONFIG_YES)
  #define PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IMMO_BOOT_POWER_OPT               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IMMEDIATE_AUXDIVSEL_ASSIGNMENT    (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU_USER_SYSCALL_ACCESS           (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET                  (CONFIG_YES)

  #define PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS           (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_LF_TUNING_CAPS                    (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IMMO_SYNCHRONIZED_LFTUNE_LOAD     (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_3D_IMMO_AUTO_CLOCK_SELECTION      (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_3D_IMMO_CONFIGURABLE_1D_INPUT     (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_PRETRIMCHX_SFRS                   (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_MDI_CMD_CPU_TEST                  (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_PA_BITFIELDS                      (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_POWERSAVE_BOOT_DELAY              (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_VDDABRNTRIM_CLEAR                 (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_HITAG_FLEX                        (CONFIG_NO)

  /* config interfaces / constants */
  /* SYSTEM <-> USER interface addresses */
  #define L_GENFUNC_PARAMS_ADDR   0x180u
  #define L_HITAG_USER_PARAM_ADDR 0x180u


#elif defined(TOKEN)

  /* system characteristics */
  #define CONFIG_SFR_SPACE                               384u
  #define CONFIG_EROM_CELL_SIZE_IN_KBYTES                 32
  #define CONFIG_RAM_SIZE_BYTES                        0x800

  /* feature control */
  #define PHFL_CONFIG_HAVE_LFCLOCK_DOUBLER                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_LFCLOCK_INPUT_FOR_SYSTIMER0       (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_3D_IMMO_AUTO_CLOCK_SELECTION      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_WRITE_EROM                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_READ_EROM                         (CONFIG_YES)
  #define PHFL_CONFIG_UHF_IRQS_IN_INT3_SFR                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_XO_START_UP                       (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_PLL_START_UP                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_RFGATE                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PAINGATE                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_CP_ICP                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_XO                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_PLL                     (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_HS                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CS_CP_ICP_TRIM                    (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_VDDABRNTRIM_CLEAR                 (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_RT_CONFIGURABLE_EROM_SIZE         (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_XSFRPG0_LOCK              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_FIXDIV_LOCK               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU1E                             (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_TOKEN_CPU_EXCEPTIONS              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CGAESDIS                  (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_PRETRIMCHX_SFRS                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_BISTCON_ENABLE_DCBUS_EXT          (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_LF_TUNING_CAPS                    (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CPUAUXDIVSEL              (CONFIG_YES)
  #define PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IMMO_BOOT_POWER_OPT               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU_USER_SYSCALL_ACCESS           (CONFIG_YES)
  #define PHFL_CONFIG_WORKAROUND_FOR_VBATREG_READ            (CONFIG_YES)
#if ROMCODE_VERSION > 4
  // Note: not yet available in production ROM code RC005.
  #define PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS           (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_ULPEE_WRITE_WHITELIST_ULP15       (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_HITAG_FLEX                        (CONFIG_YES)
#else
  #define PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS           (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_ULPEE_WRITE_WHITELIST_ULP15       (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_HITAG_FLEX                        (CONFIG_NO)
#endif

  #define PHFL_CONFIG_HAVE_3D_IMMO_CONFIGURABLE_1D_INPUT     (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IMMEDIATE_AUXDIVSEL_ASSIGNMENT    (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IMMO_SYNCHRONIZED_LFTUNE_LOAD     (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_MDI_CMD_CPU_TEST                  (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_PA_BITFIELDS                      (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_POWERSAVE_BOOT_DELAY              (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET                  (CONFIG_NO)

  /* config interfaces / constants */
  /* SYSTEM <-> USER interface addresses */
  #define L_GENFUNC_PARAMS_ADDR   0x180u
  #define L_HITAG_USER_PARAM_ADDR 0x180u


#elif defined(TOKENPLUS)

  /* system characteristics */
  #define CONFIG_SFR_SPACE                               384u
  #define CONFIG_EROM_CELL_SIZE_IN_KBYTES                 32
  #define CONFIG_EROM_CONFIG_CELL_SIZE_STEP_IN_KBYTES      4
  #define CONFIG_RAM_SIZE_BYTES                        0x800

  /* feature control */
  #define PHFL_CONFIG_HAVE_LFCLOCK_INPUT_FOR_SYSTIMER0       (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_3D_IMMO_AUTO_CLOCK_SELECTION      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_3D_IMMO_CONFIGURABLE_1D_INPUT     (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_WRITE_EROM                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_READ_EROM                         (CONFIG_YES)
  #define PHFL_CONFIG_UHF_IRQS_IN_INT3_SFR                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_RFGATE                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PAINGATE                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_CP_ICP                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_XO                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_PLL                     (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_HS                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY                (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED       (CONFIG_YES)

  #define PHFL_CONFIG_HAVE_RT_CONFIGURABLE_EROM_SIZE         (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_XSFRPG0_LOCK              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_FIXDIV_LOCK               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU1E                             (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_TOKEN_CPU_EXCEPTIONS              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CGAESDIS                  (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_PRETRIMCHX_SFRS                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_BISTCON_ENABLE_DCBUS_EXT          (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_LF_TUNING_CAPS                    (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CPUAUXDIVSEL              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IMMEDIATE_AUXDIVSEL_ASSIGNMENT    (CONFIG_YES)
  #define PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IMMO_BOOT_POWER_OPT               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU_USER_SYSCALL_ACCESS           (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_MDI_SFR_WRITE_COMMANDS            (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS           (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_PF_DLATCH_EXTERNAL                (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_ULPEE_WRITE_WHITELIST_ULP15       (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_HITAG_FLEX                        (CONFIG_YES)

  #define PHFL_CONFIG_HAVE_CS_CP_ICP_TRIM                    (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IMMO_SYNCHRONIZED_LFTUNE_LOAD     (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_LFCLOCK_DOUBLER                   (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_MDI_CMD_CPU_TEST                  (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_PA_BITFIELDS                      (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_PLL_START_UP                      (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_POWERSAVE_BOOT_DELAY              (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET                  (CONFIG_NO)
  /* TODO: to be changed after MRA1 */
  #define PHFL_CONFIG_HAVE_VDDABRNTRIM_CLEAR                 (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_XO_START_UP                       (CONFIG_NO)
  #define PHFL_CONFIG_WORKAROUND_FOR_VBATREG_READ            (CONFIG_NO)

  /* config interfaces / constants */
  /* SYSTEM <-> USER interface addresses */
  #define L_GENFUNC_PARAMS_ADDR   0x180u
  #define L_HITAG_USER_PARAM_ADDR 0x180u


#elif defined(TOKENSRX)

  /* system characteristics */
  #define CONFIG_SFR_SPACE                               384u
  #define CONFIG_EROM_CELL_SIZE_IN_KBYTES                 64
  #define CONFIG_EROM_CONFIG_CELL_SIZE_STEP_IN_KBYTES      4
  #define CONFIG_RAM_SIZE_BYTES                       0x1000

  /* feature control */
  #define PHFL_CONFIG_HAVE_LFCLOCK_INPUT_FOR_SYSTIMER0       (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_3D_IMMO_AUTO_CLOCK_SELECTION      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_3D_IMMO_CONFIGURABLE_1D_INPUT     (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_WRITE_EROM                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_READ_EROM                         (CONFIG_YES)
  #define PHFL_CONFIG_UHF_IRQS_IN_INT3_SFR                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_RFGATE                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PAINGATE                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_CP_ICP                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_XO                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_PLL                     (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_HS                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY                (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED       (CONFIG_YES)

  #define PHFL_CONFIG_HAVE_RT_CONFIGURABLE_EROM_SIZE         (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_XSFRPG0_LOCK              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_FIXDIV_LOCK               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU1E                             (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_TOKEN_CPU_EXCEPTIONS              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CGAESDIS                  (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_PRETRIMCHX_SFRS                   (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_BISTCON_ENABLE_DCBUS_EXT          (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_LF_TUNING_CAPS                    (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CPUAUXDIVSEL              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IMMEDIATE_AUXDIVSEL_ASSIGNMENT    (CONFIG_YES)
  #define PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL               (CONFIG_NO)     /* changed */
  #define PHFL_CONFIG_HAVE_IMMO_BOOT_POWER_OPT               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU_USER_SYSCALL_ACCESS           (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_LFA_TEST_SLICER_OUTPUT            (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_MDI_SFR_WRITE_COMMANDS            (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS           (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_PF_DLATCH_EXTERNAL                (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL           (CONFIG_YES)    /* NEW */
  #define PHFL_CONFIG_HAVE_TSTMEM_PF2_SUPPORT                (CONFIG_YES)    /* NEW */
  #define PHFL_CONFIG_HAVE_SYSTIMER0_PRESCALER               (CONFIG_YES)    /* NEW */

  #define PHFL_CONFIG_HAVE_HITAG_FLEX                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_ULPEE_WRITE_WHITELIST_ULP15       (CONFIG_YES)

  #define PHFL_CONFIG_HAVE_CS_CP_ICP_TRIM                    (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IMMO_SYNCHRONIZED_LFTUNE_LOAD     (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_LFCLOCK_DOUBLER                   (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_MDI_CMD_CPU_TEST                  (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_PA_BITFIELDS                      (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_PLL_START_UP                      (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_POWERSAVE_BOOT_DELAY              (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET                  (CONFIG_NO)
  /* TODO: to be changed after MRA1 */
  #define PHFL_CONFIG_HAVE_VDDABRNTRIM_CLEAR                 (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_XO_START_UP                       (CONFIG_NO)
  #define PHFL_CONFIG_WORKAROUND_FOR_VBATREG_READ            (CONFIG_NO)

  /* config interfaces / constants */
  /* SYSTEM <-> USER interface addresses */
  #define L_GENFUNC_PARAMS_ADDR   0x180u
  #define L_HITAG_USER_PARAM_ADDR 0x180u


#elif ( defined(TOKENSRX2) || defined(IMPACT) )

  /* system characteristics */
  #define CONFIG_SFR_SPACE                               384u
  #define CONFIG_EROM_CELL_SIZE_IN_KBYTES                 64
  #define CONFIG_EROM_CONFIG_CELL_SIZE_STEP_IN_KBYTES      4
  #define CONFIG_RAM_SIZE_BYTES                       0x1000

  /* feature control */
  #define PHFL_CONFIG_HAVE_LFCLOCK_INPUT_FOR_SYSTIMER0       (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_3D_IMMO_AUTO_CLOCK_SELECTION      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_3D_IMMO_CONFIGURABLE_1D_INPUT     (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_WRITE_EROM                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_READ_EROM                         (CONFIG_YES)
  #define PHFL_CONFIG_UHF_IRQS_IN_INT3_SFR                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_RFGATE                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PAINGATE                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_CP_ICP                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_XO                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_PLL                     (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SET_PTRIM_HS                      (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY                (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED       (CONFIG_YES)

  #define PHFL_CONFIG_HAVE_RT_CONFIGURABLE_EROM_SIZE         (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_XSFRPG0_LOCK              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_DEVLOCK_FIXDIV_LOCK               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU1E                             (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_TOKEN_CPU_EXCEPTIONS              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CGAESDIS                  (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_PRETRIMCHX_SFRS                   (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_BISTCON_ENABLE_DCBUS_EXT          (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_LF_TUNING_CAPS                    (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_CLKCON4_CPUAUXDIVSEL              (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IMMEDIATE_AUXDIVSEL_ASSIGNMENT    (CONFIG_YES)
  #define PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL               (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IMMO_BOOT_POWER_OPT               (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_IIU_USER_SYSCALL_ACCESS           (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_LFA_TEST_SLICER_OUTPUT            (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_MDI_SFR_WRITE_COMMANDS            (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SI_NONBLOCKING_COMMANDS           (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_PF_DLATCH_EXTERNAL                (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL           (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_TSTMEM_PF2_SUPPORT                (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_SYSTIMER0_PRESCALER               (CONFIG_YES)

  #define PHFL_CONFIG_HAVE_HITAG_FLEX                        (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_ULPEE_WRITE_WHITELIST_ULP15       (CONFIG_YES)

  #define PHFL_CONFIG_HAVE_CS_CP_ICP_TRIM                    (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IMMO_SYNCHRONIZED_LFTUNE_LOAD     (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_LFCLOCK_DOUBLER                   (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_MDI_CMD_CPU_TEST                  (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_PA_BITFIELDS                      (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_PLL_START_UP                      (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_POWERSAVE_BOOT_DELAY              (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET                  (CONFIG_NO)
  #define PHFL_CONFIG_HAVE_IIU_CAT_SYSCALL                   (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_VDDABRNTRIM_CLEAR                 (CONFIG_YES)
  #define PHFL_CONFIG_HAVE_XO_START_UP                       (CONFIG_NO)
  #define PHFL_CONFIG_WORKAROUND_FOR_VBATREG_READ            (CONFIG_NO)

  /* config interfaces / constants */
  /* SYSTEM <-> USER interface addresses */
  #define L_GENFUNC_PARAMS_ADDR   0x180u
  #define L_HITAG_USER_PARAM_ADDR 0x180u

#else
  #error Platform not supported.
#endif


 /**
 * Represents the address of the general functions parameters in USER DM area.
 * Used for passing the system call arguments and return data, from system mode to user
 * mode and vice versa
 * @warning Defined here as empty intentionally -> in case it is not defined in the build platform block the compilation MUST break
 */
#define GENFUNC_PARAMS_ADDR   (L_GENFUNC_PARAMS_ADDR)

/**
 * Definition of the address of the HITAG user parameter.
 * Used for passing the USER immobilizer command argument, from system mode to user mode
 * @warning Defined here as empty intentionally -> in case it is not defined in the build platform block the compilation MUST break
 */
#define HITAG_USER_PARAM_ADDR (L_HITAG_USER_PARAM_ADDR)

/*@}*/

#endif /* PHCAIKEYLINK_CONFIG_H */

