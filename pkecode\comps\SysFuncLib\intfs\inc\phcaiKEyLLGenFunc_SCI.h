/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_SCI.h 21074 2019-07-22 13:39:34Z dep10330 $
  $Revision: 21074 $
*/

/**
 * @file
 * Declarations of data types required by the system functions caller stubs.
 */

#ifndef PHCAIKEYLLGENFUNC_SCI_H
#define PHCAIKEYLLGENFUNC_SCI_H

#include "types.h"
#include "phcaiMRK3Versions.h"
#include "phcaiKEyLink_config.h"

/**
 * @addtogroup genfunclib_syscall API functions support
 * Defines the application-side system call interface.
 * @{
 */

/**
 * Provides a common understanding among all components where the
 * LFTUNE_ULPEE_PAGE is.
 */
#define LFTUNE_ULPEE_PAGE 0x3D4U

/**
 * Lists the possible parallel EEPROM read access modes.
 */
typedef enum phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode
{
  /** Automatic read mode. */
  EE_RAM_AUT = 0x00,
  /** Self timed read mode. */
  EE_RAM_STR = 0x10
} phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode_t;

/**
 * Lists the implemented transponder family types.
 */
typedef enum phcaiKEyLLGenFunc_SCI_Immo_Type
{
  /** HITAG2 transponder. */
  IMMO_HT2,
  /** HITAG2-Extended transponder. */
  IMMO_HT2E,
  /** HITAG3 transponder. */
  IMMO_HT3,
  /** HITAG-AES transponder. */
  IMMO_HTAES,
  /** HITAG-Pro transponder. */
  IMMO_HTPRO,
  /** Undefined transponder. */
  IMMO_UNDEF
} phcaiKEyLLGenFunc_SCI_Immo_Type_t;

/**
 * Defines the possible return values for the functions that program
 * the parallel EEPROM.
 *
 * @note The error codes are bit-coded.
 * @note ::EE_WR_HV_ERR and ::EE_WR_BRN_ERR can both occur simultaneously.
 */
typedef enum eeprom_write_error
{
  /** Writing and programming was successful. */
  EE_WR_OK        = 0x00,
  /** A brownout error occurred. */
  EE_WR_BRN_ERR   = 0x01,
  /** An HV error occurred. */
  EE_WR_HV_ERR    = 0x02,
  /** An invalid address was passed to the function. */
  EE_WR_ADDR_ERR  = 0x04,
  /** Indicates a zero length operation. */
  EE_WR_LEN_ZERO = 0x08,
  /** Indicates an invalid start address. */
  EE_WR_INVALID_START_ADDR = 0x18,
  /** Indicates an invalid end address. */
  EE_WR_INVALID_END_ADDR = 0x28,
  /** Indicates an unaligned address. */
  EE_WR_UNALIGNED_ERR = 0x38,
  /** Indicates that this system call is not permitted. */
  EE_WR_FORBIDDEN_ERR = 0x40,
  /** Indicates that the given length value is outside the valid range. */
  EE_WR_INVALID_LEN_ERR = 0x48

} eeprom_write_error_t;

/** Parameter list for the function phcaiKEyLLHal_AES_LoadData(). */
typedef struct phcaiKEyLLGenFunc_SCI_AES_Load_Data
{
  /** Pointer to the byte array to be loaded. */
  const uint8_t* data;
  /** Number of bytes to be loaded. */
  uint8_t len;
} phcaiKEyLLGenFunc_SCI_AES_Load_Data_t;

/** Parameter list for the function phcaiKEyLLHal_AES_LoadKey(). */
typedef struct phcaiKEyLLGenFunc_SCI_AES_Load_Key
{
  /** Pointer to the byte array containing the key. */
  const uint8_t* key;
} phcaiKEyLLGenFunc_SCI_AES_Load_Key_t; /* PRQA S 0779 */

/** Parameter list for the function phcaiKEyLLHal_AES_Encrypt(). */
typedef struct phcaiKEyLLGenFunc_SCI_AES_Encrypt
{
  /** Result of the encryption (16 Byte AES output vector). */
  uint8_t* output_vector;
} phcaiKEyLLGenFunc_SCI_AES_Encrypt_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_SI_Get(). */
typedef struct phcaiKEyLLGenFunc_SCI_SI_Get
{
  /** Address of the SI field. */
  uint32_t* si_field;
  /** Address of the calculated SI value (array of 4 uint8_t values). */
  uint8_t* si;
} phcaiKEyLLGenFunc_SCI_SI_Get_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_SI_Inc(). */
typedef struct phcaiKEyLLGenFunc_SCI_SI_Inc
{
  /** Address of the SI field. */
  uint32_t* si_field;
  /** Page number of the first (of eight) SI page. */
  uint8_t page;
} phcaiKEyLLGenFunc_SCI_SI_Inc_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_SI_Init(). */
typedef struct phcaiKEyLLGenFunc_SCI_SI_Init
{
  /** Address of the SI field. */
  uint32_t* si_field;
  /** Page number of the first (of eight) SI page. */
  uint8_t page;
} phcaiKEyLLGenFunc_SCI_SI_Init_t;

/**
 * Result codes of Extended SI functions.
 *
 * @see phcaiKEyLLGenFunc_CS_SI_Init_Ext()
 * @see phcaiKEyLLGenFunc_CS_SI_Inc_Ext()
 */
typedef enum SI_RESULT
{
  /** Success. */
  SI_SUCCESS  = 0,
  /** Repair not possible. */
  SI_NOREPAIR = 1,
  /** ULPEE programming error. */
  SI_PRGFAIL  = 2,
  /** Overflow condition (all pages are equal to the maximum value). */
  SI_OVERFLOW = 3,
  /** Invalid parameter(s). */
  SI_INVPARAM = 4
} SI_RESULT_t;

#if defined(PHFL_CONFIG_HAVE_PA_BITFIELDS) && (PHFL_CONFIG_HAVE_PA_BITFIELDS == CONFIG_YES)
/**
 * Bitfield selection for the PA.
 *
 * @note This enumeration is SMART2 T1A specific.
 */
typedef enum PA_bitfield_selector
{
  VCasc  = 0,
  Spare = 1,
  CapSel  = 2,
  VDriver = 3,
  MaxCurrent = 4
} PA_bitfield_selector_t;
#endif

/**
 * Parameter list and return value for the functions
 * phcaiKEyLLGenFunc_CS_SI_Init_Ext(), phcaiKEyLLGenFunc_CS_SI_Inc_Ext(),
 * phcaiKEyLLGenFunc_CS_SI_Init_Ext_NoWait(), and
 * phcaiKEyLLGenFunc_CS_SI_Inc_Ext_NoWait().
 */
typedef struct phcaiKEyLLGenFunc_SCI_SI_Ext
{
  /** Address of the SI field. */
  uint32_t* si_field;
  /** Page number of the first (of eight) SI page. */
  uint16_t page;
  /**
   * Maximal number of pages to repair.
   *
   * @see phcaiKEyLLGenFunc_CS_SI_Init_Ext()
   * @see phcaiKEyLLGenFunc_CS_SI_Init_Ext_NoWait()
   */
  uint8_t  maxnumrepair;
  /** Result code. */
  SI_RESULT_t result;
} phcaiKEyLLGenFunc_SCI_SI_Ext_t;

/**
 * Parameter list and return value for the functions phcaiKEyLLGenFunc_CS_EROM_read()
 * and phcaiKEyLLGenFunc_CS_EROM_write().
 */
typedef struct phcaiKEyLLGenFunc_SCI_erom_rw
{
  /** Start of the user RAM buffer. */
  uint16_t* ram_buffer_start;
  /** Address of the EROM start. */
  uint16_t erom_physical_start;
  /** Number of EROM bytes to deal with. */
  uint16_t len;
  /**
   * Result code.
   * @note The error codes of EEPROM are reused.
   */
  eeprom_write_error_t result;
} phcaiKEyLLGenFunc_SCI_erom_rw_t;

#if defined(PHFL_CONFIG_HAVE_PA_BITFIELDS) && (PHFL_CONFIG_HAVE_PA_BITFIELDS == CONFIG_YES)
/**
 * Parameter list for the functions phcaiKEyLLGenFunc_CS_sm2_pa_get() and
 * phcaiKEyLLGenFunc_CS_sm2_pa_set().
 */
typedef struct phcaiKEyLLGenFunc_SCI_PA_sm2
{
  /** Bitfield to work on. */
  PA_bitfield_selector_t bitfield;
  /** Start of the user RAM buffer. */
  uint8_t value;
} phcaiKEyLLGenFunc_SCI_PA_sm2_t;
#endif

/** Parameter list for the function phcaiMRK3MDI_PrintComment(). */
typedef struct phcaiKEyLLGenFunc_SCI_MDI_Prnt
{
  /** Pointer to the text string. */
  const string_t* input_string;
} phcaiKEyLLGenFunc_SCI_MDI_Prnt_t;

/** Parameter list for the function phcaiMRK3MDI_PrintCommentLn(). */
typedef struct phcaiKEyLLGenFunc_SCI_MDI_PrntLn
{
  /** Pointer to the text string. */
  const string_t* input_string;
} phcaiKEyLLGenFunc_SCI_MDI_PrntLn_t;

/**
 * Parameter list and return value for the function
 * phcaiKEyLLGenFunc_CS_Immo_Init().
 */
typedef struct phcaiKEyLLGenFunc_SCI_Immo_Init
{
  /** Immobilizer type to be initialized. */
  phcaiKEyLLGenFunc_SCI_Immo_Type_t immo_type;
  /** Error indicator. */
  error_t return_val;
} phcaiKEyLLGenFunc_SCI_Immo_Init_t;

/** Return value for the function phcaiKEyLLGenFunc_CS_Immo_Reset(). */
typedef struct phcaiKEyLLGenFunc_SCI_Immo_Reset
{
  /** Error indicator. */
  error_t return_val;
} phcaiKEyLLGenFunc_SCI_Immo_Reset_t;


/**
 * Parameter list and return value for the function
 * phcaiKEyLLGenFunc_CS_Immo_ReceiveCmd().
 */
typedef struct phcaiKEyLLGenFunc_SCI_Immo_Rcv
{
  /** Pointer to the receive buffer. */
  uint8_t* rcv_buff;
  /** Number of received bits. */
  uint8_t* len;
  /** Error indicator. */
  error_t return_val;
} phcaiKEyLLGenFunc_SCI_Immo_Rcv_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_Immo_ExecuteCmd(). */
typedef struct phcaiKEyLLGenFunc_SCI_Immo_Exec
{
  /** Pointer to the data to be processed. */
  uint8_t* rcv_data;
  /** Number of data bits. */
  uint8_t len;
} phcaiKEyLLGenFunc_SCI_Immo_Exec_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_EEPROM_Enable(). */
typedef struct phcaiKEyLLGenFunc_SCI_EEPROM_Enable
{
  /** Selected read access mode. */
  phcaiKEyLLGenFunc_SCI_EEPROM_Read_Access_Mode_t eeprom_read_access_mode;
} phcaiKEyLLGenFunc_SCI_EEPROM_Enable_t;

/**
 * Parameter list and return value for the function
 * phcaiKEyLLGenFunc_CS_EEPROM_Program().
 */
typedef struct phcaiKEyLLGenFunc_SCI_EEPROM_Prog
{
  /** Destination address within the EEPROM. */
  volatile uint8_t* addr;
  /** Pointer to the data that shall be written. */
  const uint8_t* data;
  /** Number of bytes to be written. */
  uint16_t len;
  /**
   * Indicates whether the function shall return immediately or wait until
   * programming is finished.
   */
  bool_t wait;
  /** Error indicator. */
  eeprom_write_error_t return_val;
} phcaiKEyLLGenFunc_SCI_EEPROM_Prog_t;

/**
 * Parameter list and return value for the functions
 * phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage() and
 * phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage_LE().
 */
typedef struct phcaiKEyLLGenFunc_SCI_ULPEE_WritePage
{
  /** Pointer to the first of the 4 data bytes. */
  const uint8_t* data;
  /** Page address within the ULP EEPROM. */
  uint16_t addr;
  /** Defines whether the function shall wait until programming
   * is finished or not. */
  bool_t wait;
  /** Error indicator. */
  error_t return_val;
} phcaiKEyLLGenFunc_SCI_ULPEE_WritePage_t;

/**
 * Parameter list for the functions phcaiKEyLLGenFunc_CS_ULPEE_ReadPage()
 * and phcaiKEyLLGenFunc_CS_ULPEE_ReadPage_LE().
 */
typedef struct phcaiKEyLLGenFunc_SCI_ULPEE_ReadPage
{
  /** Data being read from ULP EEPROM. */
  uint8_t* data;
  /** ULP page address where to start reading. */
  uint16_t addr;
 } phcaiKEyLLGenFunc_SCI_ULPEE_ReadPage_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_ULPEE_ReadBytes(). */
typedef struct phcaiKEyLLGenFunc_SCI_ULPEE_ReadBytes
{
  /** Data read from ULP. */
  uint8_t* data;
  /** ULP byte address where to start reading. */
  uint16_t byte_addr;
  /** Number of bytes to be read. */
  uint16_t num_bytes;
} phcaiKEyLLGenFunc_SCI_ULPEE_ReadBytes_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_GetVersion(). */
typedef struct phcaiKEyLLGenFunc_SCI_GetVersion
{
  /** Pointer to the version data. */
  phcaiMRK3Versions_Info_t* version;
} phcaiKEyLLGenFunc_SCI_GetVersion_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_SetLfclkx2Dis(). */
typedef struct phcaiKEyLLGenFunc_SCI_SetLfclkx2Dis
{
  /** Defines whether LFCLKX2DIS shall be set or cleared. */
  bool_t set;
} phcaiKEyLLGenFunc_SCI_SetLfclkx2Dis_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_MdiClkSel(). */
typedef struct phcaiKEyLLGenFunc_SCI_set_MdiClkSel
{
  /** Defines the value to be written to MDICLKSEL (only lower 2 bits). */
  uint8_t val;
} phcaiKEyLLGenFunc_SCI_set_MdiClkSel_t;


#if defined(PHFL_CONFIG_HAVE_XO_START_UP) && (PHFL_CONFIG_HAVE_XO_START_UP == CONFIG_YES)
/**
 * Parameter list and return value for the function
 * phcaiKEyLLGenFunc_CS_UHF_XoStartUp().
 */
typedef struct phcaiKEyLLGenFunc_SCI_XOStartUp
{
  /** Defines whether the function shall perform an XO ready validation or not. */
  bool_t validate;
  /** Defines whether the XO_READY signal shall be forced to 1 or not. */
  bool_t force_xo_ready;
  /** Error indicator. */
  error_t return_val;
} phcaiKEyLLGenFunc_SCI_XOStartUp_t;
#endif

#if defined(PHFL_CONFIG_HAVE_PLL_START_UP) && (PHFL_CONFIG_HAVE_PLL_START_UP == CONFIG_YES)
/**
 * Parameter list and return value for the function
 * phcaiKEyLLGenFunc_CS_UHF_PllStartUp().
 */
typedef struct phcaiKEyLLGenFunc_SCI_PllStartUp
{
  /** Defines whether the function shall perform a VCO calibration or not. */
  bool_t calibrate;
  /** Defines whether PLL_LOCK_READY shall be permanently forced to 1 or not. */
  bool_t force_pll_lock_ready;
  /**
   * Value to be written into CAL_IDAC_CTRL[4:0] in the VCO calibration
   * control register.
   */
  uint8_t cal_idac_ctrl;
  /** Error indicator. */
  error_t return_val;
} phcaiKEyLLGenFunc_SCI_PllStartUp_t;
#endif

/** Parameter list for the function phcaiKEyLLGenFunc_CS_UHF_SetRfGate(). */
typedef struct phcaiKEyLLGenFunc_SCI_SetRFGate
{
  /** Defines whether the RF Gate shall be turned off (TRUE) or not (FALSE). */
  bool_t rfgate_off;
} phcaiKEyLLGenFunc_SCI_SetRFGate_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_UHF_SetPAInGate(). */
typedef struct phcaiKEyLLGenFunc_SCI_SetPAINGate
{
  /** Defines whether the PA-IN Gate shall be turned off (TRUE) or not (FALSE). */
  bool_t paingate_off;
} phcaiKEyLLGenFunc_SCI_SetPAINGate_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_UHF_SetCP_ICP(). */
typedef struct phcaiKEyLLGenFunc_SCI_SetCPICP
{
  /** Defines the value to be written to CP ICP (lower 4 bits). */
  uint8_t value;
} phcaiKEyLLGenFunc_SCI_SetCPICP_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_XO(). */
typedef struct phcaiKEyLLGenFunc_SCI_SetPTRIMXO
{
  /** Defines the value to be written to PTRIM XO (lower 5 bits). */
  uint8_t value;
} phcaiKEyLLGenFunc_SCI_SetPTRIMXO_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_PLL(). */
typedef struct phcaiKEyLLGenFunc_SCI_SetPTRIMPLL
{
  /** Defines the value to be written to PTRIM PLL (lower 5 bits). */
  uint8_t value;
} phcaiKEyLLGenFunc_SCI_SetPTRIMPLL_t;

/** Parameter list for the function phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_HS(). */
typedef struct phcaiKEyLLGenFunc_SCI_SetPTRIMHS
{
  /** Defines the value to be written to PTRIM HS (lower 5 bits). */
  uint8_t value;
} phcaiKEyLLGenFunc_SCI_SetPTRIMHS_t;

/**
 * Parameter list for the functions phcaiKEyLLGenFunc_CS_ADC_enable_ext_input()
 * and phcaiKEyLLGenFunc_CS_ADC_disable_ext_input().
 */
typedef struct phcaiKEyLLGenFunc_SCI_EnableDCBUS_ext
{
  /** Defines whether a DCBUS external measurement pin configuration should be set. */
  bool_t enable;
} phcaiKEyLLGenFunc_SCI_EnableDCBUS_ext_t;

#if (defined(PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL) && \
        (PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL == CONFIG_YES))
/**
 * Parameter list for the functions phcaiKEyLLGenFunc_CS_ADC_poweron() and
 * phcaiKEyLLGenFunc_CS_ADC_poweroff().
 */
typedef struct phcaiKEyLLGenFunc_SCI_Enable_ADC_POWERON_ext
{
  /** Enables or disables the the POWERON bit in ADCTEST. */
  bool_t enable;
} phcaiKEyLLGenFunc_SCI_Enable_ADC_POWERON_ext_t;
#endif

/**
 * Parameter list for the functions phcaiKEyLLGenFunc_CS_clear_VDDABRNOUT()
 * and phcaiKEyLLGenFunc_CS_restore_VDDABRNOUT().
 */
typedef struct phcaiKEyLLGenFunc_SCI_clear_VDDABRNTRIM
{
  /**
   * Argument controlling whether the the VDDABRNTRIM value in PTRIM0
   * will be cleared or restored from ULP EEPROM.
   */
  bool_t clear;
} phcaiKEyLLGenFunc_SCI_clear_VDDABRNTRIM_t;

/**
 * Parameter list and return value for the function
 * phcaiKEyLLGenFunc_CS_IIU_receive().
 */
typedef struct  phcaiKEyLLGenFunc_SCI_IIU_receive
{
  /**
   * Byte buffer used by the syscall to pass the received data to the
   * user application.
   */
  uint8_t* data;
  /** Indicates the amount of @b bits received. */
  uint8_t number_of_rcv_bits;
  /** Single byte indicating the size of the data byte buffer. */
  uint8_t buffer_size;
  /** Error code. */
  error_t retval;
} phcaiKEyLLGenFunc_SCI_IIU_receive_t;

/**
 * Parameter list and return value for the function
 * phcaiKEyLLGenFunc_CS_IIU_send().
 */
typedef struct  phcaiKEyLLGenFunc_SCI_IIU_send
{
  /** Byte buffer containing the data to be sent. */
  uint8_t* data;
  /** Number indicating the number of bytes to be sent. */
  uint8_t number_of_bytes;
  /** Error code. */
  error_t retval;
} phcaiKEyLLGenFunc_SCI_IIU_send_t;

#if defined(PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET) && (PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET == CONFIG_YES)
/** Structure used to pass parameters for syscall phcaiKeyLLGenFunc_SCI_IIU_TIMX_SFR_SET */
typedef struct phcaiKEyLLGenFunc_SCI_IIU_TIMX_SFR_set
{
  /** index of the TIMX SFR to be set */
  uint8_t timx_index;
  /** desired value of the TIMX SFR */
  uint8_t value;
  /** syscalls return value */
  error_t retval;
} phcaiKEyLLGenFunc_SCI_IIU_TIMX_SFR_set_t;
#endif

#if defined(PHFL_CONFIG_HAVE_IIU_CAT_SYSCALL) && \
        (PHFL_CONFIG_HAVE_IIU_CAT_SYSCALL == CONFIG_YES)

/** Parameter list and return value for phcaiKEyLLGenFunc_CS_IIU_CatSend() */
typedef struct phcaiKEyLLGenFunc_SCI_IIU_CatSend
{
  /** Byte buffer containing the data to be sent. */
  const uint8_t* data;
  /** Number of bytes to be sent. */
  uint8_t number_of_bytes;
  /** Start of carrier-off time (14-bit counter value). */
  uint16_t t_ch_resp;
  /** Error code. */
  error_t retval;
} phcaiKEyLLGenFunc_SCI_IIU_CatSend_t;

#endif

#if (defined(PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY) && \
        (PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY == CONFIG_YES))
/** Parameter list for the function phcaiKEyLLGenFunc_CS_UHF_ForceXoReady(). */
typedef struct phcaiKEyLLGenFunc_SCI_UHF_ForceXoReady
{
  /** Determines whether the value of XO_READY should be forced to 1. */
  bool_t value;
} phcaiKEyLLGenFunc_SCI_UHF_ForceXoReady_t;
#endif

#if (defined(PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED) && \
        (PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED == CONFIG_YES))
/** Parameter list for the function phcaiKEyLLGenFunc_CS_UHF_ForcePllLockDetected(). */
typedef struct phcaiKEyLLGenFunc_SCI_UHF_ForcePllLockDetected
{
  /** Determines whether the value of PLL_LOCK_DETECTED should be forced to 1. */
  bool_t value;
} phcaiKEyLLGenFunc_SCI_UHF_ForcePllLockDetected_t;
#endif

#if (defined(PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL) && \
      (PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL == CONFIG_YES))
/** Parameter structure for syscall phcaiKEyLLGenFunc_CS_EROM_enable_PF2(). */
typedef struct phcaiKEyLLGenFunc_SCI_EROM_Enable
{
  /* Determines the desired EROM enabled state. */
  bool_t value;
} phcaiKEyLLGenFunc_SCI_EROM_Enable_t;
#endif

#if (defined(PHFL_CONFIG_HAVE_LFA_TEST_SLICER_OUTPUT) && \
     (PHFL_CONFIG_HAVE_LFA_TEST_SLICER_OUTPUT == CONFIG_YES))
/** LFA slicer output syscall options. */
typedef enum phcaiKEyLLGenFunc_CS_VbatReg_TestLfaSlicerOutputOption
{
  /** Disable slicer output. */
  LFA_SLICER_OUTPUT_DISABLE = 0,
  /** Output the CMF slicer output on P27. */
  LFA_SLICER_OUTPUT_ENABLE_CMF = 1,
  /** Output the MMF slicer output on P27. */
  LFA_SLICER_OUTPUT_ENABLE_MMF = 2
} phcaiKEyLLGenFunc_CS_VbatReg_TestLfaSlicerOutputOption_t;
#endif

/** Parameter lists of the system call functions. */
typedef union phcaiKEyLLGenFunc_SCI_Parameter_List /* PRQA S 750,759,5013 1 */
{
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_AES_LoadKey(). */
  phcaiKEyLLGenFunc_SCI_AES_Load_Key_t aes_load_key;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_AES_LoadData(). */
  phcaiKEyLLGenFunc_SCI_AES_Load_Data_t aes_load_data;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_AES_Encrypt(). */
  phcaiKEyLLGenFunc_SCI_AES_Encrypt_t aes_encrypt;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_SI_Get(). */
  phcaiKEyLLGenFunc_SCI_SI_Get_t si_get;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_SI_Inc(). */
  phcaiKEyLLGenFunc_SCI_SI_Inc_t si_inc;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_SI_Init(). */
  phcaiKEyLLGenFunc_SCI_SI_Init_t si_init;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_MDI_Print(). */
  phcaiKEyLLGenFunc_SCI_MDI_Prnt_t mdi_prnt;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_MDI_PrintLn(). */
  phcaiKEyLLGenFunc_SCI_MDI_PrntLn_t mdi_prntln;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_Immo_Init(). */
  phcaiKEyLLGenFunc_SCI_Immo_Init_t immo_init;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_Immo_Reset(). */
  phcaiKEyLLGenFunc_SCI_Immo_Reset_t immo_reset;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_Immo_ReceiveCmd(). */
  phcaiKEyLLGenFunc_SCI_Immo_Rcv_t immo_rcv;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_Immo_ExecuteCmd(). */
  phcaiKEyLLGenFunc_SCI_Immo_Exec_t immo_exec;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_EEPROM_Enable(). */
  phcaiKEyLLGenFunc_SCI_EEPROM_Enable_t eeprom_en;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_EEPROM_Program().*/
  phcaiKEyLLGenFunc_SCI_EEPROM_Prog_t eeprom_prog;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(). */
  phcaiKEyLLGenFunc_SCI_ULPEE_WritePage_t ulpee_write_pg;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_ULPEE_ReadPage().*/
  phcaiKEyLLGenFunc_SCI_ULPEE_ReadPage_t ulpee_read_pg;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_ULPEE_ReadBytes(). */
  phcaiKEyLLGenFunc_SCI_ULPEE_ReadBytes_t ulpee_read_bytes;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_GetVersion(). */
  phcaiKEyLLGenFunc_SCI_GetVersion_t get_version;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_SetLfclkx2Dis(). */
  phcaiKEyLLGenFunc_SCI_SetLfclkx2Dis_t set_lfclkx2dis;
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_MdiClkSel(). */
  phcaiKEyLLGenFunc_SCI_set_MdiClkSel_t set_mdiclksel;


#if defined(PHFL_CONFIG_HAVE_XO_START_UP) && (PHFL_CONFIG_HAVE_XO_START_UP == CONFIG_YES)
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_UHF_XoStartUp(). */
  phcaiKEyLLGenFunc_SCI_XOStartUp_t xo_startup;
#endif

#if defined(PHFL_CONFIG_HAVE_PLL_START_UP) && (PHFL_CONFIG_HAVE_PLL_START_UP == CONFIG_YES)
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_UHF_PllStartUp(). */
  phcaiKEyLLGenFunc_SCI_PllStartUp_t pll_startup;
#endif

#if defined(PHFL_CONFIG_HAVE_SET_RFGATE) && (PHFL_CONFIG_HAVE_SET_RFGATE == CONFIG_YES)
  /** Parameter list for syscall function phcaiKEyLLGenFunc_CS_UHF_SetRFGate(). */
  phcaiKEyLLGenFunc_SCI_SetRFGate_t set_rfgate;
#endif

#if defined(PHFL_CONFIG_HAVE_SET_PAINGATE) && (PHFL_CONFIG_HAVE_SET_PAINGATE == CONFIG_YES)
  /** Parameter list for syscall function phcaiKEyLLHal_UHF_SetPAInGate(). */
  phcaiKEyLLGenFunc_SCI_SetPAINGate_t set_paingate;
#endif

#if defined(PHFL_CONFIG_HAVE_SET_CP_ICP) && (PHFL_CONFIG_HAVE_SET_CP_ICP == CONFIG_YES)
  /** Parameter list for syscall function phcaiKEyLLHal_UHF_SetCP_ICP(). */
  phcaiKEyLLGenFunc_SCI_SetCPICP_t set_cpicp;
#endif

#if defined(PHFL_CONFIG_HAVE_SET_PTRIM_XO) && (PHFL_CONFIG_HAVE_SET_PTRIM_XO == CONFIG_YES)
  /** Parameter list for syscall function phcaiKEyLLHal_UHF_SetPTRIM_XO(). */
  phcaiKEyLLGenFunc_SCI_SetPTRIMXO_t set_ptrimxo;
#endif

#if defined(PHFL_CONFIG_HAVE_SET_PTRIM_PLL) && (PHFL_CONFIG_HAVE_SET_PTRIM_PLL == CONFIG_YES)
  /** Parameter list for syscall function phcaiKEyLLHal_UHF_SetPTRIM_PLL(). */
  phcaiKEyLLGenFunc_SCI_SetPTRIMPLL_t set_ptrimpll;
#endif

#if defined(PHFL_CONFIG_HAVE_SET_PTRIM_HS) && (PHFL_CONFIG_HAVE_SET_PTRIM_HS == CONFIG_YES)
  /** Parameter list for syscall function phcaiKEyLLHal_UHF_SetPTRIM_HS(). */
  phcaiKEyLLGenFunc_SCI_SetPTRIMHS_t set_ptrimhs;
#endif

#if defined(PHFL_CONFIG_HAVE_PA_BITFIELDS) && (PHFL_CONFIG_HAVE_PA_BITFIELDS == CONFIG_YES)
  /** Parameter list for phcaiKEyLLGenFunc_CS_PA_GET phcaiKEyLLGenFunc_CS_PA_SET */
  phcaiKEyLLGenFunc_SCI_PA_sm2_t  sm2_pa_rw;
#endif

  /* Extended SI calls - MMr 2012-06-06 */
  /**
   * Parameter list for phcaiKEyLLGenFunc_CS_SI_Init_Ext(),
   * phcaiKEyLLGenFunc_CS_SI_Init_Ext_NoWait(),
   * phcaiKEyLLGenFunc_CS_SI_Inc_Ext(), phcaiKEyLLGenFunc_CS_SI_Inc_Ext_NoWait().
   */
  phcaiKEyLLGenFunc_SCI_SI_Ext_t si_ext;

#if (defined(PHFL_CONFIG_HAVE_WRITE_EROM) && (PHFL_CONFIG_HAVE_WRITE_EROM == CONFIG_YES)) || \
        (defined(PHFL_CONFIG_HAVE_READ_EROM) && (PHFL_CONFIG_HAVE_READ_EROM == CONFIG_YES))
  /**
   * Parameter list for function phcaiKEyLLGenFunc_CS_EROM_read() and
   * phcaiKEyLLGenFunc_CS_EROM_write().
   */
  phcaiKEyLLGenFunc_SCI_erom_rw_t erom_rw;
#endif

#if (defined(PHFL_CONFIG_HAVE_BISTCON_ENABLE_DCBUS_EXT) && \
        (PHFL_CONFIG_HAVE_BISTCON_ENABLE_DCBUS_EXT == CONFIG_YES))
  /**
   * Parameter list for phcaiKEyLLGenFunc_CS_ADC_enable_ext_input() and
   * phcaiKEyLLGenFunc_CS_ADC_disable_ext_input().
   */
  phcaiKEyLLGenFunc_SCI_EnableDCBUS_ext_t enable_dcbus;
#endif

#if (defined(PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL) && \
        (PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL == CONFIG_YES))
  /**
   * Parameter list for phcaiKEyLLGenFunc_CS_ADC_poweron() and
   * phcaiKEyLLGenFunc_CS_ADC_poweroff().
   */
  phcaiKEyLLGenFunc_SCI_Enable_ADC_POWERON_ext_t enable_poweron;
#endif

#if (defined(PHFL_CONFIG_HAVE_VDDABRNTRIM_CLEAR) && \
        (PHFL_CONFIG_HAVE_VDDABRNTRIM_CLEAR == CONFIG_YES))
  /**
   * Parameter structure for syscall functions phcaiKEyLLGenFunc_CS_clear_VDDABRNOUT()
   * and phcaiKEyLLGenFunc_CS_restore_VDDABRNOUT().
   */
  phcaiKEyLLGenFunc_SCI_clear_VDDABRNTRIM_t clear_vddabrntrim;
#endif

#if (defined(PHFL_CONFIG_HAVE_IIU_USER_SYSCALL_ACCESS) && \
        (PHFL_CONFIG_HAVE_IIU_USER_SYSCALL_ACCESS == CONFIG_YES))
  /** Parameter structure for syscall function phcaiKEyLLGenFunc_CS_IIU_receive(). */
  phcaiKEyLLGenFunc_SCI_IIU_receive_t iiu_receive;
  /** Parameter structure for syscall phcaiKEyLLGenFunc_CS_IIU_send(). */
  phcaiKEyLLGenFunc_SCI_IIU_send_t iiu_send;
#endif

#if defined(PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET) && (PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET == CONFIG_YES)
  /** Parameter structure for syscall phcaiKeyLLHal_CS_IIU_TIMX_SFR_set(). */
  phcaiKEyLLGenFunc_SCI_IIU_TIMX_SFR_set_t iiu_timx_sfr_set;
#endif

#if (defined(PHFL_CONFIG_HAVE_IIU_CAT_SYSCALL) && \
        (PHFL_CONFIG_HAVE_IIU_CAT_SYSCALL == CONFIG_YES))
  /**
   * Parameter structure for syscall function
   * phcaiKEyLLGenFunc_CS_IIU_CatSend().
   */
  phcaiKEyLLGenFunc_SCI_IIU_CatSend_t cat_iiu_send;
#endif

#if (defined(PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY) && (PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY == CONFIG_YES))
  /** Parameter structure for syscall phcaiKEyLLGenFunc_CS_UHF_ForceXoReady(). */
  phcaiKEyLLGenFunc_SCI_UHF_ForceXoReady_t uhf_force_xo_ready;
#endif

#if (defined(PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED) && \
        (PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED == CONFIG_YES))
  /**
   * Parameter structure for syscall function
   * phcaiKEyLLGenFunc_CS_UHF_ForcePllLockDetected().
   */
  phcaiKEyLLGenFunc_SCI_UHF_ForcePllLockDetected_t uhf_force_pll_lock_detected;
#endif

#if (defined(PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL) && \
      (PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL == CONFIG_YES))
  /** Parameter structure for syscall phcaiKEyLLGenFunc_CS_EROM_enable_PF2(). */
  phcaiKEyLLGenFunc_SCI_EROM_Enable_t erom_enable;
#endif

} phcaiKEyLLGenFunc_SCI_Parameter_List_t;



/** Defines the functions codes. */
typedef enum phcaiKEyLLGenFunc_SCI_CallerProxy_function_code
{
  /** Function code for phcaiKEyLLGenFunc_CS_AES_LoadKey(). */
  KEYLL_FUNC_CODE_AES_LOAD_KEY        = 0x0U,
  /** Function code for phcaiKEyLLGenFunc_CS_AES_LoadData(). */
  KEYLL_FUNC_CODE_AES_LOAD_DATA       = 0x1U,
  /** Function code for phcaiKEyLLGenFunc_CS_AES_Init(). */
  KEYLL_FUNC_CODE_AES_INIT            = 0x2U,
  /** Function code for phcaiKEyLLGenFunc_CS_AES_Encrypt(). */
  KEYLL_FUNC_CODE_AES_ENCRYPT         = 0x3U,
  /** Function code for phcaiKEyLLGenFunc_CS_SI_Init(). */
  KEYLL_FUNC_CODE_SI_INIT             = 0x4U,
  /** Function code for phcaiKEyLLGenFunc_CS_SI_Get(). */
  KEYLL_FUNC_CODE_SI_GET              = 0x5U,
  /** Function code for phcaiKEyLLGenFunc_CS_SI_Inc(). */
  KEYLL_FUNC_CODE_SI_INC              = 0x6U,
  /** Function code for phcaiKEyLLGenFunc_CS_MDI_Print(). */
  KEYLL_FUNC_CODE_MDI_PRINT           = 0x7U,
  /** Function code for phcaiKEyLLGenFunc_CS_MDI_PrintLn(). */
  KEYLL_FUNC_CODE_MDI_PRINTLN         = 0x8U,
  /** Function code for phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage(). */
  KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE    = 0x9U,
  /** Function code for phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage_LE(). */
  KEYLL_FUNC_CODE_ULPEE_WRITE_PAGE_LE = 0xAU, /* PRQA S 0779 */
  /** Function code for phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(). */
  KEYLL_FUNC_CODE_ULPEE_READ_PAGE     = 0xBU,
  /** Function code for phcaiKEyLLGenFunc_CS_ULPEE_ReadPage_LE(). */
  KEYLL_FUNC_CODE_ULPEE_READ_PAGE_LE  = 0xCU,
  /** Function code for phcaiKEyLLGenFunc_CS_ULPEE_ReadBytes(). */
  KEYLL_FUNC_CODE_ULPEE_READ_BYTES    = 0xDU,
  /** Function code for phcaiKEyLLGenFunc_CS_SetClkCon(). */
  KEYLL_FUNC_CODE_SET_CLKCON          = 0xEU,
  /** Function code for phcaiKEyLLGenFunc_CS_SetVBatRgl(). */
  KEYLL_FUNC_CODE_SET_VBATRGL         = 0xFU,
  /** Function code for phcaiKEyLLGenFunc_CS_ResetVBat(). */
  KEYLL_FUNC_CODE_RESET_VBAT          = 0x10U,
  /** Function code for phcaiKEyLLGenFunc_CS_SetBatPORFlag(). */
  KEYLL_FUNC_CODE_SET_BATPOR_FLAG     = 0x11U,
  /** Function code for phcaiKEyLLGenFunc_CS_Immo_Init(). */
  KEYLL_FUNC_CODE_IMMO_INIT           = 0x12U,
  /** Function code for phcaiKEyLLGenFunc_CS_Immo_Reset(). */
  KEYLL_FUNC_CODE_IMMO_RESET          = 0x13U,
  /** Function code for phcaiKEyLLGenFunc_CS_Immo_ReceiveCmd(). */
  KEYLL_FUNC_CODE_IMMO_RECEIVE_CMD    = 0x14U,
  /** Function code for phcaiKEyLLGenFunc_CS_Immo_ExecuteCmd(). */
  KEYLL_FUNC_CODE_IMMO_EXECUTE_CMD    = 0x15U,
  /** Function code for phcaiKEyLLGenFunc_CS_EEPROM_Enable(). */
  KEYLL_FUNC_CODE_EEPROM_EN           = 0x16U,
  /** Function code for phcaiKEyLLGenFunc_CS_EEPROM_Disable(). */
  KEYLL_FUNC_CODE_EEPROM_DIS          = 0x17U,
  /** Function code for phcaiKEyLLGenFunc_CS_EEPROM_Program(). */
  KEYLL_FUNC_CODE_EEPROM_PROG         = 0x18U,
  /** Function code for phcaiKEyLLGenFunc_CS_GetVersion(). */
  KEYLL_FUNC_CODE_GET_VERSION         = 0x19U,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_XoStartUp(). */
  SMART2_FUNC_CODE_XO_START_UP        = 0x1AU,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_PLLStartUp(). */
  SMART2_FUNC_CODE_PLL_START_UP       = 0x1BU,
  /** Function code for phcaiKEyLLGenFunc_CS_ULPEE_SetPROGEN(). */
  KEYLL_FUNC_CODE_ULPEE_SET_PROGEN    = 0x1CU,
  /** Function code for phcaiKEyLLGenFunc_CS_SetLfclkx2Dis(). */
  KEYLL_FUNC_CODE_SET_LFCLKX2DIS      = 0x1DU,
  /** Function code for phcaiKEyLLGenFunc_CS_MdiClkSel(). */
  KEYLL_FUNC_CODE_SET_MDICLKSEL       = 0x1EU,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_SetRfGate(). */
  SMART2_FUNC_CODE_SET_RFGATE         = 0x1FU,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_SetPAInGate(). */
  SMART2_FUNC_CODE_SET_PAINGATE       = 0x20U,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_SetCP_ICP(). */
  SMART2_FUNC_CODE_SET_CP_ICP         = 0x21U,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_XO(). */
  SMART2_FUNC_CODE_SET_PTRIM_XO       = 0x22U,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_PLL(). */
  SMART2_FUNC_CODE_SET_PTRIM_PLL      = 0x23U,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_HS(). */
  SMART2_FUNC_CODE_SET_PTRIM_HS       = 0x24U,
  /** Function code for phcaiKEyLLGenFunc_CS_SI_Init_Ext(). */
  KEYLL_FUNC_CODE_SI_INIT_EXT         = 0x25U,
  /** Function code for phcaiKEyLLGenFunc_CS_SI_Inc_Ext(). */
  KEYLL_FUNC_CODE_SI_INC_EXT          = 0x26U,
  /** Function code for phcaiKEyLLGenFunc_CS_EROM_read(). */
  KEYLL_FUNC_CODE_EROM_READ           = 0x27U,
  /** Function code for phcaiKEyLLGenFunc_CS_EROM_write(). */
  KEYLL_FUNC_CODE_EROM_WRITE          = 0x28U,
  /** Function code for phcaiKEyLLGenFunc_CS_sm2_pa_set(). */
  SMART2_FUNC_CODE_SET_PA_BITFIELD    = 0x29U,
  /** Function code for phcaiKEyLLGenFunc_CS_sm2_pa_get(). */
  SMART2_FUNC_CODE_GET_PA_BITFIELD    = 0x2AU,
  /** Function code for phcaiKEyLLGenFunc_CS_CP_ICP_TRIM(). */
  UHF_FUNC_CODE_LOAD_CP_ICP_TRIM      = 0x2BU,
  /** Function code for phcaiKEyLLGenFunc_CS_AES_disable(). */
  KEYLL_FUNC_CODE_AES_DISABLE         = 0x2CU,
  /**
   * Function code for phcaiKEyLLGenFunc_CS_ADC_disable_ext_input()
   * and phcaiKEyLLGenFunc_CS_ADC_enable_ext_input().
   */
  ADC_ENABLE_EXT_INPUT                = 0x2DU,
  /**
   * Function code for phcaiKEyLLGenFunc_CS_ADC_poweron() and
   * phcaiKEyLLGenFunc_CS_ADC_poweroff().
   */
  ADC_ALLOW_POWERONOFF                = 0x2EU,
  /** Function code for phcaiKEyLLGenFunc_CS_ULPEE_Write_ULPEE_LFTUNE(). */
  KEYLL_FUNC_CODE_WRITE_ULPEE_3D4     = 0x2FU,
  /**
   * Function code for phcaiKEyLLGenFunc_CS_clear_VDDABRNOUT() and
   * phcaiKEyLLGenFunc_CS_restore_VDDABRNOUT().
   */
  KEYLL_FUNC_CODE_CLEAR_VDDABRNTRIM   = 0x30U,
  /** Function code for phcaiKEyLLGenFunc_CS_IIU_receive(). */
  KEYLL_FUNC_CODE_IIU_RECEIVE         = 0x31U,
  /** Function code for phcaiKEyLLGenFunc_CS_IIU_send(). */
  KEYLL_FUNC_CODE_IIU_SEND            = 0x32U,
  /** Function code for phcaiKeyLLHal_CS_IIU_TIMX_SFR_set(). */
  KEYLL_FUNC_CODE_IIU_TIMX_SFR_SET    = 0x33U,
  /** Function code for phcaiKEyLLGenFunc_CS_SI_Init_Ext_NoWait(). */
  KEYLL_FUNC_CODE_SI_INIT_EXT_NOWAIT  = 0x34U,
  /** Function code for phcaiKEyLLGenFunc_CS_SI_Inc_Ext_NoWait(). */
  KEYLL_FUNC_CODE_SI_INC_EXT_NOWAIT   = 0x35U,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_ForceXoReady(). */
  KEYLL_FUNC_CODE_UHF_FORCE_XO_READY  = 0x36U,
  /** Function code for phcaiKEyLLGenFunc_CS_UHF_ForcePllLockDetected(). */
  KEYLL_FUNC_CODE_UHF_FORCE_PLL_LOCK_DETECTED = 0x37U,
  /** Function code for phcaiKEyLLGenFunc_CS_IIU_CatInit(). */
  KEYLL_FUNC_CODE_IIU_CAT_INIT         = 0x38U,
  /** Function code for phcaiKEyLLGenFunc_CS_IIU_CatSend(). */
  KEYLL_FUNC_CODE_IIU_CAT_SEND         = 0x39U,
  /** Function code for phcaiKEyLLGenFunc_CS_IIU_CatSendMuted(). */
  KEYLL_FUNC_CODE_IIU_CAT_SEND_MUTED   = 0x3AU,
  /** Function code for phcaiKEyLLGenFunc_CS_EROM_enable_PF2(). */
  KEYLL_FUNC_CODE_EROM_ENABLE_PF2     = 0x3BU
} phcaiKEyLLGenFunc_SCI_CallerProxy_function_code_t;

/** Common structure to handle parameters for all caller stub functions. */
typedef struct phcaiKEyLLGenFunc_SCI_Func_Params
{
  /** Code identifying the respective function. */
  phcaiKEyLLGenFunc_SCI_CallerProxy_function_code_t function_code;
  /** Parameter list of the respective function. */
  phcaiKEyLLGenFunc_SCI_Parameter_List_t params;
} phcaiKEyLLGenFunc_SCI_Func_Params_t;

/** @} */

#endif
