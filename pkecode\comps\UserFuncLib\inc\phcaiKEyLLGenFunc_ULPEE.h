/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_ULPEE.h 15862 2018-10-05 13:39:14Z dep10330 $
  $Revision: 15862 $
*/


/**
 * @file
 * Declarations of User Functions related to ULP EEPROM.
 */

#ifndef PHCAIKEYLLGENFUNC_ULPEE_H
#define PHCAIKEYLLGENFUNC_ULPEE_H

#include "types.h"
#include "ncf29xx.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup EEPROM  ULP EEPROM related functions
 * Hardware abstractions for EEPROM access.
 * @{
 */


/**
 * Activate (power-up) an ULP EEPROM module.
 * The module is number is the upper 8 bits of the byte address.
 * Does not include waiting time for activation.
 *
 * @param[in] u16_byte_address Address within the module to activate
 * @note Activation takes up to tULP,PON(Max) = 100 us, this should be
 * considered by the caller.
 */
void phcaiKEyLLGenFunc_ULPEE_ActivateModule( const uint16_t u16_byte_address );

/**
 * Activate (power-up) a ULP EEPROM module for given byte address.
 * If the ULP or the module is not yet powered, wait for activation time.
 *
 * @param[in] u16_byte_address Address within the module to activate
 * @note Activation takes up to tULP,PON(Max) = 100 us.
 */
void phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait( const uint16_t u16_byte_address );

/**
 * Activate (power-up) ULP EEPROM module(s) for given byte address range.
 * If the ULP or required modules are not yet powered, wait for activation time.
 * All modules from start to end address are powered up.
 *
 * @param[in] u16_start_byte_address Start address
 * @param[in] u16_end_byte_address   End address
 * @note Activation takes up to tULP,PON(Max) = 100 us.
 */
void phcaiKEyLLGenFunc_ULPEE_ActivateModuleRangeWait( const uint16_t u16_start_byte_address,
                                                      const uint16_t u16_end_byte_address  );

/**
 * De-activate (power down) ULPEE module for given byte address.
 *
 * @param[in] u16_byte_address Address within the module to deactivate
 * @return Nothing
 */
void phcaiKEyLLGenFunc_ULPEE_DeactivateModule( const uint16_t u16_byte_address );

/**
 * Activate all ULPEE modules.
 * Does not include waiting time for activation.
 *
 * @note Activation takes up to tULP,PON(Max) = 100 us.
 */
void phcaiKEyLLGenFunc_ULPEE_ActivateAllModules( void );

/**
 * De-activate (power down) all ULPEE modules. The ULPPON bit is not changed.
 */
void phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules( void );


/**
 * Check if a ULPEE module is activated.
 *
 * @param[in] u16_byte_address Address to check.
 * @return TRUE if module is enabled, FALSE otherwise.
 */
bool_t phcaiKEyLLGenFunc_ULPEE_ModuleIsActivated( const uint16_t u16_byte_address );


/**
 * Read a single byte from ULPEE from a given byte address. Module is enabled if needed.
 * @param[in] u16_byte_address Address to read from.
 * @return The byte read from ULPEE.
 */
uint8_t phcaiKEyLLGenFunc_ULPEE_ReadOneByte( const uint16_t u16_byte_address );


/**
 * Read a single word (16 bit) from ULPEE from a given byte address, LSB first.
 * Module is enabled if needed.
 * @param[in] u16_byte_address Address to read from.
 * @return The word read from ULPEE.
 */
uint16_t phcaiKEyLLGenFunc_ULPEE_ReadOneWord( const uint16_t u16_byte_address );


/* Declaration/definition of inline functions */

/**
 * Function to enable the auxiliary oscillator (AUXCLK) unconditionally.
 * Set bits AUXRCOSC_EN=1 and AUXRCOSC_OUTDIS=1.
 */
inline void phcaiKEyLLGenFunc_ULPEE_EnableAUXRC( void )
{
  CLKCON2.val |= 0x06;
}

/**
 * Function to disable the auxiliary oscillator.
 * Set bits AUXRCOSC_EN=0 and AUXRCOSC_OUTDIS=0.
 */
inline void phcaiKEyLLGenFunc_ULPEE_DisableAUXRC( void )
{
  CLKCON2.val &= 0xF9U;
}


#endif

/*@}*/
/*@}*/

/* eof */
