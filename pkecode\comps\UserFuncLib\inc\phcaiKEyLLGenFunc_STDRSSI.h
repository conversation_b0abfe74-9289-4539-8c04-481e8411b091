/*
  -----------------------------------------------------------------------------
  Copyright 2010 - 2020  NXP
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, <PERSON>FF<PERSON>IATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_STDRSSI.h 28748 2020-09-21 16:18:07Z dep10330 $
  $Revision: 28748 $

*/


/**
 * @file
 * Declarations of User Library Functions related to standard (wideband) RSSI measurements.
 */

#ifndef PHCAIKEYLLGENFUNC_STDRSSI_H
#define PHCAIKEYLLGENFUNC_STDRSSI_H

#include "types.h"
#include "phcaiKEyLLGenFunc_Platform.h"
#include "phcaiKEyLLGenFunc_RSSI.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup STDRSSI  Standard RSSI support functions
 * Support functions for RSSI measurements with the standard or wideband RSSI hardware.
 * @{
 */


/**
  Initial setting of RSSI control register with RSSI_RANGEEXTDIS set to 1 for two-step power-up.
  RSSI_RANGEEXTDIS may be set to 0 (means enable the extended range) in phcaiKEyLLGenFunc_RSSI_ADCInit.
  @see phcaiKEyLLGenFunc_RSSI_ADCInit
 */
// RSSI control register RSSICON (16 bit)
// |15|14|13|12|  11         | 10           9            8                | 7        | 6              5             |     4     | 3             2         1     | 0
// | 0| 0| 0| 0| RSSI_RANGE2 | RSSI_RANGE1  RSSI_RANGE0  RSSI_RANGEEXTDIS | RSSI_PON | RSSI_CHANSEL1  RSSI_CHANSEL0 | RSSI_HOLD | RSSI_OVF2 RSSI_OVF1 RSSI_OVF0 | RSSI_RST
//   0  0  0  0     0             0             0              1               1           0                0             0       0             0         0       0
#define D_RSSICON_STUP      0x0180u


#ifdef PLATFORM_TOKEN
/**
  ADC configuration for RSSI:
   INSEL=4 (RSSI), REFSEL=4 (Vref to VSSA), BG and buffer enabled, CONVRESET=1 (reads 0)
    SAMTIM bits can be set via phcaiKEyLLGenFunc_ADC_SetSamplingTime.
  @see phcaiKEyLLGenFunc_RSSI_ADCInit
 */
// ADCCON register:
//   15  |  14  |  13  |  12   |  11   |  10   |  9    |   8   |   7   |   6     |    5     |   4    |    3   |    2   |   1     |   0     |
// INSEL2 INSEL1 INSEL0 REFSEL2|REFSEL1 REFSEL0 SAMTIM1 SAMTIM0|TSENSEN BATMEASEN BG1V2BUFEN BG1V2EN |    X        X    CONVRESET CONVSTART|
//    1      0      0      1   |   0       0      0        0   |   0       0          1         1    |    0        0       1         0     |
//hex:                     9   |                           0   |                                3    |                               2     |
#define D_ADCCON_RSSI       0x9032u
#endif

#if  defined(PLATFORM_TOKENPLUS)
/**
  ADC configuration for RSSI:
    INSEL=4 (RSSI), REFSEL=4 (Vref to VSSA), BG and buffer enabled, CONVRESET=1 (reads 0)
    SAMTIM bits can be set via phcaiKEyLLGenFunc_ADC_SetSamplingTime.
    Not required to set POWERON=1 since we use the hardware summation.
  @see phcaiKEyLLGenFunc_RSSI_ADCInit
 */
// INSEL2 INSEL1 INSEL0 REFSEL2|REFSEL1 REFSEL0 SAMTIM1 SAMTIM0|TSENSEN BATMEASEN BG1V2BUFEN BG1V2EN |SAMTIMEXT POWERON CONVRESET CONVSTART|
//    1      0      0      1   |   0       0      0        0   |   0       0          1         1    |    0        0        1         0    |
//hex:                     9   |                           0   |                                3    |                                2    |
#define D_ADCCON_RSSI       0x9032u
#endif



/**
 * Initialize, power-up and configure the RSSI gain chain and ADC
 * for subsequent RSSI operation.
 * VDDA regulator must have been enabled beforehand!
 * Sets ADCCON register and RSSICON bitfields RSSI_RANGE=RSSI_RANGE_0dB
 * and RSSI_CHANSEL = AUTOZERO.
 * Monitors VDDABRNREG at end of sequence.
 *
 * @param[in] b_EnableRangeExt set to TRUE to enable the extended RSSI range (+54 dB).
 * @param[in] e_samplingtime   ADC sampling time setting as enum type.
 * @return error indicator.
 * @see phcaiKEyLLGenFunc_STDRSSI_ADCStop
 */
error_t phcaiKEyLLGenFunc_STDRSSI_ADCInit( bool_t b_EnableRangeExt, ADC_SAMTIM_t e_samplingtime );


/**
 * Resets and stops the ADC and resets RSSICON register.
 */
void phcaiKEyLLGenFunc_STDRSSI_ADCStop( void );


/**
 * Run the offset measurement.
 * Function phcaiKEyLLGenFunc_STDRSSI_ADCInit() must have been called before.
 * The result from this measurement should be subtracted from each RSSI channel measurement result.
 *
 * @param[in] u16_num_ADC_samples            Number of ADC samples to average.
 * @return The resulting unsigned 10 bit integer value.
 *
 * @see phcaiKEyLLGenFunc_ADC_run_averaged
 */
uint16_t phcaiKEyLLGenFunc_STDRSSI_MeasOffset( uint16_t u16_num_ADC_samples, uint16_t u16_NumFractionalBits );


/**
 * Run an RSSI channel measurement.
 * Function phcaiKEyLLGenFunc_STDRSSI_ADCInit() must have been called before.
 * When used with offset compensation, the input voltage can be calculated as
 * VIN = VREF * (ADC_COMP / 512) / ( 1.8 * GRSSI )
 * ( GRSSI = Gain of RSSI chain, 54 / 36 / 18 / 0 / -18 dB ).
 * Parameter is forwarded to phcaiKEyLLGenFunc_ADC_run_averaged.
 *
 * @note The 2 ms detector is reset during the ADC sampling procedure.
 *
 * @param[in] u16_num_ADC_samples    Number of samples (ADC conversions) to be averaged.
 * @return The resulting unsigned 10 bit integer value (not compensated).
 *
 * @see phcaiKEyLLGenFunc_ADC_run_averaged
 */
uint16_t phcaiKEyLLGenFunc_STDRSSI_MeasChannel( uint16_t u16_num_ADC_samples, uint16_t u16_NumFractionalBits );


/**
 * Run an RSSI measurement of a given 3D LF active channel,
 * and select or detect the appropriate gain or attenuation setting.
 *
 * @param[in]  e_channel       one of AUTOZERO, CH1, CH2, CH3
 * @param[in]  e_range         setting for RSSI_RANGE
 *                             in RSSICON register.
 * @param[in]  b_autoRangeInd  if TRUE, select gain automatically.
 *
 * @return range (gain) information, see phcaiKEyLLGenFunc_RssiRange_t.
 */
phcaiKEyLLGenFunc_RssiRange_t
  phcaiKEyLLGenFunc_STDRSSI_SelectRange( phcaiKEyLLGenFunc_RssiChannel_t  e_channel,
                                         phcaiKEyLLGenFunc_RssiRange_t    e_range,
                                         bool_t                           b_autoRangeInd );


/*@}*/
/*@}*/

#endif
