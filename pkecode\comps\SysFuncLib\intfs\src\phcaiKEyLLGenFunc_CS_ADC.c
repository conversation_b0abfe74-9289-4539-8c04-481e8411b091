/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_ADC.c 11676 2018-01-02 13:17:01Z dep17622 $
  $Revision: 11676 $
*/

/**
 * @file
 * Implementation of the stubs to call ADC specific KEyLink Lite General Functions
 * placed in ROM.
 */

#include "phcaiKEyLLGenFunc_CS_ADC.h"
#include "phcaiKEyLLGenFunc_CS_Utils.h"
#include "phcaiKEyLLGenFunc_SCI.h"

/*
  Change Log
  2017-10-18 (MMr):
  - removed phcaiKEyLLGenFunc_CS_SetAdc_ExtConvTime since it is only available on KL3D.
  - phcaiKEyLLGenFunc_CS_SetAdc_LIntSwCon: moved to _Common module and renamed to
    phcaiKEyLLGenFunc_CS_LIntSwCon.

 */


#if defined(PHFL_CONFIG_HAVE_BISTCON_ENABLE_DCBUS_EXT) && (PHFL_CONFIG_HAVE_BISTCON_ENABLE_DCBUS_EXT == CONFIG_YES)

void phcaiKEyLLGenFunc_CS_ADC_enable_ext_input(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = ADC_ENABLE_EXT_INPUT;
  phcaiKEyLLGenFunc_Func_Params.params.enable_dcbus.enable = TRUE;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_ADC_disable_ext_input(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = ADC_ENABLE_EXT_INPUT;
  phcaiKEyLLGenFunc_Func_Params.params.enable_dcbus.enable = FALSE;
  call_syscall(5);
}
#endif

#if defined(PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL) && (PHFL_CONFIG_ALLOW_ADCPOWERON_SYSCALL == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_ADC_poweron(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = ADC_ALLOW_POWERONOFF;
  phcaiKEyLLGenFunc_Func_Params.params.enable_poweron.enable = TRUE;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_ADC_poweroff(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = ADC_ALLOW_POWERONOFF;
  phcaiKEyLLGenFunc_Func_Params.params.enable_poweron.enable = FALSE;
  call_syscall(5);
}
#endif

#if defined(PHFL_CONFIG_HAVE_VDDABRNTRIM_CLEAR) && (PHFL_CONFIG_HAVE_VDDABRNTRIM_CLEAR == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_clear_VDDABRNOUT(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_CLEAR_VDDABRNTRIM;
  phcaiKEyLLGenFunc_Func_Params.params.clear_vddabrntrim.clear = TRUE;
  call_syscall(5);
}

void phcaiKEyLLGenFunc_CS_restore_VDDABRNOUT(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_CLEAR_VDDABRNTRIM;
  phcaiKEyLLGenFunc_Func_Params.params.clear_vddabrntrim.clear = FALSE;
  call_syscall(5);
}
#else
void phcaiKEyLLGenFunc_CS_clear_VDDABRNOUT(void)
{
}

void phcaiKEyLLGenFunc_CS_restore_VDDABRNOUT(void)
{
}
#endif