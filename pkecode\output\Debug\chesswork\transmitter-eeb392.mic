
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-eeb392 mrk3


// m6;   next: m7 (next offset: 4)
000000  2 0  "0001011111111111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (12,R7,R7); 
000001  0 0  "1111111111110100"   // /
000002  2 0  "0110111001100011"   // (DM[0]) = _pl_rd_res_reg_const_store_1_B1 (R46[0],0,DM[0],R7); 
000003  0 0  "0000000000000000"   // /

// m7 chess_separator_scheduler;   next: m8 (next offset: 4)

// m8;   next: m9 (next offset: 5)
000004  1 0  "0110111101001000"   // (DM[2]) = _pl_rd_res_reg_const_store_1_B2 (RwL[1],2,DM[2],R7); 

// m9 chess_separator_scheduler;   next: m10 (next offset: 5)

// m10;   next: m11 (next offset: 6)
000005  1 0  "0110101101000011"   // (DM[4]) = _pl_rd_res_reg_const_store_2_B2 (RbL[0],4,DM[4],R7); 

// m11 chess_separator_scheduler;   next: m12 (next offset: 6)

// m12;   next: m15 (next offset: 10)
000006  2 0  "0001011111111100"   // (R46[0],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (10,R7); 
000007  0 0  "0000000000001010"   // /
000008  2 0  "0110110000001000"   // (DM[10]) = store_const_1_B1 (R46[0],DM[10]); 
000009  0 0  "0000101110111000"   // /

// m15 chess_separator_scheduler;   next: m602 (next offset: 10)

// m602;   next: m17 (next offset: 14)
000010  1 0  "0110101100000011"   // (RwL[0]) = load__pl_rd_res_reg_const_update_lo_1_B2 (4,DM[4],R7); 
000011  1 0  "0110100010000100"   // (RwL[0]) = update_hi_const_1_B2 (RwL[0]); 
000012  2 0  "0110111111110000"   // (DM9,PM) = load_const_bf_mov_const_const_store_3_B1 (RwL[0],0,DM9,PM); 
000013  0 0  "0011110000000000"   // /

// m17 chess_separator_scheduler;   next: m18 (next offset: 14)

// m18;   next: m19 (next offset: 16)
000014  1 0  "0110111100101000"   // (R46[1]) = load__pl_rd_res_reg_const_1_B2 (2,DM[2],R7); 
000015  1 0  "0110111101101010"   // (DM[6]) = _pl_rd_res_reg_const_store_1_B2 (R46[1],6,DM[6],R7); 

// m19 chess_separator_scheduler;   next: m20 (next offset: 16)

// m20;   next: m21 (next offset: 17)
000016  1 0  "0110111110000011"   // (DM[8]) = _pl_rd_res_reg_const_store_const_1_B3 (8,DM[8],R7); 

// m21 chess_separator_scheduler;   next: m68 (next offset: 17)

// m68, jump target: m2 (next offset: 21)
000017  1 0  "0110110011000000"   // (RwL[0]) = const_1_B2 (); 
000018  2 0  "0001011111111101"   // (R46[1],c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_1_B1 (6,R7); 
000019  0 0  "0000000000000110"   // /
000020  1 0  "0101101000010010"   // () = jump_const_1_B1 (18); 

// m610;   next: m32 (next offset: 23)
000021  2 0  "0110111111110000" .loop_nesting 1    // (DM9,PM) = load_const_bf_mov_const_const_store_2_B1 (RwL[0],0,DM9,PM); 
000022  0 0  "0100000000000000"   // /

// m32 chess_separator_scheduler;   next: m33 (next offset: 23)

// m33;   next: m34 (next offset: 24)
000023  1 0  "0110111100110011"   // (R46[2]) = load__pl_rd_res_reg_const_1_B2 (8,DM[8],R7); 

// m34 chess_separator_scheduler;   next: m35 (next offset: 24)

// m35;   next: m36 (next offset: 25)
000024  1 0  "0001011110001011"   // (DM[8],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B3 (8,DM[8],DM[8],R7); 

// m36 chess_separator_scheduler;   next: m59 (next offset: 25)

// m59, jump target: m1 (next offset: 30)
000025  2 0  "0001011000110011"   // (R46[2],c_flag,nz_flag,o_flag) = _pl_load__pl_rd_res_reg_const_1_B1 (R46[2],0,DM[0],R7); 
000026  0 0  "0000000000000000"   // /
000027  1 0  "0110100100001010"   // (RbL[1]) = load_1_B1 (R46[2],DM9,DM9,DM9,DM,DM,DM,DM,DM,DM); 
000028  1 0  "1100000000011001"   // (DM9,PM) = store_const_2_B2 (RbL[1],1,DM9,PM); 
000029  1 0  "0101101000000010"   // () = jump_const_1_B1 (2); 

// m48;   next: m1 (next offset: 31)
000030  1 0  "0000011110001100" .loop_nesting 2    // (DM[10],c_flag,nz_flag,o_flag) = _mi_load_const__pl_rd_res_reg_const_store_1_B3 (10,DM[10],DM[10],R7); 

// m1;   next: m454 (next offset: 31)

// m454;   next: m459, jump target: m61 (next offset: 33)
000031  2 0  "0100111000000100"   // (DM9,PM,nz_flag) = load_const__ad_const_cmp_const_cc_ne__jump_const_1_B1 (0,4,DM9,PM); 
000032  0 0  "0000000001000000"   // /

// m459;   next: m61, jump target: m48 (next offset: 35)
000033  1 0  "0100011110000100"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B3 (10,DM[10],R7); 
000034  1 0  "0101011111111100"   // () = cc_a__jump_const_1_B1 (c_flag,nz_flag,-4); 

// m61;   next: m62 (next offset: 37)
000035  2 0  "0110110000001000" .loop_nesting 1    // (DM[10]) = store_const_1_B1 (R46[0],DM[10]); 
000036  0 0  "0000101110111000"   // /

// m62 chess_separator_scheduler;   next: m63 (next offset: 37)

// m63;   next: m2 (next offset: 38)
000037  1 0  "0000010111000001"   // (DM[6],c_flag,nz_flag,o_flag) = load__mi_const_store_1_B2 (R46[1],DM[6],DM[6]); 

// m2;   next: m464 (next offset: 38)

// m464;   next: m470, jump target: m610 (next offset: 40)
000038  1 0  "0100010111000001"   // (c_flag,nz_flag,o_flag) = load_cmp_const_1_B2 (R46[1],DM[6]); 
000039  1 0  "0101100111101110"   // () = cc_ae__jump_const_1_B1 (c_flag,nz_flag,-18); 

// m470;   next: m619, jump target: m108 (next offset: 42)
000040  1 0  "0100011110000010" .loop_nesting 0    // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B3 (6,DM[6],R7); 
000041  1 0  "0101011000010010"   // () = cc_be__jump_const_1_B1 (c_flag,nz_flag,18); 

// m619;   next: m77 (next offset: 45)
000042  1 0  "0110111100101010"   // (R46[1]) = load__pl_rd_res_reg_const_1_B2 (6,DM[6],R7); 
000043  2 0  "0110111111110101"   // (DM9,PM) = load_const_bf_mov_const_const_store_1_B1 (R46[1],0,DM9,PM); 
000044  0 0  "0100000000000000"   // /

// m77 chess_separator_scheduler;   next: m78 (next offset: 45)

// m78;   next: m79 (next offset: 46)
000045  1 0  "0110111100101011"   // (R46[1]) = load__pl_rd_res_reg_const_1_B2 (8,DM[8],R7); 

// m79 chess_separator_scheduler;   next: m80 (next offset: 46)

// m80;   next: m81 (next offset: 47)
000046  1 0  "0001011110001011"   // (DM[8],c_flag,nz_flag,o_flag) = _pl_load_const__pl_rd_res_reg_const_store_1_B3 (8,DM[8],DM[8],R7); 

// m81 chess_separator_scheduler;   next: m104 (next offset: 47)

// m104, jump target: m3 (next offset: 52)
000047  2 0  "0001011000101011"   // (R46[1],c_flag,nz_flag,o_flag) = _pl_load__pl_rd_res_reg_const_1_B1 (R46[1],0,DM[0],R7); 
000048  0 0  "0000000000000000"   // /
000049  1 0  "0110100100000001"   // (RbL[0]) = load_1_B1 (R46[1],DM9,DM9,DM9,DM,DM,DM,DM,DM,DM); 
000050  1 0  "1100000000011000"   // (DM9,PM) = store_const_2_B2 (RbL[0],1,DM9,PM); 
000051  1 0  "0101101000000010"   // () = jump_const_1_B1 (2); 

// m93;   next: m3 (next offset: 53)
000052  1 0  "0000011110001100" .loop_nesting 1    // (DM[10],c_flag,nz_flag,o_flag) = _mi_load_const__pl_rd_res_reg_const_store_1_B3 (10,DM[10],DM[10],R7); 

// m3;   next: m476 (next offset: 53)

// m476;   next: m481, jump target: m106 (next offset: 55)
000053  2 0  "0100111000000100"   // (DM9,PM,nz_flag) = load_const__ad_const_cmp_const_cc_ne__jump_const_1_B1 (0,4,DM9,PM); 
000054  0 0  "0000000001000000"   // /

// m481;   next: m106, jump target: m93 (next offset: 57)
000055  1 0  "0100011110000100"   // (c_flag,nz_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B3 (10,DM[10],R7); 
000056  1 0  "0101011111111100"   // () = cc_a__jump_const_1_B1 (c_flag,nz_flag,-4); 

// m106;   next: m111 (next offset: 59)
000057  2 0  "0110110000001000" .loop_nesting 0    // (DM[10]) = store_const_1_B1 (R46[0],DM[10]); 
000058  0 0  "0000101110111000"   // /

// m108;   next: m111 (next offset: 59)

// m111 (next offset: /)
000059  2 0  "0001011111111111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (12,R7,R7); 
000060  0 0  "0000000000001100"   // /
000061  1 0  "0001101111000100"   // () = ret_1_B1 (); 

