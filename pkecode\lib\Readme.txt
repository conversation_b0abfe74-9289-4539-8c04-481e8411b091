Libraries in this folder have been compiled with the MRK-IIIe IP Programmer release as stated in
the release notes.
If you are using a different release, linking errors might occur. In this case, the libraries
must be re-built using the supplied project (.prx) files.

This applies to
1. Device support library for type <type>:
    \external\<TYPE>\lib<TYPE>.prx
    For instance the TOKEN case:
    \external\ncf29A1\libncf29A1.prx

2. System calls interface library:
    comps\SysFuncLib\toolsupport\chessde\<TYPE>\<ROMMASK>\SystemLib_<TYPE>_<ROMMASK>.prx
    Examples:
    comps\SysFuncLib\toolsupport\chessde\ncf29A1\RC005\SystemLib_ncf29A1_RC005.prx
    comps\SysFuncLib\toolsupport\chessde\ncf2957\RC001\SystemLib_ncf2957_RC001.prx

Included post-build event scripts (copy_archive.bat) copy the resulting .a file to the right location.
Please consult the user manual(s) for details.
