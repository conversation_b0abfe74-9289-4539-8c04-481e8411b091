// Linker configuration file for NCF29AE (TOKEN-SRX2).
//**************************************************************************
// (up to) 64 kBytes EROM, total 4 kBytes RAM (incl. System RAM 320 bytes).
// Hardware address range: 0x0000 .. 0x017F
// User RAM address range: 0x0180 .. 0x103F
//**************************************************************************
// Using configuration option "_shared" if available.
// EROM size in KBytes specified by optional macro EROMSIZEKB, default =32.
// $Id: ncf29AE_default.bcf 24956 2020-01-31 09:22:44Z dep10330 $
// $Revision: 24956 $
//

_exclusive DM9  0x0000..0x017F // Reserved for SFR

// DM9 ( RAM 0x0180..0x01FF) will be used preferably for global scalars
// and data explicitly assigned to DM9 (more efficient addressing modes exist to access this data).
// Next line specifies that all symbols should be sorted on size before mapping.
_symbol_sort mem_size ascending

// Default stack size: 512 bytes.
_stack     DM 0x0E40..0x103F // User mode stack

_reserved  DM 0x1040..0x117F // empty area

// TEST: locally define EROMSIZEKB to avoid recompile of all project files when changed
// in project settings.
//#undef EROMSIZEKB
//#define EROMSIZEKB 64

// TEST: locally define NO_SHARED to avoid recompile of all project files when changed
//#define NO_SHARED

#if !defined( EROMSIZEKB )
// default is 32 KBytes EROM
#warning No EROM size specified, assuming 32 KBytes (EROMSIZEKB=32).
#define EROMSIZEKB 32
#endif

// IMPORTANT - explanations on conventional versus new "_shared" EROM memory allocation.
// From release 13R1.4 onwards, the "_shared" feature is implemented in Bridge (linker).
// It removes the need to partition the EROM in a part for code and another for rodata.
// If available, we recommend to use "_shared" instead of the fixed partitioning.
// Set macro NO_SHARED to avoid _shared and use the conventional linking mode.
//
// HOWEVER, in release 13R1.4, "_shared" requires to add the linker option +P
// in the project file (means: place code first, then rodata; see MRK3 User Guide).
// This option has become the default in later releases.
// For this reason, _shared is only used in this configuration file for release version
// K-2015.06 and later.
// Do not use option +P with versions before 13R1.4 to avoid error messages.
// Notes:
// In version 13R1.4,    __tct_release__ equals 1301, __tct_patch__ equals 04.
// In version K-2015.06, __tct_release__ equals 1502.
// In version L-2016.03, __tct_release__ equals 1601.

#if ( ( (__tct_release__ * 100) + __tct_patch__ ) == 130104 )
#warning Note: Although this linker version (release 13R1.4) already supports the \
  _shared feature, it is NOT used now, since it requires the linker option "+P". \
  This is not a default option before release 2015.06 and causes errors if missing \
  or if used with tool suite versions before 13R1.4.
#endif


#if ( ( __tct_release__ >= 1502 ) && !defined(NO_SHARED) )
// Note: if you wish to use _shared with 13R1.4 already, add "+P" linker option and
// enable following line instead of the above:
//#if ( ( ( (__tct_release__ * 100) + __tct_patch__ ) >= 130104 ) && !defined(NO_SHARED) )

#if ( EROMSIZEKB == 64 )
#warning Note: selected EROM size is 64 kBytes. \
  Note: _shared is not recommended for EROM > 32 kBytes since _rodata must be placed in lower EROM bank. \
  Using conventional link mode instead.
#warning Note: 4 kBytes reserved for _rodata. If more space for code or rodata is needed, bcf must be modified.
#warning Note: Make sure to place all code for immobilizer operation (e.g. USER handler) in lower EROM bank.
_mem_size  PM 0x8000         //    PM:0x8000..0xFFFF not used
_reserved  PM 0x08C0..0x10BF // == DM:0x1180..0x217F reserved for rodata
_reserved  DM 0x2180..0xFFFF // == PM:0x10C0..0x7FFF reserved for code
_rodata    DM 0x1180..0x217F // == PM:0x08C0..0x10BF
// In case we set PF2_B = 1 and want to use _shared:
//_mem_size DM 0x10000
//// EROM is shared by PM and DM _rodata.
//_shared DM 0x1180..0xFFFF   PM 0x08C0..0x7FFF
//_rodata DM 0x1180..0xFFFF // read-only data in EROM mapped to DM

#elif ( EROMSIZEKB == 48 )
#warning Note: selected EROM size is 48 kBytes. \
  Note: _shared is not recommended for EROM > 32 kBytes since _rodata must be placed in lower EROM bank. \
  Using conventional link mode instead.
#warning Note: 4 kBytes reserved for _rodata. If more space for code or rodata is needed, bcf must be modified.
#warning Note: Make sure to place all code for immobilizer operation (e.g. USER handler) in lower EROM bank.
_mem_size  PM 0x6000         //    PM:0x6000..0xFFFF not used
_reserved  PM 0x08C0..0x10BF // == DM:0x1180..0x217F reserved for rodata
_reserved  DM 0x2180..0xFFFF // == PM:0x10C0..0x5FFF reserved for code
_rodata    DM 0x1180..0x217F // == PM:0x08C0..0x10BF
// In case we set PF2_B = 1 and want to use _shared:
//_mem_size DM 0xC000
//_shared DM 0x1180..0xBFFF   PM 0x08C0..0x5FFF
//_rodata DM 0x1180..0xBFFF

#elif ( EROMSIZEKB == 32 )
#warning Note: _shared EROM, selected EROM size is 32 kBytes
_mem_size PM 0x4000
_mem_size DM 0x8000
// EROM is shared by PM and DM _rodata.
_shared DM 0x1180..0x7FFF   PM 0x08C0..0x3FFF
_rodata DM 0x1180..0x7FFF // read-only data in EROM mapped to DM

#elif ( EROMSIZEKB == 24 )
#warning Note: _shared EROM, selected EROM size is 24 kBytes
_mem_size PM 0x3000
_mem_size DM 0x6000
_shared DM 0x1180..0x5FFF   PM 0x08C0..0x2FFF
_rodata DM 0x1180..0x5FFF

#elif ( EROMSIZEKB == 16 )
#warning Note: _shared EROM, selected EROM size is 16 kBytes
_mem_size PM 0x2000
_mem_size DM 0x4000
_shared DM 0x1180..0x3FFF   PM 0x08C0..0x1FFF
_rodata DM 0x1180..0x3FFF

//#elif ( EROMSIZEKB == MIN )
//#warning Note: _shared EROM, selected EROM_MIN_TEST
//_mem_size PM 0x15C4
//_mem_size DM 0x2B88
//_shared DM 0x1180..0x2B87   PM 0x04C0..0x15C3
//_rodata DM 0x1180..0x2B87
#else
#error Unsupported EROM size specified. Set macro EROMSIZEKB = one of 64, 48, 32, 24, 16.
#endif

#else

// Conventional link mode, NOT using "_shared", fixed partitioning of EROM for code and rodata.

#if ( EROMSIZEKB == 64 )
#warning Note: Conventional link mode, selected EROM size is 64 kBytes (partitioned in 60 kB for code / 4 kB for rodata)
#warning Note: Make sure to place all code for immobilizer operation (e.g. USER handler) in lower EROM bank.
// EROM memory (word addressing): 0x0000..0x7FFF (64 kBytes = 32 kWords)
_mem_size  PM 0x8000
// The range DM:0x1180..0xFFFF is available for read-only data.
// However, it is shared with PM:0x08C0..0x7FFF. To avoid conflicts between
// code in PM and read only data in DM, we have to divide this region
// into a part usable for code, and a part usable for constant data.
// Here the split is chosen arbitrarily. It should be adapted if the
// linker does not find enough memory for either program code or
// constant data.
_reserved  DM 0x2180..0xFFFF // Reserved for code in PM:0x10C0..0x7FFF
_rodata    DM 0x1180..0x217F // reserved in PM:0x08C0..0x10BF
_reserved  PM 0x08C0..0x10BF // rodata in   DM:0x1180..0x217F

#elif ( EROMSIZEKB == 48 )
#warning Note: Conventional link mode, selected EROM size is 48 kBytes (partitioned in 44 kB for code / 4 kB for rodata)
#warning Note: Make sure to place all code for immobilizer operation (e.g. USER handler) in lower EROM bank.
// EROM memory (word addressing): 0x0000..0x5FFF (48 kBytes)
_mem_size  PM 0x6000
// The range DM:0x1180..0xBFFF is available for read-only data.
// However, it is shared with PM:0x08C0..0x3FFF. To avoid conflicts between
// code in PM and read only data in DM, we have to divide this region
// into a part usable for code, and a part usable for constant data.
// Here the split is chosen arbitrarily. It should be adapted if the
// linker does not find enough memory for either program code or
// constant data.
_reserved  DM 0x2180..0xFFFF // Reserved for code in PM:0x10C0..0x5FFF plus empty area above
_rodata    DM 0x1180..0x217F // reserved in PM:0x08C0..0x10BF
_reserved  PM 0x08C0..0x10BF // rodata in   DM:0x1180..0x217F

#elif ( EROMSIZEKB == 32 )
#warning Note: Conventional link mode, selected EROM size is 32 kBytes (partitioned in 30 kB for code / 2 kB for rodata)
// EROM memory (word addressing): 0x0000..0x3FFF (32 kBytes)
_mem_size  PM 0x3C00         // EROM area 0000h .. 3BFFh used for program code, remaining 3C00h..0x3FFFh for _rodata
// The range DM:0x1180..0x7FFF is available for read-only data.
// However, it is shared with PM:0x08C0..0x3FFF. To avoid conflicts between
// code in PM and read only data in DM, we have to divide this region
// into a part usable for code, and a part usable for constant data.
// Here the split is chosen arbitrarily. It should be adapted if the
// linker does not find enough memory for either program code or
// constant data.
_reserved  DM 0x1180..0x77FF // Reserved for code in PM:0x08C0..0x3BFF
_rodata    DM 0x7800..0x7FFF // 2 KBytes read-only data in EROM (0x3C00..0x3FFF) mapped to DM
_reserved  DM 0x8000..0xFFFF // empty area

#elif ( EROMSIZEKB == 24 )
#warning Note: Conventional link mode, selected EROM size is 24 kBytes (partitioned in 22 kB for code / 2 kB for rodata)
// EROM memory (word addressing): 0x0000..0x2FFF (24 kBytes), use 2 kB for rodata
_mem_size  PM 0x2C00         // EROM area 0000h .. 2BFFh used for program code, remaining 2C00h..0x2FFFh for _rodata
// The range DM:0x1180..0x57FF (byte addressing) is available for read-only data.
// However, it is shared with PM:0x08C0..0x2FFF. To avoid conflicts between
// code in PM and read only data in DM, we have to divide this region
// into a part usable for code, and a part usable for constant data.
// Here the split is chosen arbitrarily. It should be adapted if the
// linker does not find enough memory for either program code or
// constant data.
_reserved  DM 0x1180..0x57FF // Reserved for code in PM:0x08C0..0x2BFF
_rodata    DM 0x5800..0x5FFF // 2048 bytes read-only data in EROM (word adr.0x2C00..0x2FFF) mapped to DM
_reserved  DM 0x6000..0xFFFF // empty area

#elif ( EROMSIZEKB == 16 )
#warning Note: Conventional link mode, selected EROM size is 16 kBytes (partitioned in 15 kB for code / 1 kB for rodata)
// EROM memory (word addressing): 0x0000..0x3FFF (16 kBytes), use 1 kByte for rodata
_mem_size  PM 0x1E00         // EROM area 0000h .. 1DFFh used for program code, remaining 1E00h..0x1FFFh for _rodata
// The range DM:0x1180..0x3FFF (byte addressing) is available for read-only data.
// However, it is shared with PM:0x08C0..0x1FFF. To avoid conflicts between
// code in PM and read only data in DM, we have to divide this region
// into a part usable for code, and a part usable for constant data.
// Here the split is chosen arbitrarily. It should be adapted if the
// linker does not find enough memory for either program code or
// constant data.
_reserved  DM 0x1180..0x3BFF // Reserved for code in PM:0x04C0..0x1EFF
_rodata    DM 0x3C00..0x3FFF // 1024 bytes read-only data in EROM (word adr.0x1E00..0x1FFF) mapped to DM
_reserved  DM 0x4000..0xFFFF // empty area

#else

#error Unsupported EROM size specified. Set macro EROMSIZEKB = one of 64, 48, 32, 24, 16.

#endif

#endif

_rwinit_mem    DM                  // place .rwinit segment in DM .rodata (with option +i)
_no_init_range DM  0x0000..0x017F  // exclude SFRs  from .rwinit
//_no_init_range DM  0x0880..0xFFFF  // exclude rodata (EROM area) from .rwinit and from loading by debugger
//_no_init_range ULP 0x0000..0x03FF  // exclude ULPEE from .rwinit and from loading by debugger

#if ( __tct_release__ >= 1601 )
#warning Note: 2016 (or newer) release; excluding ULP EEPROM and PM from _rwinit segment.
_rwinit_exclude ULP
_rwinit_exclude PM
#endif

// Defaults: symbol table and startup function main().
_symbol _vector_table 0
_entry_point _vector_table
// Note: not required, only nice to have main function at top of disassembly.
_symbol _main _after _vector_table

// These two variables are only used exclusively hence they are mapped to the same User RAM address.
_overlay hitagpro_user_param              0x180
_overlay phcaiKEyLLGenFunc_Func_Params    0x180

// eof
