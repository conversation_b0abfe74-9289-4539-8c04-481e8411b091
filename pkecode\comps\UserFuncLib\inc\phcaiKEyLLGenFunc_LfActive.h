/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_LfActive.h 31225 2021-03-10 14:44:11Z dep10330 $
  $Revision: 31225 $

*/


/**
 * @file
 * Declarations of User Library Functions related to the LF Active receiver.
 */

#ifndef PHCAIKEYLLGENFUNC_LFACT_H
#define PHCAIKEYLLGENFUNC_LFACT_H

#include "types.h"
#include "phcaiKEyLLGenFunc_Util.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup LFACTIVE LF Active support functions
 * Support functions for LF Activer receiver.
 * @{
 */


/*-----------------------------------------------------------------------------------------------*/
/* Definitions                                                                                   */
/*-----------------------------------------------------------------------------------------------*/

/**
 Define alias names for WUP bits to make code more indepedent from platform.
 */
#if defined(PLATFORM_TOKEN) || defined(PLATFORM_TOKENPLUS)
  /* WUP bit names in TOKEN and TOKEN-PLUS */
  #define PRESTAT_BIT_WUPA        PRESTAT.bits.WUP1M
  #define PRESTAT_BIT_WUPB        PRESTAT.bits.WUP2M
  #define PRESTAT_BIT_WUPC        PRESTAT.bits.WUP3M
  #define PRESTAT_BIT_WUPA_HIST   PRESTAT.bits.WUP1MH
  #define PRESTAT_BIT_WUPB_HIST   PRESTAT.bits.WUP2MH
  #define PRESTAT_BIT_WUPC_HIST   PRESTAT.bits.WUP3MH
#endif
#ifdef PLATFORM_TOKENSRX_COMMON
  /* WUP bit names in TOKEN-SRX */
  #define PRESTAT_BIT_WUPA        PRESTAT.bits.WUPAM
  #define PRESTAT_BIT_WUPB        PRESTAT.bits.WUPBM
  #define PRESTAT_BIT_WUPC        PRESTAT.bits.WUPCM
  #define PRESTAT_BIT_WUPA_HIST   PRESTAT.bits.WUPAMH
  #define PRESTAT_BIT_WUPB_HIST   PRESTAT.bits.WUPBMH
  #define PRESTAT_BIT_WUPC_HIST   PRESTAT.bits.WUPCMH
#endif

/**
 * LF reception error codes.
 */
typedef enum
{
  /** no error, data byte received sucessfully (code 0)                                  */
  LF_OK = 0,
  /** BITFAIL set                              (code 1)                                  */
  LF_BITFAIL,
  /** invalid MODE bits in PRESTAT             (code 2)                                  */
  LF_INVMODE,
  /** NEWBYTE overflow                         (code 3)                                  */
  LF_NBOVF,
  /** unexpected new LF WUP found              (code 4)                                  */
  LF_WUPXM,
  /** CRC mismatch (for use in application, not used in this module)(code 5)             */
  LF_INVCRC,
  /** Unexpected data received (for use in application, not used in this module)(code 6) */
  LF_INVDATA,
  /** Time-out waiting for NEWBYTE (SRX only)  (code 7)                                  */
  LF_TIMEOUT
}
LFRCVERROR_t;

/**
 * Preprocessor reception modes, indicated by MODE bits in
 * status register PRESTAT.
 */
#if defined(PLATFORM_TOKEN) || defined(PLATFORM_TOKENPLUS)
/* MODE (2-bit field) in TOKEN and TOKEN-PLUS */
typedef enum
{
  /** no active communication */
  PPMODE_NOCOMM    = 0u,
  /** code violation running  */
  PPMODE_CODEVIOL  = 1u,
  /** WUP matching            */
  PPMODE_WUPMATCH  = 2u,
  /** data reception          */
  PPMODE_DATAREC   = 3u
}
PRESTATMODE_t;

#else

/* MODE (1-bit field) in TOKEN-SRX */
typedef enum
{
  /** no active communication */
  PPMODE_NOCOMM    = 0u,
  /** data reception          */
  PPMODE_DATAREC   = 1u
}
PRESTATMODE_t;

#endif

/**
 * LF Active WUP index.
 * Named A,B,C for common handling.
 */
typedef enum
{
  /** No LF WUP */
  LF_WUP_NONE      = 0u,
  /** LF WUPA (SRX platform) or WUP1 (previous platforms) */
  LF_WUPA          = 1u,
  /** LF WUPB (SRX platform) or WUP2 (previous platforms) */
  LF_WUPB          = 2u,
  /** LF WUPC (SRX platform) or WUP3 (previous platforms) */
  LF_WUPC          = 3u
} LfWupIndex_t;

/**
 * Operation modes of the PostWUP timer.
 * NONE       : timer never started
 * FROMWUP    : timer started by WUP event
 * FROMNEWBYTE: timer started by NEWBYTE event
 *
 * Note: "both" WUP and NB is not encouraged. Application should decide to use either WUP or NEWBYTE.
 */
typedef enum {
  /** timer never started */
  LFPWT_NONE         = 0,
  /** timer started by WUP event */
  LFPWT_FROMWUP      = 1,
  /** timer started by NEWBYTE event */
  LFPWT_FROMNEWBYTE  = 2
} LFPWTIMERMODE_t;


#if defined(PLATFORM_TOKEN) || defined(PLATFORM_TOKENPLUS)
  // WUP bit names in TOKEN and TOKEN-PLUS
  /** Alias name for WUP pattern 1 of TOKEN and TOKEN-PLUS */
  #define PRESTAT_BIT_WUPA   PRESTAT.bits.WUP1M
  /** Alias name for WUP pattern 2 of TOKEN and TOKEN-PLUS */
  #define PRESTAT_BIT_WUPB   PRESTAT.bits.WUP2M
  /** Alias name for WUP pattern 3 of TOKEN and TOKEN-PLUS */
  #define PRESTAT_BIT_WUPC   PRESTAT.bits.WUP3M
#else
  // WUP bit names in TOKEN-SRX
  #define PRESTAT_BIT_WUPA   PRESTAT.bits.WUPAM
  #define PRESTAT_BIT_WUPB   PRESTAT.bits.WUPBM
  #define PRESTAT_BIT_WUPC   PRESTAT.bits.WUPCM
#endif


/*-----------------------------------------------------------------------------------------------*/

/**
 POSTWUPCON register in PLUS types:
 | 7         | 6         | 5            | 4          | 3         | 2    | 1        | 0        |
 | CALCNTREN | MULTISHOT | CMPWRPENDING | WUPRESTART | NBRESTART | RUN  | CMPINTEN | CMPMATCH |

 POSTWUPCON register in SRX types:
 | 7         | 6         | 5            | 4          | 3         | 2    | 1        | 0        |
 | RFU R/W0  | MULTISHOT | CMPWRPENDING | WUPRESTART | NBRESTART | RUN  | CMPINTEN | CMPMATCH |
 */

/* Bit patterns in POSTWUPCON register */
/** Enable calibration counter. PLUS types only. */
#define PWUPCON_CALCNTREN     0x80u
/** Enable multi-shot mode (1: restart counter after compare match; 0: stop counter at match) */
#define PWUPCON_MULTISHOT     0x40u
/** Status flag: if 1, compare value has not yet been moved to internal compare register. */
#define PWUPCON_CMPWRPENDING  0x20u
/** Enable counter (re)start at WUP event. */
#define PWUPCON_WUPRESTART    0x10u
/** Enable counter (re)start at NEWBYTE event. */
#define PWUPCON_NBRESTART     0x08u
/** Status flag: 1 means counter is incrementing.
Control bit: write 0 to stop the counter. */
#define PWUPCON_RUN           0x04u
/** Enable preprocessor interrupt at compare match. */
#define PWUPCON_CMPINTEN      0x02u
/** Interrupt flag, write 1 to clear. */
#define PWUPCON_CMPMATCH      0x01u

/** Tbit = duration of one bit at standard 4 kbd data rate in micro seconds */
#define LFA_TBIT_US                     256u

/** Minimum timer duration accepted (all types and modes) */
#define LFA_PWUP_MINTIME_US             200u

/** Minimum wake-up pattern length (bits) accepted (PLUS types only) */
#define LFA_PWUP_CAL_MINWUPLENGTH         8u

/** Nominal LPRC frequency [kHz] in case of no compensation enabled */
#define LFA_PWUP_CAL_NOMINAL_FLPRC_KHZ  180u

/** Offset value subtracted from calibration counter (PLUS types only) */
#define LFA_PWUP_CAL_OFFSET             (-5)

/** Offset value in microseconds subtracted from time parameter in case of NBRESTART
(SRX types only)
*/
#ifdef PLATFORM_TOKENSRX_COMMON /* TODO check on SRX2 */
#define LFA_PWUP_SRX_WUP_COMPENS_US     250u
#define LFA_PWUP_SRX_NB_COMPENS_US      915u
#endif

/** Divider used in calculation of compare value (5^6) */
#define LFA_PWUP_NCMP_DIVIDER         15625u

/*-----------------------------------------------------------------------------------------------*/

/**
 * Initializes the LF data reception.
 * Clears error code and stored "FIFO data ready" flag (m_u8_NextFifoDataReady),
 * sets default value of NEWBYTEOVFHOLD.
 * This function must be called before using the data reception functions
 * phcaiKEyLLGenFunc_LfAct_read_byte or phcaiKEyLLGenFunc_LfAct_read_block.
 * @see phcaiKEyLLGenFunc_LfAct_read_byte
 */
void phcaiKEyLLGenFunc_LfAct_read_init( void );

/*-----------------------------------------------------------------------------------------------*/

/**
 * Read the preprocessor status register PRESTAT.
 * On TOKEN and -PLUS types, includes work-around for missing synchronization of MODE bits.
 * On TOKEN types, includes work-around for VBATREG read anomaly.
 * @see phcaiKEyLLGenFunc_Util_ClearVbatRegRead
 */
inline uint16_t phcaiKEyLLGenFunc_LfAct_read_PRESTAT( void );

/*-----------------------------------------------------------------------------------------------*/

/**
 * Receive and read one byte from the Active LF demodulator.
 * Waits for data arrival, checks communication status and fetches one
 * received data byte.
 * Considers FIFO enable or disable setting on TOKEN and TOKEN-PLUS platforms.
 * This function may only be called after an LF wake-up event, and until first return of
 * an error code (result not equal to LF_OK), otherwise the results are undefined.
 * On SRX platform, may return error code LF_TIMEOUT in case the payload receiver is not set up
 * for data reception or has been stopped.
 *
 * @param[out]  pu8_data the received data byte in case of a result code LF_OK.
 *              With other result code, the pointed variable is set to 0.
 *
 * @return      Result code, also available by phcaiKEyLLGenFunc_LfAct_get_rx_errorcode().
 *
 * @see LFRCVERROR_t
 * @see phcaiKEyLLGenFunc_LfAct_read_block
 * @see phcaiKEyLLGenFunc_LfAct_get_rx_errorcode
 */
LFRCVERROR_t phcaiKEyLLGenFunc_LfAct_read_byte( uint8_t * pu8_data );

/*-----------------------------------------------------------------------------------------------*/

/**
 * Receives a number of bytes from the LF receiver.
 * Note: does not consider if any byte is or contains a checksum.
 *
 * @param[out]  pu8_Buffer       Pointer to RAM-address where the received data will be stored.
 * @param[in]   u8_numbytes      Number of data bytes to be read from the LF interface.
 *                               If this parameter is zero, does not expect any data and
 *                               returns LF_OK.
 *
 * @return      Result code, also available by phcaiKEyLLGenFunc_LfAct_get_rx_errorcode().
 * @see LFRCVERROR_t
 * @see phcaiKEyLLGenFunc_LfAct_read_byte
 */
LFRCVERROR_t  phcaiKEyLLGenFunc_LfAct_read_block( uint8_t * pu8_rx_buffer, uint8_t u8_numbytes );

/*-----------------------------------------------------------------------------------------------*/

/**
 * Returns the last error code.
 *
 * @return last error code, 00h: success, other: error during data reception
 * @see LFRCVERROR_t
 */
LFRCVERROR_t phcaiKEyLLGenFunc_LfAct_get_rx_errorcode( void );

/*-----------------------------------------------------------------------------------------------*/

/**
 * Stop the LF Active data reception mode unconditionally.
 * Applies to SRX/SRX2 types only, no effect on other platforms.
 * Writes '1' to the STOPRX bit in the PRESTAT register.
 * Reads back the PRESTAT register repeatedly until the STOPRX bit has returned to 0.
 * This operation does not clear the NEWBYTE or NEWBYTE_OVF flags.
 * @see phcaiKEyLLGenFunc_LfAct_stopRx_clearFlags
 */
void phcaiKEyLLGenFunc_LfAct_stop_rx( void );

/*-----------------------------------------------------------------------------------------------*/

/**
 * On SRX/SRX2 types: stop the LF Active data reception mode, and clear any event flags
 * (WUPxM, WUPxMH, NEWBYTE).
 * Reads back the PRESTAT register repeatedly until the STOPRX bit has returned to 0.
 * Other platforms: clears the LFA events. Note, the data reception mode cannot be stopped by
 * this function.
 * @see phcaiKEyLLGenFunc_LfAct_stop_rx
 */
void phcaiKEyLLGenFunc_LfAct_stopRx_clearFlags( void );

/*-----------------------------------------------------------------------------------------------*/

/**
 * SRX/SRX2 platforms:
 *   Check if payload receiver is active for the give WUP index.
 *   If yes, stops the payload receiver by phcaiKEyLLGenFunc_LfAct_stopRx_clearFlags.
 * Other platforms: no effect.
 *
 * @see phcaiKEyLLGenFunc_LfAct_stop_rx
 */
void phcaiKEyLLGenFunc_LfAct_checkStopRx( LfWupIndex_t en_WupIndex );

/*-----------------------------------------------------------------------------------------------*/

/**
 * Reset the LF Active receiver (incl. WUP detectors) by toggling the PRERST bit,
 * Clears the LF Active related flags in PRESTAT register and stops
 * any ongoing payload data reception.
 * RTC_WUP and IT_WUP (and the RTC/IT operation) are not affected.
 */
void phcaiKEyLLGenFunc_LfAct_reset_rx( void );

/*-----------------------------------------------------------------------------------------------*/

/**
 * SRX platforms:
 *   If data reception is active but no WUPxM flags are asserted, stop the payload receiver,
 *   to avoid missing subsequent LFA wake-ups.
 *   To be called (latest) before going to power-down / reboot.
 * Other platforms: no action.
 */
void phcaiKEyLLGenFunc_LfAct_stopRxWithNoWup( void );

/*-----------------------------------------------------------------------------------------------*/

/**
 * If BITFAIL is asserted, clear it and return TRUE.
 * Otherwise, return FALSE.
 */
bool_t phcaiKEyLLGenFunc_LfAct_clear_BITFAIL( void );

/*-----------------------------------------------------------------------------------------------*/

/**
 * Returns TRUE if the FIFO is enabled, FALSE otherwise.
 * Always FALSE for SRX types.
 * @return TRUE if the FIFO is enabled.
 */
inline bool_t phcaiKEyLLGenFunc_LfAct_FIFO_enabled( void );

/*-----------------------------------------------------------------------------------------------*/

#ifdef PLATFORM_HAS_LFPOSTWUPTIMER

/**
 * Initialize the post-wup counter to allow start by WUP or NEWBYTE (or none).
 * Enables WUPRESTART ex-or NBRESTART and optionally MULTISHOT.
 * Sets compare register to maximum (FFFFh).
 * Does not set PWUPCON_CMPINTEN.
 * For details, refer to application note AN-SCA1723.
 *
 * @param[in]   e_mode  the operating mode or use case.
 * @param[in]   b_MultiShotEnable TRUE to enable multi-shot mode.
 * @see phcaiKEyLLGenFunc_LfAct_PostWupTimer_config
 * @see LFPWTIMERMODE_t
 */
void    phcaiKEyLLGenFunc_LfAct_PostWupTimer_init( LFPWTIMERMODE_t e_mode,
                                                   bool_t b_MultiShotEnable );

/**
 * Configure timer, to be called early enough before timed action (right after WUP or latest before
 * last NEWBYTE).
 * For details, refer to application note AN-SCA1723.
 * Notes:
 * 1. Always enables the compare interrupt (CMPINTEN = 1).
 * 2. on PLUS, always employs the calibration counter register POSTWUPCAL to calculate value for
 *    POSTWUPCOMP, UNLESS u8_WUPlength=0 (in this case no calibration is done and the nominal
 *    LPRC frequency is used).
 *    Minimum WUP length is given by LFA_PWUP_CAL_MINWUPLENGTH.
 * 3. on SRX, only sets the POSTWUPCON and POSTWUPCOMP register (POSTWUPCAL doesn't exist).
 *    WUP length is not relevant (don't care).
 *    It is recommended to use the WUP started mode (LFPWT_FROMWUP).
 * 4. time to compare match in microseconds is rounded to nearest possible setting.
 * 5. max time is 65535 us = 65.5 ms, is deemed sufficient for usual applications.
 *    min time is given by LFA_PWUP_MINTIME_US.
 * 6. Function returns ERROR in case of:
 *    mode equals NONE, illegal time, invalid WUP length, invalid POSTWUPCAL value
 *    (CAL value is in not range of +/-15% of nominal).
 *
 * @param[in]   e_mode  the operating mode or use case, either LFPWT_FROMWUP or LFPWT_FROMNEWBYTE
 * @param[in]   b_MultiShotEnable TRUE to enable multi-shot mode
 * @param[in]   u8_WUPlength  Length of LF WUP pattern in bits (don't care for SRX)
 * @param[in]   u16_comptime_us  Desired timer duration in microseconds
 * @return ERROR in case of invalid parameters or calibration out of range
 * @see phcaiKEyLLGenFunc_LfAct_PostWupTimer_isExpired
 * @see phcaiKEyLLGenFunc_LfAct_PostWupTimer_wait
 *
 */
error_t phcaiKEyLLGenFunc_LfAct_PostWupTimer_config( LFPWTIMERMODE_t e_mode,
                                                     bool_t          b_MultiShotEnable,
                                                     uint8_t         u8_WUPlength,
                                                     uint16_t        u16_comptime_us );


/**
 * Checks if compare event has occurred.
 * Returns TRUE if timer is expired, means CMPMATCH bit is raised.
 * @see phcaiKEyLLGenFunc_LfAct_PostWupTimer_wait
 */
bool_t phcaiKEyLLGenFunc_LfAct_PostWupTimer_isExpired( void );


/**
 * Wait until timer expired (CMPMATCH changes to 1).
 * Returns error on time-out.
 * Clears CMPMATCH interrupt. Does not stop timer.
 * Uses timer0 for time-out.
 * @param[in] u16_timeoutms time-out period in ms.
 * @see phcaiKEyLLGenFunc_LfAct_PostWupTimer_stop
 */
error_t phcaiKEyLLGenFunc_LfAct_PostWupTimer_wait( uint16_t u16_timeoutms );


/**
 * Stop the timer, keep current settings.
 * @see phcaiKEyLLGenFunc_LfAct_PostWupTimer_init
 */
void    phcaiKEyLLGenFunc_LfAct_PostWupTimer_stop( void );


/**
 * Clear the NBRESTART and WUPRESTART bits, keep others unchanged.
 */
void phcaiKEyLLGenFunc_LfAct_PostWupTimer_clear_restart( void );

#endif



/*-----------------------------------------------------------------------------------------------*/

inline uint16_t phcaiKEyLLGenFunc_LfAct_read_PRESTAT( void )
{
  uint16_t u16_PRESTAT_Rd;

#if defined( PLATFORM_TOKEN ) || defined( PLATFORM_TOKENPLUS )

  /* Work-around for missing synchronization of MODE bits:   */
  /* read PRESTAT register twice, and read once more if the values are different (very rarely). */
  u16_PRESTAT_Rd = PRESTAT.val;
  if ( u16_PRESTAT_Rd != PRESTAT.val ) {
    u16_PRESTAT_Rd = PRESTAT.val;
  }

#endif

#ifdef PLATFORM_TOKENSRX_COMMON

  // No double-read needed here since no field with more than one bit.
  u16_PRESTAT_Rd = PRESTAT.val;

#endif

  phcaiKEyLLGenFunc_Util_ClearVbatRegRead();

  return u16_PRESTAT_Rd;
}

/*-----------------------------------------------------------------------------------------------*/

inline bool_t phcaiKEyLLGenFunc_LfAct_FIFO_enabled( void )
{
  bool_t e_res = FALSE;
  #if defined( PLATFORM_TOKEN ) || defined( PLATFORM_TOKENPLUS )
  if ( PRECON9.bits.FIFOEN != 0u ) {
    e_res = TRUE;
  }
  #else
  /* no action, result is always FALSE. */
  #endif
  return e_res;
}

/*-----------------------------------------------------------------------------------------------*/

/*@}*/
/*@}*/

#endif

