/*
 (c) NXP B.V. 2014. All rights reserved.

 Disclaimer
 1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
    warranties of any kind. NXP makes no warranties to Licensee and shall not
    indemnify Licensee or hold it harmless for any reason related to the NXP
    Software/Source Code or otherwise be liable to the NXP customer. The NXP
    customer acknowledges and agrees that the NXP Software/Source Code is
    provided AS-IS and accepts all risks of utilizing the NXP Software under
    the conditions set forth according to this disclaimer.

 2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
    BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
    FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
    RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
    SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
    INCLUDING WITHOUT LIMITATION, <PERSON>AMAG<PERSON> RESULTING OR ALLEGDED TO HAVE
    RESULTED FROM ANY DEFECT, ERROR OR OMMISSION IN THE NXP SOFTWARE/SOURCE
    CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
    RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
    THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
    INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
    (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
    AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
    SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGES.

 $Revision: 8810 $
 $Id: ncf2961_sfr.s 8810 2017-04-12 13:41:31Z nxp84227 $
*/

/**
 * @file
 * Declaration of the special function registers (SFR) of NCF2961.
 */

#ifndef NCF2961_SFR_S
#define NCF2961_SFR_S

.undef global data DBGDAT
.undef global data DBGCON
.undef global data CXPC
.undef global data CXSW
.undef global data P1INS
.undef global data P1OUT
.undef global data P1DIR
.undef global data P1INTDIS
.undef global data IIUCON2
.undef global data IIUCON0
.undef global data IIUSTAT
.undef global data IIUCON1
.undef global data IIUDAT
.undef global data IIUSTATE
.undef global data HTCON
.undef global data AESDAT
.undef global data AESCON
.undef global data CRCDAT
.undef global data CRC8DIN
.undef global data WDCON
.undef global data CLKCON0
.undef global data CLKCON1
.undef global data CLKCON2
.undef global data CLKCON3
.undef global data CLKCON4
.undef global data ADCDAT
.undef global data ADCCON
.undef global data BISTCON
.undef global data ULPADDR
.undef global data ULPSEL
.undef global data ULPCON0
.undef global data ULPDAT
.undef global data ULPCON1
.undef global data T0CON0
.undef global data T0CON1
.undef global data T0REG
.undef global data T0RLD
.undef global data T1CON0
.undef global data T1CON1
.undef global data T1CON2
.undef global data T1REG
.undef global data T1CAP
.undef global data T1CMP
.undef global data T2CON0
.undef global data T2CON1
.undef global data T2REG
.undef global data T2RLD
.undef global data RNGDAT
.undef global data RNGCON
.undef global data INTCON
.undef global data INTFLAG0
.undef global data INTFLAG1
.undef global data INTFLAG2
.undef global data INTEN0
.undef global data INTEN1
.undef global data INTEN2
.undef global data SYSINTEN0
.undef global data SYSINTEN1
.undef global data INTSET0
.undef global data INTSET1
.undef global data INTSET2
.undef global data INTCLR0
.undef global data INTCLR1
.undef global data INTCLR2
.undef global data INTVEC
.undef global data PRESWUP0
.undef global data P1WRES
.undef global data PCON0
.undef global data PCON1
.undef global data PCON2
.undef global data SPI0CON0
.undef global data SPI0CON1
.undef global data SPI0DAT
.undef global data SPI0STAT
.undef global data SPI1CON0
.undef global data SPI1CON1
.undef global data SPI1DAT
.undef global data SPI1STAT
.undef global data P1ALTF
.undef global data BITCNT
.undef global data BITSWAP
.undef global data INTFLAG3
.undef global data INTEN3
.undef global data INTSET3
.undef global data INTCLR3
.undef global data TXPCON
.undef global data CLKRSTCON
.undef global data VCOCALCON
.undef global data PLLCON
.undef global data TXDAT
.undef global data TXSPC
.undef global data ENCCON0
.undef global data ENCCON1
.undef global data FREQCON0
.undef global data FREQCON1
.undef global data BRGCON
.undef global data FSKCON
.undef global data FSKRMP
.undef global data ASKCON
.undef global data ASKRMP
.undef global data PACON
.undef global data PAPWR
.undef global data PATRIM
.undef global data PALIMIT

#endif

