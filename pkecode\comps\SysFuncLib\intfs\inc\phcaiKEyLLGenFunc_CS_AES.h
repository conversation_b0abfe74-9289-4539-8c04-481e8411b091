/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_AES.h 12264 2018-02-15 13:30:43Z dep10330 $
  $Revision: 12264 $
*/

/**
 * @file
 * Declarations of the stubs to call AES specific system functions (ROM library).
 */

#ifndef PHCAIKEYLLGENFUNC_CS_AES_H
#define PHCAIKEYLLGENFUNC_CS_AES_H

#include "types.h"

/**
 * @defgroup genfunclib_stubs_aes AES
 * Caller stubs for AES specific API functions placed
 * in ROM and executed in system mode.
 * @{
 */

/**
 * Loads data (a number of bytes) into the AES crypto unit.
 *
 * @pre phcaiKEyLLGenFunc_CS_AES_Init has to be called first.
 *
 * @param[in] input_vector Pointer to the byte array to be loaded.
 * @param[in] len The number of bytes to be loaded.
 *
 * @see phcaiKEyLLGenFunc_CS_AES_LoadKey()
 */
void phcaiKEyLLGenFunc_CS_AES_LoadData(
  const uint8_t* const input_vector, const uint8_t len);

/**
 * Loads the 16-byte secret key into the AES crypto unit.
 *
 * @pre phcaiKEyLLGenFunc_CS_AES_Init() has to be called first.
 * @param[in] key Byte array containing the key.
 *
 * @see phcaiKEyLLGenFunc_CS_AES_Encrypt()
 */
void phcaiKEyLLGenFunc_CS_AES_LoadKey(const uint8_t* const key);

/**
 * Starts the AES encryption unit (performs encryption).
 *
 * @param[out] output_vector Result of the encryption (16-byte AES output
 * vector).
 *
 * @pre phcaiKEyLLGenFunc_CS_AES_Init(), _AES_LoadData() etc. have
 * to be called first.
 */
void phcaiKEyLLGenFunc_CS_AES_Encrypt(uint8_t* const output_vector);

/**
 * Initializes the AES crypto unit.
 * AES clock is set to 8 MHz and the AES unit is reset.
 *
 * @note This function has to be called before any other AES function is used.
 * @see phcaiKEyLLHal_AES_Init()
 */
void phcaiKEyLLGenFunc_CS_AES_Init(void);

/**
 * Disables the AES crypto unit.
 */
void phcaiKEyLLGenFunc_CS_AES_disable(void);

/*@}*/

#endif
