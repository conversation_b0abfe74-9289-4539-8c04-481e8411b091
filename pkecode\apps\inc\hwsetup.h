/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: hwsetup.h 31225 2021-03-10 14:44:11Z dep10330 $
  $Revision: 31225 $
*/

/**
 * @file
 * Basic definitions of hardware setup related functions for TOKEN demo applications.
 */

/*+----------------------------------------------------------------------------
  | NOTE: The code provided herein is still under development and hence
  |       subject to change.
  +----------------------------------------------------------------------------*/


#ifndef HWSETUP_H
#define HWSETUP_H

#include "phcaiKEyLLGenFunc.h"

/*
  Load VBAT/REG SFR definitions:
  these are application specific, hence the default settings can be overridden in a project
  by setting macro HW_APP_LOCAL. In this case, the project must provide a header file
  hwsetup_app_local.h with all necessary settings.
 */
#ifndef HW_APP_LOCAL
#include "hwsetup_app.h"
#else
#include "hwsetup_app_local.h"
#endif

/*
  Board specific settings. These are application specific, hence the default settings can
  be overridden in a project by setting macro HW_BOARD_LOCAL.
  This file must also be readable for asm code files (hence only C preprocessor code).
  In this case, the project must provide a header file
  hwsetup_board_local.h with all necessary settings.
 */
#ifndef HW_BOARD_LOCAL
#include "hwsetup_boards.h"
#else
#include "hwsetup_board_local.h"
#endif

/*-----------------------------------------------------------------------------------------------*/

/**
 Enum type representing the port configuration settings PxyC in PRESWUPx registers.
 */
typedef enum
{
  PORTCFG_NOPULL_WUPFALLING = 0u,  /* 000b = 0 : Resistors off; Wake-up on falling edge              */
  PORTCFG_NOPULL_WUPRISING  = 1u,  /* 001b = 1 : Resistors off; Wake-up on rising edge               */
  PORTCFG_PULLDOWN_NOWUP    = 2u,  /* 010b = 2 : Pull-down resistor activated; Wake-up disabled      */
  PORTCFG_NOPULL_NOWUP      = 3u,  /* 011b = 3 : Resistors off; Wake-up disabled                     */
  PORTCFG_PULLUP_WUPFALLING = 4u,  /* 100b = 4 : Pull-up resistor activated; Wake-up on falling edge */
  PORTCFG_PULLUP_WUPRISING  = 5u,  /* 101b = 5 : Pull-up resistor activated; Wake-up on rising edge  */
  PORTCFG_PULLUP_NOWUP      = 6u,  /* 110b = 6 : Pull-up resistor activated; Wake-up disabled        */
  PORTCFG_RESERVED          = 7u   /* 111b = 7 : RFU                                                 */
} PortPullWupConfig_t;

/*-----------------------------------------------------------------------------------------------*/

/**
 Structured type to collect all VBAT / VBATREG register settings.
 Adapted to unified scheme for all TOKEN family platforms.
 */
typedef struct
{
  // page offset 0
  uint8_t   u8_RTCCON;
  uint8_t   u8_PRECON2;
  uint8_t   u8_PRECON3;
  uint8_t   u8_PRECON4;

  // page offset 1
  uint8_t   u8_PRECON5;
  uint8_t   u8_PRECON6;
  uint8_t   u8_PRECON7;
  uint8_t   u8_PRECON8;

  // page offset 2
  uint8_t   u8_PRECON9;
  uint8_t   u8_PRECON10;
  uint8_t   u8_PRECON11;
  uint8_t   u8_PRECON12;

  // page offset 3
  uint32_t  u32_WUPA;

  // page offset 4
  uint32_t  u32_WUPB;

  // page offset 5
  uint32_t  u32_WUPC;

  // page offset 6
  uint8_t   u8_PREPD;
  uint8_t   u8_RFU_6_2;
  uint8_t   u8_RFU_6_3;
  uint8_t   u8_RFU_6_4;

  // page offset 7
#if defined(PLATFORM_HAS_LFTUNE)
  // TOKEN types
  uint16_t  u16_LFTUNEVBAT;
  uint16_t  u16_RFU_7;
#elif defined(PLATFORM_HAS_LFTUNE_32)
  // PLUS, SRX types
  uint8_t   u8_LFTUNECH1ACT;
  uint8_t   u8_LFTUNECH2ACT;
  uint8_t   u8_LFTUNECH3ACT;
  uint8_t   u8_RFU_7;
#else
  // No tuning caps in selected type.
  uint8_t   u8_RFU_7_1;
  uint8_t   u8_RFU_7_2;
  uint8_t   u8_RFU_7_3;
  uint8_t   u8_RFU_7_4;
#endif

  // page offset 8
  uint16_t  u16_PRESWUP0;
  uint16_t  u16_PRESWUP1;

  // page offset 9
  uint16_t  u16_PRESWUP2;
  uint16_t  u16_PRESWUP3;  // PLUS, SRX types

  // page offset 10
  uint16_t  u16_PRESWUP4;  // PLUS, SRX types
  uint16_t  u16_RFU_10;

  // page offset 11
  uint8_t   u8_P1WRES;
  uint8_t   u8_P2WRES;
  uint8_t   u8_P3WRES;
  uint8_t   u8_RFU_11;

  // page offset 12
  uint8_t   u8_PREPOLL0;
  uint8_t   u8_PREPOLL1;
  uint16_t  u16_RFU_12;

  // page offset 13
  uint8_t   u8_MSICON0;
  uint8_t   u8_MSICON1;
  uint8_t   u8_MSICON2;  // only PLUS, SRX types
  uint8_t   u8_RFU_13;

  // page offset 14
  uint8_t   u8_LFAEN0;
  uint8_t   u8_LFAEN1;
  uint8_t   u8_LFAEN2;
  uint8_t   u8_LFAEN3;

  // page offset 15
  uint8_t   u8_LFAEN4;
  uint8_t   u8_RFU_15_1;
  uint8_t   u8_RFU_15_2;
  uint8_t   u8_RFU_15_3;

  // page offset 16
  uint8_t   u8_LFACON0;
  uint8_t   u8_LFACON1;
  uint8_t   u8_LFACON2;
  uint8_t   u8_LFACON3;

  // page offset 17
  uint8_t   u8_LFACON4;
  uint8_t   u8_LFACON5;
  uint8_t   u8_LFACON6;
  uint8_t   u8_LFASENSE;

  // page offset 18
  uint8_t   u8_WUPALEN;
  uint8_t   u8_WUPBLEN;
  uint8_t   u8_WUPCLEN;
  uint8_t   u8_RFU_18;

  // page offset 19
  uint16_t  u16_WUPCON;
  uint16_t  u16_PAYRXCON;

  // page offset 20
  uint16_t  u16_WUPA_WUPPATCON;
  uint16_t  u16_WUPB_WUPPATCON;

  // page offset 21
  uint16_t  u16_WUPC_WUPPATCON;
  uint16_t  u16_RFU_21;
}
VbatRegSettings_t;

// TODO use offsetof() ?
//#define EEPAGE_VBATSET_PAGE_WUPB_OFFSET    4u   // offset of u32_WUPB

/*-----------------------------------------------------------------------------------------------*/

typedef enum
{
  PKE_VBATREG_RTCCON,       /*   0 = 00h               */
  PKE_VBATREG_PRECON2,      /*   1 = 01h               */
  PKE_VBATREG_PRECON3,      /*   2 = 02h               */
  PKE_VBATREG_PRECON4,      /*   3 = 03h               */
  PKE_VBATREG_PRECON5,      /*   4 = 04h               */
  PKE_VBATREG_PRECON6,      /*   5 = 05h               */
  PKE_VBATREG_PRECON7,      /*   6 = 06h               */
  PKE_VBATREG_PRECON8,      /*   7 = 07h               */
  PKE_VBATREG_PRECON9,      /*   8 = 08h               */
  PKE_VBATREG_PRECON10,     /*   9 = 09h               */
  PKE_VBATREG_PRECON11,     /*  10 = 0Ah               */
  PKE_VBATREG_PRECON12,     /*  11 = 0Bh               */
  PKE_VBATREG_PREPD,        /*  12 = 0Ch               */
  PKE_VBATREG_WUPA,         /*  13 = 0Dh               */
  PKE_VBATREG_WUPB,         /*  14 = 0Eh               */
  PKE_VBATREG_WUPC,         /*  15 = 0Fh               */
  PKE_VBATREG_LFTUNE,       /*  16 = 10h               */
  PKE_VBATREG_PRESWUP0,     /*  17 = 11h               */
  PKE_VBATREG_PRESWUP1,     /*  18 = 12h               */
  PKE_VBATREG_PRESWUP2,     /*  19 = 13h               */
  PKE_VBATREG_PRESWUP3,     /*  20 = 14h               */
  PKE_VBATREG_PRESWUP4,     /*  21 = 15h               */
  PKE_VBATREG_PXWRES,       /*  22 = 16h               */
  PKE_VBATREG_PREPOLL,      /*  23 = 17h               */
  PKE_VBATREG_MSICON,       /*  24 = 18h               */
  PKE_VBATREG_WUPLEN        /*  25 = 19h               */
}
VbatRegSetMember_t;

/*-----------------------------------------------------------------------------------------------*/

#define PORTDIR_INPUT  0u                        // Port direction definitions (PxDIR registers)
#define PORTDIR_OUTPUT 1u

/*-----------------------------------------------------------------------------------------------*/

/**
 * Hardware initialization: watchdog and CPU clock.
 */
void hw_init_WD_CpuClock ( void );

/**
 *  Hardware initialization: port configuration and port functions
 */
void hw_init_ports( void );

/**
 * Read IDE (32 bit) from ULPEE to RAM variable g_u8arr_IDE[4].
 * ULPEE module 15 gets powered up if not yet activated.
 */
void hw_read_IDE( void );

/**
 *  Hardware initialization: VBAT brown-out detector
 */
void hw_init_VbatMonitor( void );

/**
 * Test VBAT supply (check VBAT brown-out flag, applies to point of calling this function).
 * Returns ERROR in case VBAT brown-out is indicated.
 */
inline error_t hw_test_Vbat( void );

/**
 * Test VBAT supply (check VBAT brown-out register, applies to period since last reset of
 * VBATBRNREG until calling this function).
 * Returns ERROR in case VBAT brown-out is indicated.
 */
inline error_t hw_test_Vbat_latched( void );

/**
 * Reset/clear the VBAT brown-out latch (register).
 * Writes VBATBRNREG to 0, which does not have any effect if VBATBRNFLAG is 1.
 */
inline void hw_reset_Vbat_latch( void );

/**
 * Test VDD supply (check VDD brown-out flag (VDDBRNFLAG), applies to point of calling this function).
 * Returns ERROR in case VDD brown-out is indicated.
 */
inline error_t hw_testVdd( void );

/**
 * Test VDD supply (check VDD brown-out register (VDDBRNREG), applies to period since last reset of
 * VDDBRNREG until calling this function).
 * Returns ERROR in case VDD brown-out is indicated.
 */
inline error_t hw_testVddLatched( void );

/**
 *  Enable hardware: VDDA regulator
 */
void hw_enableVddaRegulator( void );

/**
 *  Disable hardware: VDDA regulator
 */
inline void hw_disableVddaRegulator( void );

#ifdef PLATFORM_HAS_VDDA_BROWNOUT
/**
 * Test VDDA supply (check VDDA brown-out flag (VDDABRNFLAG), applies to point of calling this function).
 * Returns ERROR in case VDDA brown-out is indicated.
 */
inline error_t hw_testVdda( void );

/**
 * Test VDDA supply (check VDDA brown-out register (VDDABRNREG), applies to period since last reset of
 * VDDABRNREG until calling this function).
 * Returns ERROR in case VDDA brown-out is indicated.
 */
inline error_t hw_testVddaLatched( void );

/**
 * Clear the VDDA brown-out register (VDDABRNREG).
 * Sets VDDABRNREG = '0u', which does not have any effect if VDDABRNFLAG is set to '1u'.
 */
inline void hw_clearVddaLatched( void );
#endif

/**
 * Reset the 2 ms detection unit to avoid unintended activation of the LF passive mode during RSSI measurement.
 */
inline void hw_reset2msDetect( void );

/**
 * Initialization of registers in VBAT and VBATREG domains with values stored in ULPEE.
 * Note: only call for device initialization after VBAT POR.
 * On SRX, in case of errors, enforces VDD reset.
 *
 * @param[in]  b_useDemoAppConfig  If FALSE, use WUPA/WUP1 setting from ULPEE.
 *                                 If TRUE, use WUPA/WUP1 as required for demo application.
 * @param[in]  u16_WUPA_setting    If b_useDemoAppConfig == TRUE, pattern to be used in demo app
 *                                 as WUP1/WUPA (repeated to form a 32 bit long pattern).
 */
void hw_init_VBAT_VBATREG_registers_from_EEPROM( bool_t b_useDemoAppConfig,
                                                 uint16_t u16_WUPA_setting );

/**
 * Refresh of registers in VBAT and VBATREG domains, using values stored in ULPEE.
 * Called after RKE operation and every PKE command for robustness.
 * On SRX, in case of errors, enforces VDD reset.
 *
 * @param[in]  b_useDemoAppConfig  If FALSE, use WUPA/WUP1 setting from ULPEE.
 *                                 If TRUE, use WUPA/WUP1 as required for demo application.
 * @param[in]  u16_WUPA_setting    If b_useDemoAppConfig == TRUE, pattern to be used in demo app
 *                                 as WUP1/WUPA (repeated to form a 32 bit long pattern).
 */
void hw_refresh_VBAT_VBATREG_registers_from_EEPROM( bool_t b_useDemoAppConfig,
                                                    uint16_t u16_WUPA_setting );

/**
 * Set a LF wake-up pattern on SRX type, one of three, including the "legacy" Code Violation (CV)
 * as used in PCF795x, ACTIC-4G, TOKEN, TOKEN-PLUS etc.
 * Only the last <D_CV2LENGTH> bits of the CV pattern (called CV2) are included in the detection.
 * Writes all registers WUP?PATEVN[0..2] and WUP?PATODD[0..2] (12 in total).
 * If u8_WupIdLength is nonzero, enable bit (WUPCON.EN_WUPx, x=A..C) of given channel is set to 1,
 * while enable bits of other WUP channels are not changed.
 *
 * @param[in]  en_WupIdx   index of WUP channel, one of LF_WUPA, LF_WUPB, LF_WUPC
 * @param[in]  u32_WUPID  the WUP ID pattern, left-aligned (MSBit at pos. 31)
 * @param[in]  u8_WupIdLength  length in bits of the WUP ID pattern, between 1 and 32
 * @param[in]  u16_SFR_WUPPATCON  setting of this register for given WUP detector.
 *             Bitfields CV2LEN and WUPLEN are overwritten by this function.
 * @return  ERROR in case of invalid length parameter.
 * @see
 */
error_t hw_set_WUP_pattern( LfWupIndex_t  en_WupIdx,
                            uint32_t      u32_WUPID,
                            uint8_t       u8_WupIdLength,
                            uint16_t      u16_SFR_WUPPATCON );

/**
 * Monitor basic power registers and clock system status and indicate error if anything
 * is not as it should be for normal device operation.
 * Monitored flags and expected values are:
 * (all platforms) BATRGLEN==1, BATRGLRST==0,
 * (TOKEN-PLUS only) TOKEN32_COMPAT correct setting (includes ULPEE read),
 * (SRX only) LFA_BRNFLAG==0, LFA_DCDC_FAILREG==0, LFA_XO32K_FAILREG==0.
 *
 * @return ERROR in case of unexpected status.
 */
error_t hw_monitor_powerandclock( void );

/**
 * Power-off function.
 * Switches all ports to plain input mode,
 * switches CPU clock to AUXCLK 250 kHz,
 * waits for port states to settle,
 * performs VDD reset (power management gets reset, controller re-starts
 * in case of wake-up event).
 */
void hw_power_off( void ) property(never_returns);

/**
 * LF NMI (Non Maskable Interrupt) handler.
 * Must be invoked from EROM vector table, vector #3.
 */
void LF_NMI( void ) property(isr);

/**
 * CX NMI (Core Exceptions Non Maskable Interrupt) handler.
 * Must be invoked from EROM vector table, vector #7.
 * Increments a counter in LSByte of page EEPAGE_CX_DETECT and places
 * the status byte CXSW and the PC value CXPC in byte 1 and 2..3.
 * Since this function is expected to run only after some kind of "crash", it seems recommendable
 * to reset the VBAT domain and hence force the device to wake-up and re-initialize.
 */
void CX_NMI( void ) property(never_returns);

/**
 * Get the button code by debouncing, for all buttons on board (up to four).
 * A '1' bit indicates button pressed. Bit 0 represents button S1,
 * bit 1 button S2, bit 2 button S3, bit 3 button S4.
 * Button port numbers are determined by macros named 'HW_BUTTON_SWx_PORTNR' with x=1..4.
 */
uint8_t hw_get_button_code( void );

/**
 * Extract from the button scan results array the bits corresponding to the port numbers
 * given by macros HW_BUTTON_SWx_PORTNR (x=1..4).
 */
uint8_t hw_button_scan_to_code( uint8_t * u8arr_buttoncodes );

/**
 *  Display relevant VBAT(REG) register contents to terminal.
 */
void hw_display_VBAT_VBATREG_registers( void );

/**
 * Enable separate configuration setting for motion sensor port (P21_MD).
 *
 * @param[in]  b_enable_override if TRUE, override P21C setting in ULPEE with 2nd parameter.
 * @param[in]  en_port_config  P21C setting to use if 1st parameter is TRUE.
 */
void hw_set_sensor_port_config( bool_t b_enable_override, PortPullWupConfig_t en_port_config );


/*-----------------------------------------------------------------------------------------------*/

/* Inline functions */

inline error_t hw_test_Vbat( void )
{
  if ( 0U == PCON1.bits.VBATBRNFLAG ) {
    return SUCCESS;
  }
  else {
    return ERROR;
  }
}

inline error_t hw_test_Vbat_latched( void )
{
  if ( 0U == PCON2.bits.VBATBRNREG )  {
    return SUCCESS;
  }
  else {
    return ERROR;
  }
}

inline void hw_reset_Vbat_latch( void )
{
  PCON2.bits.VBATBRNREG = 0u;
}

inline error_t hw_testVdd( void )
{
  if ( PCON1.bits.VDDBRNFLAG == 0u ) {
    return SUCCESS;
  }
  else {
    return ERROR;
  }
}

inline error_t hw_testVddLatched( void )
{
  if ( PCON2.bits.VDDBRNREG == 0u )  {
    return SUCCESS;
  }
  else {
    return ERROR;
  }
}

inline void hw_disableVddaRegulator( void )
{
	// Disable VDDA regulator, with VDDARGLEN = 0u, VDDARST will be set automatically.
	PCON0.bits.VDDARGLEN = 0u;
}

#ifdef PLATFORM_HAS_VDDA_BROWNOUT

inline error_t hw_testVdda( void )
{
  if ( PCON1.bits.VDDABRNFLAG == 0u ) {
    return SUCCESS;
  }
  else {
    return ERROR;
  }
}

inline error_t hw_testVddaLatched( void )
{
  if ( PCON2.bits.VDDABRNREG == 0u )  {
    return SUCCESS;
  }
  else {
    return ERROR;
  }
}

inline void hw_clearVddaLatched( void )
{
  PCON2.bits.VDDABRNREG = 0u;
}
#endif

inline void hw_reset2msDetect( void )
{
  PCON2.bits.R2MSDET = 1u;
}

#endif

/* eof */
