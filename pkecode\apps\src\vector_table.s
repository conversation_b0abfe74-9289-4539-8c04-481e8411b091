
/*
 * These symbols may be defined as external, regardless of 
 * their existence in another compilation module.
 * (No need to use compile switches.)
 */ 
.undef global text _main
.undef global text void_LF_NMI
.undef global text void_HtUserCommand
//.undef global text void_CX_NMI

.undef global data PCON0

// Use the same symbol as in default vector table (ncf2960_vector_table_default.s in libncf29xx.a).
// Since this module is part of the project, it will be linked instead of the one from the library file.

.text global 0 _ncf29A1_vector_table

  // vector 0, address 0000h: battery boot vector
  BRA.a _main

  // vector 1, address 0002h: LF - Warmboot
  OR.b PCON0,#0x80  // VDDRST := 1
  
  // vector 2, address 0004h: unused/reserved
  RETI
  NOP

  // vector 3, address 0006h: LF - NMI
  BRA.a  void_LF_NMI

  // vector 4, address 0008h: unused/reserved
  RETI
  NOP

  // vector 5, address 000Ah: HT-Pro/-3/-AES User Command Handler
  BRA.a void_HtUserCommand

  // vector 6, address 000Ch: unused/reserved
  RETI
  NOP

  // vector 7, address 000Eh: MRK3 exception handler
  //BRA.a void_CX_NMI
  RETI
  NOP

/* eof */
