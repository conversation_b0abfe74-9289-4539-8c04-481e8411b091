
// File generated by noodle version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:23 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/timer.c mrk3

toolrelease _19R3;


// additional
prop gp_offset_type = ( __sint );

// inline assembly void nop()
Fvoid_nop : user_defined, volatile, assembly {
    fnm : "nop" 'inline assembly void nop()'; 
    flc : ( R46[0] R46[1] R46[2] RbH[0] RbH[1] RbH[2] RbH[3] RbL[0] RbL[1] RbL[2] RbL[3] );
    llv : 0 0 0 0 0 ;
}

// void phcaiKEyLLGenFunc_timer0_delay_us(uint16_t)
Fvoid_phcaiKEyLLGenFunc_timer0_delay_us___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_timer0_delay_us" 'void phcaiKEyLLGenFunc_timer0_delay_us(uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
}

// void phcaiKEyLLGenFunc_timer0_delay_ms(uint16_t)
Fvoid_phcaiKEyLLGenFunc_timer0_delay_ms___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_timer0_delay_ms" 'void phcaiKEyLLGenFunc_timer0_delay_ms(uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
}

// void timer_WaitNops(uint16_t)
Fvoid_timer_WaitNops___ushort : user_defined, called {
    fnm : "timer_WaitNops" 'void timer_WaitNops(uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
    frm : ( y=2 l=2 );
    llv : 0 0 0 0 0 ;
}

// void timer_delay_us(uint16_t)
Fvoid_timer_delay_us___ushort : user_defined, called {
    fnm : "timer_delay_us" 'void timer_delay_us(uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
    frm : ( y=2 l=2 );
    llv : 0 0 * * 0 ;
}

// void timer_delay_ms(uint16_t)
Fvoid_timer_delay_ms___ushort : user_defined, called {
    fnm : "timer_delay_ms" 'void timer_delay_ms(uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
    frm : ( y=2 l=2 );
    llv : 0 0 * * 0 ;
}

// void timer_DelayTBit()
Fvoid_timer_DelayTBit : user_defined, called {
    fnm : "timer_DelayTBit" 'void timer_DelayTBit()'; 
    frm : ( y=2 l=2 );
    llv : 0 0 0 0 0 ;
}

// void timer_DelayTBit_half()
Fvoid_timer_DelayTBit_half : user_defined, called {
    fnm : "timer_DelayTBit_half" 'void timer_DelayTBit_half()'; 
    frm : ( y=2 l=2 );
    llv : 0 0 0 0 0 ;
}

// void cycle_delay_ms(uint16_t)
Fvoid_cycle_delay_ms___ushort : user_defined, called {
    fnm : "cycle_delay_ms" 'void cycle_delay_ms(uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
    frm : ( y=2 l=2 );
    llv : 0 0 0 0 0 ;
}

