/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: ncf29AA.c 22437 2019-10-02 15:23:00Z dep10330 $
  $Revision: 22437 $
*/

#include "shared_sfrdef/platform_srx.h"

/**
 * @file
 * Definition of the special function registers (SFR) of the TOKEN-SRX
 * (NCF29AA/29AB) (B0 or later) platform.
 */

/**
 * @addtogroup tokensrxhw
 * @{
 */

/*
  CHANGE LOG
  2017-11-08 (MMr): started adapting from TOKEN-SRX v0a. Numerous changes.
  2017-11-13 (MMr):
  - renamed RSSICON to RSSICON0 (may use alias name RSSICON to point to RSSICON0,
    but RSSI is quite different from all predecessor types anyway)
  - added DCDCTST1, DCDCTST2
  2017-11-24 (MMr):
  - fixed typo in SFR_LFASTATUS_t
  2018-02-20 (MMr): merged changes from ncf215A.h:
  - added PRETRIMBG register
  - added bitfields in SFR_DCDCCON0_t, SFR_LFAEN0_t
  - renamed DCDCTST1,2 in DCDCCON6,7 (acc. to updated SFR sheet)
  - renamed PRETRIMBG in DCDCCON8
  - fixed size of WUPLEN in SFR_WUPPATCON_t
  2018-07-02 (MMr):
  - Changes acc. to UM Rev. 0.1 - 30 May 2018
    * in RSSICON0, rename bit RSSI_LOWNOISE_EN to RSSI_GAIN_CTRL - REJECT!
    * in WUPCON, change bit 0 to RFU
    * in WUPCON, merge bits 11..8 into SCM_CFG (needs code adaptation)
    * in LFASTATUS, make bits 3..0 RFU
    * add PFCON3 (ro)
    * add BISTCON (ro)
    * add I3DCON bitfields
    * LFAEN0: bit 1 changed to RDT R/W0
    * LFAEN1: bit 5 renamed to LFA_EN_PLL_DCDC_CLK
    *         bit 6 renamed to LFA_EN_PLL_CLK_TIMER
    * LFAEN2..4: added bit fields
    * LFACON1..5: removed bit fields
    * LFACON6: removed undocumented bit fields
    * DCDCCON1..7: removed bit fields
    * RSSICON2,3,4,5: removed bit fields
    * RSSICON7: renamed bit 3 to RSSI_RAW_VALID
  2018-08-23 (MMr):
  - removed P34,P35 related bit fields in P3* registers.
  2018-09-10 (MMr):
  - PAYRXCON: aligned bit fields with UM rev 0.1.
  2018-11-15 (MMr):
  - LFACON3, DCDCCON2 removed since not im DS/UM.
  - LFACON7, LFACON8: added
  2018-12-03 (MMr):
  - RSSICON6 removed since not im DS/UM.
  - RSSICON5: bit 3 changed to RDT.
  2018-12-13 (MMr):
  - adapted to UM rev 1.0:
    removed PRESTAT.PMCV, RSSICON0.RSSI_LOWNOISE_EN.
    renamed RSSI_OSC_TRIMSTART to RSSI_OSC_CALSTART.
  2019-03-04 (MMr):
  - renamed bit PAYRXCON.WUPRESET to RESTARTONWUP acc. to DS.
  2019-03-18 (MMr):
  - added bitfields for ULPCON0 and ULPCON1.
  2019-04-09 (MMr):
  - fixed incorrect bitfields in ASKRMP.

 */


volatile SFR_CXPC_t          chess_storage(DM9:0x0014)  CXPC;
volatile SFR_CXSW_t          chess_storage(DM9:0x0016)  CXSW;
volatile SFR_P1INS_t         chess_storage(DM9:0x0018)  P1INS;
volatile SFR_P1OUT_t         chess_storage(DM9:0x0019)  P1OUT;
volatile SFR_P1DIR_t         chess_storage(DM9:0x001A)  P1DIR;
volatile SFR_P1INTDIS_t      chess_storage(DM9:0x001B)  P1INTDIS;
volatile SFR_P2INS_t         chess_storage(DM9:0x001C)  P2INS;
volatile SFR_P2OUT_t         chess_storage(DM9:0x001D)  P2OUT;
volatile SFR_P2DIR_t         chess_storage(DM9:0x001E)  P2DIR;
volatile SFR_P2INTDIS_t      chess_storage(DM9:0x001F)  P2INTDIS;
volatile SFR_P3INS_t         chess_storage(DM9:0x0020)  P3INS;
volatile SFR_P3OUT_t         chess_storage(DM9:0x0021)  P3OUT;
volatile SFR_P3DIR_t         chess_storage(DM9:0x0022)  P3DIR;
volatile SFR_P3INTDIS_t      chess_storage(DM9:0x0023)  P3INTDIS;
volatile SFR_IIUCON0_t       chess_storage(DM9:0x0024)  IIUCON0;
volatile SFR_IIUSTAT_t       chess_storage(DM9:0x0025)  IIUSTAT;
volatile SFR_IIUCON1_t       chess_storage(DM9:0x0026)  IIUCON1;
volatile SFR_IIUDAT_t        chess_storage(DM9:0x0027)  IIUDAT;
volatile SFR_IIUSTATE_t      chess_storage(DM9:0x0028)  IIUSTATE;
volatile SFR_IIUCON2_t       chess_storage(DM9:0x0029)  IIUCON2;
volatile SFR_HTCON_t         chess_storage(DM9:0x002A)  HTCON;
volatile SFR_I3DCON_t        chess_storage(DM9:0x0031)  I3DCON;
volatile SFR_AESDAT_t        chess_storage(DM9:0x0032)  AESDAT;
volatile SFR_AESCON_t        chess_storage(DM9:0x0034)  AESCON;
volatile SFR_CRCDAT_t        chess_storage(DM9:0x0035)  CRCDAT;
volatile SFR_CRC8DIN_t       chess_storage(DM9:0x0036)  CRC8DIN;
volatile SFR_GCRCCON0_t      chess_storage(DM9:0x0037)  GCRCCON0;
volatile SFR_GCRCPOLY_t      chess_storage(DM9:0x0038)  GCRCPOLY;
volatile SFR_GCRCDAT_t       chess_storage(DM9:0x003A)  GCRCDAT;
volatile SFR_GCRCDIN_t       chess_storage(DM9:0x003C)  GCRCDIN;
volatile SFR_CPUMCCCNT0_t    chess_storage(DM9:0x004C)  CPUMCCCNT0;
volatile SFR_CPUMCCCNT1_t    chess_storage(DM9:0x004E)  CPUMCCCNT1;
volatile SFR_CPUMCCCON_t     chess_storage(DM9:0x0050)  CPUMCCCON;
volatile SFR_WDCON_t         chess_storage(DM9:0x0051)  WDCON;
volatile SFR_CLKCON0_t       chess_storage(DM9:0x0052)  CLKCON0;
volatile SFR_CLKCON1_t       chess_storage(DM9:0x0053)  CLKCON1;
volatile SFR_CLKCON2_t       chess_storage(DM9:0x0054)  CLKCON2;
volatile SFR_CLKCON3_t       chess_storage(DM9:0x0055)  CLKCON3;
volatile SFR_CLKCON4_t       chess_storage(DM9:0x0057)  CLKCON4;
volatile SFR_ADCDAT_t        chess_storage(DM9:0x005A)  ADCDAT;
volatile SFR_ADCSUM_t        chess_storage(DM9:0x005C)  ADCSUM;
volatile SFR_ADCCON_t        chess_storage(DM9:0x005E)  ADCCON;
volatile SFR_RSSICON0_t      chess_storage(DM9:0x0060)  RSSICON0;   // changed
volatile SFR_ULPADDR_t       chess_storage(DM9:0x0064)  ULPADDR;
volatile SFR_ULPSEL_t        chess_storage(DM9:0x0066)  ULPSEL;
volatile SFR_ULPCON0_t       chess_storage(DM9:0x0068)  ULPCON0;
volatile SFR_ULPDAT_t        chess_storage(DM9:0x0069)  ULPDAT;
volatile SFR_ULPCON1_t       chess_storage(DM9:0x006D)  ULPCON1;
volatile SFR_T0CON0_t        chess_storage(DM9:0x006E)  T0CON0;
volatile SFR_T0CON1_t        chess_storage(DM9:0x006F)  T0CON1;
volatile SFR_T0REG_t         chess_storage(DM9:0x0070)  T0REG;
volatile SFR_T0RLD_t         chess_storage(DM9:0x0072)  T0RLD;
volatile SFR_T1CON0_t        chess_storage(DM9:0x0074)  T1CON0;
volatile SFR_T1CON1_t        chess_storage(DM9:0x0075)  T1CON1;
volatile SFR_T1CON2_t        chess_storage(DM9:0x0076)  T1CON2;
volatile SFR_T1REG_t         chess_storage(DM9:0x0078)  T1REG;
volatile SFR_T1CAP_t         chess_storage(DM9:0x007A)  T1CAP;
volatile SFR_T1CMP_t         chess_storage(DM9:0x007C)  T1CMP;
volatile SFR_T2CON0_t        chess_storage(DM9:0x007E)  T2CON0;
volatile SFR_T2CON1_t        chess_storage(DM9:0x007F)  T2CON1;
volatile SFR_T2REG_t         chess_storage(DM9:0x0080)  T2REG;
volatile SFR_T2RLD_t         chess_storage(DM9:0x0082)  T2RLD;
volatile SFR_RNGDAT_t        chess_storage(DM9:0x0084)  RNGDAT;
volatile SFR_RNGCON_t        chess_storage(DM9:0x0086)  RNGCON;
volatile SFR_INTCON_t        chess_storage(DM9:0x0087)  INTCON;
volatile SFR_INTFLAG0_t      chess_storage(DM9:0x0088)  INTFLAG0;
volatile SFR_INTFLAG1_t      chess_storage(DM9:0x0089)  INTFLAG1;
volatile SFR_INTFLAG2_t      chess_storage(DM9:0x008A)  INTFLAG2;
volatile SFR_INTEN0_t        chess_storage(DM9:0x008B)  INTEN0;
volatile SFR_INTEN1_t        chess_storage(DM9:0x008C)  INTEN1;
volatile SFR_INTEN2_t        chess_storage(DM9:0x008D)  INTEN2;
volatile SFR_SYSINTEN0_t     chess_storage(DM9:0x008E)  SYSINTEN0;
volatile SFR_SYSINTEN1_t     chess_storage(DM9:0x008F)  SYSINTEN1;
volatile SFR_INTSET0_t       chess_storage(DM9:0x0090)  INTSET0;
volatile SFR_INTSET1_t       chess_storage(DM9:0x0091)  INTSET1;
volatile SFR_INTSET2_t       chess_storage(DM9:0x0092)  INTSET2;
volatile SFR_INTCLR0_t       chess_storage(DM9:0x0093)  INTCLR0;
volatile SFR_INTCLR1_t       chess_storage(DM9:0x0094)  INTCLR1;
volatile SFR_INTCLR2_t       chess_storage(DM9:0x0095)  INTCLR2;
volatile SFR_INTVEC_t        chess_storage(DM9:0x0096)  INTVEC;
volatile SFR_LFSHCON_t       chess_storage(DM9:0x0098)  LFSHCON;
volatile SFR_PCON0_t         chess_storage(DM9:0x0099)  PCON0;
volatile SFR_PCON1_t         chess_storage(DM9:0x009A)  PCON1;
volatile SFR_PCON2_t         chess_storage(DM9:0x009B)  PCON2;
volatile SFR_PCON5_t         chess_storage(DM9:0x009E)  PCON5;
volatile SFR_BATSYS0_t       chess_storage(DM9:0x00A0)  BATSYS0;
volatile SFR_BATSYS1_t       chess_storage(DM9:0x00A1)  BATSYS1;      // changed
volatile SFR_PRESWUP0_t      chess_storage(DM9:0x00A2)  PRESWUP0;
volatile SFR_PRESWUP1_t      chess_storage(DM9:0x00A4)  PRESWUP1;
volatile SFR_PRESWUP2_t      chess_storage(DM9:0x00A6)  PRESWUP2;
volatile SFR_PRESWUP3_t      chess_storage(DM9:0x00A8)  PRESWUP3;
volatile SFR_PRESWUP4_t      chess_storage(DM9:0x00AA)  PRESWUP4;
volatile SFR_P1WRES_t        chess_storage(DM9:0x00AC)  P1WRES;
volatile SFR_P2WRES_t        chess_storage(DM9:0x00AD)  P2WRES;
volatile SFR_P3WRES_t        chess_storage(DM9:0x00AE)  P3WRES;
volatile SFR_LFTUNECH1ACT_t  chess_storage(DM9:0x00AF)  LFTUNECH1ACT;
volatile SFR_LFTUNECH2ACT_t  chess_storage(DM9:0x00B0)  LFTUNECH2ACT;
volatile SFR_LFTUNECH3ACT_t  chess_storage(DM9:0x00B1)  LFTUNECH3ACT;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B2)  USRBAT0;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B3)  USRBAT1;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B4)  USRBAT2;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B5)  USRBAT3;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B6)  USRBAT4;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B7)  USRBAT5;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B8)  USRBAT6;
volatile SFR_USRBATx_t       chess_storage(DM9:0x00B9)  USRBAT7;
volatile SFR_LFTUNECH1IMMO_t chess_storage(DM9:0x00BA)  LFTUNECH1IMMO;
volatile SFR_LFTUNECH2IMMO_t chess_storage(DM9:0x00BB)  LFTUNECH2IMMO;
volatile SFR_LFTUNECH3IMMO_t chess_storage(DM9:0x00BC)  LFTUNECH3IMMO;
volatile SFR_SPI0CON0_t      chess_storage(DM9:0x00C0)  SPI0CON0;
volatile SFR_SPI0CON1_t      chess_storage(DM9:0x00C1)  SPI0CON1;
volatile SFR_SPI0DAT_t       chess_storage(DM9:0x00C2)  SPI0DAT;
volatile SFR_SPI0STAT_t      chess_storage(DM9:0x00C3)  SPI0STAT;
volatile SFR_SPI1CON0_t      chess_storage(DM9:0x00C4)  SPI1CON0;
volatile SFR_SPI1CON1_t      chess_storage(DM9:0x00C5)  SPI1CON1;
volatile SFR_SPI1DAT_t       chess_storage(DM9:0x00C6)  SPI1DAT;
volatile SFR_SPI1STAT_t      chess_storage(DM9:0x00C7)  SPI1STAT;
volatile SFR_P1ALTF_t        chess_storage(DM9:0x00C8)  P1ALTF;
volatile SFR_P2ALTF_t        chess_storage(DM9:0x00CA)  P2ALTF;
volatile SFR_BITCNT_t        chess_storage(DM9:0x00CC)  BITCNT;
volatile SFR_BITSWAP_t       chess_storage(DM9:0x00CE)  BITSWAP;
volatile SFR_LEDCON_t        chess_storage(DM9:0x00CF)  LEDCON;
volatile SFR_INTFLAG3_t      chess_storage(DM9:0x00D0)  INTFLAG3;
volatile SFR_INTEN3_t        chess_storage(DM9:0x00D1)  INTEN3;
volatile SFR_INTSET3_t       chess_storage(DM9:0x00D2)  INTSET3;
volatile SFR_INTCLR3_t       chess_storage(DM9:0x00D3)  INTCLR3;
volatile SFR_TXPCON_t        chess_storage(DM9:0x00D4)  TXPCON;
volatile SFR_CLKRSTCON_t     chess_storage(DM9:0x00D5)  CLKRSTCON;
volatile SFR_VCOCALCON_t     chess_storage(DM9:0x00D6)  VCOCALCON;
volatile SFR_PLLCON_t        chess_storage(DM9:0x00D7)  PLLCON;
volatile SFR_TXDAT_t         chess_storage(DM9:0x00D8)  TXDAT;
volatile SFR_TXSPC_t         chess_storage(DM9:0x00DA)  TXSPC;
volatile SFR_ENCCON0_t       chess_storage(DM9:0x00DC)  ENCCON0;
volatile SFR_ENCCON1_t       chess_storage(DM9:0x00DE)  ENCCON1;
volatile SFR_FREQCON0_t      chess_storage(DM9:0x00E0)  FREQCON0;
volatile SFR_FREQCON1_t      chess_storage(DM9:0x00E2)  FREQCON1;
volatile SFR_BRGCON_t        chess_storage(DM9:0x00E4)  BRGCON;
volatile SFR_FSKCON_t        chess_storage(DM9:0x00E6)  FSKCON;
volatile SFR_FSKRMP_t        chess_storage(DM9:0x00E7)  FSKRMP;
volatile SFR_ASKCON_t        chess_storage(DM9:0x00E8)  ASKCON;
volatile SFR_ASKRMP_t        chess_storage(DM9:0x00EA)  ASKRMP;
volatile SFR_PACON_t         chess_storage(DM9:0x00EB)  PACON;
volatile SFR_PAPWR_t         chess_storage(DM9:0x00EC)  PAPWR;
volatile SFR_PACAPTRIM_t     chess_storage(DM9:0x00ED)  PACAPTRIM;
volatile SFR_PALIMIT_t       chess_storage(DM9:0x00EE)  PALIMIT;
volatile SFR_IDENT_t         chess_storage(DM9:0x00FE)  IDENT;
volatile SFR_LFAEN0_t        chess_storage(DM9:0x0100)  LFAEN0;        // changed
volatile SFR_LFAEN1_t        chess_storage(DM9:0x0101)  LFAEN1;        // changed
volatile SFR_LFAEN2_t        chess_storage(DM9:0x0102)  LFAEN2;
volatile SFR_LFAEN3_t        chess_storage(DM9:0x0103)  LFAEN3;
volatile SFR_LFAEN4_t        chess_storage(DM9:0x0104)  LFAEN4;
volatile SFR_LFACON0_t       chess_storage(DM9:0x0105)  LFACON0;       // changed
volatile SFR_LFACON1_t       chess_storage(DM9:0x0106)  LFACON1;       // changed
volatile SFR_LFACON2_t       chess_storage(DM9:0x0107)  LFACON2;
volatile SFR_LFACON4_t       chess_storage(DM9:0x0109)  LFACON4;
volatile SFR_LFACON5_t       chess_storage(DM9:0x010A)  LFACON5;
volatile SFR_LFACON6_t       chess_storage(DM9:0x010B)  LFACON6;       // changed
volatile SFR_LFACON7_t       chess_storage(DM9:0x0134)  LFACON7;
volatile SFR_LFACON8_t       chess_storage(DM9:0x013B)  LFACON8;
volatile SFR_LFASTATUS_t     chess_storage(DM9:0x010C)  LFASTATUS;     // changed
volatile SFR_LFASENSE_t      chess_storage(DM9:0x010D)  LFASENSE;      // NEW
volatile SFR_WUPCON_t        chess_storage(DM9:0x010E)  WUPCON;        // changed
volatile SFR_WUPPATEVN0_t    chess_storage(DM9:0x0110)  WUPPATEVN0;    // changed
volatile SFR_WUPPATEVN1_t    chess_storage(DM9:0x0112)  WUPPATEVN1;    // changed
volatile SFR_WUPPATEVN2_t    chess_storage(DM9:0x0114)  WUPPATEVN2;    // changed
volatile SFR_WUPPATODD0_t    chess_storage(DM9:0x0116)  WUPPATODD0;    // changed
volatile SFR_WUPPATODD1_t    chess_storage(DM9:0x0118)  WUPPATODD1;    // changed
volatile SFR_WUPPATODD2_t    chess_storage(DM9:0x011A)  WUPPATODD2;    // changed
volatile SFR_WUPPATCON_t     chess_storage(DM9:0x011C)  WUPPATCON;     // NEW
volatile SFR_PAYRXCON_t      chess_storage(DM9:0x011E)  PAYRXCON;      // changed
volatile SFR_PREDAT_t        chess_storage(DM9:0x0120)  PREDAT;
volatile SFR_RTCCON_t        chess_storage(DM9:0x0121)  RTCCON;
volatile SFR_PRECON2_t       chess_storage(DM9:0x0122)  PRECON2;       // changed
volatile SFR_PRECON3_t       chess_storage(DM9:0x0123)  PRECON3;
volatile SFR_PRECON4_t       chess_storage(DM9:0x0124)  PRECON4;
volatile SFR_PRECON5_t       chess_storage(DM9:0x0125)  PRECON5;
volatile SFR_PRESTAT_t       chess_storage(DM9:0x0126)  PRESTAT;
volatile SFR_RTCDAT_t        chess_storage(DM9:0x0128)  RTCDAT;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x012A)  USRBATRGL0;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x012B)  USRBATRGL1;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x012C)  USRBATRGL2;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x012D)  USRBATRGL3;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x012E)  USRBATRGL4;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x012F)  USRBATRGL5;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x0130)  USRBATRGL6;
volatile SFR_USRBATRGLx_t    chess_storage(DM9:0x0131)  USRBATRGL7;
volatile SFR_DCDCCON8_t      chess_storage(DM9:0x013E)  DCDCCON8;      // NEW
volatile SFR_MSICON0_t       chess_storage(DM9:0x0142)  MSICON0;
volatile SFR_MSICON1_t       chess_storage(DM9:0x0143)  MSICON1;
volatile SFR_MSISTAT0_t      chess_storage(DM9:0x0144)  MSISTAT0;
volatile SFR_MSISTAT1_t      chess_storage(DM9:0x0145)  MSISTAT1;
volatile SFR_MSICON2_t       chess_storage(DM9:0x0146)  MSICON2;       // changed
volatile SFR_POSTWUPCON_t    chess_storage(DM9:0x0147)  POSTWUPCON;
volatile SFR_POSTWUPCOMP_t   chess_storage(DM9:0x0148)  POSTWUPCOMP;
volatile SFR_DCDCCON0_t      chess_storage(DM9:0x014A)  DCDCCON0;      // NEW
volatile SFR_DCDCCON1_t      chess_storage(DM9:0x014B)  DCDCCON1;      // NEW
volatile SFR_DCDCCON3_t      chess_storage(DM9:0x014D)  DCDCCON3;      // NEW
volatile SFR_DCDCCON4_t      chess_storage(DM9:0x014E)  DCDCCON4;      // NEW
volatile SFR_DCDCCON6_t      chess_storage(DM9:0x0150)  DCDCCON6;      // NEW
volatile SFR_DCDCCON7_t      chess_storage(DM9:0x0151)  DCDCCON7;      // NEW
volatile SFR_RSSICON1_t      chess_storage(DM9:0x0152)  RSSICON1;      // NEW
volatile SFR_RSSICON2_t      chess_storage(DM9:0x0153)  RSSICON2;      // NEW
volatile SFR_RSSICON3_t      chess_storage(DM9:0x0154)  RSSICON3;      // NEW
volatile SFR_RSSICON4_t      chess_storage(DM9:0x0155)  RSSICON4;      // NEW
volatile SFR_RSSICON5_t      chess_storage(DM9:0x0156)  RSSICON5;      // NEW
volatile SFR_RSSIVAL_t       chess_storage(DM9:0x0158)  RSSIVAL;       // NEW
volatile SFR_RSSICON7_t      chess_storage(DM9:0x015A)  RSSICON7;      // NEW
volatile SFR_PFCON3_t        chess_storage(DM9:0x015F)  PFCON3;        // NEW
volatile SFR_BISTCON_t       chess_storage(DM9:0x016C)  BISTCON;       // NEW

/*@}*/

/* eof */


