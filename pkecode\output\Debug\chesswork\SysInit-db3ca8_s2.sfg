
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:30 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i SysInit-db3ca8 mrk3

[
 -154 : __adr_gaincfg typ=int16_ bnd=m adro=23
    0 : void_hw_refresh_VBAT_VBATREG_registers typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : u8_save_bits typ=int8_ val=0t0 bnd=a sz=1 algn=1 stl=DM tref=uint8_t_DM
   21 : Temp_Buf typ=int8_ val=1t0 bnd=a sz=4 algn=1 stl=DM tref=__A4__uchar_DM
   22 : pollingid typ=int8_ val=6t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   23 : gaincfg typ=int8_ val=8t0 bnd=a sz=1 algn=1 stl=DM tref=uint8_t_DM
   24 : swversion typ=int8_ val=10t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   25 : BATSYS0 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_BATSYS0_t_DM9
   26 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   27 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   28 : __extDM_SFR_BATSYS0_t typ=int8_ bnd=b stl=DM
   29 : g_b_InCriticalSection typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=bool_t_DM9
   30 : PRESWUP0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESWUP0_t_DM9
   31 : __extDM_SFR_PRESWUP0_t typ=int8_ bnd=b stl=DM
   32 : PRESWUP1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESWUP1_t_DM9
   33 : __extDM_SFR_PRESWUP1_t typ=int8_ bnd=b stl=DM
   34 : PRESWUP2 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESWUP2_t_DM9
   35 : __extDM_SFR_word typ=int8_ bnd=b stl=DM
   36 : LFTUNEVBAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_LFTUNEVBAT_t_DM9
   37 : __extDM_SFR_LFTUNEVBAT_t typ=int8_ bnd=b stl=DM
   38 : PRECON2 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON2_t_DM9
   39 : __extDM_SFR_PRECON2_t typ=int8_ bnd=b stl=DM
   40 : PRECON3 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON3_t_DM9
   41 : __extDM_SFR_PRECON3_t typ=int8_ bnd=b stl=DM
   42 : PRECON4 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON4_t_DM9
   43 : __extDM_SFR_PRECON4_t typ=int8_ bnd=b stl=DM
   44 : PRECON5 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON5_t_DM9
   45 : __extDM_SFR_PRECON5_t typ=int8_ bnd=b stl=DM
   46 : PRECON6 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON6_t_DM9
   47 : __extDM_SFR_PRECON6_t typ=int8_ bnd=b stl=DM
   48 : PRECON7 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON7_t_DM9
   49 : __extDM_SFR_PRECON7_t typ=int8_ bnd=b stl=DM
   50 : PRECON8 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON8_t_DM9
   51 : __extDM_SFR_PRECON8_t typ=int8_ bnd=b stl=DM
   52 : PRECON9 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON9_t_DM9
   53 : __extDM_SFR_PRECON9_t typ=int8_ bnd=b stl=DM
   54 : PRECON10 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON10_t_DM9
   55 : __extDM_SFR_PRECON10_t typ=int8_ bnd=b stl=DM
   56 : PRECON11 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PRECON11_t_DM9
   57 : __extDM_SFR_PRECON11_t typ=int8_ bnd=b stl=DM
   58 : PREPD typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PREPD_t_DM9
   59 : __extDM_SFR_byte typ=int8_ bnd=b stl=DM
   60 : PRET typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRET_t_DM9
   61 : PRE3T typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRE3T_t_DM9
   62 : WUP1W0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP1W0_t_DM9
   63 : WUP1W1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP1W1_t_DM9
   64 : WUP2W0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP2W0_t_DM9
   65 : KEY_PID typ=int8_ bnd=e algn=1 stl=DM
   66 : WUP2W1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP2W1_t_DM9
   67 : KEY_ID typ=int8_ bnd=e algn=1 stl=DM
   68 : WUP3W0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_WUP3W0_t_DM9
   69 : PRESTAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_PRESTAT_t_DM9
   70 : __extDM_SFR_PRESTAT_t typ=int8_ bnd=b stl=DM
   71 : g_b_NMI_occurred typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=bool_t_DM9
   72 : PCON0 typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PCON0_t_DM9
   73 : __extDM_SFR_PCON0_t typ=int8_ bnd=b stl=DM
   74 : __extPM_void typ=uint16_ bnd=b stl=PM
   75 : __extDM_void typ=int8_ bnd=b stl=DM
   76 : __extULP_void typ=uint32_ bnd=b stl=ULP
   79 : __ptr_BATSYS0 typ=int16_ val=0a bnd=m adro=25
   81 : __ptr_g_b_InCriticalSection typ=int16_ val=0a bnd=m adro=29
   83 : __ptr_PRESWUP0 typ=int16_ val=0a bnd=m adro=30
   85 : __ptr_PRESWUP1 typ=int16_ val=0a bnd=m adro=32
   87 : __ptr_PRESWUP2 typ=int16_ val=0a bnd=m adro=34
   89 : __ptr_LFTUNEVBAT typ=int16_ val=0a bnd=m adro=36
   91 : __ptr_PRECON2 typ=int16_ val=0a bnd=m adro=38
   93 : __ptr_PRECON3 typ=int16_ val=0a bnd=m adro=40
   95 : __ptr_PRECON4 typ=int16_ val=0a bnd=m adro=42
   97 : __ptr_PRECON5 typ=int16_ val=0a bnd=m adro=44
   99 : __ptr_PRECON6 typ=int16_ val=0a bnd=m adro=46
  101 : __ptr_PRECON7 typ=int16_ val=0a bnd=m adro=48
  103 : __ptr_PRECON8 typ=int16_ val=0a bnd=m adro=50
  105 : __ptr_PRECON9 typ=int16_ val=0a bnd=m adro=52
  107 : __ptr_PRECON10 typ=int16_ val=0a bnd=m adro=54
  109 : __ptr_PRECON11 typ=int16_ val=0a bnd=m adro=56
  111 : __ptr_PREPD typ=int16_ val=0a bnd=m adro=58
  113 : __ptr_PRET typ=int16_ val=0a bnd=m adro=60
  115 : __ptr_PRE3T typ=int16_ val=0a bnd=m adro=61
  117 : __ptr_WUP1W0 typ=int16_ val=0a bnd=m adro=62
  119 : __ptr_WUP1W1 typ=int16_ val=0a bnd=m adro=63
  121 : __ptr_WUP2W0 typ=int16_ val=0a bnd=m adro=64
  123 : __ptr_KEY_PID typ=int16_ val=0a bnd=m adro=65
  125 : __ptr_WUP2W1 typ=int16_ val=0a bnd=m adro=66
  127 : __ptr_KEY_ID typ=int16_ val=0a bnd=m adro=67
  129 : __ptr_WUP3W0 typ=int16_ val=0a bnd=m adro=68
  131 : __ptr_PRESTAT typ=int16_ val=0a bnd=m adro=69
  133 : __ptr_g_b_NMI_occurred typ=int16_ val=0a bnd=m adro=71
  135 : __ptr_PCON0 typ=int16_ val=0a bnd=m adro=72
  140 : __ct_0t0 typ=int16_ val=0t0 bnd=m
  144 : __ct_1t0 typ=int16_ val=1t0 bnd=m
  146 : __adr_Temp_Buf typ=int16_ bnd=m adro=21
  148 : __ct_6t0 typ=int16_ val=6t0 bnd=m
  152 : __ct_8t0 typ=int16_ val=8t0 bnd=m
  154 : __adr_gaincfg typ=int16_ bnd=m adro=23
  156 : __ct_10t0 typ=int16_ val=10t0 bnd=m
  204 : void_Power_Off typ=int16_ val=0r bnd=m
  208 : __ct_1 typ=uint8_ val=1f bnd=m
  210 : void_phcaiKEyLLGenFunc_CS_SetBatPORFlag_bool_t typ=int16_ val=0r bnd=m
  212 : __ct_17480 typ=uint16_1_32768_ val=17480f bnd=m
  217 : __ct_50244 typ=int16_ val=-15292f bnd=m
  222 : __ct_2184 typ=uint16_1_32768_ val=2184f bnd=m
  227 : __ct_273 typ=uint16_1_32768_ val=273f bnd=m
  235 : __fch_PRECON2 typ=int8_ bnd=m
  243 : __fch_u8_save_bits typ=int8_ bnd=m
  268 : __fch_PRECON6 typ=int8_ bnd=m
  276 : __fch_u8_save_bits typ=int8_ bnd=m
  303 : __ct_209 typ=uint16_1_32768_ val=209f bnd=m
  305 : __uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort typ=int16_ val=0r bnd=m
  307 : __tmp typ=int8_ bnd=m
  328 : __ct_7440 typ=uint16_1_32768_ val=7440f bnd=m
  333 : __ct_24632 typ=uint16_1_32768_ val=24632f bnd=m
  338 : __ct_17503 typ=uint16_1_32768_ val=17503f bnd=m
  343 : __ct_8755 typ=uint16_1_32768_ val=8755f bnd=m
  351 : __fch_KEY_PID typ=int8_ bnd=m
  372 : __fch_KEY_PID typ=int8_ bnd=m
  382 : __fch_KEY_PID typ=int8_ bnd=m
  392 : __fch_KEY_PID typ=int8_ bnd=m
  402 : __fch_KEY_ID typ=int8_ bnd=m
  423 : __fch_KEY_ID typ=int8_ bnd=m
  433 : __fch_KEY_ID typ=int8_ bnd=m
  443 : __fch_KEY_ID typ=int8_ bnd=m
  450 : __ct_26 typ=uint16_1_32768_ val=26f bnd=m
  452 : __ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort typ=int16_ val=0r bnd=m
  454 : __tmp typ=int16_ bnd=m
  472 : __fch_pollingid typ=int16_ bnd=m
  550 : __tmp typ=bool bnd=m
  553 : __ct_0 typ=uint8_ val=0f bnd=m
  557 : __ct_14 typ=uint16_1_32768_ val=14f bnd=m
  561 : __tmp typ=int16_ bnd=m
  568 : __ct_3 typ=uint16_1_32768_ val=3f bnd=m
  570 : void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort typ=int16_ val=0r bnd=m
  572 : __ct_4 typ=uint16_1_32768_ val=4f bnd=m
  574 : void_timer_delay_ms___ushort typ=int16_ val=0r bnd=m
  588 : __ct_3 typ=uint16_1_32768_ val=3f bnd=m
  590 : error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort typ=int16_ val=0r bnd=m
  592 : __tmp typ=int8_ bnd=m
  593 : __ct_4 typ=uint16_1_32768_ val=4f bnd=m
  622 : __ct_12s0 typ=int16_ val=12s0 bnd=m
  630 : __tmp typ=int8_ bnd=m
  659 : __tmp typ=int8_ bnd=m
  662 : __tmp typ=int8_ bnd=m
  667 : __tmp typ=int8_ bnd=m
  670 : __tmp typ=int8_ bnd=m
  674 : __tmp typ=int8_ bnd=m
  676 : __tmp typ=int8_ bnd=m
  678 : __tmp typ=int8_ bnd=m
  681 : __tmp typ=int8_ bnd=m
  685 : __tmp typ=int8_ bnd=m
  686 : __fch_pollingid typ=int8_ bnd=m
  697 : __ct_12s0 typ=int16_ val=12s0 bnd=m
  727 : __ptr_KEY_PID__a3 typ=int16_ val=3a bnd=m adro=65
  728 : __ptr_KEY_ID__a3 typ=int16_ val=3a bnd=m adro=67
  729 : __ptr_WUP1W0__a1 typ=int16_ val=1a bnd=m adro=62
  730 : __ptr_WUP1W1__a1 typ=int16_ val=1a bnd=m adro=63
  731 : __ptr_WUP2W0__a1 typ=int16_ val=1a bnd=m adro=64
  732 : __ptr_KEY_PID__a2 typ=int16_ val=2a bnd=m adro=65
  733 : __ptr_KEY_PID__a1 typ=int16_ val=1a bnd=m adro=65
  734 : __ptr_WUP2W1__a1 typ=int16_ val=1a bnd=m adro=66
  735 : __ptr_KEY_ID__a2 typ=int16_ val=2a bnd=m adro=67
  736 : __ptr_KEY_ID__a1 typ=int16_ val=1a bnd=m adro=67
  737 : __ptr_WUP3W0__a1 typ=int16_ val=1a bnd=m adro=68
  738 : __ct_2t0 typ=int16_ val=2t0 bnd=m
  740 : __ct_3t0 typ=int16_ val=3t0 bnd=m
  742 : __ct_4t0 typ=int16_ val=4t0 bnd=m
  888 : __rt typ=int8_ bnd=m tref=__uchar__
  912 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  918 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  936 : __false typ=bool val=0f bnd=m
  937 : __either typ=bool bnd=m
  938 : __trgt typ=rel8_ val=4j bnd=m
  939 : __trgt typ=rel8_ val=4j bnd=m
  940 : __trgt typ=rel8_ val=3j bnd=m
  941 : __trgt typ=rel8_ val=6j bnd=m
  942 : __trgt typ=rel8_ val=3j bnd=m
  943 : __trgt typ=rel8_ val=19j bnd=m
  945 : __trgt typ=rel8_ val=4j bnd=m
  947 : __seff typ=any bnd=m
  948 : __seff typ=any bnd=m
  949 : __seff typ=any bnd=m
  950 : __seff typ=any bnd=m
  951 : __seff typ=any bnd=m
  952 : __seff typ=any bnd=m
  953 : __seff typ=any bnd=m
  954 : __seff typ=any bnd=m
  955 : __seff typ=any bnd=m
  956 : __seff typ=any bnd=m
  957 : __seff typ=any bnd=m
  958 : __seff typ=any bnd=m
  959 : __seff typ=any bnd=m
  960 : __seff typ=any bnd=m
  961 : __seff typ=any bnd=m
  962 : __seff typ=any bnd=m
  963 : __seff typ=any bnd=m
  964 : __seff typ=any bnd=m
  965 : __seff typ=any bnd=m
  966 : __seff typ=any bnd=m
  967 : __seff typ=any bnd=m
  968 : __seff typ=any bnd=m
  969 : __seff typ=any bnd=m
  970 : __seff typ=any bnd=m
  971 : __seff typ=any bnd=m
  972 : __seff typ=any bnd=m
  973 : __seff typ=any bnd=m
  974 : __seff typ=any bnd=m
  975 : __seff typ=any bnd=m
  976 : __seff typ=any bnd=m
  977 : __seff typ=any bnd=m
  984 : __seff typ=any bnd=m
  985 : __seff typ=any bnd=m
  986 : __seff typ=any bnd=m
  988 : __side_effect typ=any bnd=m
]
Fvoid_hw_refresh_VBAT_VBATREG_registers {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (u8_save_bits.19 var=20) source ()  <30>;
    (Temp_Buf.20 var=21) source ()  <31>;
    (pollingid.21 var=22) source ()  <32>;
    (gaincfg.22 var=23) source ()  <33>;
    (swversion.23 var=24) source ()  <34>;
    (BATSYS0.24 var=25) source ()  <35>;
    (__extDM_int8_.25 var=26) source ()  <36>;
    (__extDM_int16_.26 var=27) source ()  <37>;
    (__extDM_SFR_BATSYS0_t.27 var=28) source ()  <38>;
    (g_b_InCriticalSection.28 var=29) source ()  <39>;
    (PRESWUP0.29 var=30) source ()  <40>;
    (__extDM_SFR_PRESWUP0_t.30 var=31) source ()  <41>;
    (PRESWUP1.31 var=32) source ()  <42>;
    (__extDM_SFR_PRESWUP1_t.32 var=33) source ()  <43>;
    (PRESWUP2.33 var=34) source ()  <44>;
    (__extDM_SFR_word.34 var=35) source ()  <45>;
    (LFTUNEVBAT.35 var=36) source ()  <46>;
    (__extDM_SFR_LFTUNEVBAT_t.36 var=37) source ()  <47>;
    (PRECON2.37 var=38) source ()  <48>;
    (__extDM_SFR_PRECON2_t.38 var=39) source ()  <49>;
    (PRECON3.39 var=40) source ()  <50>;
    (__extDM_SFR_PRECON3_t.40 var=41) source ()  <51>;
    (PRECON4.41 var=42) source ()  <52>;
    (__extDM_SFR_PRECON4_t.42 var=43) source ()  <53>;
    (PRECON5.43 var=44) source ()  <54>;
    (__extDM_SFR_PRECON5_t.44 var=45) source ()  <55>;
    (PRECON6.45 var=46) source ()  <56>;
    (__extDM_SFR_PRECON6_t.46 var=47) source ()  <57>;
    (PRECON7.47 var=48) source ()  <58>;
    (__extDM_SFR_PRECON7_t.48 var=49) source ()  <59>;
    (PRECON8.49 var=50) source ()  <60>;
    (__extDM_SFR_PRECON8_t.50 var=51) source ()  <61>;
    (PRECON9.51 var=52) source ()  <62>;
    (__extDM_SFR_PRECON9_t.52 var=53) source ()  <63>;
    (PRECON10.53 var=54) source ()  <64>;
    (__extDM_SFR_PRECON10_t.54 var=55) source ()  <65>;
    (PRECON11.55 var=56) source ()  <66>;
    (__extDM_SFR_PRECON11_t.56 var=57) source ()  <67>;
    (PREPD.57 var=58) source ()  <68>;
    (__extDM_SFR_byte.58 var=59) source ()  <69>;
    (PRET.59 var=60) source ()  <70>;
    (PRE3T.60 var=61) source ()  <71>;
    (WUP1W0.61 var=62) source ()  <72>;
    (WUP1W1.62 var=63) source ()  <73>;
    (WUP2W0.63 var=64) source ()  <74>;
    (KEY_PID.64 var=65) source ()  <75>;
    (WUP2W1.65 var=66) source ()  <76>;
    (KEY_ID.66 var=67) source ()  <77>;
    (WUP3W0.67 var=68) source ()  <78>;
    (PRESTAT.68 var=69) source ()  <79>;
    (__extDM_SFR_PRESTAT_t.69 var=70) source ()  <80>;
    (g_b_NMI_occurred.70 var=71) source ()  <81>;
    (PCON0.71 var=72) source ()  <82>;
    (__extDM_SFR_PCON0_t.72 var=73) source ()  <83>;
    (__extPM_void.73 var=74) source ()  <84>;
    (__extDM_void.74 var=75) source ()  <85>;
    (__extULP_void.75 var=76) source ()  <86>;
    (__ct_0t0.3333 var=140) const_inp ()  <3317>;
    (__ct_1t0.3334 var=144) const_inp ()  <3318>;
    (__ct_6t0.3335 var=148) const_inp ()  <3319>;
    (__ct_8t0.3336 var=152) const_inp ()  <3320>;
    (__ct_12s0.3346 var=697) const_inp ()  <3330>;
    <577> {
      (__sp.140 var=19 __seff.3606 var=984 stl=c_flag_w __seff.3607 var=985 stl=nz_flag_w __seff.3608 var=986 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_12s0.3346 __sp.18 __sp.18)  <3594>;
      (__seff.3719 var=985 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3607)  <3939>;
      (__seff.3744 var=984 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.3606)  <3963>;
      (__seff.3745 var=986 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.3608)  <3964>;
    } stp=0;
    <578> {
      (u8_save_bits.172 var=20) store_const__pl_rd_res_reg_const_1_B2 (__ct_0t0.3333 u8_save_bits.19 __sp.140)  <3595>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <183>;
    } #4 off=4 nxt=5
    #5 off=4 nxt=6
    <575> {
      (Temp_Buf.179 var=21) store_const__pl_rd_res_reg_const_1_B3 (__ct_1t0.3334 Temp_Buf.20 __sp.140)  <3592>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <190>;
    } #6 off=5 nxt=7
    #7 off=5 nxt=8
    (__ct_2t0.3358 var=738) const_inp ()  <3342>;
    <574> {
      (Temp_Buf.186 var=21) store_const__pl_rd_res_reg_const_1_B3 (__ct_2t0.3358 Temp_Buf.179 __sp.140)  <3591>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <197>;
    } #8 off=6 nxt=9
    #9 off=6 nxt=10
    (__ct_3t0.3359 var=740) const_inp ()  <3343>;
    <573> {
      (Temp_Buf.193 var=21) store_const__pl_rd_res_reg_const_1_B3 (__ct_3t0.3359 Temp_Buf.186 __sp.140)  <3590>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <204>;
    } #10 off=7 nxt=11
    #11 off=7 nxt=13
    (__ct_4t0.3360 var=742) const_inp ()  <3344>;
    <572> {
      (Temp_Buf.200 var=21) store_const__pl_rd_res_reg_const_1_B3 (__ct_4t0.3360 Temp_Buf.193 __sp.140)  <3589>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <212>;
    } #13 off=8 nxt=14
    #14 off=8 nxt=15
    <571> {
      (pollingid.204 var=22) _pl_rd_res_reg_const_store_const_1_B3 (__ct_6t0.3335 pollingid.21 __sp.140)  <3588>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <216>;
    } #15 off=9 nxt=16
    #16 off=9 nxt=17
    <570> {
      (gaincfg.208 var=23) store_const_4_B2 (__adr_gaincfg.3825 gaincfg.22)  <3587>;
      (__adr_gaincfg.3825 var=-154 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_gaincfg.3826)  <4031>;
    } stp=2;
    <712> {
      (__adr_gaincfg.3827 var=-154 stl=a_w2 __side_effect.3828 var=988 stl=c_flag_w __side_effect.3830 var=988 stl=nz_flag_w __side_effect.3832 var=988 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_8t0.3336 __sp.140)  <3840>;
      (__adr_gaincfg.3826 var=-154 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_gaincfg.3827)  <4032>;
      (__side_effect.3829 var=988 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__side_effect.3828)  <4033>;
      (__side_effect.3831 var=988 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.3830)  <4034>;
      (__side_effect.3833 var=988 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.3832)  <4035>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <220>;
    } #17 off=12 nxt=18
    #18 off=12 nxt=19
    (__ct_10t0.3337 var=156) const_inp ()  <3321>;
    <568> {
      (swversion.212 var=24) _pl_rd_res_reg_const_store_const_1_B3 (__ct_10t0.3337 swversion.23 __sp.140)  <3585>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <224>;
    } #19 off=13 nxt=406
    #406 off=13 nxt=24 tgt=27
    (__ptr_BATSYS0.3304 var=79) const_inp ()  <3288>;
    (void_Power_Off.3338 var=204) const_inp ()  <3322>;
    (__trgt.3361 var=938) const_inp ()  <3345>;
    <567> {
      (BATSYS0.220 var=25 __vola.221 var=13 __seff.3589 var=977 stl=nz_flag_w) load_const__ad_const_cmp_const_cc_eq__jump_const_2_B1 (__ptr_BATSYS0.3304 __trgt.3361 BATSYS0.24 __vola.12)  <3584>;
      (__seff.3718 var=977 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3589)  <3938>;
    } stp=0;
    if {
        {
            () if_expr (__either.3289)  <340>;
            (__either.3289 var=937) undefined ()  <3265>;
        } #22
        {
        } #27 off=17 nxt=29
        {
            #24 off=15 nxt=25
            <566> {
              () call_const_1_B1 (void_Power_Off.3338)  <3583>;
            } stp=0;
            call {
                (BATSYS0.332 var=25 KEY_ID.333 var=67 KEY_PID.334 var=65 LFTUNEVBAT.335 var=36 PCON0.336 var=72 PRE3T.337 var=61 PRECON10.338 var=54 PRECON11.339 var=56 PRECON2.340 var=38 PRECON3.341 var=40 PRECON4.342 var=42 PRECON5.343 var=44 PRECON6.344 var=46 PRECON7.345 var=48 PRECON8.346 var=50 PRECON9.347 var=52 PREPD.348 var=58 PRESTAT.349 var=69 PRESWUP0.350 var=30 PRESWUP1.351 var=32 PRESWUP2.352 var=34 PRET.353 var=60 WUP1W0.354 var=62 WUP1W1.355 var=63 WUP2W0.356 var=64 WUP2W1.357 var=66 WUP3W0.358 var=68 __extDM.359 var=17 __extDM_SFR_BATSYS0_t.360 var=28 __extDM_SFR_LFTUNEVBAT_t.361 var=37 __extDM_SFR_PCON0_t.362 var=73 __extDM_SFR_PRECON10_t.363 var=55 __extDM_SFR_PRECON11_t.364 var=57 __extDM_SFR_PRECON2_t.365 var=39 __extDM_SFR_PRECON3_t.366 var=41 __extDM_SFR_PRECON4_t.367 var=43 __extDM_SFR_PRECON5_t.368 var=45 __extDM_SFR_PRECON6_t.369 var=47 __extDM_SFR_PRECON7_t.370 var=49 __extDM_SFR_PRECON8_t.371 var=51 __extDM_SFR_PRECON9_t.372 var=53 __extDM_SFR_PRESTAT_t.373 var=70 __extDM_SFR_PRESWUP0_t.374 var=31 __extDM_SFR_PRESWUP1_t.375 var=33 __extDM_SFR_byte.376 var=59 __extDM_SFR_word.377 var=35 __extDM_int16_.378 var=27 __extDM_int8_.379 var=26 __extDM_void.380 var=75 __extPM.381 var=16 __extPM_void.382 var=74 __extULP.383 var=18 __extULP_void.384 var=76 g_b_InCriticalSection.385 var=29 g_b_NMI_occurred.386 var=71 __vola.387 var=13) Fvoid_Power_Off (BATSYS0.220 KEY_ID.66 KEY_PID.64 LFTUNEVBAT.35 PCON0.71 PRE3T.60 PRECON10.53 PRECON11.55 PRECON2.37 PRECON3.39 PRECON4.41 PRECON5.43 PRECON6.45 PRECON7.47 PRECON8.49 PRECON9.51 PREPD.57 PRESTAT.68 PRESWUP0.29 PRESWUP1.31 PRESWUP2.33 PRET.59 WUP1W0.61 WUP1W1.62 WUP2W0.63 WUP2W1.65 WUP3W0.67 __extDM.16 __extDM_SFR_BATSYS0_t.27 __extDM_SFR_LFTUNEVBAT_t.36 __extDM_SFR_PCON0_t.72 __extDM_SFR_PRECON10_t.54 __extDM_SFR_PRECON11_t.56 __extDM_SFR_PRECON2_t.38 __extDM_SFR_PRECON3_t.40 __extDM_SFR_PRECON4_t.42 __extDM_SFR_PRECON5_t.44 __extDM_SFR_PRECON6_t.46 __extDM_SFR_PRECON7_t.48 __extDM_SFR_PRECON8_t.50 __extDM_SFR_PRECON9_t.52 __extDM_SFR_PRESTAT_t.69 __extDM_SFR_PRESWUP0_t.30 __extDM_SFR_PRESWUP1_t.32 __extDM_SFR_byte.58 __extDM_SFR_word.34 __extDM_int16_.26 __extDM_int8_.25 __extDM_void.74 __extPM.15 __extPM_void.73 __extULP.17 __extULP_void.75 g_b_InCriticalSection.28 g_b_NMI_occurred.70 __vola.221)  <344>;
            } #25 off=17 nxt=26
            #26 nxt=-4
            () sink (__vola.387)  <345>;
            () sink (__extPM.381)  <348>;
            () sink (__extDM.359)  <349>;
            () sink (__extULP.383)  <350>;
            () sink (__sp.140)  <351>;
            () sink (u8_save_bits.172)  <352>;
            () sink (Temp_Buf.200)  <353>;
            () sink (pollingid.204)  <354>;
            () sink (gaincfg.208)  <355>;
            () sink (swversion.212)  <356>;
            () sink (BATSYS0.332)  <357>;
            () sink (__extDM_int8_.379)  <358>;
            () sink (__extDM_int16_.378)  <359>;
            () sink (__extDM_SFR_BATSYS0_t.360)  <360>;
            () sink (g_b_InCriticalSection.385)  <361>;
            () sink (PRESWUP0.350)  <362>;
            () sink (__extDM_SFR_PRESWUP0_t.374)  <363>;
            () sink (PRESWUP1.351)  <364>;
            () sink (__extDM_SFR_PRESWUP1_t.375)  <365>;
            () sink (PRESWUP2.352)  <366>;
            () sink (__extDM_SFR_word.377)  <367>;
            () sink (LFTUNEVBAT.335)  <368>;
            () sink (__extDM_SFR_LFTUNEVBAT_t.361)  <369>;
            () sink (PRECON2.340)  <370>;
            () sink (__extDM_SFR_PRECON2_t.365)  <371>;
            () sink (PRECON3.341)  <372>;
            () sink (__extDM_SFR_PRECON3_t.366)  <373>;
            () sink (PRECON4.342)  <374>;
            () sink (__extDM_SFR_PRECON4_t.367)  <375>;
            () sink (PRECON5.343)  <376>;
            () sink (__extDM_SFR_PRECON5_t.368)  <377>;
            () sink (PRECON6.344)  <378>;
            () sink (__extDM_SFR_PRECON6_t.369)  <379>;
            () sink (PRECON7.345)  <380>;
            () sink (__extDM_SFR_PRECON7_t.370)  <381>;
            () sink (PRECON8.346)  <382>;
            () sink (__extDM_SFR_PRECON8_t.371)  <383>;
            () sink (PRECON9.347)  <384>;
            () sink (__extDM_SFR_PRECON9_t.372)  <385>;
            () sink (PRECON10.338)  <386>;
            () sink (__extDM_SFR_PRECON10_t.363)  <387>;
            () sink (PRECON11.339)  <388>;
            () sink (__extDM_SFR_PRECON11_t.364)  <389>;
            () sink (PREPD.348)  <390>;
            () sink (__extDM_SFR_byte.376)  <391>;
            () sink (PRET.353)  <392>;
            () sink (PRE3T.337)  <393>;
            () sink (WUP1W0.354)  <394>;
            () sink (WUP1W1.355)  <395>;
            () sink (WUP2W0.356)  <396>;
            () sink (KEY_PID.334)  <397>;
            () sink (WUP2W1.357)  <398>;
            () sink (KEY_ID.333)  <399>;
            () sink (WUP3W0.358)  <400>;
            () sink (PRESTAT.349)  <401>;
            () sink (__extDM_SFR_PRESTAT_t.373)  <402>;
            () sink (g_b_NMI_occurred.386)  <403>;
            () sink (PCON0.336)  <404>;
            () sink (__extDM_SFR_PCON0_t.362)  <405>;
            () sink (__extPM_void.382)  <406>;
            () sink (__extDM_void.380)  <407>;
            () sink (__extULP_void.384)  <408>;
        } #23
        {
        } #28
    } #21
    #29 off=17 nxt=30
    (__ptr_g_b_InCriticalSection.3305 var=81) const_inp ()  <3289>;
    <565> {
      (g_b_InCriticalSection.593 var=29) store_const_const_8_B1 (__ptr_g_b_InCriticalSection.3305 g_b_InCriticalSection.28)  <3582>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <614>;
    } #30 off=19 nxt=31
    #31 off=19 nxt=32
    (void_phcaiKEyLLGenFunc_CS_SetBatPORFlag_bool_t.3339 var=210) const_inp ()  <3323>;
    <564> {
      () call_const_1_B1 (void_phcaiKEyLLGenFunc_CS_SetBatPORFlag_bool_t.3339)  <3581>;
    } stp=1;
    <716> {
      (__ct_1.3835 var=208 stl=a_b0) const_15_B2 ()  <3848>;
      (__ct_1.3834 var=208 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_1.3835)  <4036>;
    } stp=0;
    call {
        (BATSYS0.599 var=25 KEY_ID.600 var=67 KEY_PID.601 var=65 LFTUNEVBAT.602 var=36 PCON0.603 var=72 PRE3T.604 var=61 PRECON10.605 var=54 PRECON11.606 var=56 PRECON2.607 var=38 PRECON3.608 var=40 PRECON4.609 var=42 PRECON5.610 var=44 PRECON6.611 var=46 PRECON7.612 var=48 PRECON8.613 var=50 PRECON9.614 var=52 PREPD.615 var=58 PRESTAT.616 var=69 PRESWUP0.617 var=30 PRESWUP1.618 var=32 PRESWUP2.619 var=34 PRET.620 var=60 WUP1W0.621 var=62 WUP1W1.622 var=63 WUP2W0.623 var=64 WUP2W1.624 var=66 WUP3W0.625 var=68 __extDM.626 var=17 __extDM_SFR_BATSYS0_t.627 var=28 __extDM_SFR_LFTUNEVBAT_t.628 var=37 __extDM_SFR_PCON0_t.629 var=73 __extDM_SFR_PRECON10_t.630 var=55 __extDM_SFR_PRECON11_t.631 var=57 __extDM_SFR_PRECON2_t.632 var=39 __extDM_SFR_PRECON3_t.633 var=41 __extDM_SFR_PRECON4_t.634 var=43 __extDM_SFR_PRECON5_t.635 var=45 __extDM_SFR_PRECON6_t.636 var=47 __extDM_SFR_PRECON7_t.637 var=49 __extDM_SFR_PRECON8_t.638 var=51 __extDM_SFR_PRECON9_t.639 var=53 __extDM_SFR_PRESTAT_t.640 var=70 __extDM_SFR_PRESWUP0_t.641 var=31 __extDM_SFR_PRESWUP1_t.642 var=33 __extDM_SFR_byte.643 var=59 __extDM_SFR_word.644 var=35 __extDM_int16_.645 var=27 __extDM_int8_.646 var=26 __extDM_void.647 var=75 __extPM.648 var=16 __extPM_void.649 var=74 __extULP.650 var=18 __extULP_void.651 var=76 g_b_InCriticalSection.652 var=29 g_b_NMI_occurred.653 var=71 __vola.654 var=13) Fvoid_phcaiKEyLLGenFunc_CS_SetBatPORFlag_bool_t (__ct_1.3834 BATSYS0.220 KEY_ID.66 KEY_PID.64 LFTUNEVBAT.35 PCON0.71 PRE3T.60 PRECON10.53 PRECON11.55 PRECON2.37 PRECON3.39 PRECON4.41 PRECON5.43 PRECON6.45 PRECON7.47 PRECON8.49 PRECON9.51 PREPD.57 PRESTAT.68 PRESWUP0.29 PRESWUP1.31 PRESWUP2.33 PRET.59 WUP1W0.61 WUP1W1.62 WUP2W0.63 WUP2W1.65 WUP3W0.67 __extDM.16 __extDM_SFR_BATSYS0_t.27 __extDM_SFR_LFTUNEVBAT_t.36 __extDM_SFR_PCON0_t.72 __extDM_SFR_PRECON10_t.54 __extDM_SFR_PRECON11_t.56 __extDM_SFR_PRECON2_t.38 __extDM_SFR_PRECON3_t.40 __extDM_SFR_PRECON4_t.42 __extDM_SFR_PRECON5_t.44 __extDM_SFR_PRECON6_t.46 __extDM_SFR_PRECON7_t.48 __extDM_SFR_PRECON8_t.50 __extDM_SFR_PRECON9_t.52 __extDM_SFR_PRESTAT_t.69 __extDM_SFR_PRESWUP0_t.30 __extDM_SFR_PRESWUP1_t.32 __extDM_SFR_byte.58 __extDM_SFR_word.34 __extDM_int16_.26 __extDM_int8_.25 __extDM_void.74 __extPM.15 __extPM_void.73 __extULP.17 __extULP_void.75 g_b_InCriticalSection.593 g_b_NMI_occurred.70 __vola.221)  <621>;
    } #32 off=22 nxt=34
    #34 off=22 nxt=35
    (__ptr_PRESWUP0.3306 var=83) const_inp ()  <3290>;
    <561> {
      (PRESWUP0.661 var=30 __vola.662 var=13) store_const_2_B2 (__ct_17480.3836 __ptr_PRESWUP0.3306 PRESWUP0.617 __vola.654)  <3578>;
      (__ct_17480.3836 var=212 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_17480.3837)  <4037>;
    } stp=2;
    <717> {
      (__ct_17480.3838 var=212 stl=__CTa_w0_uint16__cstP16_E1) const_14_B1 ()  <3851>;
      (__ct_17480.3837 var=212 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_17480.3838)  <4038>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <629>;
    } #35 off=25 nxt=36
    #36 off=25 nxt=37
    (__ptr_PRESWUP1.3307 var=85) const_inp ()  <3291>;
    <559> {
      (PRESWUP1.669 var=32 __vola.670 var=13) store_const_3_B2 (__ct_50244.3839 __ptr_PRESWUP1.3307 PRESWUP1.618 __vola.662)  <3576>;
      (__ct_50244.3839 var=217 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__ct_50244.3840)  <4039>;
    } stp=2;
    <718> {
      (__ct_50244.3841 var=217 stl=__CTa_w0_uint16__cstP16_E1) const_13_B1 ()  <3854>;
      (__ct_50244.3840 var=217 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_int16__B1 (__ct_50244.3841)  <4040>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <636>;
    } #37 off=28 nxt=38
    #38 off=28 nxt=39
    (__ptr_PRESWUP2.3308 var=87) const_inp ()  <3292>;
    <557> {
      (PRESWUP2.677 var=34 __vola.678 var=13) store_const_2_B2 (__ct_2184.3842 __ptr_PRESWUP2.3308 PRESWUP2.619 __vola.670)  <3574>;
      (__ct_2184.3842 var=222 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_2184.3843)  <4041>;
    } stp=2;
    <719> {
      (__ct_2184.3844 var=222 stl=__CTa_w0_uint16__cstP16_E1) const_12_B1 ()  <3857>;
      (__ct_2184.3843 var=222 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_2184.3844)  <4042>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <643>;
    } #39 off=31 nxt=40
    #40 off=31 nxt=41
    (__ptr_LFTUNEVBAT.3309 var=89) const_inp ()  <3293>;
    <555> {
      (LFTUNEVBAT.685 var=36 __vola.686 var=13) store_const_2_B2 (__ct_273.3845 __ptr_LFTUNEVBAT.3309 LFTUNEVBAT.602 __vola.678)  <3572>;
      (__ct_273.3845 var=227 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_273.3846)  <4043>;
    } stp=2;
    <720> {
      (__ct_273.3847 var=227 stl=__CTa_w0_uint16__cstP16_E1) const_11_B1 ()  <3860>;
      (__ct_273.3846 var=227 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_273.3847)  <4044>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <650>;
    } #41 off=34 nxt=42
    #42 off=34 nxt=43
    (__ptr_PRECON2.3310 var=91) const_inp ()  <3294>;
    <552> {
      (__fch_PRECON2.690 var=235 stl=DM_r PRECON2.691 var=38 __vola.692 var=13) load_const_2_B2 (__ptr_PRECON2.3310 PRECON2.607 __vola.686)  <3569>;
      (__fch_PRECON2.3747 var=235 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_PRECON2.690)  <3966>;
    } stp=0;
    <553> {
      (__tmp.696 var=674 stl=a_b2 __seff.3571 var=976 stl=nz_flag_w) _ad_const_3_B2 (__fch_PRECON2.3746)  <3570>;
      (__seff.3722 var=976 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3571)  <3942>;
      (__fch_PRECON2.3746 var=235 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__fch_PRECON2.3747)  <3965>;
      (__tmp.3749 var=674 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.696)  <3968>;
    } stp=1;
    <554> {
      (u8_save_bits.699 var=20) store__pl_rd_res_reg_const_2_B1 (__tmp.3748 __ct_0t0.3333 u8_save_bits.172 __sp.140)  <3571>;
      (__tmp.3748 var=674 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.3749)  <3967>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <661>;
    } #43 off=38 nxt=44
    #44 off=38 nxt=45
    <549> {
      (__fch_u8_save_bits.702 var=243 stl=DM_r) load__pl_rd_res_reg_const_2_B1 (__ct_0t0.3333 u8_save_bits.699 __sp.140)  <3566>;
      (__fch_u8_save_bits.3751 var=243 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_u8_save_bits.702)  <3970>;
    } stp=0;
    <550> {
      (__tmp.704 var=676 stl=a_b2 __seff.3564 var=975 stl=nz_flag_w) _or_const_2_B1 (__fch_u8_save_bits.3750)  <3567>;
      (__seff.3721 var=975 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3564)  <3941>;
      (__fch_u8_save_bits.3750 var=243 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__fch_u8_save_bits.3751)  <3969>;
      (__tmp.3753 var=676 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.704)  <3972>;
    } stp=2;
    <551> {
      (PRECON2.710 var=38 __vola.711 var=13) store_const_1_B2 (__tmp.3752 __ptr_PRECON2.3310 PRECON2.691 __vola.692)  <3568>;
      (__tmp.3752 var=676 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.3753)  <3971>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <672>;
    } #45 off=43 nxt=46
    #46 off=43 nxt=47
    (__ptr_PRECON3.3311 var=93) const_inp ()  <3295>;
    <548> {
      (PRECON3.718 var=40 __vola.719 var=13) store_const_const_3_B1 (__ptr_PRECON3.3311 PRECON3.608 __vola.711)  <3565>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <679>;
    } #47 off=45 nxt=48
    #48 off=45 nxt=49
    (__ptr_PRECON4.3312 var=95) const_inp ()  <3296>;
    <547> {
      (PRECON4.726 var=42 __vola.727 var=13) store_const_const_3_B1 (__ptr_PRECON4.3312 PRECON4.609 __vola.719)  <3564>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <686>;
    } #49 off=47 nxt=50
    #50 off=47 nxt=51
    (__ptr_PRECON5.3313 var=97) const_inp ()  <3297>;
    <546> {
      (PRECON5.734 var=44 __vola.735 var=13) store_const_const_3_B1 (__ptr_PRECON5.3313 PRECON5.610 __vola.727)  <3563>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <693>;
    } #51 off=49 nxt=52
    #52 off=49 nxt=53
    (__ptr_PRECON6.3314 var=99) const_inp ()  <3298>;
    <543> {
      (__fch_PRECON6.739 var=268 stl=DM_r PRECON6.740 var=46 __vola.741 var=13) load_const_2_B2 (__ptr_PRECON6.3314 PRECON6.611 __vola.735)  <3560>;
      (__fch_PRECON6.3755 var=268 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_PRECON6.739)  <3974>;
    } stp=0;
    <544> {
      (__tmp.745 var=678 stl=a_b2 __seff.3554 var=974 stl=nz_flag_w) _ad_const_2_B1 (__fch_PRECON6.3754)  <3561>;
      (__seff.3720 var=974 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3554)  <3940>;
      (__fch_PRECON6.3754 var=268 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__fch_PRECON6.3755)  <3973>;
      (__tmp.3757 var=678 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.745)  <3976>;
    } stp=1;
    <545> {
      (u8_save_bits.748 var=20) store__pl_rd_res_reg_const_2_B1 (__tmp.3756 __ct_0t0.3333 u8_save_bits.699 __sp.140)  <3562>;
      (__tmp.3756 var=678 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.3757)  <3975>;
    } stp=3;
    call {
        () chess_separator_scheduler ()  <704>;
    } #53 off=54 nxt=54
    #54 off=54 nxt=55
    <541> {
      (__fch_u8_save_bits.751 var=276 stl=DM_r) load__pl_rd_res_reg_const_2_B1 (__ct_0t0.3333 u8_save_bits.748 __sp.140)  <3558>;
      (__fch_u8_save_bits.3759 var=276 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_u8_save_bits.751)  <3978>;
    } stp=0;
    <542> {
      (PRECON6.759 var=46 __vola.760 var=13) store_const_1_B2 (__fch_u8_save_bits.3758 __ptr_PRECON6.3314 PRECON6.740 __vola.741)  <3559>;
      (__fch_u8_save_bits.3758 var=276 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_u8_save_bits.3759)  <3977>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <715>;
    } #55 off=57 nxt=56
    #56 off=57 nxt=57
    (__ptr_PRECON7.3315 var=101) const_inp ()  <3299>;
    <540> {
      (PRECON7.767 var=48 __vola.768 var=13) store_const_const_7_B1 (__ptr_PRECON7.3315 PRECON7.612 __vola.760)  <3557>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <722>;
    } #57 off=59 nxt=58
    #58 off=59 nxt=59
    (__ptr_PRECON8.3316 var=103) const_inp ()  <3300>;
    <539> {
      (PRECON8.775 var=50 __vola.776 var=13) store_const_const_6_B1 (__ptr_PRECON8.3316 PRECON8.613 __vola.768)  <3556>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <729>;
    } #59 off=61 nxt=60
    #60 off=61 nxt=61
    (__ptr_PRECON9.3317 var=105) const_inp ()  <3301>;
    <538> {
      (PRECON9.783 var=52 __vola.784 var=13) store_const_const_6_B1 (__ptr_PRECON9.3317 PRECON9.614 __vola.776)  <3555>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <736>;
    } #61 off=63 nxt=62
    #62 off=63 nxt=63
    (__ptr_PRECON10.3318 var=107) const_inp ()  <3302>;
    <537> {
      (PRECON10.791 var=54 __vola.792 var=13) store_const_const_3_B1 (__ptr_PRECON10.3318 PRECON10.605 __vola.784)  <3554>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <743>;
    } #63 off=65 nxt=64
    #64 off=65 nxt=65
    (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.3340 var=305) const_inp ()  <3324>;
    <536> {
      () call_const_1_B1 (__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort.3340)  <3553>;
    } stp=2;
    <721> {
      (__ct_209.3849 var=303 stl=__CTa_w0_uint16__cstP16_E1) const_10_B1 ()  <3862>;
      (__ct_209.3848 var=303 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B0 (__ct_209.3849)  <4045>;
    } stp=0;
    call {
        (__tmp.798 var=307 stl=RbL off=0 BATSYS0.801 var=25 KEY_ID.802 var=67 KEY_PID.803 var=65 LFTUNEVBAT.804 var=36 PCON0.805 var=72 PRE3T.806 var=61 PRECON10.807 var=54 PRECON11.808 var=56 PRECON2.809 var=38 PRECON3.810 var=40 PRECON4.811 var=42 PRECON5.812 var=44 PRECON6.813 var=46 PRECON7.814 var=48 PRECON8.815 var=50 PRECON9.816 var=52 PREPD.817 var=58 PRESTAT.818 var=69 PRESWUP0.819 var=30 PRESWUP1.820 var=32 PRESWUP2.821 var=34 PRET.822 var=60 WUP1W0.823 var=62 WUP1W1.824 var=63 WUP2W0.825 var=64 WUP2W1.826 var=66 WUP3W0.827 var=68 __extDM.828 var=17 __extDM_SFR_BATSYS0_t.829 var=28 __extDM_SFR_LFTUNEVBAT_t.830 var=37 __extDM_SFR_PCON0_t.831 var=73 __extDM_SFR_PRECON10_t.832 var=55 __extDM_SFR_PRECON11_t.833 var=57 __extDM_SFR_PRECON2_t.834 var=39 __extDM_SFR_PRECON3_t.835 var=41 __extDM_SFR_PRECON4_t.836 var=43 __extDM_SFR_PRECON5_t.837 var=45 __extDM_SFR_PRECON6_t.838 var=47 __extDM_SFR_PRECON7_t.839 var=49 __extDM_SFR_PRECON8_t.840 var=51 __extDM_SFR_PRECON9_t.841 var=53 __extDM_SFR_PRESTAT_t.842 var=70 __extDM_SFR_PRESWUP0_t.843 var=31 __extDM_SFR_PRESWUP1_t.844 var=33 __extDM_SFR_byte.845 var=59 __extDM_SFR_word.846 var=35 __extDM_int16_.847 var=27 __extDM_int8_.848 var=26 __extDM_void.849 var=75 __extPM.850 var=16 __extPM_void.851 var=74 __extULP.852 var=18 __extULP_void.853 var=76 g_b_InCriticalSection.854 var=29 g_b_NMI_occurred.855 var=71 __vola.856 var=13) F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort (__ct_209.3848 BATSYS0.599 KEY_ID.600 KEY_PID.601 LFTUNEVBAT.685 PCON0.603 PRE3T.604 PRECON10.791 PRECON11.606 PRECON2.710 PRECON3.718 PRECON4.726 PRECON5.734 PRECON6.759 PRECON7.767 PRECON8.775 PRECON9.783 PREPD.615 PRESTAT.616 PRESWUP0.661 PRESWUP1.669 PRESWUP2.677 PRET.620 WUP1W0.621 WUP1W1.622 WUP2W0.623 WUP2W1.624 WUP3W0.625 __extDM.626 __extDM_SFR_BATSYS0_t.627 __extDM_SFR_LFTUNEVBAT_t.628 __extDM_SFR_PCON0_t.629 __extDM_SFR_PRECON10_t.630 __extDM_SFR_PRECON11_t.631 __extDM_SFR_PRECON2_t.632 __extDM_SFR_PRECON3_t.633 __extDM_SFR_PRECON4_t.634 __extDM_SFR_PRECON5_t.635 __extDM_SFR_PRECON6_t.636 __extDM_SFR_PRECON7_t.637 __extDM_SFR_PRECON8_t.638 __extDM_SFR_PRECON9_t.639 __extDM_SFR_PRESTAT_t.640 __extDM_SFR_PRESWUP0_t.641 __extDM_SFR_PRESWUP1_t.642 __extDM_SFR_byte.643 __extDM_SFR_word.644 __extDM_int16_.645 __extDM_int8_.646 __extDM_void.647 __extPM.648 __extPM_void.649 __extULP.650 __extULP_void.651 g_b_InCriticalSection.652 g_b_NMI_occurred.653 __vola.792)  <750>;
    } #65 off=69 nxt=66
    #66 off=69 nxt=67
    <534> {
      (gaincfg.858 var=23) store_1_B1 (__tmp.3762 __adr_gaincfg.3876 gaincfg.208)  <3551>;
      (__tmp.3762 var=307 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.798)  <3979>;
      (__adr_gaincfg.3876 var=154 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_gaincfg.3877)  <4061>;
    } stp=2;
    <733> {
      (__adr_gaincfg.3878 var=154 stl=a_w2 __side_effect.3879 var=988 stl=c_flag_w __side_effect.3881 var=988 stl=nz_flag_w __side_effect.3883 var=988 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_8t0.3336 __sp.140)  <3891>;
      (__adr_gaincfg.3877 var=154 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_gaincfg.3878)  <4062>;
      (__side_effect.3880 var=988 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__side_effect.3879)  <4063>;
      (__side_effect.3882 var=988 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.3881)  <4064>;
      (__side_effect.3884 var=988 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.3883)  <4065>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <754>;
    } #67 off=72 nxt=372
    #372 off=72 nxt=75 tgt=72
    (__ptr_PRECON11.3319 var=109) const_inp ()  <3303>;
    (__trgt.3362 var=939) const_inp ()  <3346>;
    <532> {
      (__apl_nz.3241 var=912 stl=nz_flag_w __seff.3535 var=972 stl=c_flag_w __seff.3536 var=973 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B1 (__ct_8t0.3336 gaincfg.858 __sp.140)  <3549>;
      (__apl_nz.3724 var=912 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.3241)  <3944>;
      (__seff.3794 var=972 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.3535)  <4010>;
      (__seff.3795 var=973 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.3536)  <4011>;
    } stp=0;
    <533> {
      () cc_eq__jump_const_1_B1 (__apl_nz.3723 __trgt.3362)  <3550>;
      (__apl_nz.3723 var=912 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.3724)  <3943>;
    } stp=2;
    if {
        {
            () if_expr (__either.3291)  <861>;
            (__either.3291 var=937) undefined ()  <3268>;
        } #70
        {
            <531> {
              (PRECON11.971 var=56 __vola.972 var=13) store_const_const_5_B1 (__ptr_PRECON11.3319 PRECON11.808 __vola.856)  <3548>;
            } stp=0;
        } #72 off=78 nxt=78
        {
            (__trgt.3363 var=940) const_inp ()  <3347>;
            <529> {
              (PRECON11.979 var=56 __vola.980 var=13) store_const_const_4_B1 (__ptr_PRECON11.3319 PRECON11.808 __vola.856)  <3546>;
            } stp=0;
            <530> {
              () jump_const_1_B1 (__trgt.3363)  <3547>;
            } stp=2;
        } #75 off=75 tgt=78
        {
            (__vola.981 var=13) merge (__vola.972 __vola.980)  <876>;
            (PRECON11.982 var=56) merge (PRECON11.971 PRECON11.979)  <877>;
        } #77
    } #69
    #78 off=80 nxt=79
    (__ptr_PREPD.3320 var=111) const_inp ()  <3304>;
    <528> {
      (PREPD.989 var=58 __vola.990 var=13) store_const_const_3_B1 (__ptr_PREPD.3320 PREPD.817 __vola.981)  <3545>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <884>;
    } #79 off=82 nxt=80
    #80 off=82 nxt=81
    (__ptr_PRET.3321 var=113) const_inp ()  <3305>;
    <526> {
      (PRET.997 var=60 __vola.998 var=13) store_const_2_B2 (__ct_7440.3850 __ptr_PRET.3321 PRET.822 __vola.990)  <3543>;
      (__ct_7440.3850 var=328 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_7440.3851)  <4046>;
    } stp=2;
    <722> {
      (__ct_7440.3852 var=328 stl=__CTa_w0_uint16__cstP16_E1) const_9_B1 ()  <3865>;
      (__ct_7440.3851 var=328 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_7440.3852)  <4047>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <891>;
    } #81 off=85 nxt=82
    #82 off=85 nxt=83
    (__ptr_PRE3T.3322 var=115) const_inp ()  <3306>;
    <524> {
      (PRE3T.1005 var=61 __vola.1006 var=13) store_const_2_B2 (__ct_24632.3853 __ptr_PRE3T.3322 PRE3T.806 __vola.998)  <3541>;
      (__ct_24632.3853 var=333 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_24632.3854)  <4048>;
    } stp=2;
    <723> {
      (__ct_24632.3855 var=333 stl=__CTa_w0_uint16__cstP16_E1) const_8_B1 ()  <3868>;
      (__ct_24632.3854 var=333 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_24632.3855)  <4049>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <898>;
    } #83 off=88 nxt=84
    #84 off=88 nxt=85
    (__ptr_WUP1W0.3323 var=117) const_inp ()  <3307>;
    <522> {
      (WUP1W0.1013 var=62 __vola.1014 var=13) store_const_2_B2 (__ct_17503.3856 __ptr_WUP1W0.3323 WUP1W0.823 __vola.1006)  <3539>;
      (__ct_17503.3856 var=338 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_17503.3857)  <4050>;
    } stp=2;
    <724> {
      (__ct_17503.3858 var=338 stl=__CTa_w0_uint16__cstP16_E1) const_7_B1 ()  <3871>;
      (__ct_17503.3857 var=338 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_17503.3858)  <4051>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <905>;
    } #85 off=91 nxt=86
    #86 off=91 nxt=87
    (__ptr_WUP1W1.3324 var=119) const_inp ()  <3308>;
    <520> {
      (WUP1W1.1021 var=63 __vola.1022 var=13) store_const_2_B2 (__ct_8755.3859 __ptr_WUP1W1.3324 WUP1W1.824 __vola.1014)  <3537>;
      (__ct_8755.3859 var=343 stl=DMw_w) DMw_w_1_dr_move_Rw_1_uint16_1_32768__B1 (__ct_8755.3860)  <4052>;
    } stp=2;
    <725> {
      (__ct_8755.3861 var=343 stl=__CTa_w0_uint16__cstP16_E1) const_6_B1 ()  <3874>;
      (__ct_8755.3860 var=343 stl=R46 off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B1 (__ct_8755.3861)  <4053>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <912>;
    } #87 off=94 nxt=325
    #325 off=94 nxt=89
    (__ptr_WUP2W0.3325 var=121) const_inp ()  <3309>;
    (__ptr_KEY_PID__a3.3347 var=727) const_inp ()  <3331>;
    <515> {
      (__fch_KEY_PID.1026 var=351 stl=DM_r) load_const_1_B1 (__ptr_KEY_PID__a3.3347 KEY_PID.803)  <3532>;
      (__fch_KEY_PID.3771 var=351 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_PID.1026)  <3988>;
    } stp=0;
    <516> {
      (__tmp.1030 var=667 stl=a_b2 __seff.3508 var=968 stl=nz_flag_w) _ad_const_1_B1 (__fch_KEY_PID.3770)  <3533>;
      (__seff.3730 var=968 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3508)  <3950>;
      (__fch_KEY_PID.3770 var=351 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__fch_KEY_PID.3771)  <3987>;
      (__tmp.3773 var=667 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.1030)  <3990>;
    } stp=2;
    <517> {
      (__rt.3201 var=888 stl=a_b2 __seff.3510 var=969 stl=c_flag_w __seff.3511 var=970 stl=nz_flag_w) shl_const_1_B1 (__tmp.3772)  <3534>;
      (__seff.3729 var=970 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3511)  <3949>;
      (__tmp.3772 var=667 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__tmp.3773)  <3989>;
      (__seff.3774 var=969 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.3510)  <3991>;
      (__rt.3776 var=888 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__rt.3201)  <3993>;
    } stp=4;
    <518> {
      (__tmp.1036 var=659 stl=a_b2 __seff.3513 var=971 stl=nz_flag_w) _or_const_1_B1 (__rt.3775)  <3535>;
      (__seff.3728 var=971 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3513)  <3948>;
      (__rt.3775 var=888 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__rt.3776)  <3992>;
      (__tmp.3778 var=659 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.1036)  <3995>;
    } stp=5;
    <519> {
      (WUP2W0.1045 var=64 __vola.1046 var=13) store_const_1_B2 (__tmp.3777 __ptr_WUP2W0.3325 WUP2W0.825 __vola.1022)  <3536>;
      (__tmp.3777 var=659 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.3778)  <3994>;
    } stp=7;
    call {
        () chess_separator_scheduler ()  <935>;
    } #89 off=102 nxt=90
    #90 off=102 nxt=91
    (__ptr_WUP2W0__a1.3351 var=731) const_inp ()  <3335>;
    (__ptr_KEY_PID__a2.3352 var=732) const_inp ()  <3336>;
    <513> {
      (__fch_KEY_PID.1050 var=372 stl=DM_r) load_const_1_B1 (__ptr_KEY_PID__a2.3352 KEY_PID.803)  <3530>;
      (__fch_KEY_PID.3780 var=372 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_PID.1050)  <3997>;
    } stp=0;
    <514> {
      (WUP2W0.1058 var=64 __vola.1059 var=13) store_const_1_B2 (__fch_KEY_PID.3779 __ptr_WUP2W0__a1.3351 WUP2W0.1045 __vola.1046)  <3531>;
      (__fch_KEY_PID.3779 var=372 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_PID.3780)  <3996>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <947>;
    } #91 off=105 nxt=92
    #92 off=105 nxt=93
    (__ptr_WUP2W1.3327 var=125) const_inp ()  <3311>;
    (__ptr_KEY_PID__a1.3353 var=733) const_inp ()  <3337>;
    <511> {
      (__fch_KEY_PID.1063 var=382 stl=DM_r) load_const_1_B1 (__ptr_KEY_PID__a1.3353 KEY_PID.803)  <3528>;
      (__fch_KEY_PID.3782 var=382 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_PID.1063)  <3999>;
    } stp=0;
    <512> {
      (WUP2W1.1071 var=66 __vola.1072 var=13) store_const_1_B2 (__fch_KEY_PID.3781 __ptr_WUP2W1.3327 WUP2W1.826 __vola.1059)  <3529>;
      (__fch_KEY_PID.3781 var=382 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_PID.3782)  <3998>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <959>;
    } #93 off=108 nxt=94
    #94 off=108 nxt=95
    (__ptr_KEY_PID.3326 var=123) const_inp ()  <3310>;
    (__ptr_WUP2W1__a1.3354 var=734) const_inp ()  <3338>;
    <509> {
      (__fch_KEY_PID.1076 var=392 stl=DM_r) load_const_1_B1 (__ptr_KEY_PID.3326 KEY_PID.803)  <3526>;
      (__fch_KEY_PID.3784 var=392 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_PID.1076)  <4001>;
    } stp=0;
    <510> {
      (WUP2W1.1084 var=66 __vola.1085 var=13) store_const_1_B2 (__fch_KEY_PID.3783 __ptr_WUP2W1__a1.3354 WUP2W1.1071 __vola.1072)  <3527>;
      (__fch_KEY_PID.3783 var=392 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_PID.3784)  <4000>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <971>;
    } #95 off=111 nxt=341
    #341 off=111 nxt=97
    (__ptr_KEY_ID__a3.3348 var=728) const_inp ()  <3332>;
    <504> {
      (__fch_KEY_ID.1089 var=402 stl=DM_r) load_const_1_B1 (__ptr_KEY_ID__a3.3348 KEY_ID.802)  <3521>;
      (__fch_KEY_ID.3764 var=402 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_ID.1089)  <3981>;
    } stp=0;
    <505> {
      (__tmp.1093 var=670 stl=a_b2 __seff.3489 var=964 stl=nz_flag_w) _ad_const_1_B1 (__fch_KEY_ID.3763)  <3522>;
      (__seff.3727 var=964 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3489)  <3947>;
      (__fch_KEY_ID.3763 var=402 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__fch_KEY_ID.3764)  <3980>;
      (__tmp.3766 var=670 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.1093)  <3983>;
    } stp=2;
    <506> {
      (__rt.3211 var=888 stl=a_b2 __seff.3491 var=965 stl=c_flag_w __seff.3492 var=966 stl=nz_flag_w) shl_const_1_B1 (__tmp.3765)  <3523>;
      (__seff.3726 var=966 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3492)  <3946>;
      (__tmp.3765 var=670 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__tmp.3766)  <3982>;
      (__seff.3767 var=965 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.3491)  <3984>;
      (__rt.3769 var=888 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__rt.3211)  <3986>;
    } stp=4;
    <507> {
      (__tmp.1099 var=662 stl=a_b2 __seff.3494 var=967 stl=nz_flag_w) _or_const_1_B1 (__rt.3768)  <3524>;
      (__seff.3725 var=967 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3494)  <3945>;
      (__rt.3768 var=888 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__rt.3769)  <3985>;
      (__tmp.3786 var=662 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.1099)  <4003>;
    } stp=5;
    <508> {
      (WUP1W0.1108 var=62 __vola.1109 var=13) store_const_1_B2 (__tmp.3785 __ptr_WUP1W0.3323 WUP1W0.1013 __vola.1085)  <3525>;
      (__tmp.3785 var=662 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.3786)  <4002>;
    } stp=7;
    call {
        () chess_separator_scheduler ()  <994>;
    } #97 off=119 nxt=98
    #98 off=119 nxt=99
    (__ptr_WUP1W0__a1.3349 var=729) const_inp ()  <3333>;
    (__ptr_KEY_ID__a2.3355 var=735) const_inp ()  <3339>;
    <502> {
      (__fch_KEY_ID.1113 var=423 stl=DM_r) load_const_1_B1 (__ptr_KEY_ID__a2.3355 KEY_ID.802)  <3519>;
      (__fch_KEY_ID.3788 var=423 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_ID.1113)  <4005>;
    } stp=0;
    <503> {
      (WUP1W0.1121 var=62 __vola.1122 var=13) store_const_1_B2 (__fch_KEY_ID.3787 __ptr_WUP1W0__a1.3349 WUP1W0.1108 __vola.1109)  <3520>;
      (__fch_KEY_ID.3787 var=423 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_ID.3788)  <4004>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <1006>;
    } #99 off=122 nxt=100
    #100 off=122 nxt=101
    (__ptr_KEY_ID__a1.3356 var=736) const_inp ()  <3340>;
    <500> {
      (__fch_KEY_ID.1126 var=433 stl=DM_r) load_const_1_B1 (__ptr_KEY_ID__a1.3356 KEY_ID.802)  <3517>;
      (__fch_KEY_ID.3790 var=433 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_ID.1126)  <4007>;
    } stp=0;
    <501> {
      (WUP1W1.1134 var=63 __vola.1135 var=13) store_const_1_B2 (__fch_KEY_ID.3789 __ptr_WUP1W1.3324 WUP1W1.1021 __vola.1122)  <3518>;
      (__fch_KEY_ID.3789 var=433 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_ID.3790)  <4006>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <1018>;
    } #101 off=125 nxt=102
    #102 off=125 nxt=103
    (__ptr_KEY_ID.3328 var=127) const_inp ()  <3312>;
    (__ptr_WUP1W1__a1.3350 var=730) const_inp ()  <3334>;
    <498> {
      (__fch_KEY_ID.1139 var=443 stl=DM_r) load_const_1_B1 (__ptr_KEY_ID.3328 KEY_ID.802)  <3515>;
      (__fch_KEY_ID.3792 var=443 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch_KEY_ID.1139)  <4009>;
    } stp=0;
    <499> {
      (WUP1W1.1147 var=63 __vola.1148 var=13) store_const_1_B2 (__fch_KEY_ID.3791 __ptr_WUP1W1__a1.3350 WUP1W1.1134 __vola.1135)  <3516>;
      (__fch_KEY_ID.3791 var=443 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch_KEY_ID.3792)  <4008>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <1030>;
    } #103 off=128 nxt=104
    #104 off=128 nxt=105
    (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.3341 var=452) const_inp ()  <3325>;
    <497> {
      () call_const_1_B1 (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.3341)  <3514>;
    } stp=2;
    <726> {
      (__ct_26.3863 var=450 stl=__CTa_w0_uint16__cstP16_E1) const_5_B1 ()  <3876>;
      (__ct_26.3862 var=450 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B0 (__ct_26.3863)  <4054>;
    } stp=0;
    call {
        (__tmp.1154 var=454 stl=RwL off=0 BATSYS0.1157 var=25 KEY_ID.1158 var=67 KEY_PID.1159 var=65 LFTUNEVBAT.1160 var=36 PCON0.1161 var=72 PRE3T.1162 var=61 PRECON10.1163 var=54 PRECON11.1164 var=56 PRECON2.1165 var=38 PRECON3.1166 var=40 PRECON4.1167 var=42 PRECON5.1168 var=44 PRECON6.1169 var=46 PRECON7.1170 var=48 PRECON8.1171 var=50 PRECON9.1172 var=52 PREPD.1173 var=58 PRESTAT.1174 var=69 PRESWUP0.1175 var=30 PRESWUP1.1176 var=32 PRESWUP2.1177 var=34 PRET.1178 var=60 WUP1W0.1179 var=62 WUP1W1.1180 var=63 WUP2W0.1181 var=64 WUP2W1.1182 var=66 WUP3W0.1183 var=68 __extDM.1184 var=17 __extDM_SFR_BATSYS0_t.1185 var=28 __extDM_SFR_LFTUNEVBAT_t.1186 var=37 __extDM_SFR_PCON0_t.1187 var=73 __extDM_SFR_PRECON10_t.1188 var=55 __extDM_SFR_PRECON11_t.1189 var=57 __extDM_SFR_PRECON2_t.1190 var=39 __extDM_SFR_PRECON3_t.1191 var=41 __extDM_SFR_PRECON4_t.1192 var=43 __extDM_SFR_PRECON5_t.1193 var=45 __extDM_SFR_PRECON6_t.1194 var=47 __extDM_SFR_PRECON7_t.1195 var=49 __extDM_SFR_PRECON8_t.1196 var=51 __extDM_SFR_PRECON9_t.1197 var=53 __extDM_SFR_PRESTAT_t.1198 var=70 __extDM_SFR_PRESWUP0_t.1199 var=31 __extDM_SFR_PRESWUP1_t.1200 var=33 __extDM_SFR_byte.1201 var=59 __extDM_SFR_word.1202 var=35 __extDM_int16_.1203 var=27 __extDM_int8_.1204 var=26 __extDM_void.1205 var=75 __extPM.1206 var=16 __extPM_void.1207 var=74 __extULP.1208 var=18 __extULP_void.1209 var=76 g_b_InCriticalSection.1210 var=29 g_b_NMI_occurred.1211 var=71 __vola.1212 var=13) F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort (__ct_26.3862 BATSYS0.801 KEY_ID.802 KEY_PID.803 LFTUNEVBAT.804 PCON0.805 PRE3T.1005 PRECON10.807 PRECON11.982 PRECON2.809 PRECON3.810 PRECON4.811 PRECON5.812 PRECON6.813 PRECON7.814 PRECON8.815 PRECON9.816 PREPD.989 PRESTAT.818 PRESWUP0.819 PRESWUP1.820 PRESWUP2.821 PRET.997 WUP1W0.1121 WUP1W1.1147 WUP2W0.1058 WUP2W1.1084 WUP3W0.827 __extDM.828 __extDM_SFR_BATSYS0_t.829 __extDM_SFR_LFTUNEVBAT_t.830 __extDM_SFR_PCON0_t.831 __extDM_SFR_PRECON10_t.832 __extDM_SFR_PRECON11_t.833 __extDM_SFR_PRECON2_t.834 __extDM_SFR_PRECON3_t.835 __extDM_SFR_PRECON4_t.836 __extDM_SFR_PRECON5_t.837 __extDM_SFR_PRECON6_t.838 __extDM_SFR_PRECON7_t.839 __extDM_SFR_PRECON8_t.840 __extDM_SFR_PRECON9_t.841 __extDM_SFR_PRESTAT_t.842 __extDM_SFR_PRESWUP0_t.843 __extDM_SFR_PRESWUP1_t.844 __extDM_SFR_byte.845 __extDM_SFR_word.846 __extDM_int16_.847 __extDM_int8_.848 __extDM_void.849 __extPM.850 __extPM_void.851 __extULP.852 __extULP_void.853 g_b_InCriticalSection.854 g_b_NMI_occurred.855 __vola.1148)  <1037>;
    } #105 off=132 nxt=106
    #106 off=132 nxt=107
    <495> {
      (pollingid.1214 var=22) store__pl_rd_res_reg_const_1_B2 (__tmp.3797 __ct_6t0.3335 pollingid.204 __sp.140)  <3512>;
      (__tmp.3797 var=454 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__tmp.1154)  <4012>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <1041>;
    } #107 off=133 nxt=358
    #358 off=133 nxt=109
    (__ptr_WUP3W0.3329 var=129) const_inp ()  <3313>;
    <490> {
      (__fch_pollingid.2978 var=686 stl=a_b0) load__pl_rd_res_reg_const___uchar_1_B2 (__ct_6t0.3335 pollingid.1214 __sp.140)  <3507>;
      (__fch_pollingid.3799 var=686 stl=RbL off=0) Rb_1_dr_move_a_b0_1_int8__B0 (__fch_pollingid.2978)  <4014>;
    } stp=0;
    <491> {
      (__tmp.1218 var=685 stl=a_b2 __seff.3468 var=960 stl=nz_flag_w) _ad_const_1_B1 (__fch_pollingid.3798)  <3508>;
      (__seff.3736 var=960 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3468)  <3956>;
      (__fch_pollingid.3798 var=686 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__fch_pollingid.3799)  <4013>;
      (__tmp.3801 var=685 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.1218)  <4016>;
    } stp=1;
    <492> {
      (__rt.3221 var=888 stl=a_b2 __seff.3470 var=961 stl=c_flag_w __seff.3471 var=962 stl=nz_flag_w) shl_const_1_B1 (__tmp.3800)  <3509>;
      (__seff.3735 var=962 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3471)  <3955>;
      (__tmp.3800 var=685 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__tmp.3801)  <4015>;
      (__seff.3802 var=961 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.3470)  <4017>;
      (__rt.3804 var=888 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__rt.3221)  <4019>;
    } stp=3;
    <493> {
      (__tmp.1224 var=681 stl=a_b2 __seff.3473 var=963 stl=nz_flag_w) _or_const_1_B1 (__rt.3803)  <3510>;
      (__seff.3734 var=963 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3473)  <3954>;
      (__rt.3803 var=888 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__rt.3804)  <4018>;
      (__tmp.3806 var=681 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.1224)  <4021>;
    } stp=4;
    <494> {
      (WUP3W0.1233 var=68 __vola.1234 var=13) store_const_1_B2 (__tmp.3805 __ptr_WUP3W0.3329 WUP3W0.1183 __vola.1212)  <3511>;
      (__tmp.3805 var=681 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__tmp.3806)  <4020>;
    } stp=6;
    call {
        () chess_separator_scheduler ()  <1060>;
    } #109 off=140 nxt=262
    #262 off=140 nxt=113
    (__ptr_WUP3W0__a1.3357 var=737) const_inp ()  <3341>;
    <488> {
      (__fch_pollingid.1235 var=472 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_6t0.3335 pollingid.1214 __sp.140)  <3505>;
      (__fch_pollingid.3808 var=472 stl=RwL off=0) Rw_1_dr_move_DMw_r_1_int16__B0 (__fch_pollingid.1235)  <4023>;
    } stp=0;
    <489> {
      (WUP3W0.1247 var=68 __vola.1248 var=13) store_extract_hi_const_1_B2 (__fch_pollingid.3807 __ptr_WUP3W0__a1.3357 WUP3W0.1233 __vola.1234)  <3506>;
      (__fch_pollingid.3807 var=472 stl=__cvT4) __cvT4_1_dr_move_RwL_1_int16_ (__fch_pollingid.3808)  <4022>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <1080>;
    } #113 off=142 nxt=289
    #289 off=142 nxt=117
    <487> {
      (PRECON2.1265 var=38 __vola.1266 var=13 __seff.3462 var=959 stl=nz_flag_w) load_const__ad_const_store_2_B1 (__ptr_PRECON2.3310 PRECON2.1165 __vola.1248)  <3504>;
      (__seff.3733 var=959 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3462)  <3953>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <1099>;
    } #117 off=144 nxt=296
    #296 off=144 nxt=119
    <486> {
      (PRECON2.1286 var=38 __vola.1287 var=13 __seff.3459 var=958 stl=nz_flag_w) load_const__ad_const_store_1_B1 (__ptr_PRECON2.3310 PRECON2.1265 __vola.1266)  <3503>;
      (__seff.3732 var=958 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3459)  <3952>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <1111>;
    } #119 off=146 nxt=120
    #120 off=146 nxt=121
    (__ptr_PRESTAT.3330 var=131) const_inp ()  <3314>;
    <485> {
      (PRESTAT.1297 var=69 __vola.1298 var=13) store_const_const_2_B1 (__ptr_PRESTAT.3330 PRESTAT.1174 __vola.1287)  <3502>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <1118>;
    } #121 off=148 nxt=398
    #398 off=148 nxt=378 tgt=132
    (__trgt.3364 var=941) const_inp ()  <3348>;
    <484> {
      (BATSYS0.1306 var=25 __vola.1307 var=13 __seff.3454 var=957 stl=nz_flag_w) load_const__ad_const_cmp_const_cc_eq__jump_const_1_B1 (__ptr_BATSYS0.3304 __trgt.3364 BATSYS0.1157 __vola.1298)  <3501>;
      (__seff.3731 var=957 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3454)  <3951>;
    } stp=0;
    if {
        {
            () if_expr (__either.3294)  <1234>;
            (__either.3294 var=937) undefined ()  <3273>;
        } #124
        {
            (__false.3295 var=936) const ()  <3274>;
        } #125
        {
            (__either.3297 var=937) undefined ()  <3277>;
            (__trgt.3365 var=942) const_inp ()  <3349>;
            <481> {
              (__tmp.2933 var=630 stl=a_b2 BATSYS0.1425 var=25 __vola.1426 var=13) load_const_mov_bf_const_const_1_B1 (__ptr_BATSYS0.3304 BATSYS0.1306 __vola.1307)  <3498>;
              (__tmp.3905 var=630 stl=RbL off=0) Rb_1_dr_move_a_b2_1_int8__B0 (__tmp.2933)  <4077>;
            } stp=0;
            <482> {
              (__apl_nz.3247 var=912 stl=nz_flag_w __seff.3450 var=955 stl=c_flag_w __seff.3451 var=956 stl=o_flag_w) cmp_const_1_B2 (__tmp.3904)  <3499>;
              (__apl_nz.3903 var=912 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.3247)  <4075>;
              (__tmp.3904 var=630 stl=a_b0) a_b0_1_dr_move_Rb_1_int8__B0 (__tmp.3905)  <4076>;
              (__seff.3906 var=956 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.3451)  <4078>;
              (__seff.3907 var=955 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.3450)  <4079>;
            } stp=2;
            <483> {
              () cc_ne__jump_const_1_B1 (__apl_nz.3902 __trgt.3365)  <3500>;
              (__apl_nz.3902 var=912 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.3903)  <4074>;
            } stp=3;
        } #378 off=150 nxt=132 tgt=135
        {
            (__vola.1435 var=13) merge (__vola.1307 __vola.1426)  <1252>;
            (BATSYS0.1436 var=25) merge (BATSYS0.1306 BATSYS0.1425)  <1253>;
            (__tmp.3282 var=550) merge (__false.3295 __either.3297)  <3209>;
        } #127
    } #123
    if {
        {
            () if_expr (__tmp.3282)  <1355>;
        } #130
        {
        } #135 off=156 nxt=137
        {
            #132 off=154 nxt=133
            <480> {
              () call_const_1_B1 (void_Power_Off.3338)  <3497>;
            } stp=0;
            call {
                (BATSYS0.1540 var=25 KEY_ID.1541 var=67 KEY_PID.1542 var=65 LFTUNEVBAT.1543 var=36 PCON0.1544 var=72 PRE3T.1545 var=61 PRECON10.1546 var=54 PRECON11.1547 var=56 PRECON2.1548 var=38 PRECON3.1549 var=40 PRECON4.1550 var=42 PRECON5.1551 var=44 PRECON6.1552 var=46 PRECON7.1553 var=48 PRECON8.1554 var=50 PRECON9.1555 var=52 PREPD.1556 var=58 PRESTAT.1557 var=69 PRESWUP0.1558 var=30 PRESWUP1.1559 var=32 PRESWUP2.1560 var=34 PRET.1561 var=60 WUP1W0.1562 var=62 WUP1W1.1563 var=63 WUP2W0.1564 var=64 WUP2W1.1565 var=66 WUP3W0.1566 var=68 __extDM.1567 var=17 __extDM_SFR_BATSYS0_t.1568 var=28 __extDM_SFR_LFTUNEVBAT_t.1569 var=37 __extDM_SFR_PCON0_t.1570 var=73 __extDM_SFR_PRECON10_t.1571 var=55 __extDM_SFR_PRECON11_t.1572 var=57 __extDM_SFR_PRECON2_t.1573 var=39 __extDM_SFR_PRECON3_t.1574 var=41 __extDM_SFR_PRECON4_t.1575 var=43 __extDM_SFR_PRECON5_t.1576 var=45 __extDM_SFR_PRECON6_t.1577 var=47 __extDM_SFR_PRECON7_t.1578 var=49 __extDM_SFR_PRECON8_t.1579 var=51 __extDM_SFR_PRECON9_t.1580 var=53 __extDM_SFR_PRESTAT_t.1581 var=70 __extDM_SFR_PRESWUP0_t.1582 var=31 __extDM_SFR_PRESWUP1_t.1583 var=33 __extDM_SFR_byte.1584 var=59 __extDM_SFR_word.1585 var=35 __extDM_int16_.1586 var=27 __extDM_int8_.1587 var=26 __extDM_void.1588 var=75 __extPM.1589 var=16 __extPM_void.1590 var=74 __extULP.1591 var=18 __extULP_void.1592 var=76 g_b_InCriticalSection.1593 var=29 g_b_NMI_occurred.1594 var=71 __vola.1595 var=13) Fvoid_Power_Off (BATSYS0.1436 KEY_ID.1158 KEY_PID.1159 LFTUNEVBAT.1160 PCON0.1161 PRE3T.1162 PRECON10.1163 PRECON11.1164 PRECON2.1286 PRECON3.1166 PRECON4.1167 PRECON5.1168 PRECON6.1169 PRECON7.1170 PRECON8.1171 PRECON9.1172 PREPD.1173 PRESTAT.1297 PRESWUP0.1175 PRESWUP1.1176 PRESWUP2.1177 PRET.1178 WUP1W0.1179 WUP1W1.1180 WUP2W0.1181 WUP2W1.1182 WUP3W0.1247 __extDM.1184 __extDM_SFR_BATSYS0_t.1185 __extDM_SFR_LFTUNEVBAT_t.1186 __extDM_SFR_PCON0_t.1187 __extDM_SFR_PRECON10_t.1188 __extDM_SFR_PRECON11_t.1189 __extDM_SFR_PRECON2_t.1190 __extDM_SFR_PRECON3_t.1191 __extDM_SFR_PRECON4_t.1192 __extDM_SFR_PRECON5_t.1193 __extDM_SFR_PRECON6_t.1194 __extDM_SFR_PRECON7_t.1195 __extDM_SFR_PRECON8_t.1196 __extDM_SFR_PRECON9_t.1197 __extDM_SFR_PRESTAT_t.1198 __extDM_SFR_PRESWUP0_t.1199 __extDM_SFR_PRESWUP1_t.1200 __extDM_SFR_byte.1201 __extDM_SFR_word.1202 __extDM_int16_.1203 __extDM_int8_.1204 __extDM_void.1205 __extPM.1206 __extPM_void.1207 __extULP.1208 __extULP_void.1209 g_b_InCriticalSection.1210 g_b_NMI_occurred.1211 __vola.1435)  <1359>;
            } #133 off=156 nxt=134
            #134 nxt=-4
            () sink (__vola.1595)  <1360>;
            () sink (__extPM.1589)  <1363>;
            () sink (__extDM.1567)  <1364>;
            () sink (__extULP.1591)  <1365>;
            () sink (__sp.140)  <1366>;
            () sink (u8_save_bits.748)  <1367>;
            () sink (Temp_Buf.200)  <1368>;
            () sink (pollingid.1214)  <1369>;
            () sink (gaincfg.858)  <1370>;
            () sink (swversion.212)  <1371>;
            () sink (BATSYS0.1540)  <1372>;
            () sink (__extDM_int8_.1587)  <1373>;
            () sink (__extDM_int16_.1586)  <1374>;
            () sink (__extDM_SFR_BATSYS0_t.1568)  <1375>;
            () sink (g_b_InCriticalSection.1593)  <1376>;
            () sink (PRESWUP0.1558)  <1377>;
            () sink (__extDM_SFR_PRESWUP0_t.1582)  <1378>;
            () sink (PRESWUP1.1559)  <1379>;
            () sink (__extDM_SFR_PRESWUP1_t.1583)  <1380>;
            () sink (PRESWUP2.1560)  <1381>;
            () sink (__extDM_SFR_word.1585)  <1382>;
            () sink (LFTUNEVBAT.1543)  <1383>;
            () sink (__extDM_SFR_LFTUNEVBAT_t.1569)  <1384>;
            () sink (PRECON2.1548)  <1385>;
            () sink (__extDM_SFR_PRECON2_t.1573)  <1386>;
            () sink (PRECON3.1549)  <1387>;
            () sink (__extDM_SFR_PRECON3_t.1574)  <1388>;
            () sink (PRECON4.1550)  <1389>;
            () sink (__extDM_SFR_PRECON4_t.1575)  <1390>;
            () sink (PRECON5.1551)  <1391>;
            () sink (__extDM_SFR_PRECON5_t.1576)  <1392>;
            () sink (PRECON6.1552)  <1393>;
            () sink (__extDM_SFR_PRECON6_t.1577)  <1394>;
            () sink (PRECON7.1553)  <1395>;
            () sink (__extDM_SFR_PRECON7_t.1578)  <1396>;
            () sink (PRECON8.1554)  <1397>;
            () sink (__extDM_SFR_PRECON8_t.1579)  <1398>;
            () sink (PRECON9.1555)  <1399>;
            () sink (__extDM_SFR_PRECON9_t.1580)  <1400>;
            () sink (PRECON10.1546)  <1401>;
            () sink (__extDM_SFR_PRECON10_t.1571)  <1402>;
            () sink (PRECON11.1547)  <1403>;
            () sink (__extDM_SFR_PRECON11_t.1572)  <1404>;
            () sink (PREPD.1556)  <1405>;
            () sink (__extDM_SFR_byte.1584)  <1406>;
            () sink (PRET.1561)  <1407>;
            () sink (PRE3T.1545)  <1408>;
            () sink (WUP1W0.1562)  <1409>;
            () sink (WUP1W1.1563)  <1410>;
            () sink (WUP2W0.1564)  <1411>;
            () sink (KEY_PID.1542)  <1412>;
            () sink (WUP2W1.1565)  <1413>;
            () sink (KEY_ID.1541)  <1414>;
            () sink (WUP3W0.1566)  <1415>;
            () sink (PRESTAT.1557)  <1416>;
            () sink (__extDM_SFR_PRESTAT_t.1581)  <1417>;
            () sink (g_b_NMI_occurred.1594)  <1418>;
            () sink (PCON0.1544)  <1419>;
            () sink (__extDM_SFR_PCON0_t.1570)  <1420>;
            () sink (__extPM_void.1590)  <1421>;
            () sink (__extDM_void.1588)  <1422>;
            () sink (__extULP_void.1592)  <1423>;
        } #131
        {
        } #136
    } #129
    #137 off=156 nxt=138
    <479> {
      () call_const_1_B1 (void_phcaiKEyLLGenFunc_CS_SetBatPORFlag_bool_t.3339)  <3496>;
    } stp=1;
    <727> {
      (__ct_0.3865 var=553 stl=a_b0) const_4_B2 ()  <3878>;
      (__ct_0.3864 var=553 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_0.3865)  <4055>;
    } stp=0;
    call {
        (BATSYS0.1803 var=25 KEY_ID.1804 var=67 KEY_PID.1805 var=65 LFTUNEVBAT.1806 var=36 PCON0.1807 var=72 PRE3T.1808 var=61 PRECON10.1809 var=54 PRECON11.1810 var=56 PRECON2.1811 var=38 PRECON3.1812 var=40 PRECON4.1813 var=42 PRECON5.1814 var=44 PRECON6.1815 var=46 PRECON7.1816 var=48 PRECON8.1817 var=50 PRECON9.1818 var=52 PREPD.1819 var=58 PRESTAT.1820 var=69 PRESWUP0.1821 var=30 PRESWUP1.1822 var=32 PRESWUP2.1823 var=34 PRET.1824 var=60 WUP1W0.1825 var=62 WUP1W1.1826 var=63 WUP2W0.1827 var=64 WUP2W1.1828 var=66 WUP3W0.1829 var=68 __extDM.1830 var=17 __extDM_SFR_BATSYS0_t.1831 var=28 __extDM_SFR_LFTUNEVBAT_t.1832 var=37 __extDM_SFR_PCON0_t.1833 var=73 __extDM_SFR_PRECON10_t.1834 var=55 __extDM_SFR_PRECON11_t.1835 var=57 __extDM_SFR_PRECON2_t.1836 var=39 __extDM_SFR_PRECON3_t.1837 var=41 __extDM_SFR_PRECON4_t.1838 var=43 __extDM_SFR_PRECON5_t.1839 var=45 __extDM_SFR_PRECON6_t.1840 var=47 __extDM_SFR_PRECON7_t.1841 var=49 __extDM_SFR_PRECON8_t.1842 var=51 __extDM_SFR_PRECON9_t.1843 var=53 __extDM_SFR_PRESTAT_t.1844 var=70 __extDM_SFR_PRESWUP0_t.1845 var=31 __extDM_SFR_PRESWUP1_t.1846 var=33 __extDM_SFR_byte.1847 var=59 __extDM_SFR_word.1848 var=35 __extDM_int16_.1849 var=27 __extDM_int8_.1850 var=26 __extDM_void.1851 var=75 __extPM.1852 var=16 __extPM_void.1853 var=74 __extULP.1854 var=18 __extULP_void.1855 var=76 g_b_InCriticalSection.1856 var=29 g_b_NMI_occurred.1857 var=71 __vola.1858 var=13) Fvoid_phcaiKEyLLGenFunc_CS_SetBatPORFlag_bool_t (__ct_0.3864 BATSYS0.1436 KEY_ID.1158 KEY_PID.1159 LFTUNEVBAT.1160 PCON0.1161 PRE3T.1162 PRECON10.1163 PRECON11.1164 PRECON2.1286 PRECON3.1166 PRECON4.1167 PRECON5.1168 PRECON6.1169 PRECON7.1170 PRECON8.1171 PRECON9.1172 PREPD.1173 PRESTAT.1297 PRESWUP0.1175 PRESWUP1.1176 PRESWUP2.1177 PRET.1178 WUP1W0.1179 WUP1W1.1180 WUP2W0.1181 WUP2W1.1182 WUP3W0.1247 __extDM.1184 __extDM_SFR_BATSYS0_t.1185 __extDM_SFR_LFTUNEVBAT_t.1186 __extDM_SFR_PCON0_t.1187 __extDM_SFR_PRECON10_t.1188 __extDM_SFR_PRECON11_t.1189 __extDM_SFR_PRECON2_t.1190 __extDM_SFR_PRECON3_t.1191 __extDM_SFR_PRECON4_t.1192 __extDM_SFR_PRECON5_t.1193 __extDM_SFR_PRECON6_t.1194 __extDM_SFR_PRECON7_t.1195 __extDM_SFR_PRECON8_t.1196 __extDM_SFR_PRECON9_t.1197 __extDM_SFR_PRESTAT_t.1198 __extDM_SFR_PRESWUP0_t.1199 __extDM_SFR_PRESWUP1_t.1200 __extDM_SFR_byte.1201 __extDM_SFR_word.1202 __extDM_int16_.1203 __extDM_int8_.1204 __extDM_void.1205 __extPM.1206 __extPM_void.1207 __extULP.1208 __extULP_void.1209 g_b_InCriticalSection.1210 g_b_NMI_occurred.1211 __vola.1435)  <1632>;
    } #138 off=159 nxt=140
    #140 off=159 nxt=141
    <477> {
      () call_const_1_B1 (__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort.3341)  <3494>;
    } stp=2;
    <728> {
      (__ct_14.3867 var=557 stl=__CTa_w0_uint16__cstP16_E1) const_3_B1 ()  <3880>;
      (__ct_14.3866 var=557 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_1_uint16_1_32768__B0 (__ct_14.3867)  <4056>;
    } stp=0;
    call {
        (__tmp.1864 var=561 stl=RwL off=0 BATSYS0.1867 var=25 KEY_ID.1868 var=67 KEY_PID.1869 var=65 LFTUNEVBAT.1870 var=36 PCON0.1871 var=72 PRE3T.1872 var=61 PRECON10.1873 var=54 PRECON11.1874 var=56 PRECON2.1875 var=38 PRECON3.1876 var=40 PRECON4.1877 var=42 PRECON5.1878 var=44 PRECON6.1879 var=46 PRECON7.1880 var=48 PRECON8.1881 var=50 PRECON9.1882 var=52 PREPD.1883 var=58 PRESTAT.1884 var=69 PRESWUP0.1885 var=30 PRESWUP1.1886 var=32 PRESWUP2.1887 var=34 PRET.1888 var=60 WUP1W0.1889 var=62 WUP1W1.1890 var=63 WUP2W0.1891 var=64 WUP2W1.1892 var=66 WUP3W0.1893 var=68 __extDM.1894 var=17 __extDM_SFR_BATSYS0_t.1895 var=28 __extDM_SFR_LFTUNEVBAT_t.1896 var=37 __extDM_SFR_PCON0_t.1897 var=73 __extDM_SFR_PRECON10_t.1898 var=55 __extDM_SFR_PRECON11_t.1899 var=57 __extDM_SFR_PRECON2_t.1900 var=39 __extDM_SFR_PRECON3_t.1901 var=41 __extDM_SFR_PRECON4_t.1902 var=43 __extDM_SFR_PRECON5_t.1903 var=45 __extDM_SFR_PRECON6_t.1904 var=47 __extDM_SFR_PRECON7_t.1905 var=49 __extDM_SFR_PRECON8_t.1906 var=51 __extDM_SFR_PRECON9_t.1907 var=53 __extDM_SFR_PRESTAT_t.1908 var=70 __extDM_SFR_PRESWUP0_t.1909 var=31 __extDM_SFR_PRESWUP1_t.1910 var=33 __extDM_SFR_byte.1911 var=59 __extDM_SFR_word.1912 var=35 __extDM_int16_.1913 var=27 __extDM_int8_.1914 var=26 __extDM_void.1915 var=75 __extPM.1916 var=16 __extPM_void.1917 var=74 __extULP.1918 var=18 __extULP_void.1919 var=76 g_b_InCriticalSection.1920 var=29 g_b_NMI_occurred.1921 var=71 __vola.1922 var=13) F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort (__ct_14.3866 BATSYS0.1803 KEY_ID.1804 KEY_PID.1805 LFTUNEVBAT.1806 PCON0.1807 PRE3T.1808 PRECON10.1809 PRECON11.1810 PRECON2.1811 PRECON3.1812 PRECON4.1813 PRECON5.1814 PRECON6.1815 PRECON7.1816 PRECON8.1817 PRECON9.1818 PREPD.1819 PRESTAT.1820 PRESWUP0.1821 PRESWUP1.1822 PRESWUP2.1823 PRET.1824 WUP1W0.1825 WUP1W1.1826 WUP2W0.1827 WUP2W1.1828 WUP3W0.1829 __extDM.1830 __extDM_SFR_BATSYS0_t.1831 __extDM_SFR_LFTUNEVBAT_t.1832 __extDM_SFR_PCON0_t.1833 __extDM_SFR_PRECON10_t.1834 __extDM_SFR_PRECON11_t.1835 __extDM_SFR_PRECON2_t.1836 __extDM_SFR_PRECON3_t.1837 __extDM_SFR_PRECON4_t.1838 __extDM_SFR_PRECON5_t.1839 __extDM_SFR_PRECON6_t.1840 __extDM_SFR_PRECON7_t.1841 __extDM_SFR_PRECON8_t.1842 __extDM_SFR_PRECON9_t.1843 __extDM_SFR_PRESTAT_t.1844 __extDM_SFR_PRESWUP0_t.1845 __extDM_SFR_PRESWUP1_t.1846 __extDM_SFR_byte.1847 __extDM_SFR_word.1848 __extDM_int16_.1849 __extDM_int8_.1850 __extDM_void.1851 __extPM.1852 __extPM_void.1853 __extULP.1854 __extULP_void.1855 g_b_InCriticalSection.1856 g_b_NMI_occurred.1857 __vola.1858)  <1640>;
    } #141 off=163 nxt=385
    #385 off=163 nxt=146 tgt=160
    (__trgt.3366 var=943) const_inp ()  <3350>;
    <474> {
      (__apl_nz.3253 var=918 stl=nz_flag_w) _ad_const_cmp_const_1_B1 (__tmp.3812)  <3491>;
      (__apl_nz.3738 var=918 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.3253)  <3958>;
      (__tmp.3812 var=561 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B0 (__tmp.1864)  <4024>;
    } stp=0;
    <475> {
      () cc_eq__jump_const_1_B1 (__apl_nz.3737 __trgt.3366)  <3492>;
      (__apl_nz.3737 var=918 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.3738)  <3957>;
    } stp=2;
    if {
        {
            () if_expr (__either.3299)  <1750>;
            (__either.3299 var=937) undefined ()  <3280>;
        } #144
        {
        } #160 off=184 nxt=392
        {
            #146 off=166 nxt=147
            (void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort.3342 var=570) const_inp ()  <3326>;
            <473> {
              () call_const_1_B1 (void_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort.3342)  <3490>;
            } stp=3;
            <729> {
              (__ct_3.3869 var=568 stl=a_w0) const_2_B2 ()  <3882>;
              (__ct_3.3868 var=568 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_3.3869)  <4057>;
            } stp=0;
            <737> {
              (__adr_Temp_Buf.3886 var=146 stl=a_w2 __side_effect.3887 var=988 stl=c_flag_w __side_effect.3889 var=988 stl=nz_flag_w __side_effect.3891 var=988 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_1t0.3334 __sp.140)  <3899>;
              (__adr_Temp_Buf.3885 var=146 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_Temp_Buf.3886)  <4066>;
              (__side_effect.3888 var=988 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__side_effect.3887)  <4067>;
              (__side_effect.3890 var=988 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.3889)  <4068>;
              (__side_effect.3892 var=988 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.3891)  <4069>;
            } stp=1;
            call {
                (BATSYS0.2036 var=25 KEY_ID.2037 var=67 KEY_PID.2038 var=65 LFTUNEVBAT.2039 var=36 PCON0.2040 var=72 PRE3T.2041 var=61 PRECON10.2042 var=54 PRECON11.2043 var=56 PRECON2.2044 var=38 PRECON3.2045 var=40 PRECON4.2046 var=42 PRECON5.2047 var=44 PRECON6.2048 var=46 PRECON7.2049 var=48 PRECON8.2050 var=50 PRECON9.2051 var=52 PREPD.2052 var=58 PRESTAT.2053 var=69 PRESWUP0.2054 var=30 PRESWUP1.2055 var=32 PRESWUP2.2056 var=34 PRET.2057 var=60 Temp_Buf.2058 var=21 WUP1W0.2059 var=62 WUP1W1.2060 var=63 WUP2W0.2061 var=64 WUP2W1.2062 var=66 WUP3W0.2063 var=68 __extDM.2064 var=17 __extDM_SFR_BATSYS0_t.2065 var=28 __extDM_SFR_LFTUNEVBAT_t.2066 var=37 __extDM_SFR_PCON0_t.2067 var=73 __extDM_SFR_PRECON10_t.2068 var=55 __extDM_SFR_PRECON11_t.2069 var=57 __extDM_SFR_PRECON2_t.2070 var=39 __extDM_SFR_PRECON3_t.2071 var=41 __extDM_SFR_PRECON4_t.2072 var=43 __extDM_SFR_PRECON5_t.2073 var=45 __extDM_SFR_PRECON6_t.2074 var=47 __extDM_SFR_PRECON7_t.2075 var=49 __extDM_SFR_PRECON8_t.2076 var=51 __extDM_SFR_PRECON9_t.2077 var=53 __extDM_SFR_PRESTAT_t.2078 var=70 __extDM_SFR_PRESWUP0_t.2079 var=31 __extDM_SFR_PRESWUP1_t.2080 var=33 __extDM_SFR_byte.2081 var=59 __extDM_SFR_word.2082 var=35 __extDM_int16_.2083 var=27 __extDM_int8_.2084 var=26 __extDM_void.2085 var=75 __extPM.2086 var=16 __extPM_void.2087 var=74 __extULP.2088 var=18 __extULP_void.2089 var=76 g_b_InCriticalSection.2090 var=29 g_b_NMI_occurred.2091 var=71 __vola.2092 var=13) Fvoid_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort (__adr_Temp_Buf.3885 __ct_3.3868 BATSYS0.1867 KEY_ID.1868 KEY_PID.1869 LFTUNEVBAT.1870 PCON0.1871 PRE3T.1872 PRECON10.1873 PRECON11.1874 PRECON2.1875 PRECON3.1876 PRECON4.1877 PRECON5.1878 PRECON6.1879 PRECON7.1880 PRECON8.1881 PRECON9.1882 PREPD.1883 PRESTAT.1884 PRESWUP0.1885 PRESWUP1.1886 PRESWUP2.1887 PRET.1888 Temp_Buf.200 WUP1W0.1889 WUP1W1.1890 WUP2W0.1891 WUP2W1.1892 WUP3W0.1893 __extDM.1894 __extDM_SFR_BATSYS0_t.1895 __extDM_SFR_LFTUNEVBAT_t.1896 __extDM_SFR_PCON0_t.1897 __extDM_SFR_PRECON10_t.1898 __extDM_SFR_PRECON11_t.1899 __extDM_SFR_PRECON2_t.1900 __extDM_SFR_PRECON3_t.1901 __extDM_SFR_PRECON4_t.1902 __extDM_SFR_PRECON5_t.1903 __extDM_SFR_PRECON6_t.1904 __extDM_SFR_PRECON7_t.1905 __extDM_SFR_PRECON8_t.1906 __extDM_SFR_PRECON9_t.1907 __extDM_SFR_PRESTAT_t.1908 __extDM_SFR_PRESWUP0_t.1909 __extDM_SFR_PRESWUP1_t.1910 __extDM_SFR_byte.1911 __extDM_SFR_word.1912 __extDM_int16_.1913 __extDM_int8_.1914 __extDM_void.1915 __extPM.1916 __extPM_void.1917 __extULP.1918 __extULP_void.1919 g_b_InCriticalSection.1920 g_b_NMI_occurred.1921 __vola.1922)  <1758>;
            } #147 off=171 nxt=149
            #149 off=171 nxt=150
            (void_timer_delay_ms___ushort.3343 var=574) const_inp ()  <3327>;
            <471> {
              () call_const_1_B1 (void_timer_delay_ms___ushort.3343)  <3488>;
            } stp=1;
            <730> {
              (__ct_4.3871 var=572 stl=a_w0) const_1_B2 ()  <3884>;
              (__ct_4.3870 var=572 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_4.3871)  <4058>;
            } stp=0;
            call {
                (BATSYS0.2098 var=25 KEY_ID.2099 var=67 KEY_PID.2100 var=65 LFTUNEVBAT.2101 var=36 PCON0.2102 var=72 PRE3T.2103 var=61 PRECON10.2104 var=54 PRECON11.2105 var=56 PRECON2.2106 var=38 PRECON3.2107 var=40 PRECON4.2108 var=42 PRECON5.2109 var=44 PRECON6.2110 var=46 PRECON7.2111 var=48 PRECON8.2112 var=50 PRECON9.2113 var=52 PREPD.2114 var=58 PRESTAT.2115 var=69 PRESWUP0.2116 var=30 PRESWUP1.2117 var=32 PRESWUP2.2118 var=34 PRET.2119 var=60 Temp_Buf.2120 var=21 WUP1W0.2121 var=62 WUP1W1.2122 var=63 WUP2W0.2123 var=64 WUP2W1.2124 var=66 WUP3W0.2125 var=68 __extDM.2126 var=17 __extDM_SFR_BATSYS0_t.2127 var=28 __extDM_SFR_LFTUNEVBAT_t.2128 var=37 __extDM_SFR_PCON0_t.2129 var=73 __extDM_SFR_PRECON10_t.2130 var=55 __extDM_SFR_PRECON11_t.2131 var=57 __extDM_SFR_PRECON2_t.2132 var=39 __extDM_SFR_PRECON3_t.2133 var=41 __extDM_SFR_PRECON4_t.2134 var=43 __extDM_SFR_PRECON5_t.2135 var=45 __extDM_SFR_PRECON6_t.2136 var=47 __extDM_SFR_PRECON7_t.2137 var=49 __extDM_SFR_PRECON8_t.2138 var=51 __extDM_SFR_PRECON9_t.2139 var=53 __extDM_SFR_PRESTAT_t.2140 var=70 __extDM_SFR_PRESWUP0_t.2141 var=31 __extDM_SFR_PRESWUP1_t.2142 var=33 __extDM_SFR_byte.2143 var=59 __extDM_SFR_word.2144 var=35 __extDM_int16_.2145 var=27 __extDM_int8_.2146 var=26 __extDM_void.2147 var=75 __extPM.2148 var=16 __extPM_void.2149 var=74 __extULP.2150 var=18 __extULP_void.2151 var=76 g_b_InCriticalSection.2152 var=29 g_b_NMI_occurred.2153 var=71 __vola.2154 var=13) Fvoid_timer_delay_ms___ushort (__ct_4.3870 BATSYS0.2036 KEY_ID.2037 KEY_PID.2038 LFTUNEVBAT.2039 PCON0.2040 PRE3T.2041 PRECON10.2042 PRECON11.2043 PRECON2.2044 PRECON3.2045 PRECON4.2046 PRECON5.2047 PRECON6.2048 PRECON7.2049 PRECON8.2050 PRECON9.2051 PREPD.2052 PRESTAT.2053 PRESWUP0.2054 PRESWUP1.2055 PRESWUP2.2056 PRET.2057 Temp_Buf.2058 WUP1W0.2059 WUP1W1.2060 WUP2W0.2061 WUP2W1.2062 WUP3W0.2063 __extDM.2064 __extDM_SFR_BATSYS0_t.2065 __extDM_SFR_LFTUNEVBAT_t.2066 __extDM_SFR_PCON0_t.2067 __extDM_SFR_PRECON10_t.2068 __extDM_SFR_PRECON11_t.2069 __extDM_SFR_PRECON2_t.2070 __extDM_SFR_PRECON3_t.2071 __extDM_SFR_PRECON4_t.2072 __extDM_SFR_PRECON5_t.2073 __extDM_SFR_PRECON6_t.2074 __extDM_SFR_PRECON7_t.2075 __extDM_SFR_PRECON8_t.2076 __extDM_SFR_PRECON9_t.2077 __extDM_SFR_PRESTAT_t.2078 __extDM_SFR_PRESWUP0_t.2079 __extDM_SFR_PRESWUP1_t.2080 __extDM_SFR_byte.2081 __extDM_SFR_word.2082 __extDM_int16_.2083 __extDM_int8_.2084 __extDM_void.2085 __extPM.2086 __extPM_void.2087 __extULP.2088 __extULP_void.2089 g_b_InCriticalSection.2090 g_b_NMI_occurred.2091 __vola.2092)  <1766>;
            } #150 off=174 nxt=152
            #152 off=174 nxt=153
            <469> {
              (Temp_Buf.2168 var=21 __seff.3440 var=954 stl=nz_flag_w) load__pl_rd_res_reg_const__ad_const_store_1_B1 (__ct_1t0.3334 Temp_Buf.2120 Temp_Buf.2120 __sp.140)  <3486>;
              (__seff.3739 var=954 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3440)  <3959>;
            } stp=0;
            <742> {
              (__adr_Temp_Buf.3895 var=146 stl=a_w2 __side_effect.3896 var=988 stl=c_flag_w __side_effect.3898 var=988 stl=nz_flag_w __side_effect.3900 var=988 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_1t0.3334 __sp.140)  <3908>;
              (__adr_Temp_Buf.3894 var=146 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_Temp_Buf.3895)  <4070>;
              (__side_effect.3897 var=988 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__side_effect.3896)  <4071>;
              (__side_effect.3899 var=988 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.3898)  <4072>;
              (__side_effect.3901 var=988 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.3900)  <4073>;
            } stp=2;
            call {
                () chess_separator_scheduler ()  <1781>;
            } #153 off=178 nxt=154
            #154 off=178 nxt=155
            (error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort.3344 var=590) const_inp ()  <3328>;
            <468> {
              () call_const_1_B1 (error_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort.3344)  <3485>;
            } stp=1;
            <731> {
              (__ct_3.3873 var=588 stl=a_w0) const_2_B2 ()  <3886>;
              (__ct_3.3872 var=588 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_3.3873)  <4059>;
            } stp=0;
            call {
                (__tmp.2175 var=592 stl=RbL off=0 BATSYS0.2178 var=25 KEY_ID.2179 var=67 KEY_PID.2180 var=65 LFTUNEVBAT.2181 var=36 PCON0.2182 var=72 PRE3T.2183 var=61 PRECON10.2184 var=54 PRECON11.2185 var=56 PRECON2.2186 var=38 PRECON3.2187 var=40 PRECON4.2188 var=42 PRECON5.2189 var=44 PRECON6.2190 var=46 PRECON7.2191 var=48 PRECON8.2192 var=50 PRECON9.2193 var=52 PREPD.2194 var=58 PRESTAT.2195 var=69 PRESWUP0.2196 var=30 PRESWUP1.2197 var=32 PRESWUP2.2198 var=34 PRET.2199 var=60 Temp_Buf.2200 var=21 WUP1W0.2201 var=62 WUP1W1.2202 var=63 WUP2W0.2203 var=64 WUP2W1.2204 var=66 WUP3W0.2205 var=68 __extDM.2206 var=17 __extDM_SFR_BATSYS0_t.2207 var=28 __extDM_SFR_LFTUNEVBAT_t.2208 var=37 __extDM_SFR_PCON0_t.2209 var=73 __extDM_SFR_PRECON10_t.2210 var=55 __extDM_SFR_PRECON11_t.2211 var=57 __extDM_SFR_PRECON2_t.2212 var=39 __extDM_SFR_PRECON3_t.2213 var=41 __extDM_SFR_PRECON4_t.2214 var=43 __extDM_SFR_PRECON5_t.2215 var=45 __extDM_SFR_PRECON6_t.2216 var=47 __extDM_SFR_PRECON7_t.2217 var=49 __extDM_SFR_PRECON8_t.2218 var=51 __extDM_SFR_PRECON9_t.2219 var=53 __extDM_SFR_PRESTAT_t.2220 var=70 __extDM_SFR_PRESWUP0_t.2221 var=31 __extDM_SFR_PRESWUP1_t.2222 var=33 __extDM_SFR_byte.2223 var=59 __extDM_SFR_word.2224 var=35 __extDM_int16_.2225 var=27 __extDM_int8_.2226 var=26 __extDM_void.2227 var=75 __extPM.2228 var=16 __extPM_void.2229 var=74 __extULP.2230 var=18 __extULP_void.2231 var=76 g_b_InCriticalSection.2232 var=29 g_b_NMI_occurred.2233 var=71 __vola.2234 var=13) Ferror_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort (__adr_Temp_Buf.3894 __ct_3.3872 BATSYS0.2098 KEY_ID.2099 KEY_PID.2100 LFTUNEVBAT.2101 PCON0.2102 PRE3T.2103 PRECON10.2104 PRECON11.2105 PRECON2.2106 PRECON3.2107 PRECON4.2108 PRECON5.2109 PRECON6.2110 PRECON7.2111 PRECON8.2112 PRECON9.2113 PREPD.2114 PRESTAT.2115 PRESWUP0.2116 PRESWUP1.2117 PRESWUP2.2118 PRET.2119 Temp_Buf.2168 WUP1W0.2121 WUP1W1.2122 WUP2W0.2123 WUP2W1.2124 WUP3W0.2125 __extDM.2126 __extDM_SFR_BATSYS0_t.2127 __extDM_SFR_LFTUNEVBAT_t.2128 __extDM_SFR_PCON0_t.2129 __extDM_SFR_PRECON10_t.2130 __extDM_SFR_PRECON11_t.2131 __extDM_SFR_PRECON2_t.2132 __extDM_SFR_PRECON3_t.2133 __extDM_SFR_PRECON4_t.2134 __extDM_SFR_PRECON5_t.2135 __extDM_SFR_PRECON6_t.2136 __extDM_SFR_PRECON7_t.2137 __extDM_SFR_PRECON8_t.2138 __extDM_SFR_PRECON9_t.2139 __extDM_SFR_PRESTAT_t.2140 __extDM_SFR_PRESWUP0_t.2141 __extDM_SFR_PRESWUP1_t.2142 __extDM_SFR_byte.2143 __extDM_SFR_word.2144 __extDM_int16_.2145 __extDM_int8_.2146 __extDM_void.2147 __extPM.2148 __extPM_void.2149 __extULP.2150 __extULP_void.2151 g_b_InCriticalSection.2152 g_b_NMI_occurred.2153 __vola.2154)  <1789>;
            } #155 off=181 nxt=157
            #157 off=181 nxt=158
            <466> {
              () call_const_1_B1 (void_timer_delay_ms___ushort.3343)  <3483>;
            } stp=1;
            <732> {
              (__ct_4.3875 var=593 stl=a_w0) const_1_B2 ()  <3888>;
              (__ct_4.3874 var=593 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_4.3875)  <4060>;
            } stp=0;
            call {
                (BATSYS0.2240 var=25 KEY_ID.2241 var=67 KEY_PID.2242 var=65 LFTUNEVBAT.2243 var=36 PCON0.2244 var=72 PRE3T.2245 var=61 PRECON10.2246 var=54 PRECON11.2247 var=56 PRECON2.2248 var=38 PRECON3.2249 var=40 PRECON4.2250 var=42 PRECON5.2251 var=44 PRECON6.2252 var=46 PRECON7.2253 var=48 PRECON8.2254 var=50 PRECON9.2255 var=52 PREPD.2256 var=58 PRESTAT.2257 var=69 PRESWUP0.2258 var=30 PRESWUP1.2259 var=32 PRESWUP2.2260 var=34 PRET.2261 var=60 Temp_Buf.2262 var=21 WUP1W0.2263 var=62 WUP1W1.2264 var=63 WUP2W0.2265 var=64 WUP2W1.2266 var=66 WUP3W0.2267 var=68 __extDM.2268 var=17 __extDM_SFR_BATSYS0_t.2269 var=28 __extDM_SFR_LFTUNEVBAT_t.2270 var=37 __extDM_SFR_PCON0_t.2271 var=73 __extDM_SFR_PRECON10_t.2272 var=55 __extDM_SFR_PRECON11_t.2273 var=57 __extDM_SFR_PRECON2_t.2274 var=39 __extDM_SFR_PRECON3_t.2275 var=41 __extDM_SFR_PRECON4_t.2276 var=43 __extDM_SFR_PRECON5_t.2277 var=45 __extDM_SFR_PRECON6_t.2278 var=47 __extDM_SFR_PRECON7_t.2279 var=49 __extDM_SFR_PRECON8_t.2280 var=51 __extDM_SFR_PRECON9_t.2281 var=53 __extDM_SFR_PRESTAT_t.2282 var=70 __extDM_SFR_PRESWUP0_t.2283 var=31 __extDM_SFR_PRESWUP1_t.2284 var=33 __extDM_SFR_byte.2285 var=59 __extDM_SFR_word.2286 var=35 __extDM_int16_.2287 var=27 __extDM_int8_.2288 var=26 __extDM_void.2289 var=75 __extPM.2290 var=16 __extPM_void.2291 var=74 __extULP.2292 var=18 __extULP_void.2293 var=76 g_b_InCriticalSection.2294 var=29 g_b_NMI_occurred.2295 var=71 __vola.2296 var=13) Fvoid_timer_delay_ms___ushort (__ct_4.3874 BATSYS0.2178 KEY_ID.2179 KEY_PID.2180 LFTUNEVBAT.2181 PCON0.2182 PRE3T.2183 PRECON10.2184 PRECON11.2185 PRECON2.2186 PRECON3.2187 PRECON4.2188 PRECON5.2189 PRECON6.2190 PRECON7.2191 PRECON8.2192 PRECON9.2193 PREPD.2194 PRESTAT.2195 PRESWUP0.2196 PRESWUP1.2197 PRESWUP2.2198 PRET.2199 Temp_Buf.2200 WUP1W0.2201 WUP1W1.2202 WUP2W0.2203 WUP2W1.2204 WUP3W0.2205 __extDM.2206 __extDM_SFR_BATSYS0_t.2207 __extDM_SFR_LFTUNEVBAT_t.2208 __extDM_SFR_PCON0_t.2209 __extDM_SFR_PRECON10_t.2210 __extDM_SFR_PRECON11_t.2211 __extDM_SFR_PRECON2_t.2212 __extDM_SFR_PRECON3_t.2213 __extDM_SFR_PRECON4_t.2214 __extDM_SFR_PRECON5_t.2215 __extDM_SFR_PRECON6_t.2216 __extDM_SFR_PRECON7_t.2217 __extDM_SFR_PRECON8_t.2218 __extDM_SFR_PRECON9_t.2219 __extDM_SFR_PRESTAT_t.2220 __extDM_SFR_PRESWUP0_t.2221 __extDM_SFR_PRESWUP1_t.2222 __extDM_SFR_byte.2223 __extDM_SFR_word.2224 __extDM_int16_.2225 __extDM_int8_.2226 __extDM_void.2227 __extPM.2228 __extPM_void.2229 __extULP.2230 __extULP_void.2231 g_b_InCriticalSection.2232 g_b_NMI_occurred.2233 __vola.2234)  <1799>;
            } #158 off=184 nxt=410
            #410 off=184 nxt=392
        } #145
        {
            (__vola.2297 var=13) merge (__vola.1922 __vola.2296)  <1801>;
            (__extPM.2298 var=16) merge (__extPM.1916 __extPM.2290)  <1802>;
            (__extDM.2299 var=17) merge (__extDM.1894 __extDM.2268)  <1803>;
            (__extULP.2300 var=18) merge (__extULP.1918 __extULP.2292)  <1804>;
            (Temp_Buf.2301 var=21) merge (Temp_Buf.200 Temp_Buf.2262)  <1805>;
            (BATSYS0.2302 var=25) merge (BATSYS0.1867 BATSYS0.2240)  <1806>;
            (__extDM_int8_.2303 var=26) merge (__extDM_int8_.1914 __extDM_int8_.2288)  <1807>;
            (__extDM_int16_.2304 var=27) merge (__extDM_int16_.1913 __extDM_int16_.2287)  <1808>;
            (__extDM_SFR_BATSYS0_t.2305 var=28) merge (__extDM_SFR_BATSYS0_t.1895 __extDM_SFR_BATSYS0_t.2269)  <1809>;
            (g_b_InCriticalSection.2306 var=29) merge (g_b_InCriticalSection.1920 g_b_InCriticalSection.2294)  <1810>;
            (PRESWUP0.2307 var=30) merge (PRESWUP0.1885 PRESWUP0.2258)  <1811>;
            (__extDM_SFR_PRESWUP0_t.2308 var=31) merge (__extDM_SFR_PRESWUP0_t.1909 __extDM_SFR_PRESWUP0_t.2283)  <1812>;
            (PRESWUP1.2309 var=32) merge (PRESWUP1.1886 PRESWUP1.2259)  <1813>;
            (__extDM_SFR_PRESWUP1_t.2310 var=33) merge (__extDM_SFR_PRESWUP1_t.1910 __extDM_SFR_PRESWUP1_t.2284)  <1814>;
            (PRESWUP2.2311 var=34) merge (PRESWUP2.1887 PRESWUP2.2260)  <1815>;
            (__extDM_SFR_word.2312 var=35) merge (__extDM_SFR_word.1912 __extDM_SFR_word.2286)  <1816>;
            (LFTUNEVBAT.2313 var=36) merge (LFTUNEVBAT.1870 LFTUNEVBAT.2243)  <1817>;
            (__extDM_SFR_LFTUNEVBAT_t.2314 var=37) merge (__extDM_SFR_LFTUNEVBAT_t.1896 __extDM_SFR_LFTUNEVBAT_t.2270)  <1818>;
            (PRECON2.2315 var=38) merge (PRECON2.1875 PRECON2.2248)  <1819>;
            (__extDM_SFR_PRECON2_t.2316 var=39) merge (__extDM_SFR_PRECON2_t.1900 __extDM_SFR_PRECON2_t.2274)  <1820>;
            (PRECON3.2317 var=40) merge (PRECON3.1876 PRECON3.2249)  <1821>;
            (__extDM_SFR_PRECON3_t.2318 var=41) merge (__extDM_SFR_PRECON3_t.1901 __extDM_SFR_PRECON3_t.2275)  <1822>;
            (PRECON4.2319 var=42) merge (PRECON4.1877 PRECON4.2250)  <1823>;
            (__extDM_SFR_PRECON4_t.2320 var=43) merge (__extDM_SFR_PRECON4_t.1902 __extDM_SFR_PRECON4_t.2276)  <1824>;
            (PRECON5.2321 var=44) merge (PRECON5.1878 PRECON5.2251)  <1825>;
            (__extDM_SFR_PRECON5_t.2322 var=45) merge (__extDM_SFR_PRECON5_t.1903 __extDM_SFR_PRECON5_t.2277)  <1826>;
            (PRECON6.2323 var=46) merge (PRECON6.1879 PRECON6.2252)  <1827>;
            (__extDM_SFR_PRECON6_t.2324 var=47) merge (__extDM_SFR_PRECON6_t.1904 __extDM_SFR_PRECON6_t.2278)  <1828>;
            (PRECON7.2325 var=48) merge (PRECON7.1880 PRECON7.2253)  <1829>;
            (__extDM_SFR_PRECON7_t.2326 var=49) merge (__extDM_SFR_PRECON7_t.1905 __extDM_SFR_PRECON7_t.2279)  <1830>;
            (PRECON8.2327 var=50) merge (PRECON8.1881 PRECON8.2254)  <1831>;
            (__extDM_SFR_PRECON8_t.2328 var=51) merge (__extDM_SFR_PRECON8_t.1906 __extDM_SFR_PRECON8_t.2280)  <1832>;
            (PRECON9.2329 var=52) merge (PRECON9.1882 PRECON9.2255)  <1833>;
            (__extDM_SFR_PRECON9_t.2330 var=53) merge (__extDM_SFR_PRECON9_t.1907 __extDM_SFR_PRECON9_t.2281)  <1834>;
            (PRECON10.2331 var=54) merge (PRECON10.1873 PRECON10.2246)  <1835>;
            (__extDM_SFR_PRECON10_t.2332 var=55) merge (__extDM_SFR_PRECON10_t.1898 __extDM_SFR_PRECON10_t.2272)  <1836>;
            (PRECON11.2333 var=56) merge (PRECON11.1874 PRECON11.2247)  <1837>;
            (__extDM_SFR_PRECON11_t.2334 var=57) merge (__extDM_SFR_PRECON11_t.1899 __extDM_SFR_PRECON11_t.2273)  <1838>;
            (PREPD.2335 var=58) merge (PREPD.1883 PREPD.2256)  <1839>;
            (__extDM_SFR_byte.2336 var=59) merge (__extDM_SFR_byte.1911 __extDM_SFR_byte.2285)  <1840>;
            (PRET.2337 var=60) merge (PRET.1888 PRET.2261)  <1841>;
            (PRE3T.2338 var=61) merge (PRE3T.1872 PRE3T.2245)  <1842>;
            (WUP1W0.2339 var=62) merge (WUP1W0.1889 WUP1W0.2263)  <1843>;
            (WUP1W1.2340 var=63) merge (WUP1W1.1890 WUP1W1.2264)  <1844>;
            (WUP2W0.2341 var=64) merge (WUP2W0.1891 WUP2W0.2265)  <1845>;
            (KEY_PID.2342 var=65) merge (KEY_PID.1869 KEY_PID.2242)  <1846>;
            (WUP2W1.2343 var=66) merge (WUP2W1.1892 WUP2W1.2266)  <1847>;
            (KEY_ID.2344 var=67) merge (KEY_ID.1868 KEY_ID.2241)  <1848>;
            (WUP3W0.2345 var=68) merge (WUP3W0.1893 WUP3W0.2267)  <1849>;
            (PRESTAT.2346 var=69) merge (PRESTAT.1884 PRESTAT.2257)  <1850>;
            (__extDM_SFR_PRESTAT_t.2347 var=70) merge (__extDM_SFR_PRESTAT_t.1908 __extDM_SFR_PRESTAT_t.2282)  <1851>;
            (g_b_NMI_occurred.2348 var=71) merge (g_b_NMI_occurred.1921 g_b_NMI_occurred.2295)  <1852>;
            (PCON0.2349 var=72) merge (PCON0.1871 PCON0.2244)  <1853>;
            (__extDM_SFR_PCON0_t.2350 var=73) merge (__extDM_SFR_PCON0_t.1897 __extDM_SFR_PCON0_t.2271)  <1854>;
            (__extPM_void.2351 var=74) merge (__extPM_void.1917 __extPM_void.2291)  <1855>;
            (__extDM_void.2352 var=75) merge (__extDM_void.1915 __extDM_void.2289)  <1856>;
            (__extULP_void.2353 var=76) merge (__extULP_void.1919 __extULP_void.2293)  <1857>;
        } #161
    } #143
    #392 off=184 nxt=302 tgt=175
    (__ptr_g_b_NMI_occurred.3331 var=133) const_inp ()  <3315>;
    (__trgt.3368 var=945) const_inp ()  <3352>;
    <462> {
      (g_b_InCriticalSection.2458 var=29) store_const_const_1_B1 (__ptr_g_b_InCriticalSection.3305 g_b_InCriticalSection.2306)  <3479>;
    } stp=0;
    <463> {
      (__seff.3434 var=951 stl=c_flag_w __seff.3435 var=952 stl=nz_flag_w __seff.3436 var=953 stl=o_flag_w) load_const_cmp_const_cc_eq__jump_const_1_B1 (__ptr_g_b_NMI_occurred.3331 __trgt.3368 g_b_NMI_occurred.2348)  <3480>;
      (__seff.3740 var=952 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3435)  <3960>;
      (__seff.3819 var=951 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.3434)  <4025>;
      (__seff.3820 var=953 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.3436)  <4026>;
    } stp=2;
    if {
        {
            () if_expr (__either.3302)  <2069>;
            (__either.3302 var=937) undefined ()  <3285>;
        } #169
        {
        } #175 off=190 nxt=180
        {
            (__ptr_PCON0.3332 var=135) const_inp ()  <3316>;
            <460> {
              (PCON0.2581 var=72 __vola.2582 var=13 __seff.3432 var=950 stl=nz_flag_w) load_const__or_const_store_1_B1 (__ptr_PCON0.3332 PCON0.2349 __vola.2297)  <3477>;
              (__seff.3741 var=950 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3432)  <3961>;
            } stp=0;
        } #302 off=188 nxt=180
        {
            (__vola.2586 var=13) merge (__vola.2297 __vola.2582)  <2089>;
            (PCON0.2587 var=72) merge (PCON0.2349 PCON0.2581)  <2090>;
        } #176
    } #168
    #180 off=190 nxt=-2
    () sink (__vola.2586)  <2202>;
    () sink (__extPM.2298)  <2205>;
    () sink (__extDM.2299)  <2206>;
    () sink (__extULP.2300)  <2207>;
    () sink (__sp.2798)  <2208>;
    () sink (u8_save_bits.748)  <2209>;
    () sink (Temp_Buf.2301)  <2210>;
    () sink (pollingid.1214)  <2211>;
    () sink (gaincfg.858)  <2212>;
    () sink (swversion.212)  <2213>;
    () sink (BATSYS0.2302)  <2214>;
    () sink (__extDM_int8_.2303)  <2215>;
    () sink (__extDM_int16_.2304)  <2216>;
    () sink (__extDM_SFR_BATSYS0_t.2305)  <2217>;
    () sink (g_b_InCriticalSection.2458)  <2218>;
    () sink (PRESWUP0.2307)  <2219>;
    () sink (__extDM_SFR_PRESWUP0_t.2308)  <2220>;
    () sink (PRESWUP1.2309)  <2221>;
    () sink (__extDM_SFR_PRESWUP1_t.2310)  <2222>;
    () sink (PRESWUP2.2311)  <2223>;
    () sink (__extDM_SFR_word.2312)  <2224>;
    () sink (LFTUNEVBAT.2313)  <2225>;
    () sink (__extDM_SFR_LFTUNEVBAT_t.2314)  <2226>;
    () sink (PRECON2.2315)  <2227>;
    () sink (__extDM_SFR_PRECON2_t.2316)  <2228>;
    () sink (PRECON3.2317)  <2229>;
    () sink (__extDM_SFR_PRECON3_t.2318)  <2230>;
    () sink (PRECON4.2319)  <2231>;
    () sink (__extDM_SFR_PRECON4_t.2320)  <2232>;
    () sink (PRECON5.2321)  <2233>;
    () sink (__extDM_SFR_PRECON5_t.2322)  <2234>;
    () sink (PRECON6.2323)  <2235>;
    () sink (__extDM_SFR_PRECON6_t.2324)  <2236>;
    () sink (PRECON7.2325)  <2237>;
    () sink (__extDM_SFR_PRECON7_t.2326)  <2238>;
    () sink (PRECON8.2327)  <2239>;
    () sink (__extDM_SFR_PRECON8_t.2328)  <2240>;
    () sink (PRECON9.2329)  <2241>;
    () sink (__extDM_SFR_PRECON9_t.2330)  <2242>;
    () sink (PRECON10.2331)  <2243>;
    () sink (__extDM_SFR_PRECON10_t.2332)  <2244>;
    () sink (PRECON11.2333)  <2245>;
    () sink (__extDM_SFR_PRECON11_t.2334)  <2246>;
    () sink (PREPD.2335)  <2247>;
    () sink (__extDM_SFR_byte.2336)  <2248>;
    () sink (PRET.2337)  <2249>;
    () sink (PRE3T.2338)  <2250>;
    () sink (WUP1W0.2339)  <2251>;
    () sink (WUP1W1.2340)  <2252>;
    () sink (WUP2W0.2341)  <2253>;
    () sink (KEY_PID.2342)  <2254>;
    () sink (WUP2W1.2343)  <2255>;
    () sink (KEY_ID.2344)  <2256>;
    () sink (WUP3W0.2345)  <2257>;
    () sink (PRESTAT.2346)  <2258>;
    () sink (__extDM_SFR_PRESTAT_t.2347)  <2259>;
    () sink (g_b_NMI_occurred.2348)  <2260>;
    () sink (PCON0.2587)  <2261>;
    () sink (__extDM_SFR_PCON0_t.2350)  <2262>;
    () sink (__extPM_void.2351)  <2263>;
    () sink (__extDM_void.2352)  <2264>;
    () sink (__extULP_void.2353)  <2265>;
    (__ct_12s0.3345 var=622) const_inp ()  <3329>;
    <458> {
      (__sp.2798 var=19 __seff.3427 var=947 stl=c_flag_w __seff.3428 var=948 stl=nz_flag_w __seff.3429 var=949 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_12s0.3345 __sp.140 __sp.140)  <3475>;
      (__seff.3742 var=948 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.3428)  <3962>;
      (__seff.3821 var=947 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.3427)  <4027>;
      (__seff.3822 var=949 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.3429)  <4028>;
    } stp=0;
    <459> {
      () ret_1_B1 ()  <3476>;
    } stp=2;
} #0
0 : 'apps/src/SysInit.c';
----------
0 : (0,226:0,0);
3 : (0,228:26,1);
4 : (0,228:26,1);
5 : (0,229:26,3);
6 : (0,229:26,3);
7 : (0,229:26,4);
8 : (0,229:26,4);
9 : (0,229:26,5);
10 : (0,229:26,5);
11 : (0,229:26,6);
13 : (0,229:26,6);
14 : (0,230:24,7);
15 : (0,230:24,7);
16 : (0,232:21,8);
17 : (0,232:21,8);
18 : (0,233:24,9);
19 : (0,233:24,9);
21 : (0,238:2,10);
23 : (0,239:2,11);
24 : (0,238:31,11);
25 : (0,240:4,11);
26 : (0,240:4,11);
27 : (0,238:2,16);
29 : (0,246:23,20);
30 : (0,246:23,20);
31 : (0,248:38,20);
32 : (0,248:2,20);
34 : (0,251:17,22);
35 : (0,251:17,22);
36 : (0,252:17,23);
37 : (0,252:17,23);
38 : (0,253:17,24);
39 : (0,253:17,24);
40 : (0,257:17,25);
41 : (0,257:17,25);
42 : (0,267:17,26);
43 : (0,267:17,26);
44 : (0,268:17,27);
45 : (0,268:17,27);
46 : (0,270:17,28);
47 : (0,270:17,28);
48 : (0,271:17,29);
49 : (0,271:17,29);
50 : (0,272:17,30);
51 : (0,272:17,30);
52 : (0,281:17,31);
53 : (0,281:17,31);
54 : (0,282:17,32);
55 : (0,282:17,32);
56 : (0,284:17,33);
57 : (0,284:17,33);
58 : (0,285:17,34);
59 : (0,285:17,34);
60 : (0,286:17,35);
61 : (0,286:17,35);
62 : (0,287:17,36);
63 : (0,287:17,36);
64 : (0,289:54,36);
65 : (0,289:12,36);
66 : (0,289:10,37);
67 : (0,289:10,37);
69 : (0,290:2,37);
72 : (0,292:19,39);
75 : (0,296:19,42);
78 : (0,299:17,46);
79 : (0,299:17,46);
80 : (0,306:17,47);
81 : (0,306:17,47);
82 : (0,307:17,48);
83 : (0,307:17,48);
84 : (0,310:17,49);
85 : (0,310:17,49);
86 : (0,311:17,50);
87 : (0,311:17,50);
89 : (0,318:17,51);
90 : (0,319:17,52);
91 : (0,319:17,52);
92 : (0,320:17,53);
93 : (0,320:17,53);
94 : (0,321:17,54);
95 : (0,321:17,54);
97 : (0,323:17,55);
98 : (0,324:17,56);
99 : (0,324:17,56);
100 : (0,325:17,57);
101 : (0,325:17,57);
102 : (0,326:17,58);
103 : (0,326:17,58);
104 : (0,328:56,58);
105 : (0,328:14,58);
106 : (0,328:12,59);
107 : (0,328:12,59);
109 : (0,329:17,60);
113 : (0,332:14,62);
117 : (0,333:14,64);
119 : (0,333:25,65);
120 : (0,337:17,66);
121 : (0,337:17,66);
123 : (0,343:37,66);
125 : (0,343:37,67);
129 : (0,343:2,70);
131 : (0,344:2,71);
132 : (0,343:37,71);
133 : (0,345:4,71);
134 : (0,345:4,71);
135 : (0,343:2,76);
137 : (0,348:38,79);
138 : (0,348:2,79);
140 : (0,350:50,80);
141 : (0,350:8,80);
143 : (0,350:2,80);
145 : (0,351:2,81);
146 : (0,352:49,81);
147 : (0,352:4,81);
149 : (0,353:25,82);
150 : (0,353:4,82);
152 : (0,354:16,84);
153 : (0,354:16,84);
154 : (0,355:66,84);
155 : (0,355:4,84);
157 : (0,356:25,85);
158 : (0,356:4,85);
160 : (0,350:2,88);
168 : (0,372:43,92);
175 : (0,372:104,97);
180 : (0,373:0,102);
262 : (0,330:13,60);
289 : (0,332:14,62);
296 : (0,333:14,64);
302 : (0,372:79,94);
325 : (0,318:13,50);
341 : (0,323:13,54);
358 : (0,329:13,59);
372 : (0,290:13,37);
378 : (0,343:64,68);
385 : (0,350:60,80);
392 : (0,372:43,92);
398 : (0,343:30,66);
406 : (0,238:31,10);
----------
183 : (0,228:26,1);
190 : (0,229:26,3);
197 : (0,229:26,4);
204 : (0,229:26,5);
212 : (0,229:26,6);
216 : (0,230:24,7);
220 : (0,232:21,8);
224 : (0,233:24,9);
340 : (0,238:2,10);
344 : (0,240:4,11);
614 : (0,246:23,20);
621 : (0,248:2,20);
629 : (0,251:17,22);
636 : (0,252:17,23);
643 : (0,253:17,24);
650 : (0,257:17,25);
661 : (0,267:17,26);
672 : (0,268:17,27);
679 : (0,270:17,28);
686 : (0,271:17,29);
693 : (0,272:17,30);
704 : (0,281:17,31);
715 : (0,282:17,32);
722 : (0,284:17,33);
729 : (0,285:17,34);
736 : (0,286:17,35);
743 : (0,287:17,36);
750 : (0,289:12,36);
754 : (0,289:10,37);
861 : (0,290:2,37);
876 : (0,290:2,44);
877 : (0,290:2,44);
884 : (0,299:17,46);
891 : (0,306:17,47);
898 : (0,307:17,48);
905 : (0,310:17,49);
912 : (0,311:17,50);
935 : (0,318:17,51);
947 : (0,319:17,52);
959 : (0,320:17,53);
971 : (0,321:17,54);
994 : (0,323:17,55);
1006 : (0,324:17,56);
1018 : (0,325:17,57);
1030 : (0,326:17,58);
1037 : (0,328:14,58);
1041 : (0,328:12,59);
1060 : (0,329:17,60);
1080 : (0,332:14,62);
1099 : (0,333:14,64);
1111 : (0,333:25,65);
1118 : (0,337:17,66);
1234 : (0,343:37,66);
1252 : (0,343:37,69);
1253 : (0,343:37,69);
1355 : (0,343:2,70);
1359 : (0,345:4,71);
1632 : (0,348:2,79);
1640 : (0,350:8,80);
1750 : (0,350:2,80);
1758 : (0,352:4,81);
1766 : (0,353:4,82);
1781 : (0,354:16,84);
1789 : (0,355:4,84);
1799 : (0,356:4,85);
1801 : (0,350:2,90);
1802 : (0,350:2,90);
1803 : (0,350:2,90);
1804 : (0,350:2,90);
1805 : (0,350:2,90);
1806 : (0,350:2,90);
1807 : (0,350:2,90);
1808 : (0,350:2,90);
1809 : (0,350:2,90);
1810 : (0,350:2,90);
1811 : (0,350:2,90);
1812 : (0,350:2,90);
1813 : (0,350:2,90);
1814 : (0,350:2,90);
1815 : (0,350:2,90);
1816 : (0,350:2,90);
1817 : (0,350:2,90);
1818 : (0,350:2,90);
1819 : (0,350:2,90);
1820 : (0,350:2,90);
1821 : (0,350:2,90);
1822 : (0,350:2,90);
1823 : (0,350:2,90);
1824 : (0,350:2,90);
1825 : (0,350:2,90);
1826 : (0,350:2,90);
1827 : (0,350:2,90);
1828 : (0,350:2,90);
1829 : (0,350:2,90);
1830 : (0,350:2,90);
1831 : (0,350:2,90);
1832 : (0,350:2,90);
1833 : (0,350:2,90);
1834 : (0,350:2,90);
1835 : (0,350:2,90);
1836 : (0,350:2,90);
1837 : (0,350:2,90);
1838 : (0,350:2,90);
1839 : (0,350:2,90);
1840 : (0,350:2,90);
1841 : (0,350:2,90);
1842 : (0,350:2,90);
1843 : (0,350:2,90);
1844 : (0,350:2,90);
1845 : (0,350:2,90);
1846 : (0,350:2,90);
1847 : (0,350:2,90);
1848 : (0,350:2,90);
1849 : (0,350:2,90);
1850 : (0,350:2,90);
1851 : (0,350:2,90);
1852 : (0,350:2,90);
1853 : (0,350:2,90);
1854 : (0,350:2,90);
1855 : (0,350:2,90);
1856 : (0,350:2,90);
1857 : (0,350:2,90);
2069 : (0,372:43,92);
2089 : (0,372:43,99);
2090 : (0,372:43,99);
3209 : (0,343:37,69);
3475 : (0,373:0,0) (0,373:0,102);
3476 : (0,373:0,102);
3477 : (0,372:79,94);
3479 : (0,372:8,91);
3480 : (0,372:48,92) (0,372:43,92);
3483 : (0,356:4,85);
3485 : (0,355:4,84);
3486 : (0,354:26,83) (0,229:10,0) (0,354:29,83) (0,354:12,83);
3488 : (0,353:4,82);
3490 : (0,352:4,81);
3491 : (0,350:52,80) (0,350:60,80);
3492 : (0,350:60,80) (0,350:2,80);
3494 : (0,350:8,80);
3496 : (0,348:2,79);
3497 : (0,345:4,71);
3498 : (0,343:53,68);
3499 : (0,343:64,68);
3500 : (0,343:64,68) (0,343:2,70);
3501 : (0,343:20,66) (0,343:30,66) (0,343:37,66);
3502 : (0,337:9,65);
3503 : (0,333:14,64);
3504 : (0,332:14,62);
3505 : (0,330:20,60) (0,230:11,0);
3506 : (0,330:13,60) (0,330:17,60);
3507 : (0,329:20,59) (0,230:11,0);
3508 : (0,329:29,59);
3509 : (0,329:35,59);
3510 : (0,329:39,59);
3511 : (0,329:13,59);
3512 : (0,328:2,58) (0,230:11,0);
3514 : (0,328:14,58);
3515 : (0,326:25,57);
3516 : (0,326:13,57);
3517 : (0,325:25,56);
3518 : (0,325:13,56);
3519 : (0,324:25,55);
3520 : (0,324:13,55);
3521 : (0,323:26,54);
3522 : (0,323:29,54);
3523 : (0,323:35,54);
3524 : (0,323:39,54);
3525 : (0,323:13,54);
3526 : (0,321:26,53);
3527 : (0,321:13,53);
3528 : (0,320:26,52);
3529 : (0,320:13,52);
3530 : (0,319:26,51);
3531 : (0,319:13,51);
3532 : (0,318:27,50);
3533 : (0,318:30,50);
3534 : (0,318:36,50);
3535 : (0,318:40,50);
3536 : (0,318:13,50);
3537 : (0,311:8,49);
3539 : (0,310:8,48);
3541 : (0,307:7,47);
3543 : (0,306:6,46);
3545 : (0,299:7,45);
3546 : (0,296:12,41);
3548 : (0,292:12,38);
3549 : (0,290:5,37) (0,232:10,0) (0,290:13,37);
3550 : (0,290:13,37) (0,290:2,37);
3551 : (0,289:2,36);
3553 : (0,289:12,36);
3554 : (0,287:10,35);
3555 : (0,286:9,34);
3556 : (0,285:9,33);
3557 : (0,284:9,32);
3558 : (0,282:36,31) (0,228:10,0);
3559 : (0,282:9,31);
3560 : (0,281:26,30);
3561 : (0,281:31,30);
3562 : (0,281:2,30) (0,228:10,0);
3563 : (0,272:9,29);
3564 : (0,271:9,28);
3565 : (0,270:9,27);
3566 : (0,268:53,26) (0,228:10,0);
3567 : (0,268:51,26);
3568 : (0,268:9,26);
3569 : (0,267:26,25);
3570 : (0,267:31,25);
3571 : (0,267:2,25) (0,228:10,0);
3572 : (0,257:12,24);
3574 : (0,253:10,23);
3576 : (0,252:10,22);
3578 : (0,251:10,21);
3581 : (0,248:2,20);
3582 : (0,246:1,19);
3583 : (0,240:4,11);
3584 : (0,238:19,10) (0,238:31,10) (0,238:2,10);
3585 : (0,233:11,0) (0,233:24,9);
3587 : (0,232:21,8);
3588 : (0,230:11,0) (0,230:24,7);
3589 : (0,229:26,5) (0,229:26,0) (0,229:10,0);
3590 : (0,229:26,4) (0,229:26,0) (0,229:10,0);
3591 : (0,229:26,3) (0,229:26,0) (0,229:10,0);
3592 : (0,229:10,0) (0,229:26,2);
3594 : (0,226:5,0);
3595 : (0,228:10,0) (0,228:26,1);
3840 : (0,232:10,0);
3848 : (0,248:38,0);
3851 : (0,251:17,0);
3854 : (0,252:17,0);
3857 : (0,253:17,0);
3860 : (0,257:17,0);
3862 : (0,289:54,0);
3865 : (0,306:17,0);
3868 : (0,307:17,0);
3871 : (0,310:17,0);
3874 : (0,311:17,0);
3876 : (0,328:56,0);
3878 : (0,348:38,0);
3880 : (0,350:50,0);
3882 : (0,352:49,0);
3884 : (0,353:25,0);
3886 : (0,355:66,0);
3888 : (0,356:25,0);
3891 : (0,232:10,0);
3899 : (0,229:10,0);
3908 : (0,229:10,0);

