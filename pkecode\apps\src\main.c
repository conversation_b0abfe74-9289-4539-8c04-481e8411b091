/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of HiRain Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   HiRain Technologies.
************************************************************************************************
*   File Name       : main.c
************************************************************************************************
*   Project/Product : 
*   Title           : MAIN 
*   Author          : jian.tang
************************************************************************************************
*   Description     : Main function for whole software project 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*   Version       Date         Initials         CR#                Descriptions
*  ---------    ---------      ---------       -----               -------------
*     1.0       2017/05/02      jian.tang       N/A                
*
************************************************************************************************
* END_FILE_HDR*/
#ifndef MAIN_C
#define MAIN_C
#endif
//--------Include Files--------------------------------------------------------------------------------------
#include "phcaiKEyLLGenFunc_CS.h"
#include "phcaiKEyLLGenFunc.h"
#include "defs.h"
#include "SysInit.h"
#include "pke.h"
#include "rke.h"
#include "interrupt.h"
#include "si.h"
#include "buttons.h"
#include "timer.h"
#include "transmitter.h"
//--------public variable here--------------------------------------------------------------------------------------
#define LF_WUP_MASK     0x0767u

uint8_t  KEY_ID[4];
uint8_t  KEY_PID[4];
uint8_t  RKEIsr_Flag = INACTIVE; 
uint8_t  RKEIsrPKE_Flag = INACTIVE;
uint8_t  VbatLowPowerFlag = INACTIVE;
bool_t   g_b_InCriticalSection = FALSE;
bool_t   g_b_NMI_occurred = FALSE;
//--------static function here--------------------------------------------------------------------------------------
static void process_VBATPOR(void);
static void process_PreproWUP(void);
static void process_PortWUP(void);
void process_isr( void ) property(isr);
static void process_GlobalVariableInit(void);

/*-----------------------------------------------------------------------------
;| Name:
;|   main
;| 
;| Description:
;|     
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
void main( void ) property(never_returns) 
{ 
    //phcaiKEyLLGenFunc_Util_LoadRWInitData(FALSE); //meaningless,waste of time
    process_GlobalVariableInit();   
    CPUClock_Init();
    GPIO_Init();                
    InterruptInit();
        
    // min voltage 2.1V
    if ( Vbat_Check( PowerDown ) == SUCCESS )
    {
        if ( PRESWUP0.bits.WDTOF)               
        {  /*******watchdog over reset*******/
            PRESWUP0.bits.WDTOF = ACTIVE;
            BatReset();    // reset VBAT reg
        }

        if ( BATSYS0.bits.BATPORFLAG == ACTIVE )
        {  
            /*******insert battery*******/
            process_VBATPOR();  
        }
        else if( BATSYS0.bits.BATPORFLAG == INACTIVE )
        {   
                           
            read_IDE_from_ULPEE();       // restore ID
                      
            if ( (PRESTAT.val & LF_WUP_MASK) != 0U )        
            {
                /***preprocessor wake up***/
                process_PreproWUP();
            }
            else 
            {
                /********port wake up******/
                process_PortWUP();

            }
            
        }
        else 
        {
            //do nothing
        }
    }
    hw_refresh_VBAT_VBATREG_registers();        // refresh VBAT/VBAT RGL reg    
    //LED_ON();
    //P1DIR.val=0;
    //P1WRES.val = 0xFE;
    PRESWUP1.val=PRESWUP1.val&0xFF7F;
    PRESWUP1.val=PRESWUP1.val|0x0060;
    Power_Off();                                // POWER OFF
}

static void process_GlobalVariableInit(void)
{
    uint8_t i = 0;

    for(i = 0; i < 4; i++)
    {
        KEY_ID[i] = 0x0;
        KEY_PID[i] = 0x0;
    }
    RKEIsr_Flag = INACTIVE; 
    RKEIsrPKE_Flag = INACTIVE;
    VbatLowPowerFlag = INACTIVE;
    g_b_InCriticalSection = FALSE;
    g_b_NMI_occurred = FALSE;
    Rke_GlobalVariableInit();
    Pke_GlobalVariableInit();
    SI_GlobalVariableInit();
}
/*-----------------------------------------------------------------------------
;| Name:
;|   main
;| 
;| Description:
;|     
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
static void process_VBATPOR( void )
{
    timer0_delay_ms( 250u );
    WDRESET;
    timer0_delay_ms( 250u );
    WDRESET;
        
    if ( PCON1.bits.VDDBRNFLAG == 0U )  
    {
        PCON2.bits.VDDBRNREG = 0U;
        phcaiKEyLLGenFunc_CS_SetVBatRgl( TRUE );                // enable VBAT regulator
        if( PCON2.bits.VDDBRNREG == 1U )
        {
            Power_Off();
        } 

        phcaiKEyLLGenFunc_ULPEE_ActivateAllModules();            // power on ULPEE
        timer0_delay_us( t_ULP_PON_us );
        read_IDE_from_ULPEE();
      
        WUP_Init();                        // init VBAT\VBATRGL reg
                                

        // check VBAT regulator
        if ( (BATSYS0.bits.BATRGLEN == 0U) || (BATSYS0.bits.BATRGLRST == 1U) )
        {
            Power_Off();
        }

        phcaiKEyLLGenFunc_CS_SetBatPORFlag( FALSE );            // clear POR Flag

        // check VBAT regulator again
        if( (BATSYS0.bits.BATRGLEN == 0U) || (BATSYS0.bits.BATRGLRST == 1U) )
        {
            phcaiKEyLLGenFunc_CS_SetBatPORFlag( TRUE );            // set POR Flag,it'll wake up again
            Power_Off();
        }  

        // blink led twice
        LED_ON();
        timer0_delay_ms( 200U ); 
        LED_OFF();        
    }
}

/*-----------------------------------------------------------------------------
;| Name:
;|   process_PreproWUP
;| 
;| Description:
;|     
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
static void process_PreproWUP( void )
{
    PCON2.bits.R2MSDET = 1U;                        // reset the block of 2ms detection
    
    VbatLowPowerFlag = Vbat_Check(LowPower);
    if ( ( PRESTAT.val & LF_WUP_MASK ) != 0U )
    {
        if ((PRESTAT.bits.WUP1M != 0U )||( PRESTAT.bits.WUP1MH != 0U ))
        {
            PRESTAT.byte.lo = 0x01U;                 // clear WUP1M Flag
            WUP1M_Event();
        }
        else if ( PRESTAT.bits.WUP2M != 0U )
        {
            PRESTAT.byte.lo = 0x02U;                 // clear WUP2M Flag
            WUP2M_Event();// add your code
        }
        else if ( PRESTAT.bits.WUP3M != 0U )
        {
            PRESTAT.byte.lo = 0x04U;                 // clear WUP3M Flag
            WUP3M_Event();// add your code
        }
        else if ( PRESTAT.bits.RTC_WUP != 0U )
        {
            PRESTAT.byte.lo = 0x20U;                 // clear RTC_WUP Flag
            // add your code
        }
        else if ( PRESTAT.bits.IT_WUP != 0U )
        {
            PRESTAT.byte.lo = 0x40U;                 // ?Interval timer Flag
            // add your code
        }
        else
        {
        }    
    }
}

/*-----------------------------------------------------------------------------
;| Name:
;|   process_PortWUP
;| 
;| Description:
;|     
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
static void process_PortWUP( void )
{
    phcaiKEyLLGenFunc_ULPEE_ActivateAllModules();
    VbatLowPowerFlag = Vbat_Check(LowPower);
    rke();
}
/*-----------------------------------------------------------------------------
;| Name:
;|   process_PortWUP
;| 
;| Description:
;|     
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
 void process_isr( void ) property(isr)
{  
    if(INTFLAG0.bits.IF_PORT == 1)
    {
        INTCLR0.bits.IC_PORT = 1; 
        if(RKEIsr_Flag != ACTIVE)
        {
            KEY_Code_ISR = btn_get_bid();
    
            if( KEY_Code_ISR!=0 )
            {
                RKEIsr_Flag  = ACTIVE;
                RKEIsrPKE_Flag = ACTIVE;
            }
        }
    }
    else if(INTFLAG0.bits.IF_T0 == 1)
    {
        INTCLR0.bits.IC_T0 = 1u;
    }
    else if(INTFLAG1.bits.IF_ADC == 1)
    {
        INTCLR1.bits.IC_ADC = 1u;
    }
    
}
/*-----------------------------------------------------------------------------
;| Name:
;|   HtUserCommand
;| 
;| Description:
;|     
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/
void HtUserCommand( void ) property(isr)
{  
#ifdef HTPRO_AC
  /*Anti-collision demo with HT-PRO transponder (only)*/
  //HTPro_UserCall_AC();  
#endif
}
/*-----------------------------------------------------------------------------
;| Name:
;|   LF_NMI
;| 
;| Description:
;|     
;|   
-----------------------------------------------------------------------------
;| Parameters:
;|   
;| 
;| Return:
;|   none 
;| 
-----------------------------------------------------------------------------*/

void LF_NMI( void ) property(isr)
{
  /* Work-around for VBATREG read issue - not done for LF NMI in ROM entry point.*/
  CLEAR_VBATREG_SFR_ACCESS();
  /* Note: it is not necessary to clear the interrupt flag IF_LF. */
  if ( g_b_InCriticalSection == FALSE )
  {
    // Immediate reset
    PCON0.bits.VDDRST = 1u;
    for (;;) {}
  }
  else
  {
    // Indicate that an NMI has occurred, reset will be
    // triggered after end of critical section;
    // see macro END_CRITICAL_SECTION.
    g_b_NMI_occurred = TRUE;
  }
}

/* eof */
