
// File generated by showcolor version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:24 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\showcolor.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Dindirect_bitf -t2 -yRw timer-d63159 mrk3

[
    0 : void_timer_WaitNops___ushort typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : i typ=int8_ val=0t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   22 : __arg_i typ=int16_ bnd=p tref=uint16_t__
   27 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   43 : __ct_2s0 typ=int16_ val=2s0 bnd=m
   50 : __ct_2s0 typ=int16_ val=2s0 bnd=m
   58 : __apl_c typ=uint1_ bnd=m tref=uint1___
   60 : __apl_nz typ=uint2_ bnd=m tref=uint2___
   70 : __either typ=bool bnd=m
   71 : __trgt typ=rel8_ val=0j bnd=m
   72 : __trgt typ=rel8_ val=0j bnd=m
   73 : __seff typ=any bnd=m
   74 : __seff typ=any bnd=m
   75 : __seff typ=any bnd=m
   76 : __seff typ=any bnd=m
   77 : __seff typ=any bnd=m
   78 : __seff typ=any bnd=m
   79 : __seff typ=any bnd=m
   80 : __seff typ=any bnd=m
   81 : __seff typ=any bnd=m
   82 : __seff typ=any bnd=m
]
Fvoid_timer_WaitNops___ushort {
    #23 off=0 nxt=-3 tgt=1
    (__vola.12 var=13) source ()  <23>;
    (__sp.18 var=19) source ()  <29>;
    (i.19 var=20) source ()  <30>;
    (__arg_i.21 var=22 stl=RwL off=0) inp ()  <32>;
    () sink (__sp.29)  <120>;
    () sink (i.37)  <121>;
    () sync_sink (__vola.12) sid=1  <122>;
    () sync_sink (i.37) sid=8  <129>;
    (__vola.176 var=13) never ()  <214>;
    (i.177 var=20) never ()  <215>;
    (__ct_0t0.181 var=27) const_inp ()  <221>;
    (__ct_2s0.183 var=50) const_inp ()  <223>;
    (__trgt.184 var=71) const_inp ()  <224>;
    (__trgt.185 var=72) const_inp ()  <225>;
    <32> {
      (__sp.29 var=19 __seff.200 var=80 stl=c_flag_w __seff.201 var=81 stl=nz_flag_w __seff.202 var=82 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1 (__ct_2s0.183 __sp.18 __sp.18)  <235>;
      (__seff.212 var=81 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.201)  <273>;
      (__seff.213 var=80 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.200)  <274>;
      (__seff.217 var=82 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.202)  <278>;
    } stp=3;
    <33> {
      (i.37 var=20) _pl_rd_res_reg_const_store_1 (__arg_i.216 __ct_0t0.181 i.19 __sp.29)  <236>;
      (__arg_i.216 var=22 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16_ (__arg_i.21)  <277>;
    } stp=6;
    <34> {
      () jump_const_1 (__trgt.185)  <237>;
    } stp=11;
    do {
        {
            (__vola.54 var=13) entry (__vola.87 __vola.176)  <66>;
            (i.61 var=20) entry (i.101 i.177)  <73>;
        } #11
        {
            call {
                (__vola.65 var=13) Fvoid_nop (__vola.54)  <77>;
            } #13 off=2
            #17 off=3
            <31> {
              (i.71 var=20 __seff.196 var=77 stl=c_flag_w __seff.197 var=78 stl=nz_flag_w __seff.198 var=79 stl=o_flag_w) _mi_load_const__pl_rd_res_reg_const_store_1 (__ct_0t0.181 i.61 i.61 __sp.29)  <234>;
              (__seff.204 var=78 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.197)  <265>;
              (__seff.205 var=77 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.196)  <266>;
              (__seff.210 var=79 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.198)  <271>;
            } stp=3;
            sync {
                (__vola.72 var=13) sync_link (__vola.65) sid=1  <87>;
                (i.79 var=20) sync_link (i.71) sid=8  <94>;
            } #1 off=4 nxt=87
            #87 off=5
            <29> {
              (__apl_c.165 var=58 stl=c_flag_w __apl_nz.167 var=60 stl=nz_flag_w __seff.194 var=76 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1 (__ct_0t0.181 i.79 __sp.29)  <232>;
              (__apl_nz.207 var=60 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.167)  <268>;
              (__apl_c.209 var=58 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.165)  <270>;
              (__seff.211 var=76 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.194)  <272>;
            } stp=3;
            <30> {
              () cc_a__jump_const_1 (__apl_c.208 __apl_nz.206 __trgt.184)  <233>;
              (__apl_nz.206 var=60 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.207)  <267>;
              (__apl_c.208 var=58 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.209)  <269>;
            } stp=8;
        } #12
        {
            () while_expr (__either.179)  <102>;
            (__vola.87 var=13 __vola.88 var=13) exit (__vola.72)  <103>;
            (i.101 var=20 i.102 var=20) exit (i.79)  <110>;
            (__either.179 var=70) undefined ()  <218>;
        } #21
    } #10
    #26 off=6 nxt=-2
    () sink (__vola.88)  <161>;
    () sink (__sp.136)  <167>;
    () sink (i.102)  <168>;
    (__ct_2s0.182 var=43) const_inp ()  <222>;
    <27> {
      (__sp.136 var=19 __seff.189 var=73 stl=c_flag_w __seff.190 var=74 stl=nz_flag_w __seff.191 var=75 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1 (__ct_2s0.182 __sp.29 __sp.29)  <230>;
      (__seff.214 var=74 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.190)  <275>;
      (__seff.215 var=73 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.189)  <276>;
      (__seff.218 var=75 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.191)  <279>;
    } stp=3;
    <28> {
      () ret_1 ()  <231>;
    } stp=10;
} #0
0 : 'apps/src/timer.c';
----------
0 : (0,93:0,0);
1 : (0,115:2,8);
10 : (0,115:2,3);
12 : (0,115:2,3);
13 : (0,117:4,3);
17 : (0,118:5,6);
23 : (0,115:2,12);
26 : (0,120:0,15);
87 : (0,115:10,10);
----------
66 : (0,115:2,3);
73 : (0,115:2,3);
77 : (0,117:4,3);
102 : (0,115:2,10);
103 : (0,115:2,10);
110 : (0,115:2,10);
230 : (0,120:0,0) (0,120:0,15);
231 : (0,120:0,15);
232 : (0,115:9,10) (0,93:30,0) (0,115:10,10);
233 : (0,115:10,10) (0,115:2,10);
234 : (0,118:5,5) (0,118:4,0) (0,93:30,0) (0,118:4,5);
235 : (0,93:5,0);
236 : (0,93:30,0) (0,93:21,0);
