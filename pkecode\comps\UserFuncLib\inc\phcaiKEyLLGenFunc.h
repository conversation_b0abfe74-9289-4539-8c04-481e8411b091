/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc.h 27279 2020-06-18 08:01:22Z dep17622 $
  $Revision: 27279 $

*/

/**
 * @file
 * Include all header files of the User Functions Library.
 */

/**
 * @defgroup UserFuncLib User Functions Library for TOKEN product family
 * Functions which may be linked with a user's application and placed in EROM.
 * Note: Curently supported device types are TOKEN, ACTIC5G, SMART2A, TOKEN-PLUS,
 * ACTIC5G-PLUS, TOKEN-SRX(v0), ACTIC-SRX(v0).
 * @{
 */

#include "phcaiKEyLLGenFunc_Platform.h"
#include "phcaiKEyLLGenFunc_ULPEE.h"
#include "phcaiKEyLLGenFunc_HITAG.h"
#include "phcaiKEyLLGenFunc_Timer.h"
#include "phcaiKEyLLGenFunc_TimeConst.h"
#include "phcaiKEyLLGenFunc_ADC.h"
#include "phcaiKEyLLGenFunc_RSSI.h"
#include "phcaiKEyLLGenFunc_STDRSSI.h"
#include "phcaiKEyLLGenFunc_NBRSSI.h"
#include "phcaiKEyLLGenFunc_LfActive.h"
#include "phcaiKEyLLGenFunc_IIU.h"
#include "phcaiKEyLLGenFunc_Util.h"
#include "phcaiKEyLLGenFunc_DistMem.h"

/* @} */
