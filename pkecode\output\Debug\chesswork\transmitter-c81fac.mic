
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:20 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-c81fac mrk3


// m3;   next: m4 (next offset: 4)
000000  2 0  "0110100000000000"   // (RbL[0]) = const_1_B1 (); 
000001  0 0  "0000000000100110"   // /
000002  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000003  0 0  "0000000000000000"   // /

// m4 subroutine call;   next: m6 (next offset: 4)

// m6;   next: m7 (next offset: 6)
000004  2 0  "0110100000100000"   // (DM9,PM) = store_const_const_1_B1 (0,DM9,PM); 
000005  0 0  "0000000000000000"   // /

// m7 chess_separator_scheduler;   next: m9 (next offset: 6)

// m9 (next offset: /)
000006  1 0  "0001101111000100"   // () = ret_1_B1 (); 

