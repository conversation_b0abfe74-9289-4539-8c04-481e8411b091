/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_DistMem.h 11676 2018-01-02 13:17:01Z dep17622 $
  $Revision: 11676 $

*/

/**
 * @file
 * Declarations of User Library Functions, constants and types used for Distance Memory access.
 * The distance memory is described in the HT-PRO2 protocol specification, and the functions provided
 * in this module apply equivalent algorithms.
 */


#ifndef PHCAIKEYLLGENFUNC_DISTMEM_H
#define PHCAIKEYLLGENFUNC_DISTMEM_H

#include "types.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup HTPro2DistMem  HT-PRO2 Distance Memory access functions
 * Support functions, constants and structures for reading and writing the HT-PRO2 Distance Memory.
 * @{
 */

/** The number of bytes of the HT-PRO2 Transponder memory. */
#define NUMBER_OF_HITAGPRO_MEM_BYTES                      2016U
/** The number of pages per HT-PRO2 memory block. */
#define PHCAI_HITAGPRO_BYTES_PER_BLOCK                      32U
/** The number of blocks the HT-PRO2 memory has (2048 Byte, 32 Byte per Block). */
#define NUMBER_OF_HITAGPRO_BLOCKS (NUMBER_OF_HITAGPRO_MEM_BYTES / PHCAI_HITAGPRO_BYTES_PER_BLOCK)
/** The number of blocks reserved for the Distance Memory (if enabled). */
#define NUMBER_OF_HITAGPRO_DIST_MEM_BLOCKS                0x02U
/** The number of pages per HT-PRO2 memory block. */
#define PHCAI_HITAGPRO_PAGES_PER_BLOCK                    0x08U
/** The start page of the configuration memory. */
#define HTPRO_CFG_PAGE                                  0x01F8U
/** The number of bytes per HT-PRO2 memory page. */
#define PHCAI_HITAGPRO_BYTES_PER_PAGE                     0x04U
/** The number of blocks reserved for the Distance Memory (if enabled). */
#define NUMBER_OF_HITAGPRO_DIST_MEM_BLOCKS                0x02U
/** The number of distance entries (if enabled). */
#define NUMBER_OF_HITAGPRO_DIST_ENTRIES                   0x07U
/** The number of pages per distance entry (if enabled). */
#define NUMBER_OF_HITAGPRO_PAGES_PER_DIST_ENTRY           0x02U


/**
 * Structure to manage the runtime configuration of the distance feature.
 *
 * @see phcaiKEyLLGenFunc_DistMem_init
 * @see phcaiKEyLLGenFunc_DistMem_reset
 * @see phcaiKEyLLGenFunc_DistMem_write
 * @see phcaiKEyLLGenFunc_DistMem_read_entry
 * @see phcaiKEyLLGenFunc_DistMem_read_latest
 */
typedef struct phcaiKEyLLGenFunc_DistMem_cfg
{
   /**
    * Holds the maximum distance (24 bit).
    * Initialized by phcaiKEyLLGenFunc_DistMem_init function and updated by the phcaiKEyLLGenFunc_DistMem_write function.
    */
   uint8_t max_dist[3];
   /**
    * Holds the minimum rejected distance.
    * Initialized by phcaiKEyLLGenFunc_DistMem_init function and updated by the phcaiKEyLLGenFunc_DistMem_write function.
    */
   uint8_t min_rej_dist[3];
   /**
    * Holds the index to the last write.
    * Initialized by phcaiKEyLLGenFunc_DistMem_init function and updated by the phcaiKEyLLGenFunc_DistMem_write function.
    */
   uint8_t max_dist_entry_idx;
   /**
    * Amount of memory blocks used by the distance feature.
    * Semi-constant depends on whether the feature is enabled or not
    * Initialized by phcaiKEyLLGenFunc_DistMem_init function and updated by the phcaiKEyLLGenFunc_DistMem_write function
    */
   uint8_t dist_mem_blocks;
   /**
    * Indicator identifying whether the distance feature is enabled or not.
    * This is determined during runtime by the phcaiKEyLLGenFunc_DistMem_init fnction.
    */
   bool_t is_enabled;
} phcaiKEyLLGenFunc_DistMem_cfg_t;

/**
 * Structure type for transferring data from and to the distance feature functions.
 *
 * @see phcaiKEyLLGenFunc_DistMem_write
 * @see phcaiKEyLLGenFunc_DistMem_read_entry
 * @see phcaiKEyLLGenFunc_DistMem_read_latest
 */
typedef struct phcaiKEyLLGenFunc_DistMem_data
{
   /** Structure member holds the distance value to be read/written from/to the ULP EEPROM. */
   uint8_t distance[4];
   /** Structure member holds the date value to be read/written from/to the ULP EEPROM.
      In case of reading the rejected entry, it represent the valid distance. */
   uint8_t date[4];
} phcaiKEyLLGenFunc_DistMem_entry_t;

/**
 * Type of result code from phcaiKEyLLGenFunc_DistMem_write().
 * Some of these codes correspond to the Acknowledge codes returned by the
 * HT-PRO2 DIST_WRITE command.
 */
typedef enum
{
  /** Success, distance accepted and written to ULPEE (ACK code)         */
  DISTWR_OK              = 0x00u,
  /** distance rejected but not smaller than stored min rejected (DNACK) */
  DISTWR_REJECTED        = 0x33u,
  /** distance rejected and smaller than stored min rejected (ENACK)     */
  DISTWR_REJECTED_MIN    = 0xEEu,
  /** distance function not enabled in MODE byte                         */
  DISTWR_DISABLED        = 0xCCu,
  /** ULPEE write failed when trying to store rejected entry             */
  DISTWR_WRITE_FAIL_REJ  = 0xFEu,
  /** ULPEE write failed when trying to store valid entry                */
  DISTWR_WRITE_FAIL      = 0xFFu
} phcaiKEyLLGenFunc_DistMem_WriteResult_t;

/**
 * Initialize the configuration structure phcaiKEyLLGenFunc_DistMem_cfg_t.
 * It also powers on the ULPEE to ensure that ULPEE is readable.
 *
 * @param[out] cfg the configuration structure to be populated.
 * @return ERROR if the distance function is not enabled (cfg->is_enabled = FALSE).
 */
error_t phcaiKEyLLGenFunc_DistMem_init( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg );

/**
 * Resets (clears) the internal RAM structure and the distance memory pages in ULPEE.
 * Only active if distance feature is enabled. Does not change the MODE byte.
 *
 * @param[in] cfg the configuration of the device.
 * @return ERROR if any of the ULPEE writes fails or if the feature is disabled (cfg->is_enabled = FALSE).
 *
 * @note Must run phcaiKEyLLGenFunc_DistMem_init before calling this function.
 * @note If feature is disabled, this function will return ERROR.
 */
error_t phcaiKEyLLGenFunc_DistMem_reset( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg );

/**
 * Attempt to write a new distance entry to the distance memory in ULPEE.
 * A distance value greater than or equal to last maximum value will be accepted,
 * otherwise rejected. If the rejected value is smaller than the last stored,
 * it will replace the minimum rejected distance entry.
 * See the HT-PRO2 protocol specification (WRITE_DIST command) for details.
 *
 * @param[in] cfg the configuration of the device.
 * @param[in] data the new data entry to be written to the device
 *
 * @return result code as defined by type phcaiKEyLLGenFunc_DistMem_WriteResult_t.
 *
 * @note Must run phcaiKEyLLGenFunc_DistMem_init before calling this function.
 * @see phcaiKEyLLGenFunc_DistMem_WriteResult_t
 */
phcaiKEyLLGenFunc_DistMem_WriteResult_t
  phcaiKEyLLGenFunc_DistMem_write( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg, phcaiKEyLLGenFunc_DistMem_entry_t *data );

/**
 * Reads a distance entry identified by entry_id from the ULPEE.
 * Index 7 returns the minimum rejected distance entry, in this case,
 * ret_data->date contains the distance entry at the time of that rejection.
 *
 * @param[in]  cfg the configuration of the device.
 * @param[in]  entry_id an index to be read from the distance feature.
 *             Valid entries are numbers from 0 to 7.
 * @param[out] ret_data the entry read from distance memory in ULP EEPROM.
 *
 * @return ERROR if the feature is disabled (cfg->is_enabled = FALSE).
 *
 * @note Must run phcaiKEyLLGenFunc_DistMem_init before calling this function.
 */
error_t phcaiKEyLLGenFunc_DistMem_read_entry( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg, uint8_t entry_id, phcaiKEyLLGenFunc_DistMem_entry_t *ret_data );

/**
 * Reads the latest entry (max distance) written to the ULPEE.
 *
 * @param[in]  cfg       the configuration of the device.
 * @param[out] ret_data  the entry read from distance memory in ULP EEPROM.
 *
 * @return ERROR if the feature is disabled (cfg->is_enabled = FALSE).
 *
 * @note Must run phcaiKEyLLGenFunc_DistMem_init before calling this function.
 */
error_t phcaiKEyLLGenFunc_DistMem_read_latest( phcaiKEyLLGenFunc_DistMem_cfg_t *cfg, phcaiKEyLLGenFunc_DistMem_entry_t *ret_data );

#endif

/*@}*/
/*@}*/

/* eof */
