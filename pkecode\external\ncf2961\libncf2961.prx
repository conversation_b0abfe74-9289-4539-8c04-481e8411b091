<project name="Project" processor="mrk3" lib="..">
    <file type="c" name="ncf2961.c" path=""/>
    <file type="a" name="ncf2961_vector_table_default.s" path=""/>
    <option id="cpp.include" value="../ ../../types/" inherit="1"/>
    <option id="ear.mur" value="on"/>
    <option id="project.dir" value=""/>
    <option id="project.name" value="libncf2961.a"/>
    <option id="project.postbuild" value="../copy_archive.bat ncf2961 libncf2961.a " inherit="1"/>
    <option id="project.type" value="arch"/>
</project>
