/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_Util.c 28748 2020-09-21 16:18:07Z dep10330 $
  $Revision: 28748 $
*/

/**
 * @file
 * Implementation of various utility functions.
 */

/*
  Change Log:

  2013-07-29 (MMr):
  - Improved MISRA-C compliance
  2014-04-07 (MMr):
  - Reworked RNG functions, measured exec times, plausibility checks for output data.
  2014-11-25 (MMr):
  - in phcaiKEyLLGenFunc_Util_Debounce added switch to support NCF2961
  2015-01-21 (MMr):
  - Fixed incorrect use of EROM address in phcaiKEyLLGenFunc_Util_CalcEromSignature
   (for ROM code newer than RC002).
  2015-01-21 (MMr):
  - Improvements for MISRA-C compliance
  2015-10-16 (MMr):
  - added: alternative portable C code in phcaiKEyLLGenFunc_Util_CRC24_update
  - function phcaiKEyLLGenFunc_Util_LoadRWInitData :
    added: parameter b_skip_BSS
    bug fix: consider fill byte in case of odd number of data bytes
    optimized: faster conversion to 16 bit integers
  2016-04-21 (MMr):
  - function phcaiKEyLLGenFunc_Util_LoadRWInitData :
    added workaround for 2016.03 issue with duplicated rodata
  2016-07-15 (MMr):
  - improved MISRA-C (PC-Lint) compliance.
  2016-08-09 (MMr):
  - started port to TOKEN-PLUS/ACTIC5G-PLUS, only switches adapted. Register handling to be reworked.
    E.g.: use generic CRC hardware support, extend number of GPIOs.
  - removed phcaiKEyLLGenFunc_Util_MDI_SendUserData.
  2017-01-26 (MMr):
  - phcaiKEyLLGenFunc_Util_Debounce: simplified, always read P1INS and P2INS for all types
    (P3x is never used)
  - lowercased unsigned specifier in numeric constants ('u')
  2017-08-28 (MMr):
  - added functions phcaiKEyLLGenFunc_Util_MulU16byU16 and phcaiKEyLLGenFunc_Util_MulI16byI16.
  2017-10-17 (MMr):
  - added functions phcaiKEyLLGenFunc_Util_VerifyCompat32, phcaiKEyLLGenFunc_Util_ClearVbatRegRead and
    phcaiKEyLLGenFunc_Util_VerifyVBatRegulator.
  2018-06-11 (MMr):
  - phcaiKEyLLGenFunc_Util_Debounce: allow scanning of P3x ports if PLATFORM_HAS_PORT3x is defined.
  2018-07-06 (MMr):
  - added functions phcaiKEyLLGenFunc_Util_CpuCycleCounter_start and
    phcaiKEyLLGenFunc_Util_CpuCycleCounter_stop.
  2018-07-25 (MMr):
  - phcaiKEyLLGenFunc_Util_CalcEromSignature: reworked to support the TOKEN-SRX platform.
  - phcaiKEyLLGenFunc_Util_CpuCycleCounter_calibrate: fixed inaccuracy due to
    tail call optimization.
  2018-11-09 (MMr):
  - added function phcaiKEyLLGenFunc_Util_MDI_Print (former similar function
    phcaiKEyLLGenFunc_Util_MDI_SendUserData was removed in 2016),
    since it is easier to use with mrk3link tool than the (almost) equivalent system call
    phcaiKEyLLGenFunc_CS_MDI_Print, which sends the data with system mode bit set
  2018-11-27 (MMr):
  - phcaiKEyLLGenFunc_Util_Debounce: fixed incomplete processing of P3x.
    removed WD reset inside loop.
  2019-07-24 (MMr):
  - moved constant BASE_EROM_IN_DM to header phcaiKEyLLGenFunc_Platform.h
    and renamed to PLATFORM_BASE_EROM_IN_DM.
  2020-01-02 (MMr):
  - phcaiKEyLLGenFunc_Util_GenericCRC_bytes: added and tested with R4 setting.
  2020-03-04 (MMr):
  - needed to move the undocumented debug registers DBGDAT and DBGCON to globals
    and declare in header file, so they can be used by external units (RS232.c debug option).
 */

#include "ncf29xx.h"
#include "phcaiKEyLLGenFunc_CS.h"
#include "phcaiKEyLLGenFunc_Timer.h"
#include "phcaiKEyLLGenFunc_TimeConst.h"
#include "phcaiKEyLLGenFunc_ULPEE.h"
#include "phcaiKEyLLGenFunc_Util.h"

//#define WITH_SERIAL_OUTPUT
//#include "RS232.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @addtogroup Util
 * @{
 */


/*-----------------------------------------------------------------------------------------------*/
/* Constants */

/**
 * Size of buffer (on stack) in words used in fct phcaiKEyLLGenFunc_Util_CalcEromSignature.
 * @see phcaiKEyLLGenFunc_Util_CalcEromSignature
 */
#define SIZE_EROM_READ_BUFFER  16u

/**
 * Mask used in function phcaiKEyLLGenFunc_Util_CalcEromSignature
 */
#ifdef PLATFORM_HAS_16KB_EROM
#define EUROMMASK   0x03u
#endif
#ifdef PLATFORM_HAS_32KB_EROM
#define EUROMMASK   0x07u
#endif
#ifdef PLATFORM_HAS_64KB_EROM
#define EUROMMASK   0x0Fu
#endif

/*-----------------------------------------------------------------------------------------------*/

/**
 * Memory configuration register, read only in USER mode (not documented in DS/UM).
 * Bits [10..8] determine the available EROM size in multiples of 4096 Bytes.
 * @see phcaiKEyLLGenFunc_Util_CalcEromSignature
 * Note: do not add 'const' here since this will move the register to the _rodata section
 * and may overwrite EROM vector 0!
 */
static volatile SFR_word chess_storage(DM9:0x0000)  MEMCFG;

/*-----------------------------------------------------------------------------------------------*/

/**
 * Debug data and control register (not documented in DS/UM),
 * can be used for aynchronous user data output to debugger (2-link / mrk3link.exe).
 * Same on all platforms.
 * @see phcaiKEyLLGenFunc_Util_MDI_Print
 */
volatile SFR_word chess_storage(DM9:0x0002)  DBGDAT;
volatile SFR_word chess_storage(DM9:0x0004)  DBGCON;

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Util_Debounce( uint8_t  u8arr_button_results[3],
                                      uint16_t u16_delay_us,
                                      uint8_t  u8_num_samples,
                                      uint8_t  u8_threshold )
{
  uint8_t u8arr_counters[PLATFORM_PORT_MAJOR_MAX][8]; /* one counter for each port bit */
  uint8_t u8_portmaj, u8_portmin, u8_cntsamples;
  uint8_t u8_portsense[PLATFORM_PORT_MAJOR_MAX];

  /* reset all counters and result bits */
  for ( u8_portmaj = 0u; u8_portmaj < PLATFORM_PORT_MAJOR_MAX; u8_portmaj++ )
  {
    u8arr_button_results[u8_portmaj] = 0x00u;
    for ( u8_portmin = 0u; u8_portmin < 8u; u8_portmin++ )
    {
      u8arr_counters[u8_portmaj][u8_portmin] = 0u;
    }
  }

  /* Port sense loop.
   Takes at least  ( (u8_num_samples-1) * u16_delay_us ) microseconds */
  u8_cntsamples = 0u;
  while ( TRUE )
  {
    u8_portsense[0] = P1INS.val;
#ifdef PLATFORM_HAS_PORT2x
    u8_portsense[1] = P2INS.val;
#endif
#ifdef PLATFORM_HAS_PORT3x
    u8_portsense[2] = P3INS.val;
#endif

    for ( u8_portmaj = 0u; u8_portmaj < PLATFORM_PORT_MAJOR_MAX; u8_portmaj++ )
    {
      for ( u8_portmin = 0u; u8_portmin < 8u; u8_portmin++ )
      {
        // increment counter if port input sense is 1.
        if ( ( u8_portsense[u8_portmaj] & (uint8_t)( 1u << u8_portmin ) ) != 0u )
        {
          u8arr_counters[u8_portmaj][u8_portmin]++;
        }
      }
    }

    u8_cntsamples++;
    if ( u8_cntsamples == u8_num_samples )
    {
      break;
    }

    phcaiKEyLLGenFunc_timer0_delay_us( u16_delay_us );

    //WDCON.bits.WDCLR = 1u;                   /* Reset watchdog timer    */
  }

  /* evaluation: */
  /* if a counter is greater or equal u8_threshold, set result bit to 1, else 0. */
  for ( u8_portmaj = 0u; u8_portmaj < PLATFORM_PORT_MAJOR_MAX; u8_portmaj++ )
  {
    for ( u8_portmin = 0u; u8_portmin < 8u; u8_portmin++ )
    {
      if ( u8arr_counters[u8_portmaj][u8_portmin] >= u8_threshold )
      {
        u8arr_button_results[u8_portmaj] |= ( 1u << u8_portmin );
      }
    }
  }
}


/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Util_ToHexStr( uint8_t u8arr_Res[5], const uint16_t u16_Num )
{
  uint16_t tmp;
  int16_t  i;
  uint8_t  nib;
  tmp = u16_Num;
  for ( i = 3; i >= 0; i-- )
  {
    nib = (uint8_t) (tmp & 0x000FU);
    tmp >>=4;
    if ( nib < 10u )
    {
      u8arr_Res[i] = (uint8_t)'0' + nib;
    }
    else
    {
      u8arr_Res[i] = ( (uint8_t)'A' + nib ) - 10u;
    }
  }
  u8arr_Res[4] = 0u;
}

/*-----------------------------------------------------------------------------------------------*/

uint8_t phcaiKEyLLGenFunc_Util_CRC8_bytes( const uint8_t * pu8_data,
                                           uint16_t        u16_count,
                                           uint8_t         u8_lastCrc )
{
  CRCDAT.val = u8_lastCrc;
  while ( (u16_count--) > 0u )
  {
    CRC8DIN.val    = *pu8_data++;
  }
  return CRCDAT.val;
}

/*-----------------------------------------------------------------------------------------------*/

uint8_t phcaiKEyLLGenFunc_Util_CRC8_update( uint8_t u8_data, uint8_t u8_lastCrc )
{
  CRCDAT.val     = u8_lastCrc;
  CRC8DIN.val    = u8_data;
  return CRCDAT.val;
}

/*-----------------------------------------------------------------------------------------------*/

uint16_t phcaiKEyLLGenFunc_Util_CRC16_update_sw( uint8_t u8_data, uint16_t u16_lastCrc )
{
  uint16_t u16_crc_new;
  u8_data = u8_data ^ (uint8_t) ( u16_lastCrc & 0x00FFU );
  u8_data = u8_data ^ (uint8_t) (u8_data<<4);
  u16_crc_new =  (uint16_t)(u16_lastCrc >> 8)       ^ (uint16_t)((uint16_t)u8_data << 8)
               ^ (uint16_t)((uint16_t)u8_data << 3) ^ (uint16_t)((uint16_t)u8_data >> 4);
  return u16_crc_new;
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Util_CRC24_update( uint16_t u16_input_data, uint8_t u8arr_lastCRC[3] )
{
#ifndef _lint
  // non-portable C code (MRK-III only)
  uint1_ c;
  uint2_ nz;
  uint8_t * hash1;
  uint16_t* hash2;

  hash1 = (uint8_t *)(&u8arr_lastCRC[0]);
  hash2 = (uint16_t*)(&u8arr_lastCRC[1]);

  /* using intrinsic functions for MRK-III */
  *hash2 = shr_cmp0( (int&) *hash2, 1u, c, nz        );  /* shift right & compare with 0             */
  *hash1 = rrc_cmp0( (char&)*hash1, 1u, c, nz, c, nz );  /* rotate right with carry & compare with 0 */
  if ( !cc_ae(c, nz) )
  {
    *hash2 = *hash2 ^ (uint16_t)0xD800u;
  }
  *hash2 = *hash2 ^ u16_input_data;
#else
  // Portable C code
  uint32_t u32_reg;
  uint8_t u8_carry;

  u32_reg = (uint32_t) (  ((uint32_t)u8arr_lastCRC[0])
                        | ((uint32_t)u8arr_lastCRC[1] << 8)
                        | ((uint32_t)u8arr_lastCRC[2] << 16) );
  u8_carry = ((uint8_t) u32_reg) & 0x01u;
  u32_reg >>= 1;
  if ( u8_carry != 0u ) {
    u32_reg ^= 0x00D80000u;
  }
  u32_reg ^= ( (uint32_t)u16_input_data << 8 );

  u8arr_lastCRC[0] = (uint8_t) u32_reg & 0xFFU;
  u32_reg >>= 8;
  u8arr_lastCRC[1] = (uint8_t) u32_reg & 0xFFU;
  u32_reg >>= 8;
  u8arr_lastCRC[2] = (uint8_t) u32_reg & 0xFFU;
#endif
}

/*-----------------------------------------------------------------------------------------------*/

#ifdef PLATFORM_HAS_GENERICCRC

uint16_t phcaiKEyLLGenFunc_Util_GenericCRC_bytes( const uint8_t * pu8_data,
                                                  uint16_t        u16_count,
                                                  uint16_t        u16_initCRC,
                                                  uint16_t        u16_polynomial,
                                                  uint8_t         u8_CtrlReg )
{
  GCRCCON0.val = u8_CtrlReg;
  GCRCPOLY.val = u16_polynomial;
  GCRCDAT.val  = u16_initCRC;

  while ( (u16_count--) > 0u )
  {
    /* Note, for less overhead, we do not call phcaiKEyLLGenFunc_Util_GenericCRC_update(). */
    GCRCDIN.val = *pu8_data++;
  }
  return GCRCDAT.val;
}

#endif

/*-----------------------------------------------------------------------------------------------*/

#ifdef PLATFORM_HAS_GENERICCRC

uint16_t phcaiKEyLLGenFunc_Util_GenericCRC_update( uint8_t u8_data, uint16_t u16_lastCrc )
{
  GCRCDAT.val  = u16_lastCrc;
  GCRCDIN.val  = u8_data;
  return GCRCDAT.val;
}

#endif

/*-----------------------------------------------------------------------------------------------*/

assembly
uint32_t chess_storage(RwL)
phcaiKEyLLGenFunc_Util_MulU16byU16( uint16_t chess_storage(R0) u16_factor1,
                                    uint16_t chess_storage(R2) u16_factor2  )
{
  /*
   parameters are: R0 = u16_factor1, R2=u16_factor2
   results: (R1,R0) = product (32 bit)
   notes: opcode = 7454h (see MRK-IIIe Programmer's Reference, means bits W=1, D=1)
   duration: 4 machine cycles for MULU instruction
   */
#ifndef _lint
  asm_begin
  MULU R1,R0,R2
  RET
  asm_end
#else
  return (uint32_t) ( u16_factor1 * u16_factor2 );
#endif
}

/*-----------------------------------------------------------------------------------------------*/

assembly
int32_t chess_storage(RwL)
phcaiKEyLLGenFunc_Util_MulI16byI16( int16_t chess_storage(R0) i16_factor1,
                                    int16_t chess_storage(R2) i16_factor2  )
{
  /*
   parameters are: R0 = i16_factor1, R2=i16_factor2
   results: (R1,R0) = product (32 bit)
   notes: opcode = 7554h (see MRK-IIIe Programmer's Reference, means bits W=1, D=1)
   duration: 4 machine cycles for MULS instruction
   */
#ifndef _lint
  asm_begin
  MULS R1,R0,R2
  RET
  asm_end
#else
  return (sint32_t) ( i16_factor1 * i16_factor2 );
#endif
}

/*-----------------------------------------------------------------------------------------------*/

assembly
uint16_t chess_storage(R0)
phcaiKEyLLGenFunc_Util_DivideU32byU16( uint32_t chess_storage(RwL) u32_dividend,
                                       uint16_t chess_storage(R2)  u16_divisor  )
{
  /*
   parameters are: R0 = low(u32_dividend), R1=high(u32_dividend), R2=u16_divisor
   results: R0=quotient (passed back to caller), R1=remainder
   notes: opcode = 7654h (see MRK-IIIe Programmer's Reference, means bits W=1, D=1)
   duration: 8 machine cycles for DIVU instruction
   */
#ifndef _lint
  asm_begin
  DIVU R1,R0,R2
  RET
  asm_end
#else
  return (uint16_t) ( u32_dividend / (uint32_t) u16_divisor );
#endif
}

/*-----------------------------------------------------------------------------------------------*/

assembly
int16_t chess_storage(R0)
phcaiKEyLLGenFunc_Util_DivideI32byI16( int32_t chess_storage(RwL) i32_dividend,
                                       int16_t chess_storage(R2)  i16_divisor  )
{
  /*
   parameters are: R0 = low(i32_dividend), R1=high(i32_dividend), R2=i16_divisor
   results: R0=quotient (passed back to caller), R1=remainder
   notes: opcode = 7654h (see MRK-IIIe Programmer's Reference, means bits W=1, D=1)
   duration: 8 machine cycles for DIVU instruction
   */
#ifndef _lint
  asm_begin
  DIVS R1,R0,R2
  RET
  asm_end
#else
  return (int16_t) ( i32_dividend / (int32_t) i16_divisor );
#endif
}

/*-----------------------------------------------------------------------------------------------*/

#if ( ( (__tct_release__ * 100 ) + __tct_patch__) < 120110 )

/*
   Note: the "property(keep_in_registers)" annotation has been introduced in
   Chess release 12R.10.
 */
#warning Tool suite version before 12R1.10 is used, extended divide functions not available.

#else

assembly
phcaiGFL_ldivu_t chess_storage(RwL)
phcaiKEyLLGenFunc_Util_DivideU32byU16_ext( uint32_t chess_storage(RwL) u32_dividend,
                                           uint16_t chess_storage(R2)  u16_divisor,
                                           chess_output  uint8_t&  chess_storage(R3L) ru8_ovflag   )
{
  /*
   parameters are: R0 = low(u32_dividend), R1=high(u32_dividend), R2=u16_divisor
   results: R0=quotient, R1=remainder (passed back to caller)
   R3L=overflow flag
   */
#ifndef _lint
  asm_begin

  DIVU R1,R0,R2
  // move the O(verflow) flag to R3L bit 0
  BO  _phcaiKEyLLGenFunc_Util_DIVU_ext_is_overflow
  MOV R3L,#0
  RET

.label _phcaiKEyLLGenFunc_Util_DIVU_ext_is_overflow
  MOV R3L,#1
  RET

  asm_end
#else
  phcaiGFL_ldivu_t s_res;
  s_res.quot = (uint16_t) ( u32_dividend / (uint32_t) u16_divisor );
  s_res.rem  = (uint16_t) ( u32_dividend % (uint32_t) u16_divisor );
  ru8_ovflag = 0u; // not possible in C
  return s_res;
#endif
}

/*-----------------------------------------------------------------------------------------------*/

assembly
phcaiGFL_ldivs_t chess_storage(RwL)
phcaiKEyLLGenFunc_Util_DivideI32byI16_ext( int32_t chess_storage(RwL) i32_dividend,
                                           int16_t chess_storage(R2)  i16_divisor,
                                           chess_output  uint8_t&  chess_storage(R3L) ru8_ovflag   )
{
  /*
   parameters are: R0 = low(i32_dividend), R1=high(i32_dividend), R2=i16_divisor
   results: R0=quotient, R1=remainder (passed back to caller)
   */
#ifndef _lint
  asm_begin
  DIVS R1,R0,R2

  // move the O(verflow) flag to R3L bit 0
  BO  _phcaiKEyLLGenFunc_Util_DIVS_ext_is_overflow
  MOV R3L,#0
  RET

.label _phcaiKEyLLGenFunc_Util_DIVS_ext_is_overflow
  MOV R3L,#1
  RET

  asm_end
#else
  phcaiGFL_ldivs_t s_res;
  s_res.quot = (int16_t) ( i32_dividend / (int32_t) i16_divisor );
  s_res.rem  = (int16_t) ( i32_dividend % (int32_t) i16_divisor );
  ru8_ovflag = 0u; // not possible in C
  return s_res;
#endif
}

#endif

/*-----------------------------------------------------------------------------------------------*/

/* experimental, use with care! */
assembly
uint8_t chess_storage(R0L) phcaiKEyLLGenFunc_Util_isOverflow( void )
{
#ifndef _lint
  asm_begin

  BO.r  _phcaiKEyLLGenFunc_Util_isOverflow_1
  MOV R0L,#0  // Overflow == 0
  RET

.label _phcaiKEyLLGenFunc_Util_isOverflow_1
  MOV R0L,#1  // Overflow == 1
  RET

  asm_end

#else
  return 0u;
#endif
}

/*-----------------------------------------------------------------------------------------------*/

error_t phcaiKEyLLGenFunc_Util_CalcEromSignature( uint8_t u8arr_signature[3] )
{
  eeprom_write_error_t sc_res;
  uint16_t u16arr_readbuffer[SIZE_EROM_READ_BUFFER];
  uint32_t u32_eromaddr;
  uint16_t * pu16_eromptr;
  uint32_t u32_EROM_size;
  uint16_t i;

  //DebugPrintString("User function for EROM signature calculation\n");
  /* Initial value of CRC register is 0x000000. */
  u8arr_signature[0]=0x00u;
  u8arr_signature[1]=0x00u;
  u8arr_signature[2]=0x00u;

  /* Determine actual EROM size in BYTES from current memory configuration. */
  u32_EROM_size = (uint32_t) (( (uint32_t)((uint16_t)MEMCFG.byte.hi & EUROMMASK) + 1u ) << 12);
  //DebugPrintVarData("EROM size (bytes) in this product: %Yh\n", u32_EROM_size );

  for ( u32_eromaddr = 0x0000u; u32_eromaddr < PLATFORM_BASE_EROM_IN_DM; u32_eromaddr += (SIZE_EROM_READ_BUFFER*2u) )
  {
    /* Below PLATFORM_BASE_EROM_IN_DM, we must use the syscall to read the EROM.
       The syscall expects the WORD address in EROM and the number of BYTES to read. */
    sc_res = phcaiKEyLLGenFunc_CS_EROM_read( u16arr_readbuffer,
                                             (uint16_t)(u32_eromaddr >> 1),
                                             (uint16_t)(SIZE_EROM_READ_BUFFER*2u) );
    if ( sc_res != EE_WR_OK )
    {
      /* Note: failure should never happen here since we use only valid and even EROM (byte) addresses. */
      //DebugPrintVarData("%X: _EROM_read FAILED with error code %x\n", u32_eromaddr, sc_res );
      return ERROR;
    }
    else
    {
      //DebugPrintVarData("%X OK\n", u32_eromaddr);
      for ( i = 0u; i < SIZE_EROM_READ_BUFFER; i++ )
      {
        phcaiKEyLLGenFunc_Util_CRC24_update( u16arr_readbuffer[i], u8arr_signature );
      }
    }
  }
  /*
    From PLATFORM_BASE_EROM_IN_DM on, the EROM is directly readable from data memory.
    Increment is one word = 2 bytes.
   */
  for ( pu16_eromptr = (uint16_t *)u32_eromaddr; u32_eromaddr < u32_EROM_size; u32_eromaddr += 2u )
  {
    phcaiKEyLLGenFunc_Util_CRC24_update( *pu16_eromptr++, u8arr_signature );
  }

  return SUCCESS;
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Util_TrueRNG_init( void )
{
  /* Reset the RNG. */
  RNGCON.val = RNGCON_RESET;
  /* Set the Initialization mode. */
  RNGCON.val = RNGCON_TRUE_INITMODE;
  /* Wait to startup the oscillators */
  phcaiKEyLLGenFunc_timer0_delay_us( t_RNG_SETT_us );
  /* Set the RUN bit to start the RN generation. */
  RNGCON.val |= 0x80u;
  /* Wait while RNG is running. */
  while ((RNGCON.val & 0x80u) != 0x00u)
  {
  }
  /* set the hybrid mode for next run, keeps RNGEN=1. */
  RNGCON.val = RNGCON_TRUE_HYBRIDMODE;
}

/*-----------------------------------------------------------------------------------------------*/

uint16_t phcaiKEyLLGenFunc_Util_RNG_run( RNG_Mode_t e_rngmode, bool_t b_switchoff )
{
  bool_t osc_is_running;
  uint16_t u16_result;

  /* Wait while RNG is (possibly still) running. */
  while ( (RNGCON.val & 0x80u) != 0x00u )
  {
  }

  if ( RNG_TRUE == e_rngmode )
  {
    osc_is_running = ( (RNGCON.val & 0x02u) != 0x00u) ? TRUE : FALSE;
    /* Set the hybrid mode, enable osc. (RNGEN=1). */
    RNGCON.val = RNGCON_TRUE_HYBRIDMODE;
    if ( FALSE == osc_is_running )
    {
      /* Wait to startup the oscillators */
      phcaiKEyLLGenFunc_timer0_delay_us( t_RNG_SETT_us );
    }
  }
  else if ( RNG_CRC16 == e_rngmode )
  {
    RNGCON.val = RNGCON_CRC16_MODE;
  }
  else if ( RNG_LFSR == e_rngmode )
  {
    RNGCON.val = RNGCON_LFSR_MODE;
  }
  else
  {
    /* undefined parameter */
    return 0u;
  }

  /* Set the RUN bit to start the RN production. */
  RNGCON.val |= 0x80u;
  /* Wait while RNG is running. */
  while ((RNGCON.val & 0x80u) != 0x00u)
  {
  }
  /* Read result. */
  u16_result = RNGDAT.val;

  if ( ( RNG_TRUE == e_rngmode ) && ( b_switchoff == TRUE ) )
  {
    /* Switch off the RNG oscillators. */
    RNGCON.bits.RNGEN = 0u;
  }
  return u16_result;
}


/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Util_LoadRWInitData( bool_t b_skip_BSS )
{
  /* Note: external symbol _rwinit_data is only generated by the linker with added option +i . */
  uint8_t  * p = &_rwinit_data[0];  // pointer incrementing byte-wise over the rwinit table.
  uint16_t   u16_ndata, u16_nbss;
  uint8_t  * pu8_addr;
  uint16_t   u16_nval;
  uint16_t   i,j;

  /* initialize DATA segements */
  //DebugPrintVarData("Start of rwinit table: 0x%X\n", (uint16_t) p );
  // get number of data items
  u16_ndata  = *(uint16_t*) p;
  p += 2u;
  //DebugPrintVarData("Number of DATA items: %u\n", u16_ndata );
  for ( i = 0u; i < u16_ndata; i++ )
  {
    // get destination address in RAM
    pu8_addr  = *(uint8_t**)p;
    p += 2u;
    // get number of bytes to copy
    u16_nval  = *(uint16_t*) p;
    p += 2u;
    //DebugPrintVarData("  DATA item nr. %u @ DM:0x%X with %u values\n", i, (uint16_t)pu8_addr, u16_nval);
    if ( ((uint16_t)pu8_addr >= RAM_START) && ((((uint16_t)pu8_addr)+u16_nval) <= RAM_END ) )
    {
      //DebugPrintVarData("  data: ");
      // copy bytes from EROM to RAM
      for ( j = 0u; j < u16_nval; j++ )
      {
        //DebugPrintVarData("%x ", (uint8_t) *p );
        *pu8_addr++ = *p++;
      }
      //DebugPrintVarData("\n");
    }
    else
    {
      // workaround for issue with 2016.03 (linker duplicates rodata in rwinit):
      // skip the rwinit data
      p += u16_nval;
      //DebugPrintVarData("  (skipped since not in RAM!)\n");
    }
    // if the number of bytes is not even, the linker inserts a padding byte.
    if ( (uint16_t)( u16_nval & 0x0001u ) != 0u )
    {
      //DebugPrintVarData("  skipping fill byte at address 0x%X\n", (uint16_t) p );
      p++;
    }
  }

  if ( FALSE == b_skip_BSS )
  {
    /* fill BSS with zeroes */
    // get number of BSS items
    u16_nbss    = *(uint16_t*) p;
    p += 2u;
    //DebugPrintVarData("Number of BSS items: %u\n", u16_nbss );
    for ( i = 0u; i < u16_nbss; i++ )
    {
      pu8_addr  = *(uint8_t**)p;
      p += 2u;
      u16_nval  = *(uint16_t*)p;
      p += 2u;
      //DebugPrintVarData("  BSS item nr. %u @ DM:0x%X with %u zeroes\n", i, (uint16_t)pu8_addr, u16_nval);
      // Note: Incorrect rwinit table data for BSS has not been observed so far, nevertheless,
      // this extra precaution is recommended in order to prevent unwanted writes to registers or EROM
      // in case of a corrupted rwinit table.
      if ( ((uint16_t)pu8_addr >= RAM_START) && ((((uint16_t)pu8_addr)+u16_nval) <= RAM_END ) )
      {
        for ( j = 0u; j < u16_nval; j++ )
        {
          *pu8_addr++ = 0u;
        }
      }
      else
      {
        //DebugPrintVarData("  (skipped since not in RAM!)\n");
      }
    }
  }
}

/*-----------------------------------------------------------------------------------------------*/

error_t phcaiKEyLLGenFunc_Util_VerifyCompat32( void )
{
#ifdef PLATFORM_TOKENPLUS

  error_t e_res = ERROR;
  uint8_t u8_configbit;
  /* read config page 3F0h, byte 3, bit 3. Note: includes wait time if needed to be switched on. */
  u8_configbit = (uint8_t)((phcaiKEyLLGenFunc_ULPEE_ReadOneByte( 4035u ) & 0x08u ) >> 3 );
  if ( BATSYS0.bits.TOKEN32_COMPAT == u8_configbit ) {
    e_res = SUCCESS;
  }
  return e_res;

#else

  // On all other platforms, there is no need to verify the setting.
  return SUCCESS;

#endif

}

/*-----------------------------------------------------------------------------------------------*/
#ifdef PLATFORM_HAS_CPUMCCNT

void phcaiKEyLLGenFunc_Util_CpuCycleCounter_start( CpuMCnt_Mode_t en_mode )
{
  CPUMCCCON.val = 0x01u;   /* RSTCNT = 1, reset counter */
  if ( en_mode == CPUMCC_MACHINECLOCK ) {
    CPUMCCCON.bits.CLKSELCNT = 1u;
  }
  else if ( en_mode == CPUMCC_SYSTEMCLOCK ) {
    /* No Op; CPUMCCCON.bits.CLKSELCNT == 0u */
  }
  else {
    return; /* unknown mode */
  }
  CPUMCCCON.bits.ENCNT = 1u; /* start the counter */
}

uint32_t phcaiKEyLLGenFunc_Util_CpuCycleCounter_stop( void )
{
  uint32_t u32_res;
  CPUMCCCON.bits.ENCNT = 0u; /* stop the counter */
  u32_res = (uint32_t)( (uint32_t)CPUMCCCNT0.val | ((uint32_t)CPUMCCCNT1.val << 16 ) );
  return u32_res;
}

uint32_t phcaiKEyLLGenFunc_Util_CpuCycleCounter_calibrate( CpuMCnt_Mode_t en_mode )
{
  uint32_t u32_res;
  volatile uint8_t u8_dummy;

  phcaiKEyLLGenFunc_Util_CpuCycleCounter_start( en_mode );
  u32_res = phcaiKEyLLGenFunc_Util_CpuCycleCounter_stop();
  /* Avoid tail call optimization here, since it causes inaccuracy.
  In this function, there must be two normal calls to above functions, no jump,
  otherwise we get a lower cycle count (e.g. one machine cycle in Release build) than in
  the measurement use case.
  */
  u8_dummy = (uint8_t)en_mode;
  /* Note, also possible:
  uint8_t u8_dummy = P1INS.val;
  nop();
  */
  return u32_res;
}


#endif

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_Util_MDI_Print( const string_t * const ps_MdiString )
{
  const uint16_t * pu16_data;
  uint16_t i;

  /* stop sending if the lower or upper byte is 0. */
  for (i = 0u; ps_MdiString[i] != '\0'; i += 2u)
  {
    pu16_data = (const uint16_t*)(&ps_MdiString[i]);
    /* wait while debug data register is busy */
    while ((DBGCON.byte.lo & 0x40u) == 0x40u)
    {
    }
    DBGDAT.val = *pu16_data;
    if (ps_MdiString[i + 1u] == '\0')
    {
      break;
    }
  }
}

/*-----------------------------------------------------------------------------------------------*/

/*@}*/
/*@}*/

/* eof */
