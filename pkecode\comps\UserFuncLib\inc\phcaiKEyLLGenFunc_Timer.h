/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_Timer.h 20054 2019-05-10 10:59:25Z dep10330 $
  $Revision: 20054 $
*/

/**
 * @file
 * Declarations of User Functions for timer related tasks.
 */

#ifndef PHCAIKEYLLGENFUNC_TIMER_H
#define PHCAIKEYLLGENFUNC_TIMER_H

#include "types.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @defgroup Timers  Timer related functions
 * Support functions for timers.
 * @{
 */

/**
 * Delay by a number of microseconds using timer 0 and polling (no IDLE mode).
 * No timer used in case of zero parameter.
 *
 * @param[in] u16_us  Number of microseconds to delay.
 */
void phcaiKEyLLGenFunc_timer0_delay_us( uint16_t u16_us );

/**
 * Delay by a number of milliseconds using timer 0 and IDLE mode.
 * No timer used in case of zero parameter.
 *
 * @param[in] u16_ms  Number of milliseconds to delay.
 */
void phcaiKEyLLGenFunc_timer0_delay_ms( uint16_t u16_ms );


/**
 * Start timer 0 with 1 MHz clock and set time in microseconds.
 * Timer is not started in case of zero parameter.
 *
 * @param[in] u16_us  Number of microseconds to delay.
 */
void phcaiKEyLLGenFunc_Timer0_Start_us( uint16_t u16_us );


/**
 * This function waits in a loop until timer0 has expired.
 * If timer 0 is not running, returns immediately.
 */
void  phcaiKEyLLGenFunc_Timer0_Wait(void);


/**
 * Start Timer 1 (mode 0) with 1 MHz AUXCLK via TMUX1C.
 *
 */
void  phcaiKEyLLGenFunc_Timer1_Start_AuxClk( void );

/**
 * Start Timer 1 (mode 0) with 1 kHz AUXCLK via TMUX1C.
 *
 */
void  phcaiKEyLLGenFunc_Timer1_Start_1kHz( void );

/**
 * Capture (manually) the value of timer1 register.
 * Timer1 must be in RUN state, otherwise 0 is returned.
 *
 * @return Value read from T1CAP register.
 *
 */
uint16_t phcaiKEyLLGenFunc_Timer1_Capture( void );

/**
 * Stop timer1 and wait until stopped.
 *
 */
void phcaiKEyLLGenFunc_Timer1_Stop( void );

/**
 * This function waits in a loop until timer1 compare event.
 * If timer 1 is not running, returns immediately.
 */
void  phcaiKEyLLGenFunc_Timer1_Cmp_Wait(void);



/** Short name of frequently used function. */
#define timer0_delay_us	  phcaiKEyLLGenFunc_timer0_delay_us

/** Short name of frequently used function. */
#define timer0_delay_ms	  phcaiKEyLLGenFunc_timer0_delay_ms

/*@}*/
/*@}*/

#endif
/* eof */
