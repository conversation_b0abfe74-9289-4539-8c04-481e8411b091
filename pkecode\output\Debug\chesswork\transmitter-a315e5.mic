
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:20 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-a315e5 mrk3


// m3;   next: m4 (next offset: 3)
000000  1 0  "0000010010010111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000001  2 0  "0110101001000011"   // (DM[0]) = _pl_rd_res_reg_const_store_1_B1 (RbL[0],0,DM[0],R7); 
000002  0 0  "0000000000000000"   // /

// m4 chess_separator_scheduler;   next: m5 (next offset: 3)

// m5;   next: m6 (next offset: 4)
000003  1 0  "0110101110001000"   // (DM[1]) = _pl_rd_res_reg_const_store_const_1_B3 (1,DM[1],R7); 

// m6 chess_separator_scheduler;   next: m113 (next offset: 4)

// m113;   next: m106, jump target: m11 (next offset: 7)
000004  2 0  "0100001010001011"   // (nz_flag,c_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B2 (0,DM[0],R7); 
000005  0 0  "0000000000000000"   // /
000006  1 0  "0101000000001001"   // () = cc_eq__jump_const_1_B1 (nz_flag,9); 

// m106;   next: m33 (next offset: 9)
000007  2 0  "0010000000100000"   // (DM9,PM,nz_flag) = load_const__ad_const_store_1_B1 (0,DM9,PM); 
000008  0 0  "0000000011011111"   // /

// m33 chess_separator_scheduler;   next: m34 (next offset: 9)

// m34;   next: m35 (next offset: 13)
000009  2 0  "0110100000000000"   // (RbL[0]) = const_1_B1 (); 
000010  0 0  "0000000000100110"   // /
000011  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000012  0 0  "0000000000000000"   // /

// m35 subroutine call;   next: m37 (next offset: 13)

// m37, jump target: m42 (next offset: 15)
000013  1 0  "0110101110000000"   // (DM[1]) = store_const__pl_rd_res_reg_const_1_B3 (1,DM[1],R7); 
000014  1 0  "0101101000001110"   // () = jump_const_1_B1 (14); 

// m11;   next: m12 (next offset: 19)
000015  2 0  "0110100000000000"   // (RbL[0]) = const_3_B1 (); 
000016  0 0  "0000000001100110"   // /
000017  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000018  0 0  "0000000000000000"   // /

// m12 subroutine call;   next: m92 (next offset: 19)

// m92;   next: m17 (next offset: 21)
000019  2 0  "0010100000100000"   // (DM9,PM,nz_flag) = load_const__or_const_store_1_B1 (0,DM9,PM); 
000020  0 0  "0000000000100000"   // /

// m17 chess_separator_scheduler;   next: m18 (next offset: 21)

// m18;   next: m19 (next offset: 25)
000021  2 0  "0110110000000000"   // (RwL[0]) = const_2_B1 (); 
000022  0 0  "0000000001010011"   // /
000023  2 0  "0000111111000000"   // () = call_const_1_B1 (0); 
000024  0 0  "0000000000000000"   // /

// m19 subroutine call;   next: m119 (next offset: 25)

// m119;   next: m25, jump target: m27 (next offset: 27)
000025  2 0  "0100110000000011"   // (DM9,PM,nz_flag) = load_const__ad_const_cmp_const_cc_eq__jump_const_1_B1 (0,3,DM9,PM); 
000026  0 0  "0000000010000000"   // /

// m25;   next: m42 (next offset: 28)
000027  1 0  "0110101110000000"   // (DM[1]) = store_const__pl_rd_res_reg_const_1_B3 (1,DM[1],R7); 

// m27;   next: m42 (next offset: 28)

// m42 (next offset: /)
000028  1 0  "0110101100000000"   // (RbL[0]) = load__pl_rd_res_reg_const_1_B2 (1,DM[1],R7); 
000029  1 0  "0001010010010111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000030  1 0  "0001101111000100"   // () = ret_1_B1 (); 

