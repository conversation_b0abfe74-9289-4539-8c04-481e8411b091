/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_Vbat.h 18366 2019-02-19 15:01:29Z dep10330 $
  $Revision: 18366 $
*/

/**
 * @file
 * Declarations of the stubs to call VBAT specific system functions (ROM library).
 */

#ifndef PHCAIKEYLLGENFUNC_CS_VBAT_H
#define PHCAIKEYLLGENFUNC_CS_VBAT_H

#include "types.h"

/**
 * @defgroup genfunclib_stubs_vbat VBAT
 * Caller stubs for VBAT specific API functions placed
 * in ROM and executed in system mode.
 * @{
 */

/**
 * Turns the VBAT regulator on or off.
 *
 * @param[in] state Desired state if the VBAT regulator.
 * - If ::TRUE, the regulator gets switched on (or remains on),
 *   the VBATREG domain is reset, and the trim registers in the VBATREG domain are updated.
 * - If ::FALSE the regulator gets switched off and the reset is activated
 *   (BATRGLRST = 1).
 */
void phcaiKEyLLGenFunc_CS_SetVBatRgl(const bool_t state);

/**
 * Refreshes the VBATREG domain (re-reads trim data for the VBATREG domain).
 *
 * @pre The VBAT regulator needs to be switched on. This function has no impact
 * if the VBAT regulator is switched off.
 *
 * @see phcaiKEyLLGenFunc_CS_SetVBatRgl()
 */
void phcaiKEyLLGenFunc_CS_RefreshVBatRgl(void);

/**
 * Performs a reset of the VBAT domain and re-reads the trim data
 * for the VBAT domain.
 */
void phcaiKEyLLGenFunc_CS_ResetVBat(void);

/**
 * Sets or clears the Battery Power On Reset flag (BATPORFLAG) in the BATSYS0
 * register.
 *
 * @param[in] value If ::TRUE the BatPOR flag is set, if ::FALSE it is cleared.
 */
void phcaiKEyLLGenFunc_CS_SetBatPORFlag(const bool_t value);

/**
 * Writes the given LFTUNEVDD settings to the configuration ULPEE (page 0x3D4).
 *
 * @param[in] data Source buffer (4 bytes) containing the data to be written,
 * with the most significant byte being the first byte in the array.
 * @return ::SUCCESS in case of success, ::ERROR in case an error occurred while
 * writing to the ULPEE (HVERROR).
 *
 * @note This function behaves like the syscall
 * phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage() given target page 0x3D4.
 * @note The syscall always writes to page 0x3D4 regardless of any address
 * stored in the ulpee_write_pg.addr (syscall interface data structure).
 * @note With exception of this function, pages in ULPEE module 15 cannot be
 * written by the application code.
 * @note The interrupt flag IF_ULP is '1' at return from system mode.
 * @see phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPage()
 */
 error_t phcaiKEyLLGenFunc_CS_ULPEE_Write_ULPEE_LFTUNE(const uint8_t * const data);

#if defined(PHFL_CONFIG_HAVE_LFA_TEST_SLICER_OUTPUT) && \
      (PHFL_CONFIG_HAVE_LFA_TEST_SLICER_OUTPUT == CONFIG_YES)
/**
 * @brief Map a slicer output signal to P27.
 *
 * Map the synchronized CMF or MMF slicer output signal to the digital
 * test bus DTB1 and wire DTB1 to P27.
 *
 * @param[in] option Determines the desired output configuration
 * (CMF slicer, MMF slicer, none).
 *
 * @pre The VBATREG domain must be enabled.
 */
void phcaiKEyLLGenFunc_CS_VbatReg_TestLfaSlicerOutput(
  const phcaiKEyLLGenFunc_CS_VbatReg_TestLfaSlicerOutputOption_t option);
#endif

/*@}*/

#endif
