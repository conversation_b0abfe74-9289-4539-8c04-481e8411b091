/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_LfActive.c 31225 2021-03-10 14:44:11Z dep10330 $
  $Revision: 31225 $
*/

/*+----------------------------------------------------------------------------- */
/*| NOTE: The code provided herein is still under development and hence          */
/*|       subject to change.                                                     */
/*+----------------------------------------------------------------------------- */

/**
 * @file
 * Implementation of User Library Functions related to the LF Active receiver.
 *
 * @todo consider adding checks for validity of calibration counter register.
 */

/*
  Change Log:
  -----------
  2017-08-21 (MMr): created new module
  2017-09-12 (MMr): added phcaiKEyLLGenFunc_LfAct_read_PRESTAT
  2017-10-27 (MMr): moved CRC-8 handling back to lf_receive.c.
  2018-06-15 (MMr): separate definition of PRESTATMODE_t for SRX types to avoid confusion.
  2018-07-17 (MMr): phcaiKEyLLGenFunc_LfAct_PostWupTimer_wait: added error check.
  2018-08-22 (MMr): added function phcaiKEyLLGenFunc_LfAct_reset_rx.
  2018-12-13 (MMr): removed PRESTAT.PMCV bit (but kept the counting for now)
  2019-01-10 (MMr):
  - _LfAct_read_byte_NoFIFO: new sequence including time-out for SRX platform,
    to avoid error code LF_INVMODE, when called directly after LFA WUP and MODE bit has not yet
    changed to '1'. The hardware sets the MODE bit to 1 with a latency of about four XO32K
    clock cycles after WUP.
  - LFRCVERROR_t: added error code LF_TIMEOUT.
  - removed counter for PMCV occurances, since PMCV flag was removed from specification.
  2019-11-19 (NBn): _LfAct_read_byte_FIFO, step 3, removed waiting for BITAIL in this case
  2020-12-17 (MMr):
  - added function phcaiKEyLLGenFunc_LfAct_PostWupTimer_clear_restart.
  - added time-out in phcaiKEyLLGenFunc_LfAct_PostWupTimer_wait.
  - added internal _LfAct_PostWupTimer_waitWrPending.
  - added phcaiKEyLLGenFunc_LfAct_stopRx_clearFlags.
  - renamed static functions.
  2021-01-13 (MMr):
  - phcaiKEyLLGenFunc_LfAct_checkStopRx: added.
  - phcaiKEyLLGenFunc_LfAct_stopRxWithNoWup: added.

 */

#include "phcaiKEyLLGenFunc.h"
#include "phcaiKEyLLGenFunc_CS.h"

/**
 * @addtogroup UserFuncLib
 * @{
 */

/**
 * @addtogroup LFACTIVE
 * @{
 */

/*-----------------------------------------------------------------------------------------------*/
/* Module constants declaration                                                                  */
/*-----------------------------------------------------------------------------------------------*/

/**
  Time-out time is microseconds when waiting for NEWBYTE on SRX platform.
  Note: must be larger than the nominal time for 8 data bits (=2048 us) plus
  the additional delay of the NEWBYTE flag (about 900 us), plus worst-case tolerance (+-10 %)
  of AUXCLK.
  @see _LfAct_read_byte_NoFIFO
 */
#define LFACT_REC_BYTE_TIMEOUT_US    3300u

/*-----------------------------------------------------------------------------------------------*/
/* Module variables declaration                                                                  */
/*-----------------------------------------------------------------------------------------------*/

/**
  The result or error code of the last data reception function call.
  LF_OK means no error.
  @see LFRCVERROR_t
 */
static LFRCVERROR_t m_e_rx_errorcode;

/**
  Stored value of PRESTAT.NEXTFIFODATOK from last NEWBYTE event
  (TOKEN and T.-PLUS types only)
*/
static uint8_t m_u8_NextFifoDataReady;

/*-----------------------------------------------------------------------------------------------*/
/* Function Declarations                                                                         */
/*-----------------------------------------------------------------------------------------------*/

/**
  Read an LF data byte from the data register (might be FIFO), clear NEWBYTE while keeping
  the NEWBYTEOVF unchanged, check overflow.
*/
static LFRCVERROR_t _LfAct_read_datareg( uint8_t * pu8_data );

/**
 * Receive and read one byte from the Active LF demodulator assuming FIFO enabled.
 */
static LFRCVERROR_t _LfAct_read_byte_FIFO( uint8_t * pu8_data );

/**
 * Receive and read one byte from the Active LF demodulator assuming FIFO disabled or not
 * present in active platform.
 */
static LFRCVERROR_t _LfAct_read_byte_NoFIFO( uint8_t * pu8_data );

/**
 * Wait until CMPWRPENDING bit is cleared.
 */
static void _LfAct_PostWupTimer_waitWrPending( void );

/*-----------------------------------------------------------------------------------------------*/
/* Function Implementations                                                                      */
/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_LfAct_read_init( void )
{
  m_e_rx_errorcode       = LF_OK;

  #if defined( PLATFORM_TOKEN ) || defined( PLATFORM_TOKENPLUS )
  m_u8_NextFifoDataReady = 0u;
  PRECON8.bits.NEWBYTEOVFHOLD = 1u;
  #endif
}

/*-----------------------------------------------------------------------------------------------*/

LFRCVERROR_t phcaiKEyLLGenFunc_LfAct_read_byte( uint8_t * pu8_data )
{
  LFRCVERROR_t e_res;

  if ( TRUE == phcaiKEyLLGenFunc_LfAct_FIFO_enabled() ) {
    e_res = _LfAct_read_byte_FIFO( pu8_data );
  }
  else {
    e_res = _LfAct_read_byte_NoFIFO( pu8_data );
  }

  /* Note: DO NOT execute CRC update here! */

  phcaiKEyLLGenFunc_Util_ClearVbatRegRead();

  return e_res;
}

/*-----------------------------------------------------------------------------------------------*/

static LFRCVERROR_t _LfAct_read_byte_FIFO( uint8_t * pu8_data )
{
#if defined( PLATFORM_TOKEN ) || defined( PLATFORM_TOKENPLUS )

  uint8_t        u8_rcvbyte = 0u;
  /* Note: use same type definition as in SFR definition unit, file ncf29xy.c . */
  SFR_PRESTAT_t  u_PRESTAT_Rd;

  u_PRESTAT_Rd.val = phcaiKEyLLGenFunc_LfAct_read_PRESTAT();

  /* Step 1 */
  if ( u_PRESTAT_Rd.bits.NEWBYTE != 0u )
  {
    /* NEWBYTE == 1: data byte has been received */
    /* store indication of more available data bytes in FIFO */
    m_u8_NextFifoDataReady = u_PRESTAT_Rd.bits.NEXTFIFODATOK;
    /* read received byte from data register and clear NEWBYTE (includes step 2) */
    m_e_rx_errorcode = _LfAct_read_datareg( &u8_rcvbyte );
  }
  else
  {
     /* Step 3 */
    if ( m_u8_NextFifoDataReady != 0u )
    {
      /* Here we retrieve further data byte(s) from the FIFO,
         which have already been in the FIFO at the last call to this function.
         Wait in IDLE mode until NEWBYTE or WUPxM received.
         Note: INTEN2[5] (W0) reads '0', bitfield write allowed.
       */
      INTEN2.bits.IE_PP = 1u;        /* enable pre-processor interrupts */
      do
      {
        /* Note: single read of PRESTAT sufficient since MODE bits not evaluated below. */
        go_idle();
        u_PRESTAT_Rd.val = PRESTAT.val;
      }
      while ( (u_PRESTAT_Rd.val & 0x080Fu) == 0u );  /* wait until any of NEWBYTE_OVF, NEWBYTE, WUPxM */
      INTEN2.bits.IE_PP = 0u;        /* disable pre-processor interrupts */
      /* Step 4 */
      if ( ( u_PRESTAT_Rd.bits.NEWBYTE != 0u ) || ( u_PRESTAT_Rd.bits.NEWBYTE_OVF != 0u ) )
      {
        /* data byte has been received */
        m_u8_NextFifoDataReady = u_PRESTAT_Rd.bits.NEXTFIFODATOK;  /* store indication of more available data bytes in FIFO */
        /* read received byte from data register and clear NEWBYTE. */
        m_e_rx_errorcode = _LfAct_read_datareg( &u8_rcvbyte );
      }
      else
      {
        /* error case: unexpected new WUP (which has cleared the NEWBYTE) */
        PRESTAT.byte.lo = 0x07u;  // clear all WUPxM flags
        m_e_rx_errorcode = LF_WUPXM;
      }
    }
    else
    {
      /* Step 5 */
      /* Now the receiver should be in data receive mode, otherwise no further data bytes can be expected. */
      if ( (PRESTATMODE_t) u_PRESTAT_Rd.bits.MODE == PPMODE_DATAREC )
      {
        /* wait in IDLE mode until NEWBYTE or BITFAIL received */
        INTEN2.bits.IE_PP = 1u;        /* enable pre-processor interrupts */
        do
        {
          /* Note: single read of PRESTAT sufficient since MODE bits not evaluated below. */
          go_idle();
          u_PRESTAT_Rd.val = PRESTAT.val;
        }
        while ( (u_PRESTAT_Rd.val & 0x0818u) == 0u );  /* wait until any of NEWBYTE_OVF, BITFAIL or NEWBYTE */
        INTEN2.bits.IE_PP = 0u;        /* disable pre-processor interrupts */

        /* Step 6 */
        if ( ( u_PRESTAT_Rd.bits.NEWBYTE != 0u ) || ( u_PRESTAT_Rd.bits.NEWBYTE_OVF != 0u ) )
        {
          /* no error, data byte has been received */
          m_u8_NextFifoDataReady = u_PRESTAT_Rd.bits.NEXTFIFODATOK;  /* store indication of more available data bytes in FIFO */
          /* read received byte from data register and clear NEWBYTE. */
          m_e_rx_errorcode = _LfAct_read_datareg( &u8_rcvbyte );
        }
        else
        {
          PRESTAT.byte.lo = 0x10u;         /* clear BITFAIL interrupt */
          m_e_rx_errorcode = LF_BITFAIL;   /* error due to BITFAIL */
        }
      }
      else
      {
        m_e_rx_errorcode = LF_INVMODE;     /* error, not in data receive mode */
      }
    }
  }

#endif

#ifdef PLATFORM_TOKENSRX_COMMON

  uint8_t        u8_rcvbyte = 0u;

  // No FIFO in this platform.
  m_e_rx_errorcode = LF_INVMODE;

#endif

  /* return data byte to caller */
  *pu8_data = u8_rcvbyte;
  /* return result code */
  return m_e_rx_errorcode;
}

/*-----------------------------------------------------------------------------------------------*/

static LFRCVERROR_t _LfAct_read_byte_NoFIFO( uint8_t * pu8_data )
{
  uint8_t        u8_rcvbyte = 0u;
  /* Note: use same type definition as in SFR definition unit, file ncf29xy.c . */
  SFR_PRESTAT_t  u_PRESTAT_Rd;

  u_PRESTAT_Rd.val = phcaiKEyLLGenFunc_LfAct_read_PRESTAT();

#if defined( PLATFORM_TOKEN ) || defined( PLATFORM_TOKENPLUS )

  // TODO not yet reviewed and soak-tested.
  // Quick-tested with demo app on TOKEN and TOKEN-PLUS.
  //return LF_INVMODE;

  /* Step 1 */
  if ( u_PRESTAT_Rd.bits.NEWBYTE != 0u )
  {
    /* NEWBYTE == 1: data byte has been received */
    /* read received byte from data register and clear NEWBYTE (includes step 2) */
    m_e_rx_errorcode = _LfAct_read_datareg( &u8_rcvbyte );
  }
  else
  {
    /* Step 5 */
    /* Now the receiver should be in data receive mode, otherwise no further data bytes can be expected. */
    if ( (PRESTATMODE_t) u_PRESTAT_Rd.bits.MODE == PPMODE_DATAREC )
    {
      /* wait in IDLE mode until NEWBYTE or BITFAIL received */
      INTEN2.bits.IE_PP = 1u;        /* enable pre-processor interrupts */
      do
      {
        /* Note: single read of PRESTAT sufficient since MODE bits not evaluated below. */
        go_idle();
        u_PRESTAT_Rd.val = PRESTAT.val;
      }
      while ( (u_PRESTAT_Rd.val & 0x0818u) == 0u );  /* wait until any of NEWBYTE_OVF, BITFAIL or NEWBYTE */
      INTEN2.bits.IE_PP = 0u;        /* disable pre-processor interrupts */

      /* Step 6 */
      if ( ( u_PRESTAT_Rd.bits.NEWBYTE != 0u ) || ( u_PRESTAT_Rd.bits.NEWBYTE_OVF != 0u ) )
      {
        /* no error, data byte has been received */
        /* read received byte from data register and clear NEWBYTE. */
        m_e_rx_errorcode = _LfAct_read_datareg( &u8_rcvbyte );
      }
      else
      {
        PRESTAT.byte.lo = 0x10u;         /* clear BITFAIL interrupt */
        m_e_rx_errorcode = LF_BITFAIL;   /* error due to BITFAIL */
      }
    }
    else
    {
      m_e_rx_errorcode = LF_INVMODE;     /* error, not in data receive mode */
    }
  }

#endif /* if defined( PLATFORM_TOKEN ) || defined( PLATFORM_TOKENPLUS ) */



#ifdef PLATFORM_TOKENSRX_COMMON

  /* Step 1 */
  if ( u_PRESTAT_Rd.bits.NEWBYTE != 0u ) {
    /* NEWBYTE set: data byte has been received.
     Read received byte from data register and clear NEWBYTE. */
    m_e_rx_errorcode = _LfAct_read_datareg( &u8_rcvbyte );
  }
  else {
    /*
     New sequence: do not care about MODE bit since it is only asserted with some latency
     after LFA WUP.
     Instead, wait until NEWBYTE, or Timer0 expires (indicating time-out error).
    */
    /* start timer 0 to enable time-out */
    phcaiKEyLLGenFunc_Timer0_Start_us( LFACT_REC_BYTE_TIMEOUT_US );
    INTEN2.bits.IE_PP = 1u;        /* enable pre-processor interrupts */
    do
    {
      /* Wait until PP or T0 IRQ. */
      go_idle();
      u_PRESTAT_Rd.val = PRESTAT.val;
    }
    while ( ( u_PRESTAT_Rd.bits.NEWBYTE == 0u ) && ( INTFLAG0.bits.IF_T0 == 0u ) );
    INTEN2.bits.IE_PP = 0u;  /* disable pre-processor interrupts */
    INTEN0.bits.IE_T0 = 0u;  /* disable timer0 interrupt         */

    if ( u_PRESTAT_Rd.bits.NEWBYTE != 0u ) {
      /* NEWBYTE set: data byte has been received.
       Read received byte from data register and clear NEWBYTE. */
      m_e_rx_errorcode = _LfAct_read_datareg( &u8_rcvbyte );
    }
    else {
      m_e_rx_errorcode = LF_TIMEOUT;
    }
  }

#endif /* ifdef PLATFORM_TOKENSRX_COMMON */

  /* return data byte to caller */
  *pu8_data = u8_rcvbyte;
  /* return result code */
  return m_e_rx_errorcode;
}

/*-----------------------------------------------------------------------------------------------*/

static LFRCVERROR_t _LfAct_read_datareg( uint8_t * pu8_data )
{
  LFRCVERROR_t e_result;

#if defined( PLATFORM_TOKEN ) || defined( PLATFORM_TOKENPLUS )

  // NEWBYTEOVFHOLD is kept = 1 except in this sequence.
  //ASSERT( PRECON8.bits.NEWBYTEOVFHOLD == 1 );
  // read the PP data register and store in buffer
  *pu8_data = PREDAT.val;

  // clear NEWBYTE flag without affecting NEWBYTE_OVF
  PRESTAT.byte.lo = 0x08u;
  /* Step 2 */
  if ( PRESTAT.bits.NEWBYTE_OVF == 0u )
  {
    e_result = LF_OK;
  }
  else
  {
    e_result = LF_NBOVF;
    // Note: PRECON8[6] (W0) is read-write, bitfield write maintains default value '0'.
    PRECON8.bits.NEWBYTEOVFHOLD = 0u;
    // clear NEWBTYE_OVF
    PRESTAT.byte.lo = 0x08u;
    PRECON8.bits.NEWBYTEOVFHOLD = 1u;
  }

#endif

#ifdef PLATFORM_TOKENSRX_COMMON

  /* read the PP data register and store in buffer */
  *pu8_data = PREDAT.val;
  /* clear NEWBYTE flag without affecting NEWBYTE_OVF */
  PRESTAT.byte.lo = 0x08u;
  /* check if overflow occured, in this case, return error code. */
  if ( PRESTAT.bits.NEWBYTE_OVF == 0u )
  {
    e_result = LF_OK;
  }
  else
  {
    e_result = LF_NBOVF;
    /* clear NEWBYTE_OVF */
    PRESTAT.byte.hi = 0x08u;
  }
#endif

  phcaiKEyLLGenFunc_Util_ClearVbatRegRead();

  return e_result;
}

/*-----------------------------------------------------------------------------------------------*/

LFRCVERROR_t  phcaiKEyLLGenFunc_LfAct_read_block( uint8_t * pu8_rx_buffer, uint8_t u8_numbytes )
{
  uint8_t      u8_rcvByte;
  LFRCVERROR_t e_result = LF_OK;

  while ( u8_numbytes > 0u )
  {
    /* Fetch next byte from LF interface */
    e_result = phcaiKEyLLGenFunc_LfAct_read_byte( &u8_rcvByte );
    /* If a reception error occurred, abort and indicate failure to caller. */
    if ( e_result != LF_OK )
    {
      break;
    }

    /* Store received byte in buffer */
    *pu8_rx_buffer++ = u8_rcvByte;

    u8_numbytes--;
  } /* Loop for next byte to receive */

  return e_result;
}

/*-----------------------------------------------------------------------------------------------*/

LFRCVERROR_t phcaiKEyLLGenFunc_LfAct_get_rx_errorcode( void )
{
  return m_e_rx_errorcode;
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_LfAct_stop_rx( void )
{
  #ifdef PLATFORM_TOKENSRX_COMMON
  /* Set STOPRX=1 . Auto-clears itself after approx. two XO32KCLK cycles. */
  PRESTAT.byte.hi = 0x80u;
  /* Read back the STOPRX bit until it has been processed and cleared by the hardware.
     We must avoid that PRESTAT is written again with STOPRX=0 before receiver
     has processed the stop command. */
  while ( ( PRESTAT.byte.hi & 0x80u ) != 0u ) {}
  #endif
  /* Note: on TOKEN and TOKEN-PLUS platforms, the receiver stops by itself by a BITFAIL condition
     after end of the LF frame, or stopped by the PRERST bit. */
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_LfAct_stopRx_clearFlags( void )
{
  #ifdef PLATFORM_TOKENSRX_COMMON
    /* Set STOPRX=1 . Auto-clears after approx. two XO32KCLK cycles.
       Clear NEWBYTE_OVF, NEWBYTE and WUPxM / WUPxMH flags. */
    PRESTAT.val = 0x880Fu;
    /* Read back STOPRX until cleared.
       We must avoid that PRESTAT is written again with STOPRX=0 before receiver
       has processed the stop command. */
    while ( ( PRESTAT.byte.hi & 0x80u ) != 0u ) {}
  #else
    /* Note: on TOKEN and TOKEN-PLUS platforms, the receiver stops by itself by a BITFAIL condition
       after end of the LF frame.
       Clear WUPxM, NEWBYTE and NEWBYTE_OVF. */
    PRESTAT.val = 0x080Fu;
  #endif
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_LfAct_reset_rx( void )
{
  PRECON2.bits.PRERST = 1u;
  /* delay does not seem to be required here. */
  /* timer0_delay_us(33u); */
  PRECON2.bits.PRERST = 0u;
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_LfAct_checkStopRx( LfWupIndex_t en_WupIndex )
{
  #ifdef PLATFORM_TOKENSRX_COMMON
  /* On SRX platform, if payload for WUPx enabled, delay and stop payload receiver. */
  bool_t b_PayloadRxOn = FALSE;
  if ( ( LF_WUPA == en_WupIndex ) && ( 0u != PAYRXCON.bits.WUPRXSELA ) ) {
    b_PayloadRxOn = TRUE;
  }
  else if ( ( LF_WUPB == en_WupIndex ) && ( 0u != PAYRXCON.bits.WUPRXSELB ) ) {
    b_PayloadRxOn = TRUE;
  }
  else if ( ( LF_WUPC == en_WupIndex ) && ( 0u != PAYRXCON.bits.WUPRXSELC ) ) {
    b_PayloadRxOn = TRUE;
  }
  else { /* NOP */ }

  if ( TRUE == b_PayloadRxOn ) {
    /* delay min. one XOCLK cycle! */
    timer0_delay_us( 33u );
    phcaiKEyLLGenFunc_LfAct_stopRx_clearFlags();
  }
  else { /* NOP */ }

  #else
  /* No action for other platforms. */
  #endif
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_LfAct_stopRxWithNoWup( void )
{
  #ifdef PLATFORM_TOKENSRX_COMMON
  /* read the preprocessor status register. */
  uint16_t u16_rdPRESTAT = PRESTAT.val;
  /* is payload rx running and no WUP flags set? */
  if ( ( ( u16_rdPRESTAT & 0x8000u ) != 0u ) && ( ( u16_rdPRESTAT & 0x0007u ) == 0u ) ) {
    /* Set STOPRX=1 . Auto-clears itself after approx. two XO32KCLK cycles. */
    PRESTAT.byte.hi = 0x80u;
    /* Read back the STOPRX bit until it has been processed and cleared by the hardware.
       We must avoid that PRESTAT is written again with STOPRX=0 before receiver
       has processed the stop command. */
    while ( ( PRESTAT.byte.hi & 0x80u ) != 0u ) {}
  }
  else {
    /* NOP */
  }
  #else
  /* No action for other platforms. */
  #endif
}

/*-----------------------------------------------------------------------------------------------*/

bool_t phcaiKEyLLGenFunc_LfAct_clear_BITFAIL( void )
{
  bool_t b_res = FALSE;
  #if ( defined(PLATFORM_TOKEN) || defined(PLATFORM_TOKENPLUS) )
    if ( PRESTAT.bits.BITFAIL != 0u ) {
       PRESTAT.byte.lo = 0x10u; /* clear BITFAIL flag */
       b_res = TRUE;
    }
    else { /* NOP*/  }
  #elif PLATFORM_TOKENSRX_COMMON
    /* BITFAIL does not exist for PLATFORM_TOKENSRX_COMMON. */
  #else
    /* unknown platform. */
  #endif
  return b_res;
}

/*-----------------------------------------------------------------------------------------------*/

#ifdef PLATFORM_HAS_LFPOSTWUPTIMER

void    phcaiKEyLLGenFunc_LfAct_PostWupTimer_init( LFPWTIMERMODE_t e_mode,
                                                   bool_t b_MultiShotEnable )
{
  uint8_t u8_pwupcon;

  //if ( POSTWUPCON.bits.RUN != 0u ) {
  POSTWUPCON.val = 0x01u; /* if running, stop the counter. clear match interrupt. */
  while ( POSTWUPCON.bits.RUN != 0u ) {}
  //}

  #ifdef PLATFORM_TOKENPLUS
  u8_pwupcon = PWUPCON_CALCNTREN;
  #endif
  #ifdef PLATFORM_TOKENSRX_COMMON
  u8_pwupcon = 0x00u;
  #endif

  if ( LFPWT_FROMWUP == e_mode ) {
    u8_pwupcon |= PWUPCON_WUPRESTART;
  }
  else if ( LFPWT_FROMNEWBYTE == e_mode ) {
    u8_pwupcon |= PWUPCON_NBRESTART;
  }
  else { /* NOP */ }

  if ( b_MultiShotEnable != FALSE ) {
    u8_pwupcon |= PWUPCON_MULTISHOT;
  }
  else { /* NOP */ }

  POSTWUPCOMP.val = 0xFFFFu;
  POSTWUPCON.val  = u8_pwupcon;
  _LfAct_PostWupTimer_waitWrPending();
}

/*-----------------------------------------------------------------------------------------------*/

// TODO review with Dev
error_t phcaiKEyLLGenFunc_LfAct_PostWupTimer_config( LFPWTIMERMODE_t e_mode,
                                                     bool_t b_MultiShotEnable,
                                                     uint8_t u8_WUPlength,
                                                     uint16_t u16_matchtime_us )
{
  uint8_t  u8_pwupcon;
  uint32_t u32_temp;
  uint16_t u16_ncal, u16_ncmp = 0xFFFFu;
  uint16_t u16_divider, u16_ncali, u16_ncal_low, u16_ncal_high;
  error_t  e_res = ERROR;

  if ( POSTWUPCON.bits.CMPMATCH != 0u ) {
    // clear CMPMATCH event flag
    POSTWUPCON.val |= PWUPCON_CMPMATCH;
  }
  /* Note, RUN==1 when writing to the control register means we do NOT stop a running counter.
   Writing RUN=0 stops the counter. */
  #ifdef PLATFORM_TOKENPLUS
  u8_pwupcon = PWUPCON_CALCNTREN | PWUPCON_RUN | PWUPCON_CMPINTEN;
  #endif
  #ifdef PLATFORM_TOKENSRX_COMMON
  u8_pwupcon = PWUPCON_RUN | PWUPCON_CMPINTEN;
  #endif

  if ( LFPWT_FROMWUP == e_mode ) {
    u8_pwupcon |= PWUPCON_WUPRESTART;
  }
  else if ( LFPWT_FROMNEWBYTE == e_mode ) {
    u8_pwupcon |= PWUPCON_NBRESTART;
  }
  else { /* undefined mode or NONE, return ERROR. */
    return ERROR;
  }

  if ( b_MultiShotEnable != FALSE ) {
    u8_pwupcon |= PWUPCON_MULTISHOT;
  }

  #ifdef PLATFORM_TOKENPLUS

  if ( ( e_mode != LFPWT_NONE ) && ( u16_matchtime_us >= LFA_PWUP_MINTIME_US ) ) {
    if ( 0u == u8_WUPlength ) {
      // no calibration enabled: assume nominal LPRC frequency and ignore calibration counter.
      // Ncmp = Ttimed * fLPRC = u16_matchtime_us * 180 / 1000
      //u32_temp = mul( u16_matchtime_us, (uint16_t)LFA_PWUP_CAL_NOMINAL_FLPRC_KHZ );
      u32_temp = phcaiKEyLLGenFunc_Util_MulU16byU16( u16_matchtime_us, LFA_PWUP_CAL_NOMINAL_FLPRC_KHZ );
      u16_divider = 1000u;
      u32_temp += u16_divider / 2u;  // round to nearest integer
      u16_ncmp = phcaiKEyLLGenFunc_Util_DivideU32byU16( u32_temp, u16_divider );
      e_res = SUCCESS;
    }
    else if ( ( u8_WUPlength >= LFA_PWUP_CAL_MINWUPLENGTH ) && ( u8_WUPlength <= 32u ) )
    {
      // offset-compensated calibration counter Ncal
      u16_ncal = POSTWUPCAL.val - LFA_PWUP_CAL_OFFSET;
      // calculate ideal calib. value Ncali = NC0 + (NWUP+1)*TBIT*fLPRC
      // (no rounding here)
      u16_divider = (uint16_t)( (uint16_t)u8_WUPlength + 1u ) * LFA_TBIT_US;
      //u32_temp = mul( u16_divider, (uint16_t)LFA_PWUP_CAL_NOMINAL_FLPRC_KHZ );
      u32_temp  = phcaiKEyLLGenFunc_Util_MulU16byU16( u16_divider, LFA_PWUP_CAL_NOMINAL_FLPRC_KHZ );
      u16_ncali = phcaiKEyLLGenFunc_Util_DivideU32byU16( u32_temp, 1000u );
      u16_ncali += LFA_PWUP_CAL_OFFSET;
      // +/- 15 % limits of calibration counter value from nominal, 16*16->16 bit operations
      u16_ncal_low  = (u16_ncali * 17u ) / 20u;
      u16_ncal_high = (u16_ncali * 23u ) / 20u;
      // Return error if Ncal is outside range.
      if ( ( u16_ncal_low <= u16_ncal ) && ( u16_ncal <= u16_ncal_high ) ) {
        // calculate Ncmp = Ttimed * Ncal / ((NWUP+1)*Tbit)
        //u32_temp = mul( u16_matchtime_us, u16_ncal );
        u32_temp = phcaiKEyLLGenFunc_Util_MulU16byU16( u16_matchtime_us, u16_ncal );
        u32_temp += u16_divider / 2u;  // round to nearest integer
        u16_ncmp = phcaiKEyLLGenFunc_Util_DivideU32byU16( u32_temp, u16_divider );
        e_res = SUCCESS;
      }
    }
    if ( LFPWT_FROMWUP == e_mode ) {
      // compensate missing clock cycles
      u16_ncmp += 2u;
    }
  }

  #endif /* ifdef PLATFORM_TOKENPLUS */

  #ifdef PLATFORM_TOKENSRX_COMMON

  // validate u16_matchtime_us against minimum times
  if ( ( LFPWT_FROMNEWBYTE == e_mode )
    && ( u16_matchtime_us >= (LFA_PWUP_MINTIME_US+LFA_PWUP_SRX_NB_COMPENS_US) ) ) {
    // compensate total delay of NEWBYTE and compare match.
    u16_matchtime_us -= LFA_PWUP_SRX_NB_COMPENS_US;
    e_res = SUCCESS;
  }
  else if ( ( LFPWT_FROMWUP == e_mode )
    && ( u16_matchtime_us >= (LFA_PWUP_MINTIME_US+LFA_PWUP_SRX_WUP_COMPENS_US) ) ) {
    u16_matchtime_us -= LFA_PWUP_SRX_WUP_COMPENS_US;
    e_res = SUCCESS;
  } else {}
  // Note: return error if e_mode == LFPWT_NONE .
  if ( SUCCESS == e_res ) {
    u32_temp = (uint32_t)u16_matchtime_us * 512u;
    u32_temp += (LFA_PWUP_NCMP_DIVIDER/2u); // round result to nearest integer
    u16_ncmp = phcaiKEyLLGenFunc_Util_DivideU32byU16( u32_temp, LFA_PWUP_NCMP_DIVIDER );
  } else {}

  #endif /* ifdef PLATFORM_TOKENSRX_COMMON */

  if ( SUCCESS == e_res ) {
    POSTWUPCOMP.val = u16_ncmp;
    POSTWUPCON.val  = u8_pwupcon;
    _LfAct_PostWupTimer_waitWrPending();
  } else {}
  return e_res;
}

/*-----------------------------------------------------------------------------------------------*/

bool_t phcaiKEyLLGenFunc_LfAct_PostWupTimer_isExpired( void )
{
  if ( POSTWUPCON.bits.CMPMATCH != 0u ) {
    return TRUE;
  }
  else {
    return FALSE;
  }
}

/*-----------------------------------------------------------------------------------------------*/

error_t phcaiKEyLLGenFunc_LfAct_PostWupTimer_wait( uint16_t u16_timeoutms )
{
  error_t e_res = ERROR;

  /* CMPMATCH should be 0 at the beginning, otherwise we might be too late already.
    The RUN bit must also be set, since RUN==0 means the counter is not incrementing. */
  if ( ( POSTWUPCON.bits.CMPMATCH == 0u ) && ( POSTWUPCON.bits.RUN == 1u ) ) {
    CLKCON3.bits.TMUX0C = 1u;     /* TMUX0C[3:0] = 1 : select AUX Clock (1 MHz) */
    T0CON0.val  = 0x02u;          /* Timer 0 reset                              */
    T0CON1.val  = 0x2Au;          /* ~ 1kHz = 1 / 1 ms                          */
    T0RLD.val   = u16_timeoutms;  /* time-out in ms (1 extra cycle!)            */
    INTCLR0.bits.IC_T0 = 1u;      /* Clear interrupt flag.                      */
    T0CON0.val  = 0x05u;          /* Start timer 0 in single shot mode          */
    /* wait until CMPMATCH gets asserted, or timer 0 expires. */
    while ( POSTWUPCON.bits.CMPMATCH == 0u ) {
      if ( INTFLAG0.bits.IF_T0 != 0u ) {
        /* timer 0 expired means time-out occured. */
        //e_res = ERROR;
        break;
      } else {}
    }
    if ( POSTWUPCON.bits.CMPMATCH != 0u ) {
      // keep RUN bit unchanged, clear interrupt
      POSTWUPCON.val |= PWUPCON_CMPMATCH;
      e_res = SUCCESS;
    } else {}
    T0CON0.val  = 0x02u;          /* Timer 0 reset */
    INTCLR0.bits.IC_T0 = 1u;      /* Interrupt acknowledge (all bits read '0')*/
  }
  else { /* NOP */ }
  return e_res;
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_LfAct_PostWupTimer_stop( void )
{
  uint8_t u8_conreg;
  u8_conreg = POSTWUPCON.val;
  // set RUN=0 and clear interrupt
  u8_conreg = (u8_conreg & (~PWUPCON_RUN) ) | PWUPCON_CMPMATCH;
  POSTWUPCON.val = u8_conreg;
}

/*-----------------------------------------------------------------------------------------------*/

void phcaiKEyLLGenFunc_LfAct_PostWupTimer_clear_restart( void )
{
//POSTWUPCON.val &= 0xE7u; // clear NBRESTART and WUPRESTART
  POSTWUPCON.val &= 0xE6u; // clear NBRESTART and WUPRESTART, dont clear CMPMATCH !
}

/*-----------------------------------------------------------------------------------------------*/

static void _LfAct_PostWupTimer_waitWrPending( void )
{
  /* wait until CMPWRPENDING bit is cleared */
  while ( POSTWUPCON.bits.CMPWRPENDING != 0u ) {}
}

/*-----------------------------------------------------------------------------------------------*/

#endif /* ifdef PLATFORM_HAS_LFPOSTWUPTIMER */

/* @} */
/* @} */
