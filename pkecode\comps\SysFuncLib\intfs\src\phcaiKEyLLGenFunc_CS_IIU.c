/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_IIU.c 21074 2019-07-22 13:39:34Z dep10330 $
  $Revision: 21074 $
*/

/**
 * @file
 * Implementation of the stubs to call IIU specific KEyLink Lite General Functions
 * placed in ROM.
 */

#include "phcaiKEyLLGenFunc_CS_IIU.h"
#include "phcaiKEyLLGenFunc_CS_Utils.h"
#include "phcaiKEyLLGenFunc_SCI.h"


#if defined(PHFL_CONFIG_HAVE_IIU_USER_SYSCALL_ACCESS) && (PHFL_CONFIG_HAVE_IIU_USER_SYSCALL_ACCESS == CONFIG_YES)
error_t phcaiKEyLLGenFunc_CS_IIU_receive(uint8_t* const data_buffer, const uint8_t number_of_bytes, uint8_t * received_bits)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_IIU_RECEIVE;
  phcaiKEyLLGenFunc_Func_Params.params.iiu_receive.data = data_buffer;
  phcaiKEyLLGenFunc_Func_Params.params.iiu_receive.buffer_size = number_of_bytes;
  call_syscall(10);
  *received_bits = phcaiKEyLLGenFunc_Func_Params.params.iiu_receive.number_of_rcv_bits;
  return phcaiKEyLLGenFunc_Func_Params.params.iiu_receive.retval;

}

error_t phcaiKEyLLGenFunc_CS_IIU_send(uint8_t* const data_buffer, const uint8_t number_of_bytes)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_IIU_SEND;
  phcaiKEyLLGenFunc_Func_Params.params.iiu_send.data = data_buffer;
  phcaiKEyLLGenFunc_Func_Params.params.iiu_send.number_of_bytes = number_of_bytes;
  call_syscall(10);
  return phcaiKEyLLGenFunc_Func_Params.params.iiu_send.retval;
}

#endif

#if defined(PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET) && (PHFL_CONFIG_HAVE_IIU_TIMX_SFR_SET == CONFIG_YES)
error_t phcaiKeyLLHal_CS_IIU_TIMX_SFR_set(const uint8_t timx_index, const uint8_t value)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_IIU_TIMX_SFR_SET;
  phcaiKEyLLGenFunc_Func_Params.params.iiu_timx_sfr_set.timx_index = timx_index;
  phcaiKEyLLGenFunc_Func_Params.params.iiu_timx_sfr_set.value = value;
  call_syscall(10);
  return phcaiKEyLLGenFunc_Func_Params.params.iiu_timx_sfr_set.retval;
}
#endif

#if defined(PHFL_CONFIG_HAVE_IIU_CAT_SYSCALL) && (PHFL_CONFIG_HAVE_IIU_CAT_SYSCALL == CONFIG_YES)

void phcaiKEyLLGenFunc_CS_IIU_CatInit(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_IIU_CAT_INIT;
  call_syscall(10);
}

error_t phcaiKEyLLGenFunc_CS_IIU_CatSend(const uint8_t* const data_buffer, const uint8_t number_of_bytes, 
  const uint16_t t_ch_resp)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_IIU_CAT_SEND;
  phcaiKEyLLGenFunc_Func_Params.params.cat_iiu_send.data = data_buffer;
  phcaiKEyLLGenFunc_Func_Params.params.cat_iiu_send.number_of_bytes = number_of_bytes;
  phcaiKEyLLGenFunc_Func_Params.params.cat_iiu_send.t_ch_resp = t_ch_resp;
  call_syscall(10);
  return phcaiKEyLLGenFunc_Func_Params.params.cat_iiu_send.retval;
}

error_t phcaiKEyLLGenFunc_CS_IIU_CatSendMuted(const uint8_t number_of_bytes,
  const uint16_t t_ch_resp)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_IIU_CAT_SEND_MUTED;
  phcaiKEyLLGenFunc_Func_Params.params.cat_iiu_send.number_of_bytes = number_of_bytes;
  phcaiKEyLLGenFunc_Func_Params.params.cat_iiu_send.t_ch_resp = t_ch_resp;
  call_syscall(10);
  return phcaiKEyLLGenFunc_Func_Params.params.cat_iiu_send.retval;
}

#endif
