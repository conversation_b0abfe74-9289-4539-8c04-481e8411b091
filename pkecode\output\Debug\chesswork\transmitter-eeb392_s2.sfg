
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:21 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-eeb392 mrk3

[
  -66 : __adr_u16_rem_bits typ=int16_ bnd=m adro=28
  -62 : __adr_bitcount typ=int16_ bnd=m adro=23
    0 : void_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : pu8_buffer typ=int8_ val=0t0 bnd=a sz=2 algn=2 stl=DM tref=__P__uchar_DM
   21 : u16_numbits typ=int8_ val=2t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   22 : e_encoding typ=int8_ val=4t0 bnd=a sz=1 algn=1 stl=DM tref=DataEnc_t_DM
   23 : bitcount typ=int8_ val=10t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   24 : ENCCON0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ENCCON0_t_DM9
   25 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   26 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   27 : __extDM_SFR_ENCCON0_t typ=int8_ bnd=b stl=DM
   28 : u16_rem_bits typ=int8_ val=6t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   29 : i typ=int8_ val=8t0 bnd=a sz=2 algn=2 stl=DM tref=uint16_t_DM
   30 : ENCCON1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ENCCON1_t_DM9
   31 : __extDM_SFR_ENCCON1_t typ=int8_ bnd=b stl=DM
   32 : TXDAT typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_TXDAT_t_DM9
   33 : __extDM_SFR_word typ=int8_ bnd=b stl=DM
   36 : __ptr_ENCCON0 typ=int16_ val=0a bnd=m adro=24
   38 : __ptr_ENCCON1 typ=int16_ val=0a bnd=m adro=30
   41 : __arg_e_encoding typ=int8_ bnd=p tref=DataEnc_t__
   42 : __arg_u16_numbits typ=int16_ bnd=p tref=uint16_t__
   43 : __arg_pu8_buffer typ=int16_ bnd=p tref=__P__uchar__
   48 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   52 : __ct_2t0 typ=int16_ val=2t0 bnd=m
   56 : __ct_4t0 typ=int16_ val=4t0 bnd=m
   60 : __ct_10t0 typ=int16_ val=10t0 bnd=m
   62 : __adr_bitcount typ=int16_ bnd=m adro=23
   64 : __ct_6t0 typ=int16_ val=6t0 bnd=m
   66 : __adr_u16_rem_bits typ=int16_ bnd=m adro=28
   68 : __ct_8t0 typ=int16_ val=8t0 bnd=m
   89 : __tmp typ=int16_ bnd=m
   97 : __fch_u16_numbits typ=int16_ bnd=m
  120 : __fch_i typ=int16_ bnd=m
  125 : __tmp typ=int16_ bnd=m
  126 : __fchtmp typ=int8_ bnd=m
  157 : __tmp typ=bool bnd=m
  178 : __fch_u16_rem_bits typ=int16_ bnd=m
  187 : __fch_i typ=int16_ bnd=m
  192 : __tmp typ=int16_ bnd=m
  193 : __fchtmp typ=int8_ bnd=m
  224 : __tmp typ=bool bnd=m
  227 : __ct_12s0 typ=int16_ val=12s0 bnd=m
  232 : __ct_8 typ=uint16_1_32768_ val=8f bnd=m
  277 : __ct_12s0 typ=int16_ val=12s0 bnd=m
  286 : __ptr_TXDAT__a1 typ=int16_ val=1a bnd=m adro=32
  352 : __apl_r typ=int16_ bnd=m tref=__uint__
  357 : __apl_c typ=uint1_ bnd=m tref=uint1___
  359 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  369 : __apl_c typ=uint1_ bnd=m tref=uint1___
  371 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  451 : __false typ=bool val=0f bnd=m
  452 : __either typ=bool bnd=m
  453 : __trgt typ=rel8_ val=4j bnd=m
  454 : __trgt typ=rel8_ val=-4j bnd=m
  455 : __trgt typ=rel8_ val=-18j bnd=m
  456 : __trgt typ=rel8_ val=4j bnd=m
  457 : __trgt typ=rel8_ val=-4j bnd=m
  458 : __trgt typ=rel8_ val=18j bnd=m
  460 : __trgt typ=rel8_ val=18j bnd=m
  461 : __trgt typ=rel8_ val=2j bnd=m
  462 : __trgt typ=rel8_ val=2j bnd=m
  463 : __seff typ=any bnd=m
  464 : __seff typ=any bnd=m
  465 : __seff typ=any bnd=m
  466 : __seff typ=any bnd=m
  467 : __seff typ=any bnd=m
  468 : __seff typ=any bnd=m
  469 : __seff typ=any bnd=m
  470 : __seff typ=any bnd=m
  471 : __seff typ=any bnd=m
  472 : __seff typ=any bnd=m
  473 : __seff typ=any bnd=m
  474 : __seff typ=any bnd=m
  475 : __seff typ=any bnd=m
  476 : __seff typ=any bnd=m
  477 : __seff typ=any bnd=m
  478 : __seff typ=any bnd=m
  479 : __seff typ=any bnd=m
  480 : __seff typ=any bnd=m
  481 : __seff typ=any bnd=m
  482 : __seff typ=any bnd=m
  483 : __seff typ=any bnd=m
  484 : __seff typ=any bnd=m
  485 : __seff typ=any bnd=m
  486 : __seff typ=any bnd=m
  487 : __seff typ=any bnd=m
  488 : __seff typ=any bnd=m
  489 : __seff typ=any bnd=m
  490 : __seff typ=any bnd=m
  491 : __seff typ=any bnd=m
  492 : __seff typ=any bnd=m
  499 : __seff typ=any bnd=m
  500 : __seff typ=any bnd=m
  501 : __seff typ=any bnd=m
  503 : __side_effect typ=any bnd=m
]
Fvoid_tx_transmit_buffer_encoded_bits_DataEnc_t___ushort___P__uchar {
    #6 off=0 nxt=7
    (__vola.12 var=13) source ()  <23>;
    (__extDM.16 var=17) source ()  <27>;
    (__sp.18 var=19) source ()  <29>;
    (pu8_buffer.19 var=20) source ()  <30>;
    (u16_numbits.20 var=21) source ()  <31>;
    (e_encoding.21 var=22) source ()  <32>;
    (bitcount.22 var=23) source ()  <33>;
    (ENCCON0.23 var=24) source ()  <34>;
    (__extDM_int16_.24 var=25) source ()  <35>;
    (__extDM_int8_.25 var=26) source ()  <36>;
    (__extDM_SFR_ENCCON0_t.26 var=27) source ()  <37>;
    (u16_rem_bits.27 var=28) source ()  <38>;
    (i.28 var=29) source ()  <39>;
    (ENCCON1.29 var=30) source ()  <40>;
    (__extDM_SFR_ENCCON1_t.30 var=31) source ()  <41>;
    (TXDAT.31 var=32) source ()  <42>;
    (__extDM_SFR_word.32 var=33) source ()  <43>;
    (__arg_e_encoding.40 var=41 stl=RbL off=0) inp ()  <51>;
    (__arg_u16_numbits.43 var=42 stl=RwL off=1) inp ()  <54>;
    (__arg_pu8_buffer.46 var=43 stl=R46 off=0) inp ()  <57>;
    (__ct_0t0.1917 var=48) const_inp ()  <2258>;
    (__ct_2t0.1918 var=52) const_inp ()  <2259>;
    (__ct_4t0.1919 var=56) const_inp ()  <2260>;
    (__ct_10t0.1920 var=60) const_inp ()  <2261>;
    (__ct_6t0.1921 var=64) const_inp ()  <2262>;
    (__ct_8t0.1922 var=68) const_inp ()  <2263>;
    (__ct_12s0.1924 var=277) const_inp ()  <2265>;
    <230> {
      (__sp.54 var=19 __seff.2046 var=499 stl=c_flag_w __seff.2047 var=500 stl=nz_flag_w __seff.2048 var=501 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_12s0.1924 __sp.18 __sp.18)  <2373>;
      (__seff.2210 var=500 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2047)  <2668>;
      (__seff.2211 var=499 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2046)  <2669>;
      (__seff.2231 var=501 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2048)  <2689>;
    } stp=0;
    <231> {
      (pu8_buffer.95 var=20) _pl_rd_res_reg_const_store_1_B1 (__arg_pu8_buffer.2230 __ct_0t0.1917 pu8_buffer.19 __sp.54)  <2374>;
      (__arg_pu8_buffer.2230 var=43 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__arg_pu8_buffer.46)  <2688>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <106>;
    } #7 off=4 nxt=8
    #8 off=4 nxt=9
    <229> {
      (u16_numbits.97 var=21) _pl_rd_res_reg_const_store_1_B2 (__arg_u16_numbits.2222 __ct_2t0.1918 u16_numbits.20 __sp.54)  <2372>;
      (__arg_u16_numbits.2222 var=42 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B0 (__arg_u16_numbits.43)  <2680>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <108>;
    } #9 off=5 nxt=10
    #10 off=5 nxt=11
    <228> {
      (e_encoding.99 var=22) _pl_rd_res_reg_const_store_2_B2 (__arg_e_encoding.2225 __ct_4t0.1919 e_encoding.21 __sp.54)  <2371>;
      (__arg_e_encoding.2225 var=41 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__arg_e_encoding.40)  <2683>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <110>;
    } #11 off=6 nxt=12
    #12 off=6 nxt=15
    <227> {
      (bitcount.103 var=23) store_const_1_B1 (__adr_bitcount.2244 bitcount.22)  <2370>;
      (__adr_bitcount.2244 var=-62 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_bitcount.2245)  <2702>;
    } stp=2;
    <360> {
      (__adr_bitcount.2246 var=-62 stl=a_w2 __side_effect.2247 var=503 stl=c_flag_w __side_effect.2249 var=503 stl=nz_flag_w __side_effect.2251 var=503 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_10t0.1920 __sp.54)  <2584>;
      (__adr_bitcount.2245 var=-62 stl=R46 off=0) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_bitcount.2246)  <2703>;
      (__side_effect.2248 var=503 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.2247)  <2704>;
      (__side_effect.2250 var=503 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2249)  <2705>;
      (__side_effect.2252 var=503 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2251)  <2706>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <121>;
    } #15 off=10 nxt=602
    #602 off=10 nxt=17
    (__ptr_ENCCON0.1915 var=36) const_inp ()  <2256>;
    <223> {
      (__apl_r.1623 var=352 stl=my_cv_int16_t1) load__pl_rd_res_reg_const_update_lo_1_B2 (__ct_4t0.1919 e_encoding.99 __sp.54)  <2366>;
      (__apl_r.2227 var=352 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t1_1_int16_ (__apl_r.1623)  <2685>;
    } stp=0;
    <224> {
      (__tmp.1625 var=89 stl=my_cv_int16_t5) update_hi_const_1_B2 (__apl_r.2226)  <2367>;
      (__apl_r.2226 var=352 stl=my_cv_int16_t4) my_cv_int16_t4_1_dr_move_RwL_1_int16_ (__apl_r.2227)  <2684>;
      (__tmp.2229 var=89 stl=RwL off=0) RwL_1_dr_move_my_cv_int16_t5_1_int16_ (__tmp.1625)  <2687>;
    } stp=1;
    <225> {
      (ENCCON0.119 var=24 __vola.120 var=13) load_const_bf_mov_const_const_store_3_B1 (__tmp.2228 __ptr_ENCCON0.1915 ENCCON0.23 __vola.12)  <2368>;
      (__tmp.2228 var=89 stl=a_w1) a_w1_1_dr_move_Rw_1_int16__B0 (__tmp.2229)  <2686>;
    } stp=2;
    call {
        () chess_separator_scheduler ()  <132>;
    } #17 off=14 nxt=18
    #18 off=14 nxt=19
    <220> {
      (__fch_u16_numbits.124 var=97 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_2t0.1918 u16_numbits.97 __sp.54)  <2363>;
      (__fch_u16_numbits.2224 var=97 stl=R46 off=1) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_u16_numbits.124)  <2682>;
    } stp=0;
    <221> {
      (u16_rem_bits.126 var=28) _pl_rd_res_reg_const_store_1_B2 (__fch_u16_numbits.2223 __ct_6t0.1921 u16_rem_bits.27 __sp.54)  <2364>;
      (__fch_u16_numbits.2223 var=97 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__fch_u16_numbits.2224)  <2681>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <135>;
    } #19 off=16 nxt=20
    #20 off=16 nxt=21
    <219> {
      (i.130 var=29) _pl_rd_res_reg_const_store_const_1_B3 (__ct_8t0.1922 i.28 __sp.54)  <2362>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <139>;
    } #21 off=17 nxt=68
    #68 off=17 nxt=-3 tgt=2
    () sink (__vola.120)  <756>;
    () sink (__sp.54)  <762>;
    () sink (pu8_buffer.95)  <763>;
    () sink (u16_numbits.97)  <764>;
    () sink (e_encoding.99)  <765>;
    () sink (bitcount.103)  <766>;
    () sink (ENCCON0.119)  <767>;
    () sink (u16_rem_bits.126)  <771>;
    () sink (i.130)  <772>;
    () sync_sink (__vola.120) sid=43  <777>;
    () sync_sink (bitcount.103) sid=53  <787>;
    () sync_sink (ENCCON0.119) sid=54  <788>;
    () sync_sink (u16_rem_bits.126) sid=58  <792>;
    () sync_sink (i.130) sid=59  <793>;
    () sync_sink (ENCCON1.29) sid=60  <794>;
    () sync_sink (TXDAT.31) sid=62  <796>;
    (__vola.1887 var=13) never ()  <2218>;
    (bitcount.1888 var=23) never ()  <2219>;
    (ENCCON0.1889 var=24) never ()  <2220>;
    (u16_rem_bits.1890 var=28) never ()  <2221>;
    (i.1891 var=29) never ()  <2222>;
    (ENCCON1.1892 var=30) never ()  <2223>;
    (TXDAT.1893 var=32) never ()  <2224>;
    (__ptr_ENCCON1.1916 var=38) const_inp ()  <2257>;
    (__ptr_TXDAT__a1.1925 var=286) const_inp ()  <2266>;
    (__trgt.1926 var=453) const_inp ()  <2267>;
    (__trgt.1927 var=454) const_inp ()  <2268>;
    (__trgt.1928 var=455) const_inp ()  <2269>;
    (__trgt.1933 var=460) const_inp ()  <2274>;
    (__trgt.1934 var=461) const_inp ()  <2275>;
    <218> {
      () jump_const_1_B1 (__trgt.1933)  <2361>;
    } stp=3;
    <367> {
      (__ct_8.2259 var=232 stl=a_w0) const_1_B2 ()  <2596>;
      (__ct_8.2258 var=232 stl=RwL off=0) Rw_1_dr_move___CTa_w0_uint16__cstP16_E1_a_w0_1_uint16_1_32768__B2 (__ct_8.2259)  <2708>;
    } stp=0;
    <368> {
      (__adr_u16_rem_bits.2261 var=-66 stl=a_w2 __side_effect.2262 var=503 stl=c_flag_w __side_effect.2264 var=503 stl=nz_flag_w __side_effect.2266 var=503 stl=o_flag_w) _pl_rd_res_reg_const_1_B1 (__ct_6t0.1921 __sp.54)  <2598>;
      (__adr_u16_rem_bits.2260 var=-66 stl=R46 off=1) Rw_1_dr_move_a_w2_1_int16__B1 (__adr_u16_rem_bits.2261)  <2709>;
      (__side_effect.2263 var=503 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__side_effect.2262)  <2710>;
      (__side_effect.2265 var=503 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__side_effect.2264)  <2711>;
      (__side_effect.2267 var=503 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__side_effect.2266)  <2712>;
    } stp=1;
    do {
        {
            (__vola.178 var=13) entry (__vola.681 __vola.1887)  <188>;
            (bitcount.188 var=23) entry (bitcount.701 bitcount.1888)  <198>;
            (ENCCON0.189 var=24) entry (ENCCON0.703 ENCCON0.1889)  <199>;
            (u16_rem_bits.193 var=28) entry (u16_rem_bits.711 u16_rem_bits.1890)  <203>;
            (i.194 var=29) entry (i.713 i.1891)  <204>;
            (ENCCON1.195 var=30) entry (ENCCON1.715 ENCCON1.1892)  <205>;
            (TXDAT.197 var=32) entry (TXDAT.719 TXDAT.1893)  <207>;
        } #27
        {
            #610 off=21 nxt=32
            <216> {
              (ENCCON1.235 var=30 __vola.236 var=13) load_const_bf_mov_const_const_store_2_B1 (__ct_8.2168 __ptr_ENCCON1.1916 ENCCON1.195 __vola.178)  <2359>;
              (__ct_8.2168 var=232 stl=a_w1) a_w1_1_dr_move_Rw_1_uint16_1_32768__B0 (__ct_8.2258)  <2657>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <247>;
            } #32 off=23 nxt=33
            #33 off=23 nxt=34
            <215> {
              (__fch_i.241 var=120 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_8t0.1922 i.194 __sp.54)  <2358>;
              (__fch_i.2149 var=120 stl=R46 off=2) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_i.241)  <2646>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <250>;
            } #34 off=24 nxt=35
            #35 off=24 nxt=36
            <214> {
              (i.246 var=29 __seff.2020 var=490 stl=c_flag_w __seff.2021 var=491 stl=nz_flag_w __seff.2022 var=492 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_8t0.1922 i.194 i.194 __sp.54)  <2357>;
              (__seff.2140 var=491 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2021)  <2637>;
              (__seff.2141 var=490 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2020)  <2638>;
              (__seff.2155 var=492 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2022)  <2652>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <255>;
            } #36 off=25 nxt=59
            #59 off=25 nxt=-3 tgt=1
            () sink (__vola.258)  <510>;
            () sink (__sp.54)  <516>;
            () sink (pu8_buffer.95)  <517>;
            () sink (u16_numbits.97)  <518>;
            () sink (e_encoding.99)  <519>;
            () sink (bitcount.188)  <520>;
            () sink (ENCCON0.189)  <521>;
            () sink (u16_rem_bits.193)  <525>;
            () sink (i.246)  <526>;
            () sink (ENCCON1.235)  <527>;
            () sink (TXDAT.257)  <529>;
            () sync_sink (__vola.258) sid=1  <531>;
            () sync_sink (bitcount.188) sid=11  <541>;
            () sync_sink (ENCCON0.189) sid=12  <542>;
            (__vola.1884 var=13) never ()  <2215>;
            (bitcount.1885 var=23) never ()  <2216>;
            (ENCCON0.1886 var=24) never ()  <2217>;
            <210> {
              (__tmp.248 var=125 stl=a_w2 __seff.2013 var=487 stl=c_flag_w __seff.2014 var=488 stl=nz_flag_w __seff.2015 var=489 stl=o_flag_w) _pl_load__pl_rd_res_reg_const_1_B1 (__fch_i.2148 __ct_0t0.1917 pu8_buffer.95 __sp.54)  <2353>;
              (__seff.2138 var=488 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2014)  <2635>;
              (__seff.2139 var=487 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2013)  <2636>;
              (__fch_i.2148 var=120 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B1 (__fch_i.2149)  <2645>;
              (__seff.2150 var=489 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2015)  <2647>;
              (__tmp.2152 var=125 stl=R46 off=2) Rw_1_dr_move_a_w2_1_int16__B1 (__tmp.248)  <2649>;
            } stp=0;
            <211> {
              (__fchtmp.249 var=126 stl=DM_r) load_1_B1 (__tmp.2151 ENCCON0.189 ENCCON1.235 TXDAT.197 __extDM.16 __extDM_SFR_ENCCON0_t.26 __extDM_SFR_ENCCON1_t.30 __extDM_SFR_word.32 __extDM_int16_.24 __extDM_int8_.25)  <2354>;
              (__tmp.2151 var=125 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__tmp.2152)  <2648>;
              (__fchtmp.2154 var=126 stl=RbL off=1) Rb_1_dr_move_DM_r_1_int8__B0 (__fchtmp.249)  <2651>;
            } stp=2;
            <212> {
              (TXDAT.257 var=32 __vola.258 var=13) store_const_2_B2 (__fchtmp.2153 __ptr_TXDAT__a1.1925 TXDAT.197 __vola.236)  <2355>;
              (__fchtmp.2153 var=126 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fchtmp.2154)  <2650>;
            } stp=3;
            <213> {
              () jump_const_1_B1 (__trgt.1934)  <2356>;
            } stp=4;
            do {
                {
                    (__vola.302 var=13) entry (__vola.457 __vola.1884)  <311>;
                    (bitcount.312 var=23) entry (bitcount.477 bitcount.1885)  <321>;
                    (ENCCON0.313 var=24) entry (ENCCON0.479 ENCCON0.1886)  <322>;
                } #44
                {
                    #48 off=30 nxt=1
                    <209> {
                      (bitcount.349 var=23 __seff.2009 var=484 stl=c_flag_w __seff.2010 var=485 stl=nz_flag_w __seff.2011 var=486 stl=o_flag_w) _mi_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_10t0.1920 bitcount.312 bitcount.312 __sp.54)  <2352>;
                      (__seff.2129 var=485 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2010)  <2626>;
                      (__seff.2130 var=484 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.2009)  <2627>;
                      (__seff.2132 var=486 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2011)  <2629>;
                    } stp=0;
                    sync {
                        (__vola.350 var=13) sync_link (__vola.302) sid=1  <361>;
                        (bitcount.360 var=23) sync_link (bitcount.349) sid=11  <371>;
                        (ENCCON0.361 var=24) sync_link (ENCCON0.313) sid=12  <372>;
                    } #1 off=31 nxt=454
                    #454 off=31 nxt=459 tgt=61
                    <208> {
                      (ENCCON0.399 var=24 __vola.400 var=13 __seff.2007 var=483 stl=nz_flag_w) load_const__ad_const_cmp_const_cc_ne__jump_const_1_B1 (__ptr_ENCCON0.1915 __trgt.1926 ENCCON0.361 __vola.350)  <2351>;
                      (__seff.2131 var=483 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.2007)  <2628>;
                    } stp=0;
                    if {
                        {
                            () if_expr (__either.1898)  <459>;
                            (__either.1898 var=452) undefined ()  <2230>;
                        } #53
                        {
                            (__false.1899 var=451) const ()  <2231>;
                        } #55
                        {
                            (__either.1901 var=452) undefined ()  <2234>;
                            <206> {
                              (__apl_c.1640 var=369 stl=c_flag_w __apl_nz.1642 var=371 stl=nz_flag_w __seff.2004 var=482 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B3 (__ct_10t0.1920 bitcount.360 __sp.54)  <2349>;
                              (__apl_nz.2134 var=371 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1642)  <2631>;
                              (__apl_c.2136 var=369 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.1640)  <2633>;
                              (__seff.2137 var=482 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2004)  <2634>;
                            } stp=0;
                            <207> {
                              () cc_a__jump_const_1_B1 (__apl_c.2135 __apl_nz.2133 __trgt.1927)  <2350>;
                              (__apl_nz.2133 var=371 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2134)  <2630>;
                              (__apl_c.2135 var=369 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.2136)  <2632>;
                            } stp=1;
                        } #459 off=33 nxt=61 tgt=48
                        {
                            (__tmp.456 var=157) merge (__false.1899 __either.1901)  <466>;
                        } #56
                    } #52
                } #45
                {
                    () while_expr (__tmp.456)  <467>;
                    (__vola.457 var=13 __vola.458 var=13) exit (__vola.400)  <468>;
                    (bitcount.477 var=23 bitcount.478 var=23) exit (bitcount.360)  <478>;
                    (ENCCON0.479 var=24 ENCCON0.480 var=24) exit (ENCCON0.399)  <479>;
                } #57
            } #43
            #61 off=35 nxt=62
            <205> {
              (bitcount.628 var=23) store_const_1_B1 (__adr_bitcount.2177 bitcount.478)  <2348>;
              (__adr_bitcount.2177 var=62 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_bitcount.2245)  <2658>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <660>;
            } #62 off=37 nxt=63
            #63 off=37 nxt=2
            <204> {
              (u16_rem_bits.634 var=28 __seff.1998 var=479 stl=c_flag_w __seff.1999 var=480 stl=nz_flag_w __seff.2000 var=481 stl=o_flag_w) load__mi_const_store_1_B2 (__adr_u16_rem_bits.2158 u16_rem_bits.193 u16_rem_bits.193)  <2347>;
              (__seff.2142 var=480 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1999)  <2639>;
              (__seff.2143 var=479 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1998)  <2640>;
              (__seff.2156 var=481 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.2000)  <2653>;
              (__adr_u16_rem_bits.2158 var=66 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_u16_rem_bits.2260)  <2655>;
            } stp=0;
            sync {
                (__vola.635 var=13) sync_link (__vola.458) sid=43  <667>;
                (bitcount.645 var=23) sync_link (bitcount.628) sid=53  <677>;
                (ENCCON0.646 var=24) sync_link (ENCCON0.480) sid=54  <678>;
                (u16_rem_bits.650 var=28) sync_link (u16_rem_bits.634) sid=58  <682>;
                (i.651 var=29) sync_link (i.246) sid=59  <683>;
                (ENCCON1.652 var=30) sync_link (ENCCON1.235) sid=60  <684>;
                (TXDAT.654 var=32) sync_link (TXDAT.257) sid=62  <686>;
            } #2 off=38 nxt=464
            #464 off=38 nxt=470 tgt=610
            <202> {
              (__apl_c.1646 var=357 stl=c_flag_w __apl_nz.1648 var=359 stl=nz_flag_w __seff.1996 var=478 stl=o_flag_w) load_cmp_const_1_B2 (__adr_u16_rem_bits.2167 u16_rem_bits.650)  <2345>;
              (__apl_nz.2145 var=359 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1648)  <2642>;
              (__apl_c.2147 var=357 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.1646)  <2644>;
              (__seff.2157 var=478 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1996)  <2654>;
              (__adr_u16_rem_bits.2167 var=66 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_u16_rem_bits.2260)  <2656>;
            } stp=0;
            <203> {
              () cc_ae__jump_const_1_B1 (__apl_c.2146 __apl_nz.2144 __trgt.1928)  <2346>;
              (__apl_nz.2144 var=359 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2145)  <2641>;
              (__apl_c.2146 var=357 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.2147)  <2643>;
            } stp=1;
        } #28
        {
            () while_expr (__either.1903)  <713>;
            (__vola.681 var=13 __vola.682 var=13) exit (__vola.635)  <714>;
            (bitcount.701 var=23 bitcount.702 var=23) exit (bitcount.645)  <724>;
            (ENCCON0.703 var=24 ENCCON0.704 var=24) exit (ENCCON0.646)  <725>;
            (u16_rem_bits.711 var=28 u16_rem_bits.712 var=28) exit (u16_rem_bits.650)  <729>;
            (i.713 var=29 i.714 var=29) exit (i.651)  <730>;
            (ENCCON1.715 var=30 ENCCON1.716 var=30) exit (ENCCON1.652)  <731>;
            (TXDAT.719 var=32 TXDAT.720 var=32) exit (TXDAT.654)  <733>;
            (__either.1903 var=452) undefined ()  <2237>;
        } #66
    } #26
    #470 off=40 nxt=619 tgt=108
    (__trgt.1931 var=458) const_inp ()  <2272>;
    <200> {
      (__apl_c.1652 var=369 stl=c_flag_w __apl_nz.1654 var=371 stl=nz_flag_w __seff.1993 var=477 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B3 (__ct_6t0.1921 u16_rem_bits.712 __sp.54)  <2343>;
      (__apl_nz.2213 var=371 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1654)  <2671>;
      (__apl_c.2215 var=369 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.1652)  <2673>;
      (__seff.2241 var=477 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1993)  <2699>;
    } stp=0;
    <201> {
      () cc_be__jump_const_1_B1 (__apl_c.2214 __apl_nz.2212 __trgt.1931)  <2344>;
      (__apl_nz.2212 var=371 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2213)  <2670>;
      (__apl_c.2214 var=369 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.2215)  <2672>;
    } stp=1;
    if {
        {
            () if_expr (__either.1910)  <949>;
            (__either.1910 var=452) undefined ()  <2247>;
        } #72
        {
        } #108 off=59 nxt=111
        {
            #619 off=42 nxt=77
            <198> {
              (__fch_u16_rem_bits.901 var=178 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_6t0.1921 u16_rem_bits.712 __sp.54)  <2341>;
              (__fch_u16_rem_bits.2236 var=178 stl=R46 off=1) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_u16_rem_bits.901)  <2694>;
            } stp=0;
            <199> {
              (ENCCON1.909 var=30 __vola.910 var=13) load_const_bf_mov_const_const_store_1_B1 (__fch_u16_rem_bits.2235 __ptr_ENCCON1.1916 ENCCON1.716 __vola.682)  <2342>;
              (__fch_u16_rem_bits.2235 var=178 stl=a_w1) a_w1_1_dr_move_Rw_1_int16__B1 (__fch_u16_rem_bits.2236)  <2693>;
            } stp=1;
            call {
                () chess_separator_scheduler ()  <966>;
            } #77 off=45 nxt=78
            #78 off=45 nxt=79
            <197> {
              (__fch_i.915 var=187 stl=DMw_r) load__pl_rd_res_reg_const_1_B2 (__ct_8t0.1922 i.714 __sp.54)  <2340>;
              (__fch_i.2233 var=187 stl=R46 off=1) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_i.915)  <2691>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <969>;
            } #79 off=46 nxt=80
            #80 off=46 nxt=81
            <196> {
              (i.920 var=29 __seff.1984 var=474 stl=c_flag_w __seff.1985 var=475 stl=nz_flag_w __seff.1986 var=476 stl=o_flag_w) _pl_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_8t0.1922 i.714 i.714 __sp.54)  <2339>;
              (__seff.2218 var=475 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1985)  <2676>;
              (__seff.2219 var=474 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1984)  <2677>;
              (__seff.2242 var=476 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1986)  <2700>;
            } stp=0;
            call {
                () chess_separator_scheduler ()  <974>;
            } #81 off=47 nxt=104
            #104 off=47 nxt=-3 tgt=3
            () sink (__vola.932)  <1229>;
            () sink (__sp.54)  <1235>;
            () sink (pu8_buffer.95)  <1236>;
            () sink (u16_numbits.97)  <1237>;
            () sink (e_encoding.99)  <1238>;
            () sink (bitcount.702)  <1239>;
            () sink (ENCCON0.704)  <1240>;
            () sink (u16_rem_bits.712)  <1244>;
            () sink (i.920)  <1245>;
            () sink (ENCCON1.909)  <1246>;
            () sink (TXDAT.931)  <1248>;
            () sync_sink (__vola.932) sid=85  <1250>;
            () sync_sink (bitcount.702) sid=95  <1260>;
            () sync_sink (ENCCON0.704) sid=96  <1261>;
            (__vola.1894 var=13) never ()  <2225>;
            (bitcount.1895 var=23) never ()  <2226>;
            (ENCCON0.1896 var=24) never ()  <2227>;
            (__trgt.1929 var=456) const_inp ()  <2270>;
            (__trgt.1930 var=457) const_inp ()  <2271>;
            (__trgt.1935 var=462) const_inp ()  <2276>;
            <192> {
              (__tmp.922 var=192 stl=a_w2 __seff.1977 var=471 stl=c_flag_w __seff.1978 var=472 stl=nz_flag_w __seff.1979 var=473 stl=o_flag_w) _pl_load__pl_rd_res_reg_const_1_B1 (__fch_i.2232 __ct_0t0.1917 pu8_buffer.95 __sp.54)  <2335>;
              (__seff.2216 var=472 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1978)  <2674>;
              (__seff.2217 var=471 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1977)  <2675>;
              (__fch_i.2232 var=187 stl=a_w0) a_w0_1_dr_move_Rw_1_int16__B1 (__fch_i.2233)  <2690>;
              (__seff.2234 var=473 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1979)  <2692>;
              (__tmp.2238 var=192 stl=R46 off=1) Rw_1_dr_move_a_w2_1_int16__B1 (__tmp.922)  <2696>;
            } stp=0;
            <193> {
              (__fchtmp.923 var=193 stl=DM_r) load_1_B1 (__tmp.2237 ENCCON0.704 ENCCON1.909 TXDAT.720 __extDM.16 __extDM_SFR_ENCCON0_t.26 __extDM_SFR_ENCCON1_t.30 __extDM_SFR_word.32 __extDM_int16_.24 __extDM_int8_.25)  <2336>;
              (__tmp.2237 var=192 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__tmp.2238)  <2695>;
              (__fchtmp.2240 var=193 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fchtmp.923)  <2698>;
            } stp=2;
            <194> {
              (TXDAT.931 var=32 __vola.932 var=13) store_const_2_B2 (__fchtmp.2239 __ptr_TXDAT__a1.1925 TXDAT.720 __vola.910)  <2337>;
              (__fchtmp.2239 var=193 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fchtmp.2240)  <2697>;
            } stp=3;
            <195> {
              () jump_const_1_B1 (__trgt.1935)  <2338>;
            } stp=4;
            do {
                {
                    (__vola.976 var=13) entry (__vola.1131 __vola.1894)  <1030>;
                    (bitcount.986 var=23) entry (bitcount.1151 bitcount.1895)  <1040>;
                    (ENCCON0.987 var=24) entry (ENCCON0.1153 ENCCON0.1896)  <1041>;
                } #89
                {
                    #93 off=52 nxt=3
                    <191> {
                      (bitcount.1023 var=23 __seff.1973 var=468 stl=c_flag_w __seff.1974 var=469 stl=nz_flag_w __seff.1975 var=470 stl=o_flag_w) _mi_load_const__pl_rd_res_reg_const_store_1_B3 (__ct_10t0.1920 bitcount.986 bitcount.986 __sp.54)  <2334>;
                      (__seff.2198 var=469 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1974)  <2659>;
                      (__seff.2199 var=468 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1973)  <2660>;
                      (__seff.2201 var=470 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1975)  <2662>;
                    } stp=0;
                    sync {
                        (__vola.1024 var=13) sync_link (__vola.976) sid=85  <1080>;
                        (bitcount.1034 var=23) sync_link (bitcount.1023) sid=95  <1090>;
                        (ENCCON0.1035 var=24) sync_link (ENCCON0.987) sid=96  <1091>;
                    } #3 off=53 nxt=476
                    #476 off=53 nxt=481 tgt=106
                    <190> {
                      (ENCCON0.1073 var=24 __vola.1074 var=13 __seff.1971 var=467 stl=nz_flag_w) load_const__ad_const_cmp_const_cc_ne__jump_const_1_B1 (__ptr_ENCCON0.1915 __trgt.1929 ENCCON0.1035 __vola.1024)  <2333>;
                      (__seff.2200 var=467 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1971)  <2661>;
                    } stp=0;
                    if {
                        {
                            () if_expr (__either.1905)  <1178>;
                            (__either.1905 var=452) undefined ()  <2240>;
                        } #98
                        {
                            (__false.1906 var=451) const ()  <2241>;
                        } #100
                        {
                            (__either.1908 var=452) undefined ()  <2244>;
                            <188> {
                              (__apl_c.1664 var=369 stl=c_flag_w __apl_nz.1666 var=371 stl=nz_flag_w __seff.1968 var=466 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B3 (__ct_10t0.1920 bitcount.1034 __sp.54)  <2331>;
                              (__apl_nz.2203 var=371 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.1666)  <2664>;
                              (__apl_c.2205 var=369 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_uint1_ (__apl_c.1664)  <2666>;
                              (__seff.2206 var=466 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1968)  <2667>;
                            } stp=0;
                            <189> {
                              () cc_a__jump_const_1_B1 (__apl_c.2204 __apl_nz.2202 __trgt.1930)  <2332>;
                              (__apl_nz.2202 var=371 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.2203)  <2663>;
                              (__apl_c.2204 var=369 stl=c_flag_r) c_flag_r_1_dr_move_c_flag_1_uint1_ (__apl_c.2205)  <2665>;
                            } stp=1;
                        } #481 off=55 nxt=106 tgt=93
                        {
                            (__tmp.1130 var=224) merge (__false.1906 __either.1908)  <1185>;
                        } #101
                    } #97
                } #90
                {
                    () while_expr (__tmp.1130)  <1186>;
                    (__vola.1131 var=13 __vola.1132 var=13) exit (__vola.1074)  <1187>;
                    (bitcount.1151 var=23 bitcount.1152 var=23) exit (bitcount.1034)  <1197>;
                    (ENCCON0.1153 var=24 ENCCON0.1154 var=24) exit (ENCCON0.1073)  <1198>;
                } #102
            } #88
            #106 off=57 nxt=111
            <186> {
              (bitcount.1302 var=23) store_const_1_B1 (__adr_bitcount.2253 bitcount.1152)  <2329>;
              (__adr_bitcount.2253 var=62 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__adr_bitcount.2245)  <2707>;
            } stp=0;
        } #73
        {
            (__vola.1303 var=13) merge (__vola.682 __vola.1132)  <1380>;
            (bitcount.1313 var=23) merge (bitcount.702 bitcount.1302)  <1390>;
            (ENCCON0.1314 var=24) merge (ENCCON0.704 ENCCON0.1154)  <1391>;
            (i.1319 var=29) merge (i.714 i.920)  <1396>;
            (ENCCON1.1320 var=30) merge (ENCCON1.716 ENCCON1.909)  <1397>;
            (TXDAT.1322 var=32) merge (TXDAT.720 TXDAT.931)  <1399>;
        } #109
    } #71
    #111 off=59 nxt=-2
    () sink (__vola.1303)  <1428>;
    () sink (__sp.1350)  <1434>;
    () sink (pu8_buffer.95)  <1435>;
    () sink (u16_numbits.97)  <1436>;
    () sink (e_encoding.99)  <1437>;
    () sink (bitcount.1313)  <1438>;
    () sink (ENCCON0.1314)  <1439>;
    () sink (u16_rem_bits.712)  <1443>;
    () sink (i.1319)  <1444>;
    () sink (ENCCON1.1320)  <1445>;
    () sink (TXDAT.1322)  <1447>;
    (__ct_12s0.1923 var=227) const_inp ()  <2264>;
    <184> {
      (__sp.1350 var=19 __seff.1962 var=463 stl=c_flag_w __seff.1963 var=464 stl=nz_flag_w __seff.1964 var=465 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_12s0.1923 __sp.54 __sp.54)  <2327>;
      (__seff.2220 var=464 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.1963)  <2678>;
      (__seff.2221 var=463 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.1962)  <2679>;
      (__seff.2243 var=465 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.1964)  <2701>;
    } stp=0;
    <185> {
      () ret_1_B1 ()  <2328>;
    } stp=2;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,198:0,0);
1 : (0,212:4,21);
2 : (0,207:2,35);
3 : (0,224:4,53);
6 : (0,198:82,0);
7 : (0,198:82,0);
8 : (0,198:60,0);
9 : (0,198:60,0);
10 : (0,198:38,0);
11 : (0,198:38,0);
12 : (0,201:26,5);
15 : (0,203:14,7);
17 : (0,203:22,8);
18 : (0,205:15,9);
19 : (0,205:15,9);
20 : (0,206:4,10);
21 : (0,206:4,10);
26 : (0,207:2,11);
28 : (0,207:2,11);
32 : (0,209:24,13);
33 : (0,210:32,14);
34 : (0,210:32,14);
35 : (0,210:32,15);
36 : (0,210:32,15);
43 : (0,212:4,17);
45 : (0,212:4,17);
48 : (0,214:17,19);
52 : (0,212:45,23);
55 : (0,212:45,25);
59 : (0,212:4,29);
61 : (0,216:13,33);
62 : (0,216:13,33);
63 : (0,217:17,34);
68 : (0,207:2,39);
71 : (0,220:2,42);
73 : (0,221:2,43);
77 : (0,222:24,45);
78 : (0,223:32,46);
79 : (0,223:32,46);
80 : (0,223:32,47);
81 : (0,223:32,47);
88 : (0,224:4,49);
90 : (0,224:4,49);
93 : (0,226:17,51);
97 : (0,224:45,55);
100 : (0,224:45,57);
104 : (0,224:4,61);
106 : (0,228:13,65);
108 : (0,220:2,67);
111 : (0,220:2,70);
454 : (0,212:39,23);
459 : (0,212:56,24);
464 : (0,207:23,37);
470 : (0,220:20,42);
476 : (0,224:39,55);
481 : (0,224:56,56);
602 : (0,203:14,7);
610 : (0,209:16,12);
619 : (0,222:16,44);
----------
106 : (0,198:82,0);
108 : (0,198:60,0);
110 : (0,198:38,0);
121 : (0,203:14,7);
132 : (0,203:22,8);
135 : (0,205:15,9);
139 : (0,206:4,10);
188 : (0,207:2,11);
198 : (0,207:2,11);
199 : (0,207:2,11);
203 : (0,207:2,11);
204 : (0,207:2,11);
205 : (0,207:2,11);
207 : (0,207:2,11);
247 : (0,209:24,13);
250 : (0,210:32,14);
255 : (0,210:32,15);
311 : (0,212:4,17);
321 : (0,212:4,17);
322 : (0,212:4,17);
459 : (0,212:45,23);
466 : (0,212:45,26);
467 : (0,212:4,27);
468 : (0,212:4,27);
478 : (0,212:4,27);
479 : (0,212:4,27);
660 : (0,216:13,33);
713 : (0,207:2,37);
714 : (0,207:2,37);
724 : (0,207:2,37);
725 : (0,207:2,37);
729 : (0,207:2,37);
730 : (0,207:2,37);
731 : (0,207:2,37);
733 : (0,207:2,37);
949 : (0,220:2,42);
966 : (0,222:24,45);
969 : (0,223:32,46);
974 : (0,223:32,47);
1030 : (0,224:4,49);
1040 : (0,224:4,49);
1041 : (0,224:4,49);
1178 : (0,224:45,55);
1185 : (0,224:45,58);
1186 : (0,224:4,59);
1187 : (0,224:4,59);
1197 : (0,224:4,59);
1198 : (0,224:4,59);
1380 : (0,220:2,69);
1390 : (0,220:2,69);
1391 : (0,220:2,69);
1396 : (0,220:2,69);
1397 : (0,220:2,69);
1399 : (0,220:2,69);
2327 : (0,220:2,0) (0,220:2,70);
2328 : (0,220:2,70);
2329 : (0,228:4,64);
2331 : (0,224:48,56) (0,201:11,0) (0,224:56,56);
2332 : (0,224:56,56) (0,224:4,59);
2333 : (0,224:26,55) (0,224:30,55) (0,224:39,55) (0,224:45,55);
2334 : (0,226:17,50) (0,226:9,0) (0,201:11,0) (0,226:9,50);
2335 : (0,223:30,47) (0,223:20,0) (0,198:98,0);
2336 : (0,223:30,47);
2337 : (0,223:14,47);
2339 : (0,223:32,46) (0,223:31,0) (0,200:25,0) (0,223:31,46);
2340 : (0,223:31,45) (0,200:25,0);
2341 : (0,222:39,44) (0,200:11,0);
2342 : (0,222:16,44);
2343 : (0,220:7,42) (0,200:11,0) (0,220:20,42);
2344 : (0,220:20,42) (0,220:2,42);
2345 : (0,207:10,37) (0,207:23,37);
2346 : (0,207:23,37) (0,207:2,37);
2347 : (0,217:4,33) (0,217:17,33);
2348 : (0,216:4,32);
2349 : (0,212:48,24) (0,201:11,0) (0,212:56,24);
2350 : (0,212:56,24) (0,212:4,27);
2351 : (0,212:26,23) (0,212:30,23) (0,212:39,23) (0,212:45,23);
2352 : (0,214:17,18) (0,214:9,0) (0,201:11,0) (0,214:9,18);
2353 : (0,210:30,15) (0,210:20,0) (0,198:98,0);
2354 : (0,210:30,15);
2355 : (0,210:14,15);
2357 : (0,210:32,14) (0,210:31,0) (0,200:25,0) (0,210:31,14);
2358 : (0,210:31,13) (0,200:25,0);
2359 : (0,209:16,12);
2362 : (0,200:25,0) (0,206:2,9);
2363 : (0,205:17,8) (0,198:69,0);
2364 : (0,200:11,0) (0,205:2,8);
2366 : (0,203:37,7) (0,198:48,0) (0,203:24,7);
2367 : (0,203:24,7);
2368 : (0,203:14,7);
2370 : (0,201:26,5);
2371 : (0,198:48,0) (0,198:38,0);
2372 : (0,198:69,0) (0,198:60,0);
2373 : (0,198:5,0);
2374 : (0,198:98,0) (0,198:82,0);
2584 : (0,201:11,0);
2596 : (0,209:26,0);
2598 : (0,200:11,0);

