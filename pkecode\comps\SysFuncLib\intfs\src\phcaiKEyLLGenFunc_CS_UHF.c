/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_UHF.c 11676 2018-01-02 13:17:01Z dep17622 $
  $Revision: 11676 $
*/

/**
 * @file
 * Implementation of the stubs to call UHF specific KEyLink Lite General Functions
 * placed in ROM.
 */

#include "phcaiKEyLLGenFunc_CS_UHF.h"
#include "phcaiKEyLLGenFunc_CS_Utils.h"
#include "phcaiKEyLLGenFunc_SCI.h"

#if defined(PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY) && (PHFL_CONFIG_HAVE_UHF_FORCE_XO_READY == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_UHF_ForceXoReady(const bool_t value)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_UHF_FORCE_XO_READY;
  phcaiKEyLLGenFunc_Func_Params.params.uhf_force_xo_ready.value = value;
  call_syscall(5);
}
#endif

#if defined(PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED) && (PHFL_CONFIG_HAVE_UHF_FORCE_PLL_LOCK_DETECTED == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_UHF_ForcePllLockDetected(const bool_t value)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_UHF_FORCE_PLL_LOCK_DETECTED;
  phcaiKEyLLGenFunc_Func_Params.params.uhf_force_pll_lock_detected.value = value;
  call_syscall(5);
}
#endif


#if defined(PHFL_CONFIG_HAVE_XO_START_UP) && (PHFL_CONFIG_HAVE_XO_START_UP == CONFIG_YES)
error_t phcaiKEyLLGenFunc_CS_UHF_XoStartUp(bool_t validate, bool_t force_xo_ready)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_XO_START_UP;
  phcaiKEyLLGenFunc_Func_Params.params.xo_startup.validate = validate;
  phcaiKEyLLGenFunc_Func_Params.params.xo_startup.force_xo_ready = force_xo_ready;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.xo_startup.return_val;
}
#endif

#if defined(PHFL_CONFIG_HAVE_PLL_START_UP) && (PHFL_CONFIG_HAVE_PLL_START_UP == CONFIG_YES)
error_t phcaiKEyLLGenFunc_CS_UHF_PllStartUp(bool_t calibrate,
  bool_t force_pll_lock_ready, uint8_t cal_idac_ctrl)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_PLL_START_UP;
  phcaiKEyLLGenFunc_Func_Params.params.pll_startup.calibrate = calibrate;
  phcaiKEyLLGenFunc_Func_Params.params.pll_startup.force_pll_lock_ready = force_pll_lock_ready;
  phcaiKEyLLGenFunc_Func_Params.params.pll_startup.cal_idac_ctrl = cal_idac_ctrl;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.pll_startup.return_val;
}
#endif

#if defined(PHFL_CONFIG_HAVE_SET_RFGATE) && (PHFL_CONFIG_HAVE_SET_RFGATE == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_UHF_SetRfGate(bool_t rfgate_off)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_SET_RFGATE;
  phcaiKEyLLGenFunc_Func_Params.params.set_rfgate.rfgate_off = rfgate_off;
  call_syscall(5);
}
#endif

#if defined(PHFL_CONFIG_HAVE_SET_PAINGATE) && (PHFL_CONFIG_HAVE_SET_PAINGATE == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_UHF_SetPAInGate(bool_t paingate_off)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_SET_PAINGATE;
  phcaiKEyLLGenFunc_Func_Params.params.set_paingate.paingate_off = paingate_off;
  call_syscall(5);
}
#endif

#if defined(PHFL_CONFIG_HAVE_SET_CP_ICP) && (PHFL_CONFIG_HAVE_SET_CP_ICP == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_UHF_SetCP_ICP(uint8_t value)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_SET_CP_ICP;
  phcaiKEyLLGenFunc_Func_Params.params.set_cpicp.value = value;
  call_syscall(5);
}
#endif

#if defined(PHFL_CONFIG_HAVE_SET_PTRIM_XO) && (PHFL_CONFIG_HAVE_SET_PTRIM_XO == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_XO(uint8_t value)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_SET_PTRIM_XO;
  phcaiKEyLLGenFunc_Func_Params.params.set_ptrimxo.value = value;
  call_syscall(5);
}
#endif

#if defined(PHFL_CONFIG_HAVE_SET_PTRIM_PLL) && (PHFL_CONFIG_HAVE_SET_PTRIM_PLL == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_PLL(uint8_t value)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_SET_PTRIM_PLL;
  phcaiKEyLLGenFunc_Func_Params.params.set_ptrimpll.value = value;
  call_syscall(5);
}
#endif

#if defined(PHFL_CONFIG_HAVE_SET_PTRIM_HS) && (PHFL_CONFIG_HAVE_SET_PTRIM_HS == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_UHF_SetPTRIM_HS(uint8_t value)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_SET_PTRIM_HS;
  phcaiKEyLLGenFunc_Func_Params.params.set_ptrimhs.value = value;
  call_syscall(5);
}
#endif

#if defined(PHFL_CONFIG_HAVE_PA_BITFIELDS) && (PHFL_CONFIG_HAVE_PA_BITFIELDS == CONFIG_YES)
uint8_t phcaiKEyLLGenFunc_CS_sm2_pa_get(PA_bitfield_selector_t bitfield)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_GET_PA_BITFIELD;
  phcaiKEyLLGenFunc_Func_Params.params.sm2_pa_rw.bitfield = bitfield;
  call_syscall(5);
  return phcaiKEyLLGenFunc_Func_Params.params.sm2_pa_rw.value;
}

void phcaiKEyLLGenFunc_CS_sm2_pa_set(PA_bitfield_selector_t bitfield, uint8_t value)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = SMART2_FUNC_CODE_SET_PA_BITFIELD;
  phcaiKEyLLGenFunc_Func_Params.params.sm2_pa_rw.value = value;
  phcaiKEyLLGenFunc_Func_Params.params.sm2_pa_rw.bitfield = bitfield;
  call_syscall(5);
}
#endif //defined(PHFL_CONFIG_HAVE_PA_BITFIELDS)

#if defined(PHFL_CONFIG_HAVE_CS_CP_ICP_TRIM) && (PHFL_CONFIG_HAVE_CS_CP_ICP_TRIM == CONFIG_YES)
void phcaiKEyLLGenFunc_CS_CP_ICP_TRIM(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = UHF_FUNC_CODE_LOAD_CP_ICP_TRIM;
  call_syscall(5);
}
#endif
