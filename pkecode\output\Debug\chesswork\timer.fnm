
// File generated by noodle version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:23 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/timer.c mrk3

// toolrelease _19R3;

"D:/pke/01_code/00_230519/AP210054_SMARTKEY_CODE/apps/src/timer.c"
"D:\pke\01_code\00_230519\AP210054_SMARTKEY_CODE"

"timer-a1f114.sfg"
  : void_timer_DelayTBit
  : "timer_DelayTBit" global 66 Ofile
  (
    void_nop
  )

"timer-f635ae.sfg"
  : void_timer_DelayTBit_half
  : "timer_DelayTBit_half" global 80 Ofile
  (
    void_nop
  )

"timer-d63159.sfg"
  : void_timer_WaitNops___ushort
  : "timer_WaitNops" global 93 Ofile
  (
    void_nop
  )

"timer-2ac077.sfg"
  : void_timer_delay_us___ushort
  : "timer_delay_us" global 137 Ofile
  (
    void_phcaiKEyLLGenFunc_timer0_delay_us___ushort
  )

"timer-258389.sfg"
  : void_timer_delay_ms___ushort
  : "timer_delay_ms" global 157 Ofile
  (
    void_phcaiKEyLLGenFunc_timer0_delay_ms___ushort
  )

"timer-3dbc9d.sfg"
  : void_cycle_delay_ms___ushort
  : "cycle_delay_ms" global 177 Ofile
  (
    void_nop
  )

"timer-8342f7.asm"
  : void_nop
  : "nop" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_timer0_delay_us___ushort
  : "phcaiKEyLLGenFunc_timer0_delay_us" global 1 Ofile
  (
  )

""
  : void_phcaiKEyLLGenFunc_timer0_delay_ms___ushort
  : "phcaiKEyLLGenFunc_timer0_delay_ms" global 1 Ofile
  (
  )

