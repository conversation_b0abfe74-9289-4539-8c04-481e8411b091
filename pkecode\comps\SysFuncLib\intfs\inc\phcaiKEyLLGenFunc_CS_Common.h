/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_Common.h 31225 2021-03-10 14:44:11Z dep10330 $
  $Revision: 31225 $
*/

/**
 * @file
 * Declarations of the stubs to call common system functions (ROM library).
 */

#ifndef PHCAIKEYLLGENFUNC_CS_COMMON_H
#define PHCAIKEYLLGENFUNC_CS_COMMON_H

#include "types.h"
#include "phcaiKEyLLGenFunc_SCI.h"

/**
 * @defgroup genfunclib_stubs_common Common
 * Caller stubs for common API functions placed
 * in ROM and executed in system mode.
 * @{
 */

/**
 * Forwards the given text string to the MDI asynchronous output where it can
 * be captured and visualized by a debugger.
 *
 * @param[in] text Pointer to the text string.
 *
 * @see phcaiKEyLLGenFunc_CS_MDI_PrintLn()
 */
void phcaiKEyLLGenFunc_CS_MDI_Print(const string_t* const text);

/**
 * Forwards the given text string (after appending a new line) to the MDI
 * asynchronous output where it can be captured and visualized by a debugger.
 *
 * @param[in] text Pointer to the string.
 *
 * @see phcaiKEyLLGenFunc_CS_MDI_Print()
 */
void phcaiKEyLLGenFunc_CS_MDI_PrintLn(const string_t* const text);

/**
 * Returns the complete version information structure.
 *
 * @param[out] version Pointer to the (empty) structure to be filled with the
 *                     version information.
 */
void phcaiKEyLLGenFunc_CS_GetVersion(phcaiMRK3Versions_Info_t* const version);

/**
 * Sets the Clock Control Register 0 (MRK3 clock control):
 *
 * @param[in] clkcon The CLKCON0 register value that shall be set.
 * <table>
 *  <tr> <td>clkcon[7]</td><td>RFU - set to '0'</td></tr>
 *  <tr> <td>clkcon[6..0]</td><td>CLKCON0[6..0]</td></tr>
 * </table>
 * @note: CLKCON0[6:5] = CPUCLKSEL[1:0] CPU clock source selection,
 *        CLKCON0[4:0] = CLKCYC[4:0] number of CPUCLK periods per machine cycle
 */
void phcaiKEyLLGenFunc_CS_SetClkCon(const uint8_t clkcon);

/**
 * Sets the clock control registers.
 *
 * This function is a wrapper for ::phcaiKEyLLGenFunc_CS_SetClkCon.
 * If the caller aims at selecting the main RC oscillator,
 * the function ensures that CPUCLKCYC is greater or equal to 1,
 * in order to avoid corrupted VBATREG SFR reads.
 *
 * @param[in] clkcon The CLKCON register value that shall be set.
 * <table>
 *  <tr> <td>clkcon[7]</td><td>RFU - must be '0'</td></tr>
 *  <tr> <td>clkcon[6..0]</td><td>CLKCON[6..0]</td></tr>
 * </table>
 */
void phcaiKEyLLGenFunc_CS_SetClkCon_safe(const uint8_t clkcon);

/**
 * Sets the AUXCLK divider CPUAUXDIVSEL in CLKCON4 in order to provide a divided
 * clock signal (CPUAUXDIVCLK) to the CPU, in case CPUCLKSEL is set to 0.
 * There are four possible divisions /1, /2, /4, /8.
 *
 * @param[in] value New value to be stored as AUXDIVCLK.
 * The lowest 2 bits are considered.
 *
 * @note The syscall runtime (user-system-user duration) is approximately 28
 * CPU cycles.
 */
void phcaiKEyLLGenFunc_CS_SetAuxDivClk(const uint8_t value);

/**
 * Reads sequence increment data from eight ULPEE pages, starting at the given
 * page, and stores them in the array @p si_field.
 * Up to three corrupted SI pages can be detected and corrected.
 * ULPEE must be powered on (ULPPON=1) and the module must be enabled
 * (ULPSEL register) at least t_ULP_PON_us before calling this function.
 * This function is provided for compatibility only.
 * For new designs, it is recommended to use phcaiKEyLLGenFunc_CS_SI_Init_Ext.
 *
 * @param[out] si_field Pointer to the SI field array.
 * @param[in]  page Page number of the first (of eight) SI-page.
 *
 * @note The SI field is a copy of the 8 ULPEE SI pages in RAM, after the repair algorithm
 *       has been working over these pages.
 * @see phcaiKEyLLGenFunc_CS_SI_Init_Ext function.
 */
void phcaiKEyLLGenFunc_CS_SI_Init(uint32_t si_field[8], uint8_t page);

/**
 * Reads sequence increment data from eight ULPEE pages, starting at the given
 * page, and stores them in the array @p si_field.
 * Up to three corrupted SI pages can be detected and corrected in ULPEE, while the maximum
 * number of pages can be specified.
 * Note that up to three pages can be repaired in the RAM storage, even if a lower number
 * (including 0) is repaired in ULPEE.
 * Further documentation is available from NXP customer support.
 * ULPEE must be powered on (ULPPON=1) and the module must be enabled
 * (ULPSEL register) at least t_ULP_PON_us before calling this function.
 *
 * @param[in] si_field Address of the SI field.
 * @param[in] page Page number of the first (of eight) SI page
 *                 (valid range 0..0x01E0).
 * @param[in] maxnumrepair Maximal number of pages to be repaired in
 *                  ULPEE (0 to 3; if 0, nothing is written to ULPEE).
 * @return Result code
 *
 * @note The SI field is a copy of the 8 ULPEE-SI-Pages in RAM, after the repair algorithm
 *       has been working over these pages.
 * @see SI_RESULT_t
 */
SI_RESULT_t phcaiKEyLLGenFunc_CS_SI_Init_Ext(uint32_t si_field[8], uint16_t page, uint8_t maxnumrepair);

/**
 * Reads sequence increment data from eight ULPEE pages, starting at the given
 * page, and stores them in the array @p si_field.
 * One corrupted SI page can be detected and corrected. In case a page needs to
 * be corrected, the function does not wait until the ULPEE programming is
 * finished.
 *
 * @param[in] si_field Address of the SI field
 * @param[in] page Page number of the first (of eight) SI page
 *                 (valid range 0..0x01E0).
 * @param[in] maxnumrepair Maximal number of pages to be repaired in ULPEE (0, 1).
 * @return Result code
 *
 * @note The SI field is a copy of the 8 ULPEE SI pages in RAM.
 * @note For details see "Sequence Increment Recovery Improvements.pdf".
 * @see SI_RESULT_t
 */
SI_RESULT_t phcaiKEyLLGenFunc_CS_SI_Init_Ext_NoWait(uint32_t si_field[8],
    uint16_t page, uint8_t maxnumrepair);

/**
 * Calculates the SI value from a given SI field.
 *
 * @param[in] si_field Address of the SI field.
 * @param[out] si Address of the calculated SI value (array of 4 uint8_t values, MSB first).
 *
 * @note The SI field is a copy of the 8 ULPEE SI pages in RAM.
 * @see phcaiKEyLLGenFunc_CS_SI_Inc()
 */
void phcaiKEyLLGenFunc_CS_SI_Get(uint32_t si_field[8], uint8_t* const si);


/**
 * Increments the SI value by 1.
 *
 * @param[in] si_field Address of the SI field.
 * @param[in] page Page number of the first (of eight) SI page.
 *
 * @note The SI field is a copy of the 8 ULPEE SI pages in RAM.
 * @see phcaiKEyLLGenFunc_CS_SI_Inc_Ext()
 */
void phcaiKEyLLGenFunc_CS_SI_Inc(uint32_t si_field[8], uint8_t page);

/**
 * Increments the SI value by 1 and returns the result code.
 *
 * @param[in] si_field Address of the SI field.
 * @param[in] page Page number of the first (of eight) SI page
 *                 (valid range 0..0x01E0).
 * @return Result code (only ::SI_SUCCESS or ::SI_PRGFAIL may occur).
 *
 * @note The SI field is a copy of the 8 ULPEE SI pages in RAM.
 * @see SI_RESULT_t
 * @see phcaiKEyLLGenFunc_CS_SI_Inc_Ext_NoWait()
 */
SI_RESULT_t phcaiKEyLLGenFunc_CS_SI_Inc_Ext( uint32_t si_field[8], uint16_t page );

/**
 * Increments the SI value by 1 and returns immediately after ULPEE programming
 * has started (does not wait until programming has ended).
 *
 * @param[in] si_field Address of the SI field.
 * @param[in] page Page number of the first (of eight) SI page
 *                 (valid range 0..0x01E0).
 *
 * @note The SI field is a copy of the 8 ULPEE SI pages in RAM.
 * @see phcaiKEyLLGenFunc_CS_SI_Inc_Ext()
*/
void phcaiKEyLLGenFunc_CS_SI_Inc_Ext_NoWait(uint32_t si_field[8], uint16_t page);

/**
 *  Sets the LFCLKX2DIS bit in CLKCON1.
 *
 * @param[in] set Defines whether LFCLKX2DIS shall be set (::TRUE)
 *                or cleared (::FALSE).
 */
void phcaiKEyLLGenFunc_CS_SetLfclkx2Dis(bool_t set);

/**
 *  Sets MDICLKSEL in CLKCON1.
 *
 * @param[in] val Defines the value to be written to MDICLKSEL (lower 2 bits).
 */
void phcaiKEyLLGenFunc_CS_MdiClkSel(uint8_t val);

/**
 * Copies data from EROM to a buffer in RAM.
 *
 * @param[out] ram_buffer            Pointer to the start of the target buffer in user RAM.
 * @param[in]  erom_start_word_addr  Physical (word) start address of the desired
 *                                   source content in EROM.
 * @param[in] number_of_bytes        Number of bytes to be copied (must be even and <= 256).
 * @return ::EE_WR_OK in case of success, an appropriate error code otherwise.
 * @see eeprom_write_error_t
 */
eeprom_write_error_t phcaiKEyLLGenFunc_CS_EROM_read(
  uint16_t* const ram_buffer, uint16_t const erom_start_word_addr, const uint16_t number_of_bytes);

/**
 * Writes data from a RAM source to EROM.
 * Must be enabled by a configuration setting in ULPEE module 15, otherwise no effect.
 * ULP module 15 must be enabled beforehand.
 *
 * @param[in] ram_buffer              Pointer to the base of the user RAM
 * @param[in] erom_start_word_addr    Pointer to the word address in EROM, to where the data will be written.
 * @param[in] number_of_bytes         Number of bytes to be copied (must be even and <= 256)
 * @return ::EE_WR_OK in case of success, an appropriate error code otherwise.
 */
eeprom_write_error_t phcaiKEyLLGenFunc_CS_EROM_write(
  uint16_t* const ram_buffer, uint16_t const erom_start_word_addr, const uint16_t number_of_bytes);


#if (defined(PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL) && \
 (PHFL_CONFIG_HAVE_EROM_ENABLE_PF2_SYSCALL == CONFIG_YES))
/**
 * @brief Enables or disables the second pageflash module.
 * @param[in] enabled Determines the desired status of the second
 * pageflash module.
 */
void phcaiKEyLLGenFunc_CS_EROM_enable_PF2(const bool_t enabled);
#endif

/**
 * Sets or clears the LINTSWCON flag in the INTCON register.
 * See datasheet for description.
 * This switch can be used to test interrupt routines,
 * by enabling the INTSETx bits also for level sensitive interrupts.
 *
 * @param[in] set If TRUE the LINTSWCON Flag is set, if FALSE it is cleared.
 */
void phcaiKEyLLGenFunc_CS_LIntSwCon(const bool_t set);


/*@}*/

#endif
