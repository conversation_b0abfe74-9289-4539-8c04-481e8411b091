
// File generated by mist version P-2019.09#78e58cd307#210222, Tue Sep 26 11:12:54 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-e68161 mrk3

[
    0 : void_tx_apply_configuration___PTxRegSettings_t typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : ps_ActTxRegSettings typ=int8_ val=0t0 bnd=a sz=2 algn=2 stl=DM tref=__PTxRegSettings_t_DM
   21 : FREQCON0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_FREQCON0_t_DM9
   25 : FREQCON1 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_FREQCON1_t_DM9
   26 : ASKCON typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ASKCON_t_DM9
   28 : FSKCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_FSKCON_t_DM9
   30 : BRGCON typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_BRGCON_t_DM9
   32 : ASKRMP typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_ASKRMP_t_DM9
   34 : FSKRMP typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_FSKRMP_t_DM9
   36 : PACON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PACON_t_DM9
   38 : PAPWR typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PAPWR_t_DM9
   40 : PATRIM typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PATRIM_t_DM9
   41 : PALIMIT typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_PALIMIT_t_DM9
   43 : ENCCON0 typ=int8_ bnd=e sz=2 algn=2 stl=DM9 tref=SFR_ENCCON0_t_DM9
   46 : __extDM_TxRegSettings_t_Cfg_FREQCON0 typ=int8_ bnd=b stl=DM
   47 : __extDM_TxRegSettings_t_Cfg_FREQCON1 typ=int8_ bnd=b stl=DM
   48 : __extDM_TxRegSettings_t_Cfg_ASKCON typ=int8_ bnd=b stl=DM
   49 : __extDM_TxRegSettings_t_Cfg_FSKCON typ=int8_ bnd=b stl=DM
   50 : __extDM_TxRegSettings_t_Cfg_BRGCON typ=int8_ bnd=b stl=DM
   51 : __extDM_TxRegSettings_t_Cfg_ASKRMP typ=int8_ bnd=b stl=DM
   52 : __extDM_TxRegSettings_t_Cfg_FSKRMP typ=int8_ bnd=b stl=DM
   53 : __extDM_TxRegSettings_t_Cfg_PACON typ=int8_ bnd=b stl=DM
   54 : __extDM_TxRegSettings_t_Cfg_PAPWR typ=int8_ bnd=b stl=DM
   55 : __extDM_TxRegSettings_t_Cfg_PATRIM typ=int8_ bnd=b stl=DM
   56 : __extDM_TxRegSettings_t_Cfg_PALIMIT typ=int8_ bnd=b stl=DM
   57 : __extDM_TxRegSettings_t_Cfg_ENCCON0 typ=int8_ bnd=b stl=DM
   60 : __ptr_FREQCON0 typ=int16_ val=0a bnd=m adro=21
   62 : __ptr_FREQCON1 typ=int16_ val=0a bnd=m adro=25
   64 : __ptr_ASKCON typ=int16_ val=0a bnd=m adro=26
   66 : __ptr_FSKCON typ=int16_ val=0a bnd=m adro=28
   68 : __ptr_BRGCON typ=int16_ val=0a bnd=m adro=30
   70 : __ptr_ASKRMP typ=int16_ val=0a bnd=m adro=32
   72 : __ptr_FSKRMP typ=int16_ val=0a bnd=m adro=34
   74 : __ptr_PACON typ=int16_ val=0a bnd=m adro=36
   76 : __ptr_PAPWR typ=int16_ val=0a bnd=m adro=38
   78 : __ptr_PATRIM typ=int16_ val=0a bnd=m adro=40
   80 : __ptr_PALIMIT typ=int16_ val=0a bnd=m adro=41
   82 : __ptr_ENCCON0 typ=int16_ val=0a bnd=m adro=43
   83 : __arg_ps_ActTxRegSettings typ=int16_ bnd=p tref=__PTxRegSettings_t__
   88 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   91 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
   95 : __fch___extDM_TxRegSettings_t_Cfg_FREQCON0 typ=int16_ bnd=m
   99 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  103 : __fch___extDM_TxRegSettings_t_Cfg_FREQCON1 typ=int16_ bnd=m
  107 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  111 : __fch___extDM_TxRegSettings_t_Cfg_ASKCON typ=int16_ bnd=m
  115 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  119 : __fch___extDM_TxRegSettings_t_Cfg_FSKCON typ=int8_ bnd=m
  123 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  127 : __fch___extDM_TxRegSettings_t_Cfg_BRGCON typ=int16_ bnd=m
  131 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  135 : __fch___extDM_TxRegSettings_t_Cfg_ASKRMP typ=int8_ bnd=m
  139 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  143 : __fch___extDM_TxRegSettings_t_Cfg_FSKRMP typ=int8_ bnd=m
  147 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  151 : __fch___extDM_TxRegSettings_t_Cfg_PACON typ=int8_ bnd=m
  155 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  159 : __fch___extDM_TxRegSettings_t_Cfg_PAPWR typ=int8_ bnd=m
  163 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  167 : __fch___extDM_TxRegSettings_t_Cfg_PATRIM typ=int8_ bnd=m
  171 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  175 : __fch___extDM_TxRegSettings_t_Cfg_PALIMIT typ=int8_ bnd=m
  179 : __fch_ps_ActTxRegSettings typ=int16_ bnd=m
  183 : __fch___extDM_TxRegSettings_t_Cfg_ENCCON0 typ=int16_ bnd=m
  187 : __ct_2s0 typ=int16_ val=2s0 bnd=m
  194 : __ct_2s0 typ=int16_ val=2s0 bnd=m
  195 : __seff typ=any bnd=m
  196 : __seff typ=any bnd=m
  197 : __seff typ=any bnd=m
  198 : __seff typ=any bnd=m
  199 : __seff typ=any bnd=m
  200 : __seff typ=any bnd=m
]
Fvoid_tx_apply_configuration___PTxRegSettings_t {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__sp.18 var=19) source ()  <29>;
    (ps_ActTxRegSettings.19 var=20) source ()  <30>;
    (FREQCON0.20 var=21) source ()  <31>;
    (FREQCON1.24 var=25) source ()  <35>;
    (ASKCON.25 var=26) source ()  <36>;
    (FSKCON.27 var=28) source ()  <38>;
    (BRGCON.29 var=30) source ()  <40>;
    (ASKRMP.31 var=32) source ()  <42>;
    (FSKRMP.33 var=34) source ()  <44>;
    (PACON.35 var=36) source ()  <46>;
    (PAPWR.37 var=38) source ()  <48>;
    (PATRIM.39 var=40) source ()  <50>;
    (PALIMIT.40 var=41) source ()  <51>;
    (ENCCON0.42 var=43) source ()  <53>;
    (__extDM_TxRegSettings_t_Cfg_FREQCON0.45 var=46) source ()  <56>;
    (__extDM_TxRegSettings_t_Cfg_FREQCON1.46 var=47) source ()  <57>;
    (__extDM_TxRegSettings_t_Cfg_ASKCON.47 var=48) source ()  <58>;
    (__extDM_TxRegSettings_t_Cfg_FSKCON.48 var=49) source ()  <59>;
    (__extDM_TxRegSettings_t_Cfg_BRGCON.49 var=50) source ()  <60>;
    (__extDM_TxRegSettings_t_Cfg_ASKRMP.50 var=51) source ()  <61>;
    (__extDM_TxRegSettings_t_Cfg_FSKRMP.51 var=52) source ()  <62>;
    (__extDM_TxRegSettings_t_Cfg_PACON.52 var=53) source ()  <63>;
    (__extDM_TxRegSettings_t_Cfg_PAPWR.53 var=54) source ()  <64>;
    (__extDM_TxRegSettings_t_Cfg_PATRIM.54 var=55) source ()  <65>;
    (__extDM_TxRegSettings_t_Cfg_PALIMIT.55 var=56) source ()  <66>;
    (__extDM_TxRegSettings_t_Cfg_ENCCON0.56 var=57) source ()  <67>;
    (__arg_ps_ActTxRegSettings.82 var=83 stl=R46 off=0) inp ()  <93>;
    (__ct_0t0.312 var=88) const_inp ()  <380>;
    (__ct_2s0.314 var=194) const_inp ()  <382>;
    <109> {
      (__sp.90 var=19 __seff.379 var=198 stl=c_flag_w __seff.380 var=199 stl=nz_flag_w __seff.381 var=200 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.314 __sp.18 __sp.18)  <443>;
      (__seff.432 var=198 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.379)  <557>;
      (__seff.433 var=199 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.380)  <558>;
      (__seff.434 var=200 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.381)  <559>;
    } stp=0;
    <110> {
      (ps_ActTxRegSettings.97 var=20) _pl_rd_res_reg_const_store_1_B1 (__arg_ps_ActTxRegSettings.383 __ct_0t0.312 ps_ActTxRegSettings.19 __sp.90)  <444>;
      (__arg_ps_ActTxRegSettings.383 var=83 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__arg_ps_ActTxRegSettings.82)  <508>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <108>;
    } #4 off=3 nxt=5
    #5 off=3 nxt=6
    (__ptr_FREQCON0.300 var=60) const_inp ()  <368>;
    <106> {
      (__fch_ps_ActTxRegSettings.98 var=91 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <440>;
      (__fch_ps_ActTxRegSettings.407 var=91 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.98)  <532>;
    } stp=0;
    <107> {
      (__fch___extDM_TxRegSettings_t_Cfg_FREQCON0.102 var=95 stl=DMw_r) load_1_B1 (__fch_ps_ActTxRegSettings.406 __extDM_TxRegSettings_t_Cfg_FREQCON0.45)  <441>;
      (__fch_ps_ActTxRegSettings.406 var=91 stl=DM_rmw_addr) DM_rmw_addr_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.407)  <531>;
      (__fch___extDM_TxRegSettings_t_Cfg_FREQCON0.409 var=95 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_FREQCON0.102)  <534>;
    } stp=2;
    <108> {
      (FREQCON0.107 var=21 __vola.108 var=13) store_const_1_B2 (__fch___extDM_TxRegSettings_t_Cfg_FREQCON0.408 __ptr_FREQCON0.300 FREQCON0.20 __vola.12)  <442>;
      (__fch___extDM_TxRegSettings_t_Cfg_FREQCON0.408 var=95 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_FREQCON0.409)  <533>;
    } stp=3;
    call {
        () chess_separator_scheduler ()  <118>;
    } #6 off=7 nxt=7
    #7 off=7 nxt=8
    (__ptr_FREQCON1.301 var=62) const_inp ()  <369>;
    <103> {
      (__fch_ps_ActTxRegSettings.109 var=99 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <437>;
      (__fch_ps_ActTxRegSettings.405 var=99 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.109)  <530>;
    } stp=0;
    <104> {
      (__fch___extDM_TxRegSettings_t_Cfg_FREQCON1.113 var=103 stl=DMw_r) _pl_const_load_11_B1 (__fch_ps_ActTxRegSettings.404 __extDM_TxRegSettings_t_Cfg_FREQCON1.46)  <438>;
      (__fch_ps_ActTxRegSettings.404 var=99 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.405)  <529>;
      (__fch___extDM_TxRegSettings_t_Cfg_FREQCON1.411 var=103 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_FREQCON1.113)  <536>;
    } stp=2;
    <105> {
      (FREQCON1.118 var=25 __vola.119 var=13) store_const_1_B2 (__fch___extDM_TxRegSettings_t_Cfg_FREQCON1.410 __ptr_FREQCON1.301 FREQCON1.24 __vola.108)  <439>;
      (__fch___extDM_TxRegSettings_t_Cfg_FREQCON1.410 var=103 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_FREQCON1.411)  <535>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <128>;
    } #8 off=12 nxt=9
    #9 off=12 nxt=10
    (__ptr_ASKCON.302 var=64) const_inp ()  <370>;
    <100> {
      (__fch_ps_ActTxRegSettings.120 var=107 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <434>;
      (__fch_ps_ActTxRegSettings.403 var=107 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.120)  <528>;
    } stp=0;
    <101> {
      (__fch___extDM_TxRegSettings_t_Cfg_ASKCON.124 var=111 stl=DMw_r) _pl_const_load_10_B1 (__fch_ps_ActTxRegSettings.402 __extDM_TxRegSettings_t_Cfg_ASKCON.47)  <435>;
      (__fch_ps_ActTxRegSettings.402 var=107 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.403)  <527>;
      (__fch___extDM_TxRegSettings_t_Cfg_ASKCON.413 var=111 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_ASKCON.124)  <538>;
    } stp=2;
    <102> {
      (ASKCON.129 var=26 __vola.130 var=13) store_const_1_B2 (__fch___extDM_TxRegSettings_t_Cfg_ASKCON.412 __ptr_ASKCON.302 ASKCON.25 __vola.119)  <436>;
      (__fch___extDM_TxRegSettings_t_Cfg_ASKCON.412 var=111 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_ASKCON.413)  <537>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <138>;
    } #10 off=17 nxt=11
    #11 off=17 nxt=12
    (__ptr_FSKCON.303 var=66) const_inp ()  <371>;
    <97> {
      (__fch_ps_ActTxRegSettings.131 var=115 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <431>;
      (__fch_ps_ActTxRegSettings.401 var=115 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.131)  <526>;
    } stp=0;
    <98> {
      (__fch___extDM_TxRegSettings_t_Cfg_FSKCON.135 var=119 stl=DM_r) _pl_const_load_9_B1 (__fch_ps_ActTxRegSettings.400 __extDM_TxRegSettings_t_Cfg_FSKCON.48)  <432>;
      (__fch_ps_ActTxRegSettings.400 var=115 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.401)  <525>;
      (__fch___extDM_TxRegSettings_t_Cfg_FSKCON.415 var=119 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_FSKCON.135)  <540>;
    } stp=2;
    <99> {
      (FSKCON.140 var=28 __vola.141 var=13) store_const_2_B2 (__fch___extDM_TxRegSettings_t_Cfg_FSKCON.414 __ptr_FSKCON.303 FSKCON.27 __vola.130)  <433>;
      (__fch___extDM_TxRegSettings_t_Cfg_FSKCON.414 var=119 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_FSKCON.415)  <539>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <148>;
    } #12 off=22 nxt=13
    #13 off=22 nxt=14
    (__ptr_BRGCON.304 var=68) const_inp ()  <372>;
    <94> {
      (__fch_ps_ActTxRegSettings.142 var=123 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <428>;
      (__fch_ps_ActTxRegSettings.399 var=123 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.142)  <524>;
    } stp=0;
    <95> {
      (__fch___extDM_TxRegSettings_t_Cfg_BRGCON.146 var=127 stl=DMw_r) _pl_const_load_8_B1 (__fch_ps_ActTxRegSettings.398 __extDM_TxRegSettings_t_Cfg_BRGCON.49)  <429>;
      (__fch_ps_ActTxRegSettings.398 var=123 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.399)  <523>;
      (__fch___extDM_TxRegSettings_t_Cfg_BRGCON.417 var=127 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_BRGCON.146)  <542>;
    } stp=2;
    <96> {
      (BRGCON.151 var=30 __vola.152 var=13) store_const_1_B2 (__fch___extDM_TxRegSettings_t_Cfg_BRGCON.416 __ptr_BRGCON.304 BRGCON.29 __vola.141)  <430>;
      (__fch___extDM_TxRegSettings_t_Cfg_BRGCON.416 var=127 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_BRGCON.417)  <541>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <158>;
    } #14 off=27 nxt=15
    #15 off=27 nxt=16
    (__ptr_ASKRMP.305 var=70) const_inp ()  <373>;
    <91> {
      (__fch_ps_ActTxRegSettings.153 var=131 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <425>;
      (__fch_ps_ActTxRegSettings.397 var=131 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.153)  <522>;
    } stp=0;
    <92> {
      (__fch___extDM_TxRegSettings_t_Cfg_ASKRMP.157 var=135 stl=DM_r) _pl_const_load_7_B1 (__fch_ps_ActTxRegSettings.396 __extDM_TxRegSettings_t_Cfg_ASKRMP.50)  <426>;
      (__fch_ps_ActTxRegSettings.396 var=131 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.397)  <521>;
      (__fch___extDM_TxRegSettings_t_Cfg_ASKRMP.419 var=135 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_ASKRMP.157)  <544>;
    } stp=2;
    <93> {
      (ASKRMP.162 var=32 __vola.163 var=13) store_const_2_B2 (__fch___extDM_TxRegSettings_t_Cfg_ASKRMP.418 __ptr_ASKRMP.305 ASKRMP.31 __vola.152)  <427>;
      (__fch___extDM_TxRegSettings_t_Cfg_ASKRMP.418 var=135 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_ASKRMP.419)  <543>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <168>;
    } #16 off=32 nxt=17
    #17 off=32 nxt=18
    (__ptr_FSKRMP.306 var=72) const_inp ()  <374>;
    <88> {
      (__fch_ps_ActTxRegSettings.164 var=139 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <422>;
      (__fch_ps_ActTxRegSettings.395 var=139 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.164)  <520>;
    } stp=0;
    <89> {
      (__fch___extDM_TxRegSettings_t_Cfg_FSKRMP.168 var=143 stl=DM_r) _pl_const_load_6_B1 (__fch_ps_ActTxRegSettings.394 __extDM_TxRegSettings_t_Cfg_FSKRMP.51)  <423>;
      (__fch_ps_ActTxRegSettings.394 var=139 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.395)  <519>;
      (__fch___extDM_TxRegSettings_t_Cfg_FSKRMP.421 var=143 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_FSKRMP.168)  <546>;
    } stp=2;
    <90> {
      (FSKRMP.173 var=34 __vola.174 var=13) store_const_2_B2 (__fch___extDM_TxRegSettings_t_Cfg_FSKRMP.420 __ptr_FSKRMP.306 FSKRMP.33 __vola.163)  <424>;
      (__fch___extDM_TxRegSettings_t_Cfg_FSKRMP.420 var=143 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_FSKRMP.421)  <545>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <178>;
    } #18 off=37 nxt=19
    #19 off=37 nxt=20
    (__ptr_PACON.307 var=74) const_inp ()  <375>;
    <85> {
      (__fch_ps_ActTxRegSettings.175 var=147 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <419>;
      (__fch_ps_ActTxRegSettings.393 var=147 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.175)  <518>;
    } stp=0;
    <86> {
      (__fch___extDM_TxRegSettings_t_Cfg_PACON.179 var=151 stl=DM_r) _pl_const_load_5_B1 (__fch_ps_ActTxRegSettings.392 __extDM_TxRegSettings_t_Cfg_PACON.52)  <420>;
      (__fch_ps_ActTxRegSettings.392 var=147 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.393)  <517>;
      (__fch___extDM_TxRegSettings_t_Cfg_PACON.423 var=151 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_PACON.179)  <548>;
    } stp=2;
    <87> {
      (PACON.184 var=36 __vola.185 var=13) store_const_2_B2 (__fch___extDM_TxRegSettings_t_Cfg_PACON.422 __ptr_PACON.307 PACON.35 __vola.174)  <421>;
      (__fch___extDM_TxRegSettings_t_Cfg_PACON.422 var=151 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_PACON.423)  <547>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <188>;
    } #20 off=42 nxt=21
    #21 off=42 nxt=22
    (__ptr_PAPWR.308 var=76) const_inp ()  <376>;
    <82> {
      (__fch_ps_ActTxRegSettings.186 var=155 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <416>;
      (__fch_ps_ActTxRegSettings.391 var=155 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.186)  <516>;
    } stp=0;
    <83> {
      (__fch___extDM_TxRegSettings_t_Cfg_PAPWR.190 var=159 stl=DM_r) _pl_const_load_4_B1 (__fch_ps_ActTxRegSettings.390 __extDM_TxRegSettings_t_Cfg_PAPWR.53)  <417>;
      (__fch_ps_ActTxRegSettings.390 var=155 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.391)  <515>;
      (__fch___extDM_TxRegSettings_t_Cfg_PAPWR.425 var=159 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_PAPWR.190)  <550>;
    } stp=2;
    <84> {
      (PAPWR.195 var=38 __vola.196 var=13) store_const_2_B2 (__fch___extDM_TxRegSettings_t_Cfg_PAPWR.424 __ptr_PAPWR.308 PAPWR.37 __vola.185)  <418>;
      (__fch___extDM_TxRegSettings_t_Cfg_PAPWR.424 var=159 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_PAPWR.425)  <549>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <198>;
    } #22 off=47 nxt=23
    #23 off=47 nxt=24
    (__ptr_PATRIM.309 var=78) const_inp ()  <377>;
    <79> {
      (__fch_ps_ActTxRegSettings.197 var=163 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <413>;
      (__fch_ps_ActTxRegSettings.389 var=163 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.197)  <514>;
    } stp=0;
    <80> {
      (__fch___extDM_TxRegSettings_t_Cfg_PATRIM.201 var=167 stl=DM_r) _pl_const_load_3_B1 (__fch_ps_ActTxRegSettings.388 __extDM_TxRegSettings_t_Cfg_PATRIM.54)  <414>;
      (__fch_ps_ActTxRegSettings.388 var=163 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.389)  <513>;
      (__fch___extDM_TxRegSettings_t_Cfg_PATRIM.427 var=167 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_PATRIM.201)  <552>;
    } stp=2;
    <81> {
      (PATRIM.206 var=40 __vola.207 var=13) store_const_2_B2 (__fch___extDM_TxRegSettings_t_Cfg_PATRIM.426 __ptr_PATRIM.309 PATRIM.39 __vola.196)  <415>;
      (__fch___extDM_TxRegSettings_t_Cfg_PATRIM.426 var=167 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_PATRIM.427)  <551>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <208>;
    } #24 off=52 nxt=25
    #25 off=52 nxt=26
    (__ptr_PALIMIT.310 var=80) const_inp ()  <378>;
    <76> {
      (__fch_ps_ActTxRegSettings.208 var=171 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <410>;
      (__fch_ps_ActTxRegSettings.387 var=171 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.208)  <512>;
    } stp=0;
    <77> {
      (__fch___extDM_TxRegSettings_t_Cfg_PALIMIT.212 var=175 stl=DM_r) _pl_const_load_2_B1 (__fch_ps_ActTxRegSettings.386 __extDM_TxRegSettings_t_Cfg_PALIMIT.55)  <411>;
      (__fch_ps_ActTxRegSettings.386 var=171 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.387)  <511>;
      (__fch___extDM_TxRegSettings_t_Cfg_PALIMIT.429 var=175 stl=RbL off=0) Rb_1_dr_move_DM_r_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_PALIMIT.212)  <554>;
    } stp=2;
    <78> {
      (PALIMIT.217 var=41 __vola.218 var=13) store_const_2_B2 (__fch___extDM_TxRegSettings_t_Cfg_PALIMIT.428 __ptr_PALIMIT.310 PALIMIT.40 __vola.207)  <412>;
      (__fch___extDM_TxRegSettings_t_Cfg_PALIMIT.428 var=175 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__fch___extDM_TxRegSettings_t_Cfg_PALIMIT.429)  <553>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <218>;
    } #26 off=57 nxt=27
    #27 off=57 nxt=28
    (__ptr_ENCCON0.311 var=82) const_inp ()  <379>;
    <73> {
      (__fch_ps_ActTxRegSettings.219 var=179 stl=DMw_r) load__pl_rd_res_reg_const_1_B1 (__ct_0t0.312 ps_ActTxRegSettings.97 __sp.90)  <407>;
      (__fch_ps_ActTxRegSettings.385 var=179 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch_ps_ActTxRegSettings.219)  <510>;
    } stp=0;
    <74> {
      (__fch___extDM_TxRegSettings_t_Cfg_ENCCON0.223 var=183 stl=DMw_r) _pl_const_load_1_B1 (__fch_ps_ActTxRegSettings.384 __extDM_TxRegSettings_t_Cfg_ENCCON0.56)  <408>;
      (__fch_ps_ActTxRegSettings.384 var=179 stl=agu_0) agu_0_1_dr_move_R46_1_int16_ (__fch_ps_ActTxRegSettings.385)  <509>;
      (__fch___extDM_TxRegSettings_t_Cfg_ENCCON0.431 var=183 stl=R46 off=0) Rw_1_dr_move_DMw_r_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_ENCCON0.223)  <556>;
    } stp=2;
    <75> {
      (ENCCON0.228 var=43 __vola.229 var=13) store_const_1_B2 (__fch___extDM_TxRegSettings_t_Cfg_ENCCON0.430 __ptr_ENCCON0.311 ENCCON0.42 __vola.218)  <409>;
      (__fch___extDM_TxRegSettings_t_Cfg_ENCCON0.430 var=183 stl=DMw_w) DMw_w_1_dr_move_Rw_1_int16__B1 (__fch___extDM_TxRegSettings_t_Cfg_ENCCON0.431)  <555>;
    } stp=4;
    call {
        () chess_separator_scheduler ()  <228>;
    } #28 off=62 nxt=30
    #30 off=62 nxt=-2
    () sink (__vola.229)  <235>;
    () sink (__sp.235)  <241>;
    () sink (ps_ActTxRegSettings.97)  <242>;
    () sink (FREQCON0.107)  <243>;
    () sink (FREQCON1.118)  <247>;
    () sink (ASKCON.129)  <248>;
    () sink (FSKCON.140)  <250>;
    () sink (BRGCON.151)  <252>;
    () sink (ASKRMP.162)  <254>;
    () sink (FSKRMP.173)  <256>;
    () sink (PACON.184)  <258>;
    () sink (PAPWR.195)  <260>;
    () sink (PATRIM.206)  <262>;
    () sink (PALIMIT.217)  <263>;
    () sink (ENCCON0.228)  <265>;
    (__ct_2s0.313 var=187) const_inp ()  <381>;
    <71> {
      (__sp.235 var=19 __seff.327 var=195 stl=c_flag_w __seff.328 var=196 stl=nz_flag_w __seff.329 var=197 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.313 __sp.90 __sp.90)  <405>;
      (__seff.435 var=195 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.327)  <560>;
      (__seff.436 var=196 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.328)  <561>;
      (__seff.437 var=197 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.329)  <562>;
    } stp=0;
    <72> {
      () ret_1_B1 ()  <406>;
    } stp=1;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,80:0,0);
3 : (0,80:32,0);
4 : (0,80:32,0);
5 : (0,82:15,3);
6 : (0,82:15,3);
7 : (0,83:15,4);
8 : (0,83:15,4);
9 : (0,85:15,5);
10 : (0,85:15,5);
11 : (0,86:15,6);
12 : (0,86:15,6);
13 : (0,87:15,7);
14 : (0,87:15,7);
15 : (0,88:15,8);
16 : (0,88:15,8);
17 : (0,89:15,9);
18 : (0,89:15,9);
19 : (0,90:15,10);
20 : (0,90:15,10);
21 : (0,91:15,11);
22 : (0,91:15,11);
23 : (0,92:15,12);
24 : (0,92:15,12);
25 : (0,93:15,13);
26 : (0,93:15,13);
27 : (0,94:15,14);
28 : (0,94:15,14);
30 : (0,98:0,14);
----------
108 : (0,80:32,0);
118 : (0,82:15,3);
128 : (0,83:15,4);
138 : (0,85:15,5);
148 : (0,86:15,6);
158 : (0,87:15,7);
168 : (0,88:15,8);
178 : (0,89:15,9);
188 : (0,90:15,10);
198 : (0,91:15,11);
208 : (0,92:15,12);
218 : (0,93:15,13);
228 : (0,94:15,14);
405 : (0,98:0,0) (0,98:0,14);
406 : (0,98:0,14);
407 : (0,94:17,13) (0,80:56,0);
408 : (0,94:36,13);
409 : (0,94:10,13);
410 : (0,93:17,12) (0,80:56,0);
411 : (0,93:36,12);
412 : (0,93:10,12);
413 : (0,92:17,11) (0,80:56,0);
414 : (0,92:36,11);
415 : (0,92:10,11);
416 : (0,91:17,10) (0,80:56,0);
417 : (0,91:36,10);
418 : (0,91:10,10);
419 : (0,90:17,9) (0,80:56,0);
420 : (0,90:36,9);
421 : (0,90:10,9);
422 : (0,89:17,8) (0,80:56,0);
423 : (0,89:36,8);
424 : (0,89:10,8);
425 : (0,88:17,7) (0,80:56,0);
426 : (0,88:36,7);
427 : (0,88:10,7);
428 : (0,87:17,6) (0,80:56,0);
429 : (0,87:36,6);
430 : (0,87:10,6);
431 : (0,86:17,5) (0,80:56,0);
432 : (0,86:36,5);
433 : (0,86:10,5);
434 : (0,85:17,4) (0,80:56,0);
435 : (0,85:36,4);
436 : (0,85:10,4);
437 : (0,83:17,3) (0,80:56,0);
438 : (0,83:36,3);
439 : (0,83:10,3);
440 : (0,82:17,2) (0,80:56,0);
441 : (0,82:36,2);
442 : (0,82:10,2);
443 : (0,80:8,0);
444 : (0,80:56,0) (0,80:32,0);

