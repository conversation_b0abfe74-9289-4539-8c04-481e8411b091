/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiKEyLLGenFunc_CS_Immo.c 11676 2018-01-02 13:17:01Z dep17622 $
  $Revision: 11676 $
*/

/**
 * @file
 * Implementation of the stubs to call immobilizer specific KEyLink Lite General
 * Functions placed in ROM.
 */

#include "phcaiKEyLLGenFunc_CS_Immo.h"
#include "phcaiKEyLLGenFunc_CS_Utils.h"
#include "phcaiKEyLLGenFunc_SCI.h"

void phcaiKEyLLGenFunc_CS_Start_Monolith_Transp(void)
{
  sys(3);
}

/**
 * Initializes the modular immobilizer emulation.
 *
 * @param immo_type Immo type that shall be initialized.
 */
error_t phcaiKEyLLGenFunc_CS_Immo_Init(
  const phcaiKEyLLGenFunc_SCI_Immo_Type_t immo_type)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_IMMO_INIT;
  phcaiKEyLLGenFunc_Func_Params.params.immo_init.immo_type = immo_type;
  call_syscall(10);
  return phcaiKEyLLGenFunc_Func_Params.params.immo_init.return_val;
}

error_t phcaiKEyLLGenFunc_CS_Immo_Reset(void)
{
  phcaiKEyLLGenFunc_Func_Params.function_code = KEYLL_FUNC_CODE_IMMO_RESET;
  call_syscall(10);
  return phcaiKEyLLGenFunc_Func_Params.params.immo_reset.return_val;
}

error_t phcaiKEyLLGenFunc_CS_Immo_ReceiveCmd(
  uint8_t* const rcv_buff, uint8_t* const len)
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_IMMO_RECEIVE_CMD;
  phcaiKEyLLGenFunc_Func_Params.params.immo_rcv.rcv_buff = rcv_buff;
  phcaiKEyLLGenFunc_Func_Params.params.immo_rcv.len = len;
  call_syscall(10);
  return phcaiKEyLLGenFunc_Func_Params.params.immo_rcv.return_val;
}

void phcaiKEyLLGenFunc_CS_Immo_ExecuteCmd(
  uint8_t* const rcv_data, const uint8_t len)
{
  phcaiKEyLLGenFunc_Func_Params.function_code
    = KEYLL_FUNC_CODE_IMMO_EXECUTE_CMD;
  phcaiKEyLLGenFunc_Func_Params.params.immo_exec.rcv_data = rcv_data;
  phcaiKEyLLGenFunc_Func_Params.params.immo_exec.len = len;
  call_syscall(10);
}

