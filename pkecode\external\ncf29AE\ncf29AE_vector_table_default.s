/*
 * User Mode Vector Table (EROM) for NCF29AE/29AF (TOKEN-SRX2 platform).
 *
 * Important: Pre-processor symbols must be defined by project settings!
 * Please refer to the documentation of preprocessor symbols in main.c.
 *
 * Note: each entry in vector table must be 32 bits wide, either
 * a) a branch instruction with absolute address (BRA.a), or
 * b) an RETI and NOP (each 16 bit wide)
 *
 * If the corresponding routine is implemented in C code,
 * it must be annotated with 'property(isr)'.
 *
 * $Id: ncf29AE_vector_table_default.s 24956 2020-01-31 09:22:44Z dep10330 $
 * $Revision: 24956 $
 */

.undef global text  _main
//.undef global text  void_user_LF_boot
//.undef global text  void_user_LF_NMI
//.undef global text  void_HT_user_command
// include SFR declarations
#include "ncf29AE_sfr.s"

.text global 0 _vector_table

  // vector 0, address 0000h: battery boot vector
  BRA.a _main

  // vector 1, address 0002h: LF - Warmboot
  //BRA.a void_user_LF_boot
  OR.b PCON0,#0x80  // VDDRST := 1

  // vector 2, address 0004h: reserved
  OR.b PCON0,#0x80  // VDDRST := 1

  // vector 3, address 0006h: LF - NMI
  //BRA.a void_user_LF_NMI
  RETI
  NOP

  // vector 4, address 0008h: reserved
  OR.b PCON0,#0x80  // VDDRST := 1

  // vector 5, address 000Ah: HT-Pro/-3/-AES User Call
  //BRA.a void_HT_user_command
  RETI
  NOP

  // vector 6, address 000Ch: reserved
  OR.b PCON0,#0x80  // VDDRST := 1

  // vector 7, address 000Eh: MRK3 core exception handler
  OR.b PCON0,#0x80  // VDDRST := 1


// eof
