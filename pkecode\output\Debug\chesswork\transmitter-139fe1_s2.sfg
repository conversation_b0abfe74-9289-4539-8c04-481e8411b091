
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:20 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i transmitter-139fe1 mrk3

[
    0 : error_t_tx_enable_Xtal_oscillator_bool_t typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : b_OnOff typ=int8_ val=0t0 bnd=a sz=1 algn=1 stl=DM tref=bool_t_DM
   21 : TXPCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_TXPCON_t_DM9
   22 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   23 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   24 : __extDM_SFR_TXPCON_t typ=int8_ bnd=b stl=DM
   25 : __extPM_void typ=uint16_ bnd=b stl=PM
   26 : __extDM_void typ=int8_ bnd=b stl=DM
   27 : __extULP_void typ=uint32_ bnd=b stl=ULP
   30 : __ptr_TXPCON typ=int16_ val=0a bnd=m adro=21
   31 : __rt typ=int8_ bnd=p tref=error_t__
   32 : __arg_b_OnOff typ=int8_ bnd=p tref=bool_t__
   37 : __ct_0t0 typ=int16_ val=0t0 bnd=m
   46 : __ct_1 typ=uint8_ val=1f bnd=m
   48 : __ct_0 typ=uint8_ val=0f bnd=m
   50 : error_t_phcaiKEyLLGenFunc_CS_UHF_XoStartUp_bool_t_bool_t typ=int16_ val=0r bnd=m
   52 : __tmp typ=int8_ bnd=m
   69 : __ct_0 typ=uint8_ val=0f bnd=m
   71 : __ct_2s0 typ=int16_ val=2s0 bnd=m
   89 : __ct_2s0 typ=int16_ val=2s0 bnd=m
  120 : __apl_nz typ=uint2_ bnd=m tref=uint2___
  129 : __either typ=bool bnd=m
  130 : __trgt typ=rel8_ val=5j bnd=m
  131 : __trgt typ=rel8_ val=5j bnd=m
  132 : __seff typ=any bnd=m
  133 : __seff typ=any bnd=m
  134 : __seff typ=any bnd=m
  135 : __seff typ=any bnd=m
  136 : __seff typ=any bnd=m
  137 : __seff typ=any bnd=m
  138 : __seff typ=any bnd=m
  139 : __seff typ=any bnd=m
  140 : __seff typ=any bnd=m
]
Ferror_t_tx_enable_Xtal_oscillator_bool_t {
    #3 off=0 nxt=4
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (b_OnOff.19 var=20) source ()  <30>;
    (TXPCON.20 var=21) source ()  <31>;
    (__extDM_int8_.21 var=22) source ()  <32>;
    (__extDM_int16_.22 var=23) source ()  <33>;
    (__extDM_SFR_TXPCON_t.23 var=24) source ()  <34>;
    (__extPM_void.24 var=25) source ()  <35>;
    (__extDM_void.25 var=26) source ()  <36>;
    (__extULP_void.26 var=27) source ()  <37>;
    (__arg_b_OnOff.31 var=32 stl=RbL off=0) inp ()  <42>;
    (__ct_0t0.216 var=37) const_inp ()  <269>;
    (__ct_2s0.219 var=89) const_inp ()  <272>;
    <64> {
      (__sp.39 var=19 __seff.238 var=138 stl=c_flag_w __seff.239 var=139 stl=nz_flag_w __seff.240 var=140 stl=o_flag_w) _mi_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.219 __sp.18 __sp.18)  <292>;
      (__seff.248 var=139 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.239)  <337>;
      (__seff.254 var=138 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.238)  <340>;
      (__seff.255 var=140 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.240)  <341>;
    } stp=0;
    <65> {
      (b_OnOff.47 var=20) _pl_rd_res_reg_const_store_1_B1 (__arg_b_OnOff.251 __ct_0t0.216 b_OnOff.19 __sp.39)  <293>;
      (__arg_b_OnOff.251 var=32 stl=DM_w) DM_w_1_dr_move_Rb_1_int8__B0 (__arg_b_OnOff.31)  <339>;
    } stp=1;
    call {
        () chess_separator_scheduler ()  <58>;
    } #4 off=3 nxt=42
    #42 off=3 nxt=44 tgt=9
    (__trgt.220 var=130) const_inp ()  <273>;
    <62> {
      (__apl_nz.204 var=120 stl=nz_flag_w __seff.235 var=136 stl=c_flag_w __seff.236 var=137 stl=o_flag_w) load__pl_rd_res_reg_const_cmp_const_1_B2 (__ct_0t0.216 b_OnOff.47 __sp.39)  <290>;
      (__apl_nz.247 var=120 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_uint2_ (__apl_nz.204)  <336>;
      (__seff.256 var=136 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.235)  <342>;
      (__seff.257 var=137 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.236)  <343>;
    } stp=0;
    <63> {
      () cc_eq__jump_const_1_B1 (__apl_nz.246 __trgt.220)  <291>;
      (__apl_nz.246 var=120 stl=nz_flag_r) nz_flag_r_1_dr_move_nz_flag_1_uint2_ (__apl_nz.247)  <335>;
    } stp=2;
    if {
        {
            () if_expr (__either.213)  <84>;
            (__either.213 var=129) undefined ()  <265>;
        } #7
        {
            #9 off=10 nxt=10
            (error_t_phcaiKEyLLGenFunc_CS_UHF_XoStartUp_bool_t_bool_t.217 var=50) const_inp ()  <270>;
            <61> {
              () call_const_1_B1 (error_t_phcaiKEyLLGenFunc_CS_UHF_XoStartUp_bool_t_bool_t.217)  <289>;
            } stp=2;
            <77> {
              (__ct_1.261 var=46 stl=a_b0) const_2_B2 ()  <322>;
              (__ct_1.260 var=46 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_1.261)  <346>;
            } stp=0;
            <78> {
              (__ct_0.263 var=48 stl=a_b0) const_1_B2 ()  <324>;
              (__ct_0.262 var=48 stl=RbH off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B3 (__ct_0.263)  <347>;
            } stp=1;
            call {
                (__tmp.81 var=52 stl=RbL off=0 TXPCON.84 var=21 __extDM.85 var=17 __extDM_SFR_TXPCON_t.86 var=24 __extDM_int16_.87 var=23 __extDM_int8_.88 var=22 __extDM_void.89 var=26 __extPM.90 var=16 __extPM_void.91 var=25 __extULP.92 var=18 __extULP_void.93 var=27 __vola.94 var=13) Ferror_t_phcaiKEyLLGenFunc_CS_UHF_XoStartUp_bool_t_bool_t (__ct_1.260 __ct_0.262 TXPCON.20 __extDM.16 __extDM_SFR_TXPCON_t.23 __extDM_int16_.22 __extDM_int8_.21 __extDM_void.25 __extPM.15 __extPM_void.24 __extULP.17 __extULP_void.26 __vola.12)  <94>;
            } #10 off=14 nxt=45
            #45 off=14 nxt=21
        } #8
        {
            (__ptr_TXPCON.215 var=30) const_inp ()  <268>;
            (__trgt.221 var=131) const_inp ()  <274>;
            <57> {
              (TXPCON.111 var=21 __vola.112 var=13 __seff.231 var=135 stl=nz_flag_w) load_const__ad_const_store_1_B1 (__ptr_TXPCON.215 TXPCON.20 __vola.12)  <285>;
              (__seff.265 var=135 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.231)  <348>;
            } stp=0;
            <58> {
              () jump_const_1_B1 (__trgt.221)  <286>;
            } stp=3;
            <81> {
              (__ct_0.267 var=69 stl=a_b0) const_1_B2 ()  <329>;
              (__ct_0.266 var=69 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_a_b0_1_uint8__B2 (__ct_0.267)  <349>;
            } stp=2;
        } #44 off=6 tgt=21
        {
            (__vola.118 var=13) merge (__vola.94 __vola.112)  <120>;
            (__extPM.119 var=16) merge (__extPM.90 __extPM.15)  <121>;
            (__extDM.120 var=17) merge (__extDM.85 __extDM.16)  <122>;
            (__extULP.121 var=18) merge (__extULP.92 __extULP.17)  <123>;
            (TXPCON.122 var=21) merge (TXPCON.84 TXPCON.111)  <124>;
            (__extDM_int8_.123 var=22) merge (__extDM_int8_.88 __extDM_int8_.21)  <125>;
            (__extDM_int16_.124 var=23) merge (__extDM_int16_.87 __extDM_int16_.22)  <126>;
            (__extDM_SFR_TXPCON_t.125 var=24) merge (__extDM_SFR_TXPCON_t.86 __extDM_SFR_TXPCON_t.23)  <127>;
            (__extPM_void.126 var=25) merge (__extPM_void.91 __extPM_void.24)  <128>;
            (__extDM_void.127 var=26) merge (__extDM_void.89 __extDM_void.25)  <129>;
            (__extULP_void.128 var=27) merge (__extULP_void.93 __extULP_void.26)  <130>;
            (__rt.264 var=31 stl=RbL off=0) merge (__tmp.81 __ct_0.266)  <325>;
        } #19
    } #6
    #21 off=14 nxt=-2
    () out (__rt.264)  <140>;
    () sink (__vola.118)  <141>;
    () sink (__extPM.119)  <144>;
    () sink (__extDM.120)  <145>;
    () sink (__extULP.121)  <146>;
    () sink (__sp.136)  <147>;
    () sink (b_OnOff.47)  <148>;
    () sink (TXPCON.122)  <149>;
    () sink (__extDM_int8_.123)  <150>;
    () sink (__extDM_int16_.124)  <151>;
    () sink (__extDM_SFR_TXPCON_t.125)  <152>;
    () sink (__extPM_void.126)  <153>;
    () sink (__extDM_void.127)  <154>;
    () sink (__extULP_void.128)  <155>;
    (__ct_2s0.218 var=71) const_inp ()  <271>;
    <54> {
      (__sp.136 var=19 __seff.225 var=132 stl=c_flag_w __seff.226 var=133 stl=nz_flag_w __seff.227 var=134 stl=o_flag_w) _pl_rd_res_reg_const_wr_res_reg_1_B2 (__ct_2s0.218 __sp.39 __sp.39)  <282>;
      (__seff.249 var=133 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.226)  <338>;
      (__seff.258 var=132 stl=c_flag off=0) c_flag_1_dr_move_c_flag_w_1_any (__seff.225)  <344>;
      (__seff.259 var=134 stl=o_flag off=0) o_flag_1_dr_move_o_flag_w_1_any (__seff.227)  <345>;
    } stp=0;
    <55> {
      () ret_1_B1 ()  <283>;
    } stp=1;
} #0
0 : 'apps/src/transmitter.c';
----------
0 : (0,101:0,0);
3 : (0,101:35,0);
4 : (0,101:35,0);
6 : (0,103:2,2);
8 : (0,112:4,3);
9 : (0,107:53,3);
10 : (0,107:11,3);
21 : (0,103:2,14);
42 : (0,103:12,2);
----------
58 : (0,101:35,0);
84 : (0,103:2,2);
94 : (0,107:11,3);
120 : (0,103:2,13);
121 : (0,103:2,13);
122 : (0,103:2,13);
123 : (0,103:2,13);
124 : (0,103:2,13);
125 : (0,103:2,13);
126 : (0,103:2,13);
127 : (0,103:2,13);
128 : (0,103:2,13);
129 : (0,103:2,13);
130 : (0,103:2,13);
282 : (0,103:2,0) (0,103:2,14);
283 : (0,103:2,14);
285 : (0,111:15,8);
289 : (0,107:11,3);
290 : (0,103:15,2) (0,101:42,0) (0,103:12,2);
291 : (0,103:12,2) (0,103:2,2);
292 : (0,101:8,0);
293 : (0,101:42,0) (0,101:35,0);
322 : (0,107:47,0);
324 : (0,107:53,0);
329 : (0,112:11,0);

