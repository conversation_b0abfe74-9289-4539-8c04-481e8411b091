/*
  -----------------------------------------------------------------------------
  (c) NXP B.V. All rights reserved.
  Disclaimer
  1. The NXP Software/Source Code is provided to Licensee "AS IS" without any
     warranties of any kind. NXP makes no warranties to Licensee and shall not
     indemnify Licensee or hold it harmless for any reason related to the NXP
     Software/Source Code or otherwise be liable to the NXP customer. The NXP
     customer acknowledges and agrees that the NXP Software/Source Code is
     provided AS-IS and accepts all risks of utilizing the NXP Software under
     the conditions set forth according to this disclaimer.

  2. NXP EXPRESSLY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING,
     BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS
     FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT OF INTELLECTUAL PROPERTY
     RIGHTS. NXP SHALL HAVE NO LIABILITY TO THE NXP CUSTOMER, OR ITS
     SUBSIDIARIES, AFFILIATES, OR ANY OTHER THIRD PARTY FOR ANY DAMAGES,
     INCLUDING WITHOUT LIMITATION, DAMAGES RESULTING OR ALLEGED TO HAVE
     RESULTED FROM ANY DEFECT, ERROR OR OMISSION IN THE NXP SOFTWARE/SOURCE
     CODE, THIRD PARTY APPLICATION SOFTWARE AND/OR DOCUMENTATION, OR AS A
     RESULT OF ANY INFRINGEMENT OF ANY INTELLECTUAL PROPERTY RIGHT OF ANY
     THIRD PARTY. IN NO EVENT SHALL NXP BE LIABLE FOR ANY INCIDENTAL,
     INDIRECT, SPECIAL, EXEMPLARY, PUNITIVE, OR CONSEQUENTIAL DAMAGES
     (INCLUDING LOST PROFITS) SUFFERED BY NXP CUSTOMER OR ITS SUBSIDIARIES,
     AFFILIATES, OR ANY OTHER THIRD PARTY ARISING OUT OF OR RELATED TO THE NXP
     SOFTWARE/SOURCE CODE EVEN IF NXP HAS BEEN ADVISED OF THE POSSIBILITY OF
     SUCH DAMAGES.

  3. NXP reserves the right to make changes to the NXP Software/Sourcecode any
     time, also without informing customer.

  4. Licensee agrees to indemnify and hold harmless NXP and its affiliated
     companies from and against any claims, suits, losses, damages,
     liabilities, costs and expenses (including reasonable attorney's fees)
     resulting from Licensee's and/or Licensee customer's/licensee's use of the
     NXP Software/Source Code.
  -----------------------------------------------------------------------------
  $Id: phcaiMRK3Versions.h 12264 2018-02-15 13:30:43Z dep10330 $
  $Revision: 12264 $
*/

/**
 * @file
 * Declaration of the version information constants and types.
 */

#ifndef PHCAIMRK3VERSIONS_H
#define PHCAIMRK3VERSIONS_H

#include "types.h"

/**
 * @addtogroup genfunclib_stubs_ver Versions
 * Manages the versions of the ROM Library software modules.
 */
/*@{*/

/** major version number of the MRK3 versions module. */
#define PHCAI_MRK3VERSIONS_VERSION_MAJOR 2U
/** minor version number of the MRK3 versions module. */
#define PHCAI_MRK3VERSIONS_VERSION_MINOR 0U

/**
 * Enumeration of the version identifiers of the software components.
 */
typedef enum {
  /** The hardware version of the MRK3 system. */
  hardware_version     = 0x0000U,
  /** The product version/identifier. */
  product_version      = 0x0001U,
  /* the CPU ID */
  mrk3_cpu_id          = 0x0002U,
  /** The overall unique system ROM version. */
  system_rom_version   = 0x1000U,
  /** The boot loader module version. */
  boot_loader_version  = 0x1001U,
  /** The monitoring and download interface (MDI) module version. */
  mdi_version          = 0x1002U,
  /** The hardware abstraction layer (HAL) module version. */
  hal_version          = 0x1003U,
  /** The general functions library module version. */
  gen_func_lib_version = 0x1004U,
  /** The versions module version. */
  version_version      = 0x1005U,
  /** The HITAG common (shared functionality) module version. */
  hitag_cmn_version    = 0x1006U,
  /** The HITAG2 immobilizer module version. */
  hitag2_version       = 0x1007U,
  /** The HITAG2 Extended immobilizer module version (optional). */
  hitag2_ext_version   = 0x1008U,
  /** The HITAG-Pro immobilizer module version (optional). */
  hitag_pro_version    = 0x1009U,
  /** The HITAG3 immobilizer module version (optional). */
  hitag3_version       = 0x100AU,
  /** The ISO 14443/3 (contactless I/F) module version (optional). */
  iso14443_3_version   = 0x100BU,
  /** The HITAGAES immobilizer module version (optional). */
  hitag_aes_version    = 0x100CU,
  /** Customer specific module version (optional). */
  custom_mod_version   = 0x1010U,
  /** Customer specific immobilizer module version (optional). */
  custom_immo_version  = 0x1013U,
  /** The first 16 bit of the 32/64 bit IDE. */
  ide_0                = 0x2000U,
  /** The second 16 bit of the 32/64 bit IDE. */
  ide_1                = 0x2001U,
  /** The third 16 bit of the 64 bit IDE. */
  ide_2                = 0x2002U,
  /** The fourth 16 bit of the 64 bit IDE. */
  ide_3                = 0x2003U,
  /** Unused enumeration. Used to fill in structure members that are not used */
  unused_version_id    = 0xF00FU
} phcaiMRK3Versions_Id_t;

/** Structure to hold the version information of one module. */
typedef struct
{
  /** The version ID */
  phcaiMRK3Versions_Id_t id;
  /** The major version number. */
  uint8_t major;
  /** The minor version number. */
  uint8_t minor;
}  phcaiMRK3Versions_Item_t;


/** Number of NUMBER_OF_MDI_ITEMS_IN_STRUCT should be generated dynamically based on the configuration
 * 9 are the fixed items number 0 - 8.
 * those are:
 * @pre hardware_version
 * @pre product_version
 * @pre system_rom_version
 * @pre boot_loader_version
 * @pre mdi_version
 * @pre hal_version
 * @pre gen_func_lib_version
 * @pre version_version
 * @pre hitag_cmn_version
 * @warning keep in mind that the mdi code adds some additional items as well (assembler code cmdGetVersion).
 * Worst-case size is put here.
 */
#define NUMBER_OF_MDI_ITEMS_IN_STRUCT  18u

/**
 * Type used to store the version information of all modules of the system
 * software.
 */
typedef struct
{
  /** The size of the array #items. */
  uint16_t nrItems;
  /** The array with the version items. */
  phcaiMRK3Versions_Item_t items[NUMBER_OF_MDI_ITEMS_IN_STRUCT];
} phcaiMRK3Versions_Info_t;


/*@}*/

#endif

