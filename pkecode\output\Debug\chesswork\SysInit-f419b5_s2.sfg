
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:29 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i SysInit-f419b5 mrk3

[
    0 : void_CPUClock_Init typ=uint16_ bnd=e stl=PM
   13 : __vola typ=uint16_ bnd=b stl=PM
   16 : __extPM typ=uint16_ bnd=b stl=PM
   17 : __extDM typ=int8_ bnd=b stl=DM
   18 : __extULP typ=uint32_ bnd=b stl=ULP
   19 : __sp typ=int16_ bnd=b stl=R7
   20 : WDCON typ=int8_ bnd=e sz=1 algn=1 stl=DM9 tref=SFR_WDCON_t_DM9
   21 : __extDM_int8_ typ=int8_ bnd=b stl=DM
   22 : __extDM_int16_ typ=int8_ bnd=b stl=DM
   23 : __extDM_SFR_WDCON_t typ=int8_ bnd=b stl=DM
   24 : __extPM_void typ=uint16_ bnd=b stl=PM
   25 : __extDM_void typ=int8_ bnd=b stl=DM
   26 : __extULP_void typ=uint32_ bnd=b stl=ULP
   29 : __ptr_WDCON typ=int16_ val=0a bnd=m adro=20
   55 : __ct_38 typ=uint8_ val=38f bnd=m
   57 : void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar typ=int16_ val=0r bnd=m
  121 : __seff typ=any bnd=m
]
Fvoid_CPUClock_Init {
    #3 off=0 nxt=6
    (__vola.12 var=13) source ()  <23>;
    (__extPM.15 var=16) source ()  <26>;
    (__extDM.16 var=17) source ()  <27>;
    (__extULP.17 var=18) source ()  <28>;
    (__sp.18 var=19) source ()  <29>;
    (WDCON.19 var=20) source ()  <30>;
    (__extDM_int8_.20 var=21) source ()  <31>;
    (__extDM_int16_.21 var=22) source ()  <32>;
    (__extDM_SFR_WDCON_t.22 var=23) source ()  <33>;
    (__extPM_void.23 var=24) source ()  <34>;
    (__extDM_void.24 var=25) source ()  <35>;
    (__extULP_void.25 var=26) source ()  <36>;
    (__ptr_WDCON.160 var=29) const_inp ()  <204>;
    <38> {
      (WDCON.42 var=20 __vola.43 var=13) store_const_const_1_B1 (__ptr_WDCON.160 WDCON.19 __vola.12)  <219>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <60>;
    } #6 off=2 nxt=26
    #26 off=2 nxt=8
    <36> {
      (WDCON.60 var=20 __vola.61 var=13 __seff.173 var=121 stl=nz_flag_w) load_const__or_const_store_1_B1 (__ptr_WDCON.160 WDCON.42 __vola.43)  <217>;
      (__seff.183 var=121 stl=nz_flag off=0) nz_flag_1_dr_move_nz_flag_w_1_any (__seff.173)  <242>;
    } stp=0;
    call {
        () chess_separator_scheduler ()  <72>;
    } #8 off=4 nxt=9
    #9 off=4 nxt=10
    (void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar.161 var=57) const_inp ()  <205>;
    <35> {
      () call_const_1_B1 (void_phcaiKEyLLGenFunc_CS_SetClkCon___uchar.161)  <216>;
    } stp=2;
    <47> {
      (__ct_38.188 var=55 stl=__CTa_b0_int8__cstP24_E1) const_1_B1 ()  <237>;
      (__ct_38.187 var=55 stl=RbL off=0) Rb_1_dr_move___CTa_b0_int8__cstP24_E1_1_uint8__B0 (__ct_38.188)  <246>;
    } stp=0;
    call {
        (WDCON.70 var=20 __extDM.71 var=17 __extDM_SFR_WDCON_t.72 var=23 __extDM_int16_.73 var=22 __extDM_int8_.74 var=21 __extDM_void.75 var=25 __extPM.76 var=16 __extPM_void.77 var=24 __extULP.78 var=18 __extULP_void.79 var=26 __vola.80 var=13) Fvoid_phcaiKEyLLGenFunc_CS_SetClkCon___uchar (__ct_38.187 WDCON.60 __extDM.16 __extDM_SFR_WDCON_t.22 __extDM_int16_.21 __extDM_int8_.20 __extDM_void.24 __extPM.15 __extPM_void.23 __extULP.17 __extULP_void.25 __vola.61)  <79>;
    } #10 off=8 nxt=13
    #13 off=8 nxt=-2
    () sink (__vola.80)  <87>;
    () sink (__extPM.76)  <90>;
    () sink (__extDM.71)  <91>;
    () sink (__extULP.78)  <92>;
    () sink (__sp.18)  <93>;
    () sink (WDCON.70)  <94>;
    () sink (__extDM_int8_.74)  <95>;
    () sink (__extDM_int16_.73)  <96>;
    () sink (__extDM_SFR_WDCON_t.72)  <97>;
    () sink (__extPM_void.77)  <98>;
    () sink (__extDM_void.75)  <99>;
    () sink (__extULP_void.79)  <100>;
    <33> {
      () ret_1_B1 ()  <214>;
    } stp=0;
} #0
0 : 'apps/src/SysInit.c';
----------
0 : (0,43:0,0);
3 : (0,46:12,2);
6 : (0,47:11,3);
8 : (0,47:18,4);
9 : (0,49:39,4);
10 : (0,49:2,4);
13 : (0,50:0,5);
26 : (0,47:11,3);
----------
60 : (0,47:11,3);
72 : (0,47:18,4);
79 : (0,49:2,4);
214 : (0,50:0,5);
216 : (0,49:2,4);
217 : (0,47:11,3);
219 : (0,46:7,1);
237 : (0,49:39,0);

