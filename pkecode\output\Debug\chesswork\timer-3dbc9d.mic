
// File generated by mist version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:25 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\mist.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -r -Dindirect_bitf +f +i timer-3dbc9d mrk3


// m5;   next: m6 (next offset: 3)
000000  1 0  "0000010010010111"   // (R7,c_flag,nz_flag,o_flag) = _mi_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000001  2 0  "0110111001000011"   // (DM[0]) = _pl_rd_res_reg_const_store_1_B1 (RwL[0],0,DM[0],R7); 
000002  0 0  "0000000000000000"   // /

// m6 chess_separator_scheduler;   next: m193 (next offset: 3)

// m193;   next: m199, jump target: m19 (next offset: 6)
000003  2 0  "0100010000110011"   // (nz_flag,c_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B1 (0,DM[0],R7); 
000004  0 0  "0000000001101110"   // /
000005  1 0  "0101000100000100"   // () = cc_ne__jump_const_1_B1 (nz_flag,4); 

// m199;   next: m281, jump target: m19 (next offset: 8)
000006  2 0  "0100110000000011"   // (DM9,PM,nz_flag) = load_const__ad_const_cmp_const_cc_eq__jump_const_1_B1 (0,3,DM9,PM); 
000007  0 0  "0000000000000100"   // /

// m281;   next: m17 (next offset: 8)

// m17 inline assembly;   next: m278 (next offset: 9)
000008  1 0  "0110100001000000" .srcref "C:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3_base/lib" "mrk3_int.h" 604 .srcref "" "apps/src/timer.c" 181  .inline_asm_begin .inline_asm_end // 

// m278;   next: m21 (next offset: 9)

// m19;   next: m21 (next offset: 9)

// m21;   next: m22 (next offset: 11)
000009  2 0  "0110110000100000"   // (DM9) = store_const_const_1_B1 (0,DM9); 
000010  0 0  "0000000000000000"   // /

// m22 chess_separator_scheduler;   next: m59 (next offset: 11)

// m59, jump target: m2 (next offset: 16)
000011  2 0  "0110111000000011"   // (RwL[0]) = load__pl_rd_res_reg_const_1_B1 (0,DM[0],R7); 
000012  0 0  "0000000000000000"   // /
000013  2 0  "0110110000000100"   // (R46[0]) = const_1_B1 (); 
000014  0 0  "0000000111110100"   // /
000015  1 0  "0101101000001011"   // () = jump_const_1_B1 (11); 

// m30;   next: m282 (next offset: 18)
000016  2 0  "0110110000100000" .loop_nesting 1    // (DM9) = store_const_const_1_B1 (0,DM9); 
000017  0 0  "0000000000000000"   // /

// m282;   next: m39 (next offset: 18)

// m39 inline assembly;   next: m43 (next offset: 19)
000018  1 0  "0110100001000000"  .loop_nesting 2 .srcref "C:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3_base/lib" "mrk3_int.h" 604 .srcref "" "apps/src/timer.c" 187  .inline_asm_begin .inline_asm_end // 

// m43;   next: m45 (next offset: 21)
000019  2 0  "0001010000100000" .loop_nesting 2    // (DM9,c_flag,nz_flag,o_flag) = _pl_load_const_const_store_1_B1 (0,DM9,DM9); 
000020  0 0  "0000000000000001"   // /

// m45 chess_separator_scheduler;   next: m215 (next offset: 21)

// m215;   next: m53, jump target: m282 (next offset: 24)
000021  2 0  "0100010000011100"   // (c_flag,nz_flag,o_flag) = load_const_cmp_2_B1 (R46[0],0,DM9); 
000022  0 0  "0000000000000000"   // /
000023  1 0  "0101100011111011"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-5); 

// m53;   next: m2 (next offset: 26)
000024  2 0  "0001010000100000" .loop_nesting 1    // (DM9,c_flag,nz_flag,o_flag) = _pl_load_const_const_store_1_B1 (0,DM9,DM9); 
000025  0 0  "0000000000000001"   // /

// m2;   next: m220 (next offset: 26)

// m220;   next: m226, jump target: m30 (next offset: 29)
000026  2 0  "0100010000011000"   // (c_flag,nz_flag,o_flag) = load_const_cmp_1_B1 (RwL[0],0,DM9); 
000027  0 0  "0000000000000000"   // /
000028  1 0  "0101100011110100"   // () = cc_b__jump_const_1_B1 (c_flag,nz_flag,-12); 

// m226;   next: m232, jump target: m73 (next offset: 32)
000029  2 0  "0100010000110011" .loop_nesting 0    // (nz_flag,c_flag,o_flag) = load__pl_rd_res_reg_const_cmp_const_1_B1 (0,DM[0],R7); 
000030  0 0  "0000000001101110"   // /
000031  1 0  "0101000100000100"   // () = cc_ne__jump_const_1_B1 (nz_flag,4); 

// m232;   next: m283, jump target: m73 (next offset: 34)
000032  2 0  "0100110000000011"   // (DM9,PM,nz_flag) = load_const__ad_const_cmp_const_cc_eq__jump_const_1_B1 (0,3,DM9,PM); 
000033  0 0  "0000000000000100"   // /

// m283;   next: m71 (next offset: 34)

// m71 inline assembly;   next: m280 (next offset: 35)
000034  1 0  "0110100001000000" .srcref "C:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3_base/lib" "mrk3_int.h" 604 .srcref "" "apps/src/timer.c" 192  .inline_asm_begin .inline_asm_end // 

// m280;   next: m76 (next offset: 35)

// m73;   next: m76 (next offset: 35)

// m76 (next offset: /)
000035  1 0  "0001010010010111"   // (R7,c_flag,nz_flag,o_flag) = _pl_rd_res_reg_const_wr_res_reg_1_B2 (2,R7,R7); 
000036  1 0  "0001101111000100"   // () = ret_1_B1 (); 

