
// File generated by noodle version P-2019.09#78e58cd307#210222, Thu Jun  8 16:46:28 2023
// Copyright 2014-2019 Synopsys, Inc. All rights reserved.
// C:\Synopsys\ipp-mrk3-e\P-2019.09a\win64\bin\WINbin\noodle.exe -B -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/isg -Iapps/inc -Icomps/SysFuncLib/intfs/inc -Icomps/UserFuncLib/inc -Iexternal -Iexternal/ncf29A1 -Itypes -Ilib/ncf29A1 -Ilib/ncf29A1/RC005 -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/runtime/include -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib/../../mrk3_base/lib -IC:/Synopsys/ipp-mrk3-e/P-2019.09a/win64/designs/mrk3/lib -D__tct_mrk3e__ -D__mrk3e__ -DNCF29A1 -DROMCODE_VERSION=5 -DEROMSIZEKB=32 -DCRYPT_HT3 -DHT3_INIT -imrk3_chess.h -i../../mrk3_base/lib/mrk3_envelope.h +Osc,uc +NOreloc +Ocul +Sal +Sca +Osps +NOtcr +NOcar +NOcse +NOcce +NOild +NOrlt +woutput/Debug/chesswork apps/src/SysInit.c mrk3

toolrelease _19R3;


// additional
prop gp_offset_type = ( __sint );

// void phcaiKEyLLGenFunc_CS_SetClkCon(const uint8_t)
Fvoid_phcaiKEyLLGenFunc_CS_SetClkCon___uchar : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_SetClkCon" 'void phcaiKEyLLGenFunc_CS_SetClkCon(const uint8_t)'; 
    arg : ( int8_:i );
    loc : ( RbL[0] );
}

// error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait(const uint8_t * const, const uint16_t)
Ferror_t_phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait___P__uchar___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait" 'error_t phcaiKEyLLGenFunc_CS_ULPEE_WriteAndProgramPageNotWait(const uint8_t * const, const uint16_t)'; 
    arg : ( int8_:r int16_:i int16_:i );
    loc : ( RbL[0] R46[0] RwL[0] );
}

// void phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(uint8_t * const, const uint16_t)
Fvoid_phcaiKEyLLGenFunc_CS_ULPEE_ReadPage___P__uchar___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_ULPEE_ReadPage" 'void phcaiKEyLLGenFunc_CS_ULPEE_ReadPage(uint8_t * const, const uint16_t)'; 
    arg : ( int16_:i int16_:i );
    loc : ( R46[0] RwL[0] );
}

// void phcaiKEyLLGenFunc_CS_ResetVBat()
Fvoid_phcaiKEyLLGenFunc_CS_ResetVBat : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_ResetVBat" 'void phcaiKEyLLGenFunc_CS_ResetVBat()'; 
}

// void phcaiKEyLLGenFunc_CS_SetBatPORFlag(const bool_t)
Fvoid_phcaiKEyLLGenFunc_CS_SetBatPORFlag_bool_t : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_CS_SetBatPORFlag" 'void phcaiKEyLLGenFunc_CS_SetBatPORFlag(const bool_t)'; 
    arg : ( int8_:i );
    loc : ( RbL[0] );
}

// void phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait(const uint16_t)
Fvoid_phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait" 'void phcaiKEyLLGenFunc_ULPEE_ActivateModuleWait(const uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
}

// void phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules()
Fvoid_phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules" 'void phcaiKEyLLGenFunc_ULPEE_DeactivateAllModules()'; 
}

// uint8_t phcaiKEyLLGenFunc_ULPEE_ReadOneByte(const uint16_t)
F__uchar_phcaiKEyLLGenFunc_ULPEE_ReadOneByte___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_ULPEE_ReadOneByte" 'uint8_t phcaiKEyLLGenFunc_ULPEE_ReadOneByte(const uint16_t)'; 
    arg : ( int8_:r int16_:i );
    loc : ( RbL[0] RwL[0] );
}

// uint16_t phcaiKEyLLGenFunc_ULPEE_ReadOneWord(const uint16_t)
F__ushort_phcaiKEyLLGenFunc_ULPEE_ReadOneWord___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_ULPEE_ReadOneWord" 'uint16_t phcaiKEyLLGenFunc_ULPEE_ReadOneWord(const uint16_t)'; 
    arg : ( int16_:r int16_:i );
    loc : ( RwL[0] RwL[0] );
}

// void phcaiKEyLLGenFunc_timer0_delay_us(uint16_t)
Fvoid_phcaiKEyLLGenFunc_timer0_delay_us___ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_timer0_delay_us" 'void phcaiKEyLLGenFunc_timer0_delay_us(uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
}

// error_t phcaiKEyLLGenFunc_ADC_check_VbatMin(uint16_t, uint16_t *)
Ferror_t_phcaiKEyLLGenFunc_ADC_check_VbatMin___ushort___P__ushort : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_ADC_check_VbatMin" 'error_t phcaiKEyLLGenFunc_ADC_check_VbatMin(uint16_t, uint16_t *)'; 
    arg : ( int8_:r int16_:i int16_:i );
    loc : ( RbL[0] RwL[0] R46[0] );
}

// void phcaiKEyLLGenFunc_Util_Debounce(uint8_t *, uint16_t, uint8_t, uint8_t)
Fvoid_phcaiKEyLLGenFunc_Util_Debounce___P__uchar___ushort___uchar___uchar : user_defined, called {
    fnm : "phcaiKEyLLGenFunc_Util_Debounce" 'void phcaiKEyLLGenFunc_Util_Debounce(uint8_t *, uint16_t, uint8_t, uint8_t)'; 
    arg : ( int16_:i int16_:i int8_:i int8_:i );
    loc : ( R46[0] RwL[0] RbL[1] RbH[1] );
}

// void timer_delay_ms(uint16_t)
Fvoid_timer_delay_ms___ushort : user_defined, called {
    fnm : "timer_delay_ms" 'void timer_delay_ms(uint16_t)'; 
    arg : ( int16_:i );
    loc : ( RwL[0] );
}

// void CPUClock_Init()
Fvoid_CPUClock_Init : user_defined, called {
    fnm : "CPUClock_Init" 'void CPUClock_Init()'; 
    frm : ( y=2 );
    llv : 0 0 * * 0 ;
}

// void GPIO_Init()
Fvoid_GPIO_Init : user_defined, called {
    fnm : "GPIO_Init" 'void GPIO_Init()'; 
    frm : ( y=2 );
    llv : 0 0 0 0 0 ;
}

// int8_t Vbat_Check(uint16_t)
F__schar_Vbat_Check___ushort : user_defined, called {
    fnm : "Vbat_Check" 'int8_t Vbat_Check(uint16_t)'; 
    arg : ( int8_:r int16_:i );
    loc : ( RbL[0] RwL[0] );
    frm : ( y=2 l=4 );
    llv : 0 0 * * 0 ;
}

// void WUP_Init()
Fvoid_WUP_Init : user_defined, called {
    fnm : "WUP_Init" 'void WUP_Init()'; 
    frm : ( y=2 l=8 );
    llv : 0 0 * * 0 ;
}

// void hw_refresh_VBAT_VBATREG_registers()
Fvoid_hw_refresh_VBAT_VBATREG_registers : user_defined, called {
    fnm : "hw_refresh_VBAT_VBATREG_registers" 'void hw_refresh_VBAT_VBATREG_registers()'; 
    frm : ( y=2 l=12 );
    llv : 0 0 * * 0 ;
}

// void BatReset()
Fvoid_BatReset : user_defined, called {
    fnm : "BatReset" 'void BatReset()'; 
    frm : ( y=2 );
    llv : 0 0 * * 0 ;
}

// void Power_Off()
Fvoid_Power_Off : user_defined, never_returns, called {
    fnm : "Power_Off" 'void Power_Off()'; 
    frm : ( y=2 );
    llv : 0 0 * * 0 ;
}

// void read_IDE_from_ULPEE()
Fvoid_read_IDE_from_ULPEE : user_defined, called {
    fnm : "read_IDE_from_ULPEE" 'void read_IDE_from_ULPEE()'; 
    frm : ( y=2 l=8 );
    llv : 0 0 * * 0 ;
}

// void restore_IDE_in_RAM()
Fvoid_restore_IDE_in_RAM : user_defined, called {
    fnm : "restore_IDE_in_RAM" 'void restore_IDE_in_RAM()'; 
    frm : ( y=2 );
    llv : 0 0 0 0 0 ;
}

// uint8_t hw_get_button_code()
F__uchar_hw_get_button_code : user_defined, called {
    fnm : "hw_get_button_code" 'uint8_t hw_get_button_code()'; 
    arg : ( int8_:r );
    loc : ( RbL[0] );
    frm : ( y=2 l=4 );
    llv : 0 0 * * 0 ;
}

